{"version": 3, "sources": ["../../src/utils/sentry.tsx"], "sourcesContent": ["import * as Sentry from \"@sentry/nextjs\";\n\nexport const sentryBaseConfig: Sentry.BrowserOptions & Sentry.NodeOptions & Sentry.VercelEdgeOptions = {\n  ignoreErrors: [\n    // React throws these errors when used with some browser extensions (eg. Google Translate)\n    \"NotFoundError: Failed to execute 'remove<PERSON>hild' on 'Node': The node to be removed is not a child of this node.\",\n    \"NotFoundError: Failed to execute 'insertBefore' on 'Node': The node before which the new node is to be inserted is not a child of this node.\",\n  ],\n\n  normalizeDepth: 5,\n  maxValueLength: 5000,\n\n  // Adjust this value in production, or use tracesSampler for greater control\n  tracesSampleRate: 1.0,\n\n  // Setting this option to true will print useful information to the console while you're setting up Sentry.\n  debug: false,\n\n  replaysOnErrorSampleRate: 1.0,\n\n  replaysSessionSampleRate: 1.0,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,IAAM,mBAA0F;AAAA,EACrG,cAAc;AAAA;AAAA,IAEZ;AAAA,IACA;AAAA,EACF;AAAA,EAEA,gBAAgB;AAAA,EAChB,gBAAgB;AAAA;AAAA,EAGhB,kBAAkB;AAAA;AAAA,EAGlB,OAAO;AAAA,EAEP,0BAA0B;AAAA,EAE1B,0BAA0B;AAC5B;", "names": []}