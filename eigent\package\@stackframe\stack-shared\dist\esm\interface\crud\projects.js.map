{"version": 3, "sources": ["../../../../src/interface/crud/projects.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport * as schemaFields from \"../../schema-fields\";\nimport { yupArray, yupObject, yupString } from \"../../schema-fields\";\n\nconst teamPermissionSchema = yupObject({\n  id: yupString().defined(),\n}).defined();\n\nconst oauthProviderSchema = yupObject({\n  id: schemaFields.oauthIdSchema.defined(),\n  type: schemaFields.oauthTypeSchema.defined(),\n  client_id: schemaFields.yupDefinedAndNonEmptyWhen(\n    schemaFields.oauthClientIdSchema,\n    { type: 'standard' },\n  ),\n  client_secret: schemaFields.yupDefinedAndNonEmptyWhen(\n    schemaFields.oauthClientSecretSchema,\n    { type: 'standard' },\n  ),\n\n  // extra params\n  facebook_config_id: schemaFields.oauthFacebookConfigIdSchema.optional(),\n  microsoft_tenant_id: schemaFields.oauthMicrosoftTenantIdSchema.optional(),\n});\n\nconst enabledOAuthProviderSchema = yupObject({\n  id: schemaFields.oauthIdSchema.defined(),\n});\n\nexport const emailConfigSchema = yupObject({\n  type: schemaFields.emailTypeSchema.defined(),\n  host: schemaFields.yupDefinedAndNonEmptyWhen(schemaFields.emailHostSchema, {\n    type: 'standard',\n  }),\n  port: schemaFields.yupDefinedWhen(schemaFields.emailPortSchema, {\n    type: 'standard',\n  }),\n  username: schemaFields.yupDefinedAndNonEmptyWhen(schemaFields.emailUsernameSchema, {\n    type: 'standard',\n  }),\n  password: schemaFields.yupDefinedAndNonEmptyWhen(schemaFields.emailPasswordSchema, {\n    type: 'standard',\n  }),\n  sender_name: schemaFields.yupDefinedAndNonEmptyWhen(schemaFields.emailSenderNameSchema, {\n    type: 'standard',\n  }),\n  sender_email: schemaFields.yupDefinedAndNonEmptyWhen(schemaFields.emailSenderEmailSchema, {\n    type: 'standard',\n  }),\n});\n\nexport const emailConfigWithoutPasswordSchema = emailConfigSchema.pick(['type', 'host', 'port', 'username', 'sender_name', 'sender_email']);\n\nconst domainSchema = yupObject({\n  domain: schemaFields.urlSchema.defined()\n    .matches(/^https?:\\/\\//, 'URL must start with http:// or https://')\n    .meta({ openapiField: { description: 'URL. Must start with http:// or https://', exampleValue: 'https://example.com' } }),\n  handler_path: schemaFields.handlerPathSchema.defined(),\n});\n\nexport const projectsCrudAdminReadSchema = yupObject({\n  id: schemaFields.projectIdSchema.defined(),\n  display_name: schemaFields.projectDisplayNameSchema.defined(),\n  description: schemaFields.projectDescriptionSchema.nonNullable().defined(),\n  created_at_millis: schemaFields.projectCreatedAtMillisSchema.defined(),\n  user_count: schemaFields.projectUserCountSchema.defined(),\n  is_production_mode: schemaFields.projectIsProductionModeSchema.defined(),\n  /** @deprecated */\n  config: yupObject({\n    allow_localhost: schemaFields.projectAllowLocalhostSchema.defined(),\n    sign_up_enabled: schemaFields.projectSignUpEnabledSchema.defined(),\n    credential_enabled: schemaFields.projectCredentialEnabledSchema.defined(),\n    magic_link_enabled: schemaFields.projectMagicLinkEnabledSchema.defined(),\n    passkey_enabled: schemaFields.projectPasskeyEnabledSchema.defined(),\n    // TODO: remove this\n    client_team_creation_enabled: schemaFields.projectClientTeamCreationEnabledSchema.defined(),\n    client_user_deletion_enabled: schemaFields.projectClientUserDeletionEnabledSchema.defined(),\n    allow_user_api_keys: schemaFields.yupBoolean().defined(),\n    allow_team_api_keys: schemaFields.yupBoolean().defined(),\n    oauth_providers: yupArray(oauthProviderSchema.defined()).defined(),\n    enabled_oauth_providers: yupArray(enabledOAuthProviderSchema.defined()).defined().meta({ openapiField: { hidden: true } }),\n    domains: yupArray(domainSchema.defined()).defined(),\n    email_config: emailConfigSchema.defined(),\n    create_team_on_sign_up: schemaFields.projectCreateTeamOnSignUpSchema.defined(),\n    team_creator_default_permissions: yupArray(teamPermissionSchema.defined()).defined(),\n    team_member_default_permissions: yupArray(teamPermissionSchema.defined()).defined(),\n    user_default_permissions: yupArray(teamPermissionSchema.defined()).defined(),\n    oauth_account_merge_strategy: schemaFields.oauthAccountMergeStrategySchema.defined(),\n  }).defined().meta({ openapiField: { hidden: true } }),\n}).defined();\n\nexport const projectsCrudClientReadSchema = yupObject({\n  id: schemaFields.projectIdSchema.defined(),\n  display_name: schemaFields.projectDisplayNameSchema.defined(),\n  config: yupObject({\n    sign_up_enabled: schemaFields.projectSignUpEnabledSchema.defined(),\n    credential_enabled: schemaFields.projectCredentialEnabledSchema.defined(),\n    magic_link_enabled: schemaFields.projectMagicLinkEnabledSchema.defined(),\n    passkey_enabled: schemaFields.projectPasskeyEnabledSchema.defined(),\n    client_team_creation_enabled: schemaFields.projectClientTeamCreationEnabledSchema.defined(),\n    client_user_deletion_enabled: schemaFields.projectClientUserDeletionEnabledSchema.defined(),\n    allow_user_api_keys: schemaFields.yupBoolean().defined(),\n    allow_team_api_keys: schemaFields.yupBoolean().defined(),\n    enabled_oauth_providers: yupArray(enabledOAuthProviderSchema.defined()).defined().meta({ openapiField: { hidden: true } }),\n  }).defined().meta({ openapiField: { hidden: true } }),\n}).defined();\n\n\nexport const projectsCrudAdminUpdateSchema = yupObject({\n  display_name: schemaFields.projectDisplayNameSchema.optional(),\n  description: schemaFields.projectDescriptionSchema.optional(),\n  is_production_mode: schemaFields.projectIsProductionModeSchema.optional(),\n  config: yupObject({\n    sign_up_enabled: schemaFields.projectSignUpEnabledSchema.optional(),\n    credential_enabled: schemaFields.projectCredentialEnabledSchema.optional(),\n    magic_link_enabled: schemaFields.projectMagicLinkEnabledSchema.optional(),\n    passkey_enabled: schemaFields.projectPasskeyEnabledSchema.optional(),\n    client_team_creation_enabled: schemaFields.projectClientTeamCreationEnabledSchema.optional(),\n    client_user_deletion_enabled: schemaFields.projectClientUserDeletionEnabledSchema.optional(),\n    allow_localhost: schemaFields.projectAllowLocalhostSchema.optional(),\n    allow_user_api_keys: schemaFields.yupBoolean().optional(),\n    allow_team_api_keys: schemaFields.yupBoolean().optional(),\n    email_config: emailConfigSchema.optional().default(undefined),\n    domains: yupArray(domainSchema.defined()).optional().default(undefined),\n    oauth_providers: yupArray(oauthProviderSchema.defined()).optional().default(undefined),\n    create_team_on_sign_up: schemaFields.projectCreateTeamOnSignUpSchema.optional(),\n    team_creator_default_permissions: yupArray(teamPermissionSchema.defined()).optional(),\n    team_member_default_permissions: yupArray(teamPermissionSchema.defined()).optional(),\n    user_default_permissions: yupArray(teamPermissionSchema.defined()).optional(),\n    oauth_account_merge_strategy: schemaFields.oauthAccountMergeStrategySchema.optional(),\n  }).optional().default(undefined),\n}).defined();\n\nexport const projectsCrudAdminCreateSchema = projectsCrudAdminUpdateSchema.concat(yupObject({\n  display_name: schemaFields.projectDisplayNameSchema.defined(),\n}).defined());\n\nexport const projectsCrudAdminDeleteSchema = schemaFields.yupMixed();\n\nexport const clientProjectsCrud = createCrud({\n  clientReadSchema: projectsCrudClientReadSchema,\n  docs: {\n    clientRead: {\n      summary: 'Get the current project',\n      description: 'Get the current project information including display name, OAuth providers and authentication methods. Useful for display the available login options to the user.',\n      tags: ['Projects'],\n    },\n  },\n});\nexport type ClientProjectsCrud = CrudTypeOf<typeof clientProjectsCrud>;\n\nexport const projectsCrud = createCrud({\n  adminReadSchema: projectsCrudAdminReadSchema,\n  adminUpdateSchema: projectsCrudAdminUpdateSchema,\n  adminDeleteSchema: projectsCrudAdminDeleteSchema,\n  docs: {\n    adminRead: {\n      summary: 'Get the current project',\n      description: 'Get the current project information and configuration including display name, OAuth providers, email configuration, etc.',\n      tags: ['Projects'],\n    },\n    adminUpdate: {\n      summary: 'Update the current project',\n      description: 'Update the current project information and configuration including display name, OAuth providers, email configuration, etc.',\n      tags: ['Projects'],\n    },\n    adminDelete: {\n      summary: 'Delete the current project',\n      description: 'Delete the current project and all associated data (including users, teams, API keys, project configs, etc.). Be careful, this action is irreversible.',\n      tags: ['Projects'],\n    },\n  },\n});\nexport type ProjectsCrud = CrudTypeOf<typeof projectsCrud>;\n\nexport const adminUserProjectsCrud = createCrud({\n  clientReadSchema: projectsCrudAdminReadSchema,\n  clientCreateSchema: projectsCrudAdminCreateSchema,\n  docs: {\n    clientList: {\n      hidden: true,\n    },\n    clientCreate: {\n      hidden: true,\n    },\n  },\n});\nexport type AdminUserProjectsCrud = CrudTypeOf<typeof adminUserProjectsCrud>;\n"], "mappings": ";AAAA,SAAqB,kBAAkB;AACvC,YAAY,kBAAkB;AAC9B,SAAS,UAAU,WAAW,iBAAiB;AAE/C,IAAM,uBAAuB,UAAU;AAAA,EACrC,IAAI,UAAU,EAAE,QAAQ;AAC1B,CAAC,EAAE,QAAQ;AAEX,IAAM,sBAAsB,UAAU;AAAA,EACpC,IAAiB,2BAAc,QAAQ;AAAA,EACvC,MAAmB,6BAAgB,QAAQ;AAAA,EAC3C,WAAwB;AAAA,IACT;AAAA,IACb,EAAE,MAAM,WAAW;AAAA,EACrB;AAAA,EACA,eAA4B;AAAA,IACb;AAAA,IACb,EAAE,MAAM,WAAW;AAAA,EACrB;AAAA;AAAA,EAGA,oBAAiC,yCAA4B,SAAS;AAAA,EACtE,qBAAkC,0CAA6B,SAAS;AAC1E,CAAC;AAED,IAAM,6BAA6B,UAAU;AAAA,EAC3C,IAAiB,2BAAc,QAAQ;AACzC,CAAC;AAEM,IAAM,oBAAoB,UAAU;AAAA,EACzC,MAAmB,6BAAgB,QAAQ;AAAA,EAC3C,MAAmB,uCAAuC,8BAAiB;AAAA,IACzE,MAAM;AAAA,EACR,CAAC;AAAA,EACD,MAAmB,4BAA4B,8BAAiB;AAAA,IAC9D,MAAM;AAAA,EACR,CAAC;AAAA,EACD,UAAuB,uCAAuC,kCAAqB;AAAA,IACjF,MAAM;AAAA,EACR,CAAC;AAAA,EACD,UAAuB,uCAAuC,kCAAqB;AAAA,IACjF,MAAM;AAAA,EACR,CAAC;AAAA,EACD,aAA0B,uCAAuC,oCAAuB;AAAA,IACtF,MAAM;AAAA,EACR,CAAC;AAAA,EACD,cAA2B,uCAAuC,qCAAwB;AAAA,IACxF,MAAM;AAAA,EACR,CAAC;AACH,CAAC;AAEM,IAAM,mCAAmC,kBAAkB,KAAK,CAAC,QAAQ,QAAQ,QAAQ,YAAY,eAAe,cAAc,CAAC;AAE1I,IAAM,eAAe,UAAU;AAAA,EAC7B,QAAqB,uBAAU,QAAQ,EACpC,QAAQ,gBAAgB,yCAAyC,EACjE,KAAK,EAAE,cAAc,EAAE,aAAa,4CAA4C,cAAc,sBAAsB,EAAE,CAAC;AAAA,EAC1H,cAA2B,+BAAkB,QAAQ;AACvD,CAAC;AAEM,IAAM,8BAA8B,UAAU;AAAA,EACnD,IAAiB,6BAAgB,QAAQ;AAAA,EACzC,cAA2B,sCAAyB,QAAQ;AAAA,EAC5D,aAA0B,sCAAyB,YAAY,EAAE,QAAQ;AAAA,EACzE,mBAAgC,0CAA6B,QAAQ;AAAA,EACrE,YAAyB,oCAAuB,QAAQ;AAAA,EACxD,oBAAiC,2CAA8B,QAAQ;AAAA;AAAA,EAEvE,QAAQ,UAAU;AAAA,IAChB,iBAA8B,yCAA4B,QAAQ;AAAA,IAClE,iBAA8B,wCAA2B,QAAQ;AAAA,IACjE,oBAAiC,4CAA+B,QAAQ;AAAA,IACxE,oBAAiC,2CAA8B,QAAQ;AAAA,IACvE,iBAA8B,yCAA4B,QAAQ;AAAA;AAAA,IAElE,8BAA2C,oDAAuC,QAAQ;AAAA,IAC1F,8BAA2C,oDAAuC,QAAQ;AAAA,IAC1F,qBAAkC,wBAAW,EAAE,QAAQ;AAAA,IACvD,qBAAkC,wBAAW,EAAE,QAAQ;AAAA,IACvD,iBAAiB,SAAS,oBAAoB,QAAQ,CAAC,EAAE,QAAQ;AAAA,IACjE,yBAAyB,SAAS,2BAA2B,QAAQ,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,KAAK,EAAE,CAAC;AAAA,IACzH,SAAS,SAAS,aAAa,QAAQ,CAAC,EAAE,QAAQ;AAAA,IAClD,cAAc,kBAAkB,QAAQ;AAAA,IACxC,wBAAqC,6CAAgC,QAAQ;AAAA,IAC7E,kCAAkC,SAAS,qBAAqB,QAAQ,CAAC,EAAE,QAAQ;AAAA,IACnF,iCAAiC,SAAS,qBAAqB,QAAQ,CAAC,EAAE,QAAQ;AAAA,IAClF,0BAA0B,SAAS,qBAAqB,QAAQ,CAAC,EAAE,QAAQ;AAAA,IAC3E,8BAA2C,6CAAgC,QAAQ;AAAA,EACrF,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,KAAK,EAAE,CAAC;AACtD,CAAC,EAAE,QAAQ;AAEJ,IAAM,+BAA+B,UAAU;AAAA,EACpD,IAAiB,6BAAgB,QAAQ;AAAA,EACzC,cAA2B,sCAAyB,QAAQ;AAAA,EAC5D,QAAQ,UAAU;AAAA,IAChB,iBAA8B,wCAA2B,QAAQ;AAAA,IACjE,oBAAiC,4CAA+B,QAAQ;AAAA,IACxE,oBAAiC,2CAA8B,QAAQ;AAAA,IACvE,iBAA8B,yCAA4B,QAAQ;AAAA,IAClE,8BAA2C,oDAAuC,QAAQ;AAAA,IAC1F,8BAA2C,oDAAuC,QAAQ;AAAA,IAC1F,qBAAkC,wBAAW,EAAE,QAAQ;AAAA,IACvD,qBAAkC,wBAAW,EAAE,QAAQ;AAAA,IACvD,yBAAyB,SAAS,2BAA2B,QAAQ,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,KAAK,EAAE,CAAC;AAAA,EAC3H,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,KAAK,EAAE,CAAC;AACtD,CAAC,EAAE,QAAQ;AAGJ,IAAM,gCAAgC,UAAU;AAAA,EACrD,cAA2B,sCAAyB,SAAS;AAAA,EAC7D,aAA0B,sCAAyB,SAAS;AAAA,EAC5D,oBAAiC,2CAA8B,SAAS;AAAA,EACxE,QAAQ,UAAU;AAAA,IAChB,iBAA8B,wCAA2B,SAAS;AAAA,IAClE,oBAAiC,4CAA+B,SAAS;AAAA,IACzE,oBAAiC,2CAA8B,SAAS;AAAA,IACxE,iBAA8B,yCAA4B,SAAS;AAAA,IACnE,8BAA2C,oDAAuC,SAAS;AAAA,IAC3F,8BAA2C,oDAAuC,SAAS;AAAA,IAC3F,iBAA8B,yCAA4B,SAAS;AAAA,IACnE,qBAAkC,wBAAW,EAAE,SAAS;AAAA,IACxD,qBAAkC,wBAAW,EAAE,SAAS;AAAA,IACxD,cAAc,kBAAkB,SAAS,EAAE,QAAQ,MAAS;AAAA,IAC5D,SAAS,SAAS,aAAa,QAAQ,CAAC,EAAE,SAAS,EAAE,QAAQ,MAAS;AAAA,IACtE,iBAAiB,SAAS,oBAAoB,QAAQ,CAAC,EAAE,SAAS,EAAE,QAAQ,MAAS;AAAA,IACrF,wBAAqC,6CAAgC,SAAS;AAAA,IAC9E,kCAAkC,SAAS,qBAAqB,QAAQ,CAAC,EAAE,SAAS;AAAA,IACpF,iCAAiC,SAAS,qBAAqB,QAAQ,CAAC,EAAE,SAAS;AAAA,IACnF,0BAA0B,SAAS,qBAAqB,QAAQ,CAAC,EAAE,SAAS;AAAA,IAC5E,8BAA2C,6CAAgC,SAAS;AAAA,EACtF,CAAC,EAAE,SAAS,EAAE,QAAQ,MAAS;AACjC,CAAC,EAAE,QAAQ;AAEJ,IAAM,gCAAgC,8BAA8B,OAAO,UAAU;AAAA,EAC1F,cAA2B,sCAAyB,QAAQ;AAC9D,CAAC,EAAE,QAAQ,CAAC;AAEL,IAAM,gCAA6C,sBAAS;AAE5D,IAAM,qBAAqB,WAAW;AAAA,EAC3C,kBAAkB;AAAA,EAClB,MAAM;AAAA,IACJ,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,UAAU;AAAA,IACnB;AAAA,EACF;AACF,CAAC;AAGM,IAAM,eAAe,WAAW;AAAA,EACrC,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,MAAM;AAAA,IACJ,WAAW;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,UAAU;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,UAAU;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,UAAU;AAAA,IACnB;AAAA,EACF;AACF,CAAC;AAGM,IAAM,wBAAwB,WAAW;AAAA,EAC9C,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,MAAM;AAAA,IACJ,YAAY;AAAA,MACV,QAAQ;AAAA,IACV;AAAA,IACA,cAAc;AAAA,MACZ,QAAQ;AAAA,IACV;AAAA,EACF;AACF,CAAC;", "names": []}