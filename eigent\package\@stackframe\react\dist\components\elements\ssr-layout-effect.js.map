{"version": 3, "sources": ["../../../src/components/elements/ssr-layout-effect.tsx"], "sourcesContent": ["\"use client\";\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { useLayoutEffect } from \"react\";\n\nexport function SsrScript(props: { script: string, nonce?: string }) {\n  useLayoutEffect(() => {\n    // TODO fix workaround: React has a bug where it doesn't run the script on the first CSR render if SSR has been skipped due to suspense\n    // As a workaround, we run the script in the <script> tag again after the first render\n    // Note that we do an indirect eval as described here: https://esbuild.github.io/content-types/#direct-eval\n    (0, eval)(props.script);\n  }, []);\n\n  return (\n    <script\n      suppressHydrationWarning  // the transpiler is setup differently for client/server targets, so if `script` was generated with Function.toString they will differ\n      nonce={props.nonce}\n      dangerouslySetInnerHTML={{ __html: props.script }}\n    />\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,mBAAgC;AAW5B;AATG,SAAS,UAAU,OAA2C;AACnE,oCAAgB,MAAM;AAIpB,KAAC,GAAG,MAAM,MAAM,MAAM;AAAA,EACxB,GAAG,CAAC,CAAC;AAEL,SACE;AAAA,IAAC;AAAA;AAAA,MACC,0BAAwB;AAAA,MACxB,OAAO,MAAM;AAAA,MACb,yBAAyB,EAAE,QAAQ,MAAM,OAAO;AAAA;AAAA,EAClD;AAEJ;", "names": []}