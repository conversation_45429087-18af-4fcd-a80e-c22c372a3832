{"version": 3, "sources": ["../../../../../src/components-page/account-settings/teams/team-api-keys-section.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { StackAssertionError } from \"@stackframe/stack-shared/dist/utils/errors\";\nimport { Button } from \"@stackframe/stack-ui\";\nimport { useState } from \"react\";\nimport { CreateApiKeyDialog, ShowApiKeyDialog } from \"../../../components/api-key-dialogs\";\nimport { ApiKeyTable } from \"../../../components/api-key-table\";\nimport { useStackApp, useUser } from \"../../../lib/hooks\";\nimport { TeamApiKeyFirstView } from \"../../../lib/stack-app/api-keys\";\nimport { Team } from \"../../../lib/stack-app/teams\";\nimport { useTranslation } from \"../../../lib/translations\";\nimport { Section } from \"../section\";\n\n\nexport function TeamApiKeysSection(props: { team: Team }) {\n  const user = useUser({ or: 'redirect' });\n  const team = user.useTeam(props.team.id);\n  const stackApp = useStackApp();\n  const project = stackApp.useProject();\n\n  if (!team) {\n    throw new StackAssertionError(\"Team not found\");\n  }\n\n  const teamApiKeysEnabled = project.config.allowTeamApiKeys;\n  const manageApiKeysPermission = user.usePermission(props.team, '$manage_api_keys');\n  if (!manageApiKeysPermission || !teamApiKeysEnabled) {\n    return null;\n  }\n\n  return <TeamApiKeysSectionInner team={props.team} />;\n}\n\nfunction TeamApiKeysSectionInner(props: { team: Team }) {\n  const { t } = useTranslation();\n\n  const [isNewApiKeyDialogOpen, setIsNewApiKeyDialogOpen] = useState(false);\n  const [returnedApiKey, setReturnedApiKey] = useState<TeamApiKeyFirstView | null>(null);\n\n  const apiKeys = props.team.useApiKeys();\n\n  const CreateDialog = CreateApiKeyDialog<\"team\">;\n  const ShowDialog = ShowApiKeyDialog<\"team\">;\n\n  return (\n    <>\n      <Section\n        title={t(\"API Keys\")}\n        description={t(\"API keys grant programmatic access to your team.\")}\n      >\n        <Button onClick={() => setIsNewApiKeyDialogOpen(true)}>\n          {t(\"Create API Key\")}\n        </Button>\n      </Section>\n      <ApiKeyTable apiKeys={apiKeys} />\n\n      <CreateDialog\n        open={isNewApiKeyDialogOpen}\n        onOpenChange={setIsNewApiKeyDialogOpen}\n        onKeyCreated={setReturnedApiKey}\n        createApiKey={async (data) => {\n          const apiKey = await props.team.createApiKey(data);\n          return apiKey;\n        }}\n      />\n      <ShowDialog\n        apiKey={returnedApiKey}\n        onClose={() => setReturnedApiKey(null)}\n      />\n    </>\n  );\n}\n"], "mappings": ";AAIA,SAAS,2BAA2B;AACpC,SAAS,cAAc;AACvB,SAAS,gBAAgB;AACzB,SAAS,oBAAoB,wBAAwB;AACrD,SAAS,mBAAmB;AAC5B,SAAS,aAAa,eAAe;AAGrC,SAAS,sBAAsB;AAC/B,SAAS,eAAe;AAmBf,SAeL,UAfK,KAeL,YAfK;AAhBF,SAAS,mBAAmB,OAAuB;AACxD,QAAM,OAAO,QAAQ,EAAE,IAAI,WAAW,CAAC;AACvC,QAAM,OAAO,KAAK,QAAQ,MAAM,KAAK,EAAE;AACvC,QAAM,WAAW,YAAY;AAC7B,QAAM,UAAU,SAAS,WAAW;AAEpC,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,oBAAoB,gBAAgB;AAAA,EAChD;AAEA,QAAM,qBAAqB,QAAQ,OAAO;AAC1C,QAAM,0BAA0B,KAAK,cAAc,MAAM,MAAM,kBAAkB;AACjF,MAAI,CAAC,2BAA2B,CAAC,oBAAoB;AACnD,WAAO;AAAA,EACT;AAEA,SAAO,oBAAC,2BAAwB,MAAM,MAAM,MAAM;AACpD;AAEA,SAAS,wBAAwB,OAAuB;AACtD,QAAM,EAAE,EAAE,IAAI,eAAe;AAE7B,QAAM,CAAC,uBAAuB,wBAAwB,IAAI,SAAS,KAAK;AACxE,QAAM,CAAC,gBAAgB,iBAAiB,IAAI,SAAqC,IAAI;AAErF,QAAM,UAAU,MAAM,KAAK,WAAW;AAEtC,QAAM,eAAe;AACrB,QAAM,aAAa;AAEnB,SACE,iCACE;AAAA;AAAA,MAAC;AAAA;AAAA,QACC,OAAO,EAAE,UAAU;AAAA,QACnB,aAAa,EAAE,kDAAkD;AAAA,QAEjE,8BAAC,UAAO,SAAS,MAAM,yBAAyB,IAAI,GACjD,YAAE,gBAAgB,GACrB;AAAA;AAAA,IACF;AAAA,IACA,oBAAC,eAAY,SAAkB;AAAA,IAE/B;AAAA,MAAC;AAAA;AAAA,QACC,MAAM;AAAA,QACN,cAAc;AAAA,QACd,cAAc;AAAA,QACd,cAAc,OAAO,SAAS;AAC5B,gBAAM,SAAS,MAAM,MAAM,KAAK,aAAa,IAAI;AACjD,iBAAO;AAAA,QACT;AAAA;AAAA,IACF;AAAA,IACA;AAAA,MAAC;AAAA;AAAA,QACC,QAAQ;AAAA,QACR,SAAS,MAAM,kBAAkB,IAAI;AAAA;AAAA,IACvC;AAAA,KACF;AAEJ;", "names": []}