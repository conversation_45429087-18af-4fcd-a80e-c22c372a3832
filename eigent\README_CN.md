<div align="center"><a name="readme-top"></a>

[![][image-head]][eigent-site]

[![][image-seperator]][eigent-site]

### Eigent：全球首个多智能体工作流，释放卓越生产力

<!-- SHIELD GROUP -->

[![][download-shield]][eigent-download]
[![][github-star]][eigent-github]
[![][social-x-shield]][social-x-link]
[![][discord-image]][discord-url]<br>
[![Reddit][reddit-image]][reddit-url]
[![Wechat][wechat-image]][wechat-url]
[![][sponsor-shield]][sponsor-link]
[![][built-with-camel]][camel-github]

</div>

<hr/>
<div align="center">

**English** · [简体中文](./README_CN.md) · [官方网站][eigent-site] · [文档][docs-site] · [反馈][github-issue-link]

</div>
<br/>

**Eigent** 是全球首个 **多智能体工作流** 桌面应用程序，帮助您构建、管理和部署定制化的 AI 工作团队，将最复杂的工作流程转化为自动化任务。

基于 [CAMEL-AI][camel-site] 广受赞誉的开源项目，我们的系统引入了 **多智能体工作流**，通过并行执行、定制化和隐私保护 **提升生产力**。

### ⭐ 100% 开源 - 🥇 本地部署 - 🏆 MCP 集成

- ✅ **零配置** - 无需技术设置  
- ✅ **多智能体协作** - 处理复杂的多智能体工作流  
- ✅ **企业级功能** - SSO/访问控制  
- ✅ **本地部署**  
- ✅ **开源**  
- ✅ **支持自定义模型**  
- ✅ **MCP 集成**  

<br/>

<details>
<summary><kbd>目录</kbd></summary>

#### 目录

- [🚀 快速开始](#-快速开始)
  - [☁️ 云版本](#️-云版本)
  - [🏠 自托管（社区版）](#-自托管社区版)
  - [🏢 企业版](#-企业版)
- [✨ 核心功能](#-核心功能)
  - [🏭 工作流](#-工作流)
  - [🧠 全面模型支持](#-全面模型支持)
  - [🔌 MCP 工具集成](#-mcp-工具集成)
  - [✋ 人工介入](#-人工介入)
  - [👐 100% 开源](#-100-开源)
- [🧩 使用案例](#-使用案例)
- [🛠️ 技术栈](#️-技术栈)
  - [后端](#后端)
  - [前端](#前端)
- [🌟 保持领先](#保持领先)
- [🗺️ 路线图](#️-路线图)
- [📖 贡献](#-贡献)
  - [核心贡献者](#核心贡献者)
  - [杰出大使](#杰出大使)
- [生态系统](#生态系统)
- [📄 开源许可证](#-开源许可证)
- [🌐 社区与联系](#-社区与联系)

####

<br/>

</details>

## **🚀 快速开始**

有三种方式开始使用 Eigent：

### ☁️ 云版本

最快体验 Eigent 多智能体 AI 能力的方式是通过我们的云平台，适合希望无需复杂设置即可立即使用的团队和个人。我们将托管模型、API 和云存储，确保 Eigent 流畅运行。

- **即时访问** - 几分钟内开始构建多智能体工作流。  
- **托管基础设施** - 我们负责扩展、更新和维护。  
- **优先支持** - 订阅后获得工程团队的优先协助。  

<br/>

[![image-public-beta]][eigent-download]

<div align="right">
<a href="https://www.eigent.ai">Get started at Eigent.ai →</a>
</div>

### 🏠 自托管（社区版）

适合偏好本地控制、数据隐私或定制的用户，此选项适用于需要以下功能的组织：

- **数据隐私** - 敏感数据保留在您的基础设施内。  
- **定制化** - 修改和扩展平台以满足需求。  
- **成本控制** - 避免大规模部署的持续云费用。  

#### 1. 前提条件

- Node.js 和 npm  

#### 2. 快速开始

```bash
git clone https://github.com/eigent-ai/Eigent-desktop.git
cd Eigent-desktop
npm install
npm run dev
```

### 🏢 企业版

适合需要最高安全性、定制化和控制的组织：

- **商业许可证** - [查看许可证 →](LICENSE)  
- **独家功能**（如 SSO 和定制开发）  
- **可扩展的企业部署**  
- **协商的 SLA** 和实施服务  

📧 更多详情，请联系 [<EMAIL>](mailto:<EMAIL>)。

## **✨ 核心功能**
通过 Eigent 的强大功能释放卓越生产力的全部潜力——专为无缝集成、智能任务执行和无边界自动化而设计。

### 🏭 工作流  
部署一支专业 AI 智能体团队，协作解决复杂任务。Eigent 动态分解任务并激活多个智能体 **并行工作**。

Eigent 预定义了以下智能体工作者：

- **开发智能体**：编写和执行代码，运行终端命令。  
- **搜索智能体**：搜索网络并提取内容。  
- **文档智能体**：创建和管理文档。  
- **多模态智能体**：处理图像和音频。  

![Workforce](/public/images/feature_dynamic_workforce.gif)

[![][download-shield]][eigent-download]

<br/>

### 🧠 全面模型支持  
使用您偏好的模型本地部署 Eigent。  

![Model](/public/images/feature_local_model.gif)

[![][download-shield]][eigent-download]

<br/>

### 🔌 MCP 工具集成  
Eigent 内置大量 **模型上下文协议（MCP）** 工具（用于网页浏览、代码执行、Notion、Google 套件、Slack 等），并允许您 **安装自己的工具**。为智能体配备适合您场景的工具——甚至集成内部 API 或自定义功能——以增强其能力。

![MCP](/public/images/feature_add_mcps.gif)

[![][download-shield]][eigent-download]

<br/>

### ✋ 人工介入  
如果任务卡住或遇到不确定性，Eigent 会自动请求人工输入。  

![Human-in-the-loop](/public/images/feature_human_in_the_loop.gif)

[![][download-shield]][eigent-download]

<br/>

### 👐 100% 开源  
Eigent 完全开源。您可以下载、检查和修改代码，确保透明度并促进多智能体创新的社区驱动生态系统。

![Opensource][image-opensource]

[![][download-shield]][eigent-download]

<br/>

## 🧩 使用案例

### 1. 棕榈泉网球旅行行程与 Slack 摘要 [回放 ▶️](https://www.eigent.ai/download?share_token=IjE3NTM0MzUxNTEzMzctNzExMyI.aIeysw.MUeG6ZcBxI1GqvPDvn4dcv-CDWw__1753435151337-7113)

<details>
<summary><strong>提示：</strong> <kbd>我们是两个网球爱好者，想去观看 2026 年棕榈泉的网球比赛... <kbd></summary>
<br>
我们是两个网球爱好者，想去观看 2026 年棕榈泉的网球比赛。我住在旧金山——请准备一个详细的行程，包括航班、酒店、为期 3 天的活动安排——围绕半决赛/决赛的时间。我们喜欢徒步、素食和 Spa。预算为 5,000 美元。行程应是一个详细的时间表，包括时间、活动、费用、其他细节，以及购买门票/预订的链接（如适用）。完成后，请生成一份关于此次旅行的 HTML 报告；编写此计划的摘要，并将文本摘要和报告 HTML 链接发送到 Slack #tennis-trip-sf 频道。
</details>

<br>

<video width="100%" controls>
  <source src="public/images/palm_springs_tennis_trip_itinerary_with_slack_summary.mp4" type="video/mp4">
  您的浏览器不支持视频标签。
</video>

<br>

[![][download-shield]][eigent-download]

### 2. 从 CSV 银行数据生成 Q2 报告 [回放 ▶️](https://www.eigent.ai/download?share_token=***************************.aIjJmQ.WTdoX9mATwrcBr_w53BmGEHPo8U__1753526891808-8739)

<details>
<summary><strong>提示：</strong> <kbd>请根据我桌面上的银行转账记录文件 bank_transacation.csv... <kbd></summary>
<br>
请根据我桌面上的银行转账记录文件 bank_transacation.csv，帮我准备一份 Q2 财务报表，生成带图表的 HTML 报告，向投资者展示我们的支出情况。
</details>

<br>

<video width="100%" controls>
  <source src="public/images/generate_q2_report_from_csv_bank_data.mp4" type="video/mp4">
  您的浏览器不支持视频标签。
</video>

<br>

[![][download-shield]][eigent-download]

### 3. 英国医疗市场调研报告自动化 [回放 ▶️](https://www.eigent.ai/download?share_token=***************************.aIey-Q.Jh9QXzYrRYarY0kz_qsgoj3ewX0__1753393558797-8707)

<details>
<summary><strong>提示：</strong> <kbd>我们是一家生产高端电动滑板的公司... <kbd></summary>
<br>
我们是一家生产高端电动滑板的公司，正在考虑进入德国市场。请为我准备一份详细的市场进入可行性报告。报告需涵盖以下方面：1. 市场规模与法规；2. 消费者画像；3. 渠道与分销；4. 成本与定价；5. 综合报告与演示。
</details>

<br>

<video width="100%" controls>
  <source src="public/images/uk_healthcare_market_research_report_automation.mp4" type="video/mp4">
  您的浏览器不支持视频标签。
</video>

<br>

[![][download-shield]][eigent-download]

### 4. 德国电动滑板市场可行性 [回放 ▶️](https://www.eigent.ai/download?share_token=IjE3NTM2NTI4MjY3ODctNjk2Ig.aIjGiA.t-qIXxk_BZ4ENqa-yVIm0wMVyXU__1753652826787-696)

<details>
<summary><strong>提示：</strong> <kbd>我们是一家生产高端电动滑板的公司... <kbd></summary>
<br>
我们是一家生产高端电动滑板的公司，正在考虑进入德国市场。请为我准备一份详细的市场进入可行性报告。报告需涵盖以下方面：1. 市场规模与法规；2. 消费者画像；3. 渠道与分销；4. 成本与定价；5. 综合报告与演示。
</details>

<br>

<video width="100%" controls>
  <source src="public/images/german_electric_skateboard_market_feasibility.mp4" type="video/mp4">
  您的浏览器不支持视频标签。
</video>

<br>

[![][download-shield]][eigent-download]

### 5. 多智能体产品发布的 SEO 审计 [回放 ▶️](https://www.eigent.ai/download?share_token=IjE3NTM2OTk5NzExNDQtNTY5NiI.aIex0w.jc_NIPmfIf9e3zGt-oG9fbMi3K4__1753699971144-5696)

<details>
<summary><strong>提示：</strong> <kbd>为了支持我们新的多智能体产品发布... <kbd></summary>
<br>
为了支持我们新的多智能体产品发布，请对我们的官方网站 (https://www.camel-ai.org/) 进行全面的 SEO 审计，并提供带有可操作建议的详细优化报告。
</details>

<br>

<video width="100%" controls>
  <source src="public/images/seo_audit_for_workforce_multiagent_launch.mp4" type="video/mp4">
  您的浏览器不支持视频标签。
</video>

<br>

[![][download-shield]][eigent-download]

### 6. 识别下载文件夹中的重复文件 [回放 ▶️](https://www.eigent.ai/download?share_token=IjE3NTM3NjAzODgxNzEtMjQ4Ig.aIhKLQ.epOG--0Nj0o4Bqjtdqm9OZdaqRQ__1753760388171-248)

<details>
<summary><strong>提示：</strong> <kbd>我的 Documents 目录中有一个名为 mydocs 的文件夹... <kbd></summary>
<br>
我的 Documents 目录中有一个名为 mydocs 的文件夹。请扫描并识别所有完全或近似重复的文件——包括内容相同、文件大小或格式相同的文件（即使文件名或扩展名不同）。清晰列出它们，按相似性分组。
</details>

<br>

<video width="100%" controls>
  <source src="public/images/identify_duplicate_files_in_downloads.mp4" type="video/mp4">
  您的浏览器不支持视频标签。
</video>

<br>

[![][download-shield]][eigent-download]

## 🛠️ 技术栈

### 后端
- **框架：** FastAPI  
- **包管理器：** uv  
- **异步服务器：** Uvicorn  
- **认证：** OAuth 2.0, Passlib  
- **多智能体框架：** CAMEL  

### 前端
- **框架：** React  
- **桌面应用框架：** Electron  
- **语言：** TypeScript  
- **UI：** Tailwind CSS, Radix UI, Lucide React, Framer Motion  
- **状态管理：** Zustand  
- **流程编辑器：** React Flow  

## 🌟 保持领先

> \[!重要]
>
> **给 Eigent 加星标**，您将通过 GitHub 及时收到所有发布通知 ⭐️

![][image-star-us]

## 🗺️ 路线图

| 主题                   | 问题   | Discord 频道 |
| ------------------------ | -- |-- |
| **上下文工程** | - 提示缓存<br> - 系统提示优化<br> - 工具包文档优化<br> - 上下文压缩 | [**加入 Discord →**](https://discord.gg/D2e3rBWD) |
| **多模态增强** | - 使用浏览器时更准确的图像理解<br> - 高级视频生成 | [**加入 Discord →**](https://discord.gg/kyapNCeJ) |
| **多智能体系统** | - 工作流支持固定流程<br> - 工作流支持多轮对话 | [**加入 Discord →**](https://discord.gg/bFRmPuDB) |
| **浏览器工具包** | - BrowseCamp 集成<br> - 基准测试改进<br> - 禁止重复访问页面<br> - 自动缓存按钮点击 | [**加入 Discord →**](https://discord.gg/NF73ze5v) |
| **文档工具包** | - 支持动态文件编辑 | [**加入 Discord →**](https://discord.gg/4yAWJxYr) |
| **终端工具包** | - 基准测试改进<br> - Terminal-Bench 集成 | [**加入 Discord →**](https://discord.gg/FjQfnsrV) |
| **环境与强化学习** | - 环境设计<br> - 数据生成<br> - 强化学习框架集成（VERL, TRL, OpenRLHF） | [**加入 Discord →**](https://discord.gg/MaVZXEn8) |

## [🤝 贡献][contribution-link]

我们相信通过开源协作建立信任。您的创意贡献将推动 `Eigent` 的创新。探索我们的 GitHub 问题与项目，加入我们 🤝❤️ [贡献指南][contribution-link]

## [❤️ 赞助][sponsor-link]

Eigent 基于 [CAMEL-AI.org][camel-ai-org-github] 的研究和基础设施构建。[赞助 CAMEL-AI.org][sponsor-link] 将使 `Eigent` 变得更好。

## **📄 开源许可证**

本仓库采用 [**Eigent 开源许可证**](LICENSE)，基于 Apache 2.0 并附加额外条款。

## 🌐 社区与联系
更多信息请联系 <EMAIL>

- **GitHub Issues：** 报告错误、请求功能并跟踪开发进度。[提交问题][github-issue-link]  

- **Discord：** 获取实时支持、与社区交流并保持更新。[加入我们](https://discord.camel-ai.org/)  

- **X (Twitter)：** 关注更新、AI 见解和重要公告。[关注我们][social-x-link]  

- **微信社区：** 扫描下方二维码加入我们的微信社区。

![wechat](https://eigent-ai.github.io/.github/assets/wechat.jpg)



<!-- LINK GROUP -->
<!-- Social -->
[discord-url]: https://discord.camel-ai.org/
[discord-image]: https://img.shields.io/discord/1082486657678311454?logo=discord&labelColor=%20%235462eb&logoColor=%20%23f5f5f5&color=%20%235462eb

[built-with-camel]:https://img.shields.io/badge/-Built--with--CAMEL-4C19E8.svg?logo=data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQ4IiBoZWlnaHQ9IjI3MiIgdmlld0JveD0iMCAwIDI0OCAyNzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik04LjgzMTE3IDE4LjU4NjVMMCAzMC44MjY3QzUuNDY2OTIgMzUuMDQzMiAxNS4xMzkxIDM4LjgyNTggMjQuODExNCAzNi4yOTU5QzMwLjY5ODggNDAuOTM0MSAzOS42NzAyIDQwLjIzMTMgNDQuMTU1OSA0MC4wOTA4QzQzLjQ1NSA0Ny4zOTk0IDQyLjQ3MzcgNzAuOTU1OCA0NC4xNTU5IDEwNi43MTJDNDUuODM4IDE0Mi40NjggNzEuNzcwOCAxNjYuODY4IDg0LjUyNjkgMTc0LjU5OEw3Ni4wMDAyIDIyMEw4NC41MjY5IDI3MkgxMDguOTE4TDk4LjAwMDIgMjIwTDEwOC45MTggMTc0LjU5OEwxMjkuOTQ0IDI3MkgxNTQuNzU2TDEzNC4xNSAxNzQuNTk4SDE4Ny4xMzdMMTY2LjUzMSAyNzJIMTkxLjc2M0wyMTIuMzY5IDE3NC41OThMMjI2IDIyMEwyMTIuMzY5IDI3MkgyMzcuNjAxTDI0OC4wMDEgMjIwTDIzNy4xOCAxNzQuNTk4QzIzOS4yODMgMTY5LjExNyAyNDAuNDAxIDE2Ni45NzYgMjQxLjgwNiAxNjEuMTA1QzI0OS4zNzUgMTI5LjQ4MSAyMzUuMDc3IDEwMy45MDEgMjI2LjY2NyA5NC40ODRMMjA2LjQ4MSA3My44MjNDMTk3LjY1IDY0Ljk2ODMgMTgyLjUxMSA2NC41NDY3IDE3Mi44MzkgNzIuNTU4MUMxNjUuNzI4IDc4LjQ0NzcgMTYxLjcwMSA3OC43NzI3IDE1NC43NTYgNzIuNTU4MUMxNTEuODEyIDcwLjAyODEgMTQ0LjUzNSA2MS40ODg5IDEzNC45OTEgNTMuNTgzN0MxMjUuMzE5IDQ1LjU3MjMgMTA4LjQ5NyA0OC45NDU1IDEwMi4xODkgNTUuNjkxOUw3My41OTMxIDg0LjM2NDRWNy42MjM0OUw3OS4xMjczIDBDNjAuOTA0MiAzLjY1NDMzIDIzLjgwMjEgOS41NjMwOSAxOS43NjUgMTAuNTc1MUMxNS43Mjc5IDExLjU4NyAxMC43OTM3IDE2LjMzNzcgOC44MzExNyAxOC41ODY1WiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTQzLjIwMzggMTguNzE4N0w0OS4wOTEyIDEzLjA0OTNMNTQuOTc4NyAxOC43MTg3TDQ5LjA5MTIgMjQuODI0Mkw0My4yMDM4IDE4LjcxODdaIiBmaWxsPSIjNEMxOUU4Ii8+Cjwvc3ZnPgo=

[eigent-github]: https://github.com/eigent-ai/eigent
[github-star]: https://img.shields.io/github/stars/eigent-ai?color=F5F4F0&labelColor=gray&style=plastic&logo=github
[camel-ai-org-github]: https://github.com/camel-ai

[camel-github]: https://github.com/camel-ai/camel
[eigent-github]: https://github.com/eigent-ai/eigent
[contribution-link]: https:/github.com/eigent-ai/eigent/blob/master/CONTRIBUTING.md

[social-x-link]: https://x.com/Eigent_AI
[social-x-shield]: https://img.shields.io/badge/-%40Eigent_AI-white?labelColor=gray&logo=x&logoColor=white&style=plastic

[reddit-url]: https://www.reddit.com/r/CamelAI/
[reddit-image]: https://img.shields.io/reddit/subreddit-subscribers/CamelAI?style=plastic&logo=reddit&label=r%2FCAMEL&labelColor=white

[wechat-url]: https://ghli.org/camel/wechat.png
[wechat-image]: https://img.shields.io/badge/WeChat-CamelAIOrg-brightgreen?logo=wechat&logoColor=white

[sponsor-link]: https://github.com/sponsors/camel-ai
[sponsor-shield]: https://img.shields.io/badge/-Sponsor%20CAMEL--AI-1d1d1d?logo=github&logoColor=white&style=plastic

[eigent-download]: https://www.eigent.ai/download
[download-shield]: https://img.shields.io/badge/Download%20Eigent-363AF5?style=plastic

<!-- camel & eigent -->
[camel-site]: https://www.camel-ai.org
[eigent-site]: https://www.eigent.ai
[docs-site]: https://docs.eigent.ai
[github-issue-link]: https://github.com/eigent-ai/eigent/issues

<!-- marketing -->
[image-seperator]: https://eigent-ai.github.io/.github/assets/seperator.png 
[image-head]: https://eigent-ai.github.io/.github/assets/head.png 
[image-public-beta]: https://eigent-ai.github.io/.github/assets/banner.png
[image-star-us]: https://eigent-ai.github.io/.github/assets/star-us.gif
[image-opensource]: https://eigent-ai.github.io/.github/assets/opensource.png
[image-wechat]: https://eigent-ai.github.io/.github/assets/wechat.png

<!-- feature -->
[image-workforce]: https://eigent-ai.github.io/.github/assets/feature_dynamic_workforce.gif
[image-human-in-the-loop]: https://eigent-ai.github.io/.github/assets/feature_human_in_the_loop.gif
[image-customise-workers]: https://eigent-ai.github.io/.github/assets/feature_customise_workers.gif
[image-add-mcps]: https://eigent-ai.github.io/.github/assets/feature_add_mcps.gif
[image-local-model]: https://eigent-ai.github.io/.github/assets/feature_local_model.gif
