{"version": 3, "sources": ["../../src/utils/math.tsx"], "sourcesContent": ["/**\n * Similar to the modulo operator, but always returns a positive number (even when the input is negative).\n */\nexport function remainder(n: number, d: number): number {\n  return ((n % d) + Math.abs(d)) % d;\n}\nundefined?.test(\"remainder\", ({ expect }) => {\n  expect(remainder(10, 3)).toBe(1);\n  expect(remainder(10, 5)).toBe(0);\n  expect(remainder(10, 7)).toBe(3);\n  // Test with negative numbers\n  expect(remainder(-10, 3)).toBe(2);\n  expect(remainder(-5, 2)).toBe(1);\n  expect(remainder(-7, 4)).toBe(1);\n  // Test with decimal numbers\n  expect(remainder(10.5, 3)).toBeCloseTo(1.5);\n  expect(remainder(-10.5, 3)).toBeCloseTo(1.5);\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGO,SAAS,UAAU,GAAW,GAAmB;AACtD,UAAS,IAAI,IAAK,KAAK,IAAI,CAAC,KAAK;AACnC;", "names": []}