body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f9f9f9;
    padding: 20px;
    max-width: 800px;
    margin: auto;
}

h1, h2, h3 {
    color: #0056b3;
    margin-bottom: 15px;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
}

a {
    color: #ff5722;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #e64a19;
    text-decoration: underline;
}

p {
    margin-bottom: 15px;
}

blockquote {
    border-left: 4px solid #ff5722;
    padding-left: 15px;
    color: #666;
    font-style: italic;
    margin: 20px 0;
}

ul, ol {
    margin-left: 20px;
    margin-bottom: 15px;
}

img {
    max-width: 100%;
    height: auto;
    border-radius: 5px;
}

button, .btn {
    background-color: #ff5722;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

button:hover, .btn:hover {
    background-color: #e64a19;
}

footer {
    text-align: center;
    padding: 20px;
    color: #aaa;
    font-size: 0.9rem;
    border-top: 1px solid #ddd;
    margin-top: 40px;
}