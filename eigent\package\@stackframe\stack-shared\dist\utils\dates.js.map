{"version": 3, "sources": ["../../src/utils/dates.tsx"], "sourcesContent": ["import { remainder } from \"./math\";\n\nexport function isWeekend(date: Date): boolean {\n  return date.getDay() === 0 || date.getDay() === 6;\n}\n\nundefined?.test(\"isWeekend\", ({ expect }) => {\n  // Sunday (day 0)\n  expect(isWeekend(new Date(2023, 0, 1))).toBe(true);\n  // Saturday (day 6)\n  expect(isWeekend(new Date(2023, 0, 7))).toBe(true);\n  // Monday (day 1)\n  expect(isWeekend(new Date(2023, 0, 2))).toBe(false);\n  // Friday (day 5)\n  expect(isWeekend(new Date(2023, 0, 6))).toBe(false);\n});\n\nconst agoUnits = [\n  [60, 'second'],\n  [60, 'minute'],\n  [24, 'hour'],\n  [7, 'day'],\n  [5, 'week'],\n] as const;\n\nexport function fromNow(date: Date): string {\n  return fromNowDetailed(date).result;\n}\n\nundefined?.test(\"fromNow\", ({ expect }) => {\n  // Set a fixed date for testing\n  const fixedDate = new Date(\"2023-01-15T12:00:00.000Z\");\n\n  // Use Vitest's fake timers\n  undefined?.vi.useFakeTimers();\n  undefined?.vi.setSystemTime(fixedDate);\n\n  // Test past times\n  expect(fromNow(new Date(\"2023-01-15T11:59:50.000Z\"))).toBe(\"just now\");\n  expect(fromNow(new Date(\"2023-01-15T11:59:00.000Z\"))).toBe(\"1 minute ago\");\n  expect(fromNow(new Date(\"2023-01-15T11:00:00.000Z\"))).toBe(\"1 hour ago\");\n  expect(fromNow(new Date(\"2023-01-14T12:00:00.000Z\"))).toBe(\"1 day ago\");\n  expect(fromNow(new Date(\"2023-01-08T12:00:00.000Z\"))).toBe(\"1 week ago\");\n\n  // Test future times\n  expect(fromNow(new Date(\"2023-01-15T12:00:10.000Z\"))).toBe(\"just now\");\n  expect(fromNow(new Date(\"2023-01-15T12:01:00.000Z\"))).toBe(\"in 1 minute\");\n  expect(fromNow(new Date(\"2023-01-15T13:00:00.000Z\"))).toBe(\"in 1 hour\");\n  expect(fromNow(new Date(\"2023-01-16T12:00:00.000Z\"))).toBe(\"in 1 day\");\n  expect(fromNow(new Date(\"2023-01-22T12:00:00.000Z\"))).toBe(\"in 1 week\");\n\n  // Test very old dates (should use date format)\n  expect(fromNow(new Date(\"2022-01-15T12:00:00.000Z\"))).toMatch(/Jan 15, 2022/);\n\n  // Restore real timers\n  undefined?.vi.useRealTimers();\n});\n\nexport function fromNowDetailed(date: Date): {\n  result: string,\n  /**\n   * May be Infinity if the result will never change.\n   */\n  secondsUntilChange: number,\n} {\n  if (!(date instanceof Date)) {\n    throw new Error(`fromNow only accepts Date objects (received: ${date})`);\n  }\n\n  const now = new Date();\n  const elapsed = now.getTime() - date.getTime();\n\n  let remainingInUnit = Math.abs(elapsed) / 1000;\n  if (remainingInUnit < 15) {\n    return {\n      result: 'just now',\n      secondsUntilChange: 15 - remainingInUnit,\n    };\n  }\n  let unitInSeconds = 1;\n  for (const [nextUnitSize, unitName] of agoUnits) {\n    const rounded = Math.round(remainingInUnit);\n    if (rounded < nextUnitSize) {\n      if (elapsed < 0) {\n        return {\n          result: `in ${rounded} ${unitName}${rounded === 1 ? '' : 's'}`,\n          secondsUntilChange: remainder((remainingInUnit - rounded + 0.5) * unitInSeconds, unitInSeconds),\n        };\n      } else {\n        return {\n          result: `${rounded} ${unitName}${rounded === 1 ? '' : 's'} ago`,\n          secondsUntilChange: remainder((rounded - remainingInUnit - 0.5) * unitInSeconds, unitInSeconds),\n        };\n      }\n    }\n    unitInSeconds *= nextUnitSize;\n    remainingInUnit /= nextUnitSize;\n  }\n\n  return {\n    result: date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' }),\n    secondsUntilChange: Infinity,\n  };\n}\n\n/**\n * Returns a string representation of the given date in the format expected by the `datetime-local` input type.\n */\nexport function getInputDatetimeLocalString(date: Date): string {\n  date = new Date(date);\n  date.setMinutes(date.getMinutes() - date.getTimezoneOffset());\n  return date.toISOString().slice(0, 19);\n}\n\nundefined?.test(\"getInputDatetimeLocalString\", ({ expect }) => {\n  // Use Vitest's fake timers to ensure consistent timezone behavior\n  undefined?.vi.useFakeTimers();\n\n  // Test with a specific date\n  const mockDate = new Date(\"2023-01-15T12:30:45.000Z\");\n  const result = getInputDatetimeLocalString(mockDate);\n\n  // The result should be in the format YYYY-MM-DDTHH:MM:SS\n  expect(result).toMatch(/^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}$/);\n\n  // Test with different dates\n  const dates = [\n    new Date(\"2023-01-01T00:00:00.000Z\"),\n    new Date(\"2023-06-15T23:59:59.000Z\"),\n    new Date(\"2023-12-31T12:34:56.000Z\"),\n  ];\n\n  for (const date of dates) {\n    const result = getInputDatetimeLocalString(date);\n    expect(result).toMatch(/^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}$/);\n  }\n\n  // Restore real timers\n  undefined?.vi.useRealTimers();\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAA0B;AAEnB,SAAS,UAAU,MAAqB;AAC7C,SAAO,KAAK,OAAO,MAAM,KAAK,KAAK,OAAO,MAAM;AAClD;AAaA,IAAM,WAAW;AAAA,EACf,CAAC,IAAI,QAAQ;AAAA,EACb,CAAC,IAAI,QAAQ;AAAA,EACb,CAAC,IAAI,MAAM;AAAA,EACX,CAAC,GAAG,KAAK;AAAA,EACT,CAAC,GAAG,MAAM;AACZ;AAEO,SAAS,QAAQ,MAAoB;AAC1C,SAAO,gBAAgB,IAAI,EAAE;AAC/B;AA+BO,SAAS,gBAAgB,MAM9B;AACA,MAAI,EAAE,gBAAgB,OAAO;AAC3B,UAAM,IAAI,MAAM,gDAAgD,IAAI,GAAG;AAAA,EACzE;AAEA,QAAM,MAAM,oBAAI,KAAK;AACrB,QAAM,UAAU,IAAI,QAAQ,IAAI,KAAK,QAAQ;AAE7C,MAAI,kBAAkB,KAAK,IAAI,OAAO,IAAI;AAC1C,MAAI,kBAAkB,IAAI;AACxB,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,oBAAoB,KAAK;AAAA,IAC3B;AAAA,EACF;AACA,MAAI,gBAAgB;AACpB,aAAW,CAAC,cAAc,QAAQ,KAAK,UAAU;AAC/C,UAAM,UAAU,KAAK,MAAM,eAAe;AAC1C,QAAI,UAAU,cAAc;AAC1B,UAAI,UAAU,GAAG;AACf,eAAO;AAAA,UACL,QAAQ,MAAM,OAAO,IAAI,QAAQ,GAAG,YAAY,IAAI,KAAK,GAAG;AAAA,UAC5D,wBAAoB,wBAAW,kBAAkB,UAAU,OAAO,eAAe,aAAa;AAAA,QAChG;AAAA,MACF,OAAO;AACL,eAAO;AAAA,UACL,QAAQ,GAAG,OAAO,IAAI,QAAQ,GAAG,YAAY,IAAI,KAAK,GAAG;AAAA,UACzD,wBAAoB,wBAAW,UAAU,kBAAkB,OAAO,eAAe,aAAa;AAAA,QAChG;AAAA,MACF;AAAA,IACF;AACA,qBAAiB;AACjB,uBAAmB;AAAA,EACrB;AAEA,SAAO;AAAA,IACL,QAAQ,KAAK,mBAAmB,SAAS,EAAE,MAAM,WAAW,OAAO,SAAS,KAAK,UAAU,CAAC;AAAA,IAC5F,oBAAoB;AAAA,EACtB;AACF;AAKO,SAAS,4BAA4B,MAAoB;AAC9D,SAAO,IAAI,KAAK,IAAI;AACpB,OAAK,WAAW,KAAK,WAAW,IAAI,KAAK,kBAAkB,CAAC;AAC5D,SAAO,KAAK,YAAY,EAAE,MAAM,GAAG,EAAE;AACvC;", "names": []}