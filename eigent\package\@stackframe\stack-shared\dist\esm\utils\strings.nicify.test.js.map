{"version": 3, "sources": ["../../../src/utils/strings.nicify.test.ts"], "sourcesContent": ["import { describe, expect, test } from \"vitest\";\nimport { NicifyOptions, deindent, nicify } from \"./strings\";\n\ndescribe(\"nicify\", () => {\n  describe(\"primitive values\", () => {\n    test(\"numbers\", () => {\n      expect(nicify(123)).toBe(\"123\");\n      expect(nicify(123n)).toBe(\"123n\");\n    });\n\n    test(\"strings\", () => {\n      expect(nicify(\"hello\")).toBe('\"hello\"');\n    });\n\n    test(\"booleans\", () => {\n      expect(nicify(true)).toBe(\"true\");\n      expect(nicify(false)).toBe(\"false\");\n    });\n\n    test(\"null and undefined\", () => {\n      expect(nicify(null)).toBe(\"null\");\n      expect(nicify(undefined)).toBe(\"undefined\");\n    });\n\n    test(\"symbols\", () => {\n      expect(nicify(Symbol(\"test\"))).toBe(\"Symbol(test)\");\n    });\n  });\n\n  describe(\"arrays\", () => {\n    test(\"empty array\", () => {\n      expect(nicify([])).toBe(\"[]\");\n    });\n\n    test(\"single-element array\", () => {\n      expect(nicify([1])).toBe(\"[1]\");\n    });\n\n    test(\"single-element array with long content\", () => {\n      expect(nicify([\"123123123123123\"])).toBe('[\"123123123123123\"]');\n    });\n\n    test(\"flat array\", () => {\n      expect(nicify([1, 2, 3])).toBe(\"[1, 2, 3]\");\n    });\n\n    test(\"longer array\", () => {\n      expect(nicify([10000, 2, 3])).toBe(deindent`\n        [\n          10000,\n          2,\n          3,\n        ]\n      `);\n    });\n\n    test(\"nested array\", () => {\n      expect(nicify([1, [2, 3]])).toBe(deindent`\n        [\n          1,\n          [2, 3],\n        ]\n      `);\n    });\n  });\n\n  describe(\"objects\", () => {\n    test(\"empty object\", () => {\n      expect(nicify({})).toBe(\"{}\");\n    });\n\n    test(\"simple object\", () => {\n      expect(nicify({ a: 1 })).toBe('{ \"a\": 1 }');\n    });\n\n    test(\"multiline object\", () => {\n      expect(nicify({ a: 1, b: 2 })).toBe(deindent`\n        {\n          \"a\": 1,\n          \"b\": 2,\n        }\n      `);\n    });\n  });\n\n  describe(\"custom classes\", () => {\n    test(\"class instance\", () => {\n      class TestClass {\n        constructor(public value: number) {}\n      }\n      expect(nicify(new TestClass(42))).toBe('TestClass { \"value\": 42 }');\n    });\n  });\n\n  describe(\"built-in objects\", () => {\n    test(\"URL\", () => {\n      expect(nicify(new URL(\"https://example.com\"))).toBe('URL(\"https://example.com/\")');\n    });\n\n    test(\"TypedArrays\", () => {\n      expect(nicify(new Uint8Array([1, 2, 3]))).toBe(\"Uint8Array([1,2,3])\");\n      expect(nicify(new Int32Array([1, 2, 3]))).toBe(\"Int32Array([1,2,3])\");\n    });\n\n    test(\"Error objects\", () => {\n      const error = new Error(\"test error\");\n      const nicifiedError = nicify({ error });\n      expect(nicifiedError).toMatch(new RegExp(deindent`\n        ^\\{\n          \"error\": Error: test error\n            Stack:\n              at (.|\\\\n)*\n        \\}$\n      `));\n    });\n\n    test(\"Error objects with cause and an extra property\", () => {\n      const error = new Error(\"test error\", { cause: new Error(\"cause\") });\n      (error as any).extra = \"something\";\n      const nicifiedError = nicify(error, { lineIndent: \"--\" });\n      expect(nicifiedError).toMatch(new RegExp(deindent`\n        ^Error: test error\n        --Stack:\n        ----at (.|\\\\n)+\n        --Extra properties: \\{ \"extra\": \"something\" \\}\n        --Cause:\n        ----Error: cause\n        ------Stack:\n        --------at (.|\\\\n)+$\n      `));\n    });\n\n    test(\"Headers\", () => {\n      const headers = new Headers();\n      headers.append(\"Content-Type\", \"application/json\");\n      headers.append(\"Accept\", \"text/plain\");\n      expect(nicify(headers)).toBe(deindent`\n        Headers {\n          \"accept\": \"text/plain\",\n          \"content-type\": \"application/json\",\n        }`\n      );\n    });\n  });\n\n  describe(\"multiline strings\", () => {\n    test(\"basic multiline\", () => {\n      expect(nicify(\"line1\\nline2\")).toBe('deindent`\\n  line1\\n  line2\\n`');\n    });\n\n    test(\"multiline with trailing newline\", () => {\n      expect(nicify(\"line1\\nline2\\n\")).toBe('deindent`\\n  line1\\n  line2\\n` + \"\\\\n\"');\n    });\n  });\n\n  describe(\"circular references\", () => {\n    test(\"object with self reference\", () => {\n      const circular: any = { a: 1 };\n      circular.self = circular;\n      expect(nicify(circular)).toBe(deindent`\n        {\n          \"a\": 1,\n          \"self\": Ref<value>,\n        }`\n      );\n    });\n  });\n\n  describe(\"configuration options\", () => {\n    test(\"maxDepth\", () => {\n      const deep = { a: { b: { c: { d: { e: 1 } } } } };\n      expect(nicify(deep, { maxDepth: 2 })).toBe('{ \"a\": { \"b\": { ... } } }');\n    });\n\n    test(\"lineIndent\", () => {\n      expect(nicify({ a: 1, b: 2 }, { lineIndent: \"    \" })).toBe(deindent`\n        {\n            \"a\": 1,\n            \"b\": 2,\n        }\n      `);\n    });\n\n    test(\"hideFields\", () => {\n      expect(nicify({ a: 1, b: 2, secret: \"hidden\" }, { hideFields: [\"secret\"] })).toBe(deindent`\n        {\n          \"a\": 1,\n          \"b\": 2,\n          <some fields may have been hidden>,\n        }\n      `);\n    });\n  });\n\n  describe(\"custom overrides\", () => {\n    test(\"override with custom type\", () => {\n      expect(nicify({ type: \"special\" }, {\n        overrides: ((value: unknown) => {\n          if (typeof value === \"object\" && value && \"type\" in value && (value as any).type === \"special\") {\n            return \"SPECIAL\";\n          }\n          return null;\n        }) as NicifyOptions[\"overrides\"]\n      })).toBe(\"SPECIAL\");\n    });\n  });\n\n  describe(\"functions\", () => {\n    test(\"named function\", () => {\n      expect(nicify(function namedFunction() {})).toBe(\"function namedFunction(...) { ... }\");\n    });\n\n    test(\"arrow function\", () => {\n      expect(nicify(() => {})).toBe(\"(...) => { ... }\");\n    });\n  });\n\n  describe(\"Nicifiable interface\", () => {\n    test(\"object implementing Nicifiable\", () => {\n      const nicifiable = {\n        value: 42,\n        getNicifiableKeys() {\n          return [\"value\"];\n        },\n        getNicifiedObjectExtraLines() {\n          return [\"// custom comment\"];\n        }\n      };\n      expect(nicify(nicifiable)).toBe(deindent`\n        {\n          \"value\": 42,\n          // custom comment,\n        }\n      `);\n    });\n  });\n\n  describe(\"unknown types\", () => {\n    test(\"object without prototype\", () => {\n      const unknownType = Object.create(null);\n      unknownType.value = \"test\";\n      expect(nicify(unknownType)).toBe('{ \"value\": \"test\" }');\n    });\n  });\n});\n"], "mappings": ";AAAA,SAAS,UAAU,QAAQ,YAAY;AACvC,SAAwB,UAAU,cAAc;AAEhD,SAAS,UAAU,MAAM;AACvB,WAAS,oBAAoB,MAAM;AACjC,SAAK,WAAW,MAAM;AACpB,aAAO,OAAO,GAAG,CAAC,EAAE,KAAK,KAAK;AAC9B,aAAO,OAAO,IAAI,CAAC,EAAE,KAAK,MAAM;AAAA,IAClC,CAAC;AAED,SAAK,WAAW,MAAM;AACpB,aAAO,OAAO,OAAO,CAAC,EAAE,KAAK,SAAS;AAAA,IACxC,CAAC;AAED,SAAK,YAAY,MAAM;AACrB,aAAO,OAAO,IAAI,CAAC,EAAE,KAAK,MAAM;AAChC,aAAO,OAAO,KAAK,CAAC,EAAE,KAAK,OAAO;AAAA,IACpC,CAAC;AAED,SAAK,sBAAsB,MAAM;AAC/B,aAAO,OAAO,IAAI,CAAC,EAAE,KAAK,MAAM;AAChC,aAAO,OAAO,MAAS,CAAC,EAAE,KAAK,WAAW;AAAA,IAC5C,CAAC;AAED,SAAK,WAAW,MAAM;AACpB,aAAO,OAAO,OAAO,MAAM,CAAC,CAAC,EAAE,KAAK,cAAc;AAAA,IACpD,CAAC;AAAA,EACH,CAAC;AAED,WAAS,UAAU,MAAM;AACvB,SAAK,eAAe,MAAM;AACxB,aAAO,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI;AAAA,IAC9B,CAAC;AAED,SAAK,wBAAwB,MAAM;AACjC,aAAO,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK;AAAA,IAChC,CAAC;AAED,SAAK,0CAA0C,MAAM;AACnD,aAAO,OAAO,CAAC,iBAAiB,CAAC,CAAC,EAAE,KAAK,qBAAqB;AAAA,IAChE,CAAC;AAED,SAAK,cAAc,MAAM;AACvB,aAAO,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,WAAW;AAAA,IAC5C,CAAC;AAED,SAAK,gBAAgB,MAAM;AACzB,aAAO,OAAO,CAAC,KAAO,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAMlC;AAAA,IACH,CAAC;AAED,SAAK,gBAAgB,MAAM;AACzB,aAAO,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,OAKhC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAED,WAAS,WAAW,MAAM;AACxB,SAAK,gBAAgB,MAAM;AACzB,aAAO,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI;AAAA,IAC9B,CAAC;AAED,SAAK,iBAAiB,MAAM;AAC1B,aAAO,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,KAAK,YAAY;AAAA,IAC5C,CAAC;AAED,SAAK,oBAAoB,MAAM;AAC7B,aAAO,OAAO,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,OAKnC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAED,WAAS,kBAAkB,MAAM;AAC/B,SAAK,kBAAkB,MAAM;AAAA,MAC3B,MAAM,UAAU;AAAA,QACd,YAAmB,OAAe;AAAf;AAAA,QAAgB;AAAA,MACrC;AACA,aAAO,OAAO,IAAI,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,2BAA2B;AAAA,IACpE,CAAC;AAAA,EACH,CAAC;AAED,WAAS,oBAAoB,MAAM;AACjC,SAAK,OAAO,MAAM;AAChB,aAAO,OAAO,IAAI,IAAI,qBAAqB,CAAC,CAAC,EAAE,KAAK,6BAA6B;AAAA,IACnF,CAAC;AAED,SAAK,eAAe,MAAM;AACxB,aAAO,OAAO,IAAI,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,qBAAqB;AACpE,aAAO,OAAO,IAAI,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,qBAAqB;AAAA,IACtE,CAAC;AAED,SAAK,iBAAiB,MAAM;AAC1B,YAAM,QAAQ,IAAI,MAAM,YAAY;AACpC,YAAM,gBAAgB,OAAO,EAAE,MAAM,CAAC;AACtC,aAAO,aAAa,EAAE,QAAQ,IAAI,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAMxC,CAAC;AAAA,IACJ,CAAC;AAED,SAAK,kDAAkD,MAAM;AAC3D,YAAM,QAAQ,IAAI,MAAM,cAAc,EAAE,OAAO,IAAI,MAAM,OAAO,EAAE,CAAC;AACnE,MAAC,MAAc,QAAQ;AACvB,YAAM,gBAAgB,OAAO,OAAO,EAAE,YAAY,KAAK,CAAC;AACxD,aAAO,aAAa,EAAE,QAAQ,IAAI,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OASxC,CAAC;AAAA,IACJ,CAAC;AAED,SAAK,WAAW,MAAM;AACpB,YAAM,UAAU,IAAI,QAAQ;AAC5B,cAAQ,OAAO,gBAAgB,kBAAkB;AACjD,cAAQ,OAAO,UAAU,YAAY;AACrC,aAAO,OAAO,OAAO,CAAC,EAAE;AAAA,QAAK;AAAA;AAAA;AAAA;AAAA;AAAA,MAK7B;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,WAAS,qBAAqB,MAAM;AAClC,SAAK,mBAAmB,MAAM;AAC5B,aAAO,OAAO,cAAc,CAAC,EAAE,KAAK,gCAAgC;AAAA,IACtE,CAAC;AAED,SAAK,mCAAmC,MAAM;AAC5C,aAAO,OAAO,gBAAgB,CAAC,EAAE,KAAK,wCAAwC;AAAA,IAChF,CAAC;AAAA,EACH,CAAC;AAED,WAAS,uBAAuB,MAAM;AACpC,SAAK,8BAA8B,MAAM;AACvC,YAAM,WAAgB,EAAE,GAAG,EAAE;AAC7B,eAAS,OAAO;AAChB,aAAO,OAAO,QAAQ,CAAC,EAAE;AAAA,QAAK;AAAA;AAAA;AAAA;AAAA;AAAA,MAK9B;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,WAAS,yBAAyB,MAAM;AACtC,SAAK,YAAY,MAAM;AACrB,YAAM,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAChD,aAAO,OAAO,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,2BAA2B;AAAA,IACxE,CAAC;AAED,SAAK,cAAc,MAAM;AACvB,aAAO,OAAO,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,YAAY,OAAO,CAAC,CAAC,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,OAK3D;AAAA,IACH,CAAC;AAED,SAAK,cAAc,MAAM;AACvB,aAAO,OAAO,EAAE,GAAG,GAAG,GAAG,GAAG,QAAQ,SAAS,GAAG,EAAE,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAMjF;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAED,WAAS,oBAAoB,MAAM;AACjC,SAAK,6BAA6B,MAAM;AACtC,aAAO,OAAO,EAAE,MAAM,UAAU,GAAG;AAAA,QACjC,WAAY,CAAC,UAAmB;AAC9B,cAAI,OAAO,UAAU,YAAY,SAAS,UAAU,SAAU,MAAc,SAAS,WAAW;AAC9F,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,MACF,CAAC,CAAC,EAAE,KAAK,SAAS;AAAA,IACpB,CAAC;AAAA,EACH,CAAC;AAED,WAAS,aAAa,MAAM;AAC1B,SAAK,kBAAkB,MAAM;AAC3B,aAAO,OAAO,SAAS,gBAAgB;AAAA,MAAC,CAAC,CAAC,EAAE,KAAK,qCAAqC;AAAA,IACxF,CAAC;AAED,SAAK,kBAAkB,MAAM;AAC3B,aAAO,OAAO,MAAM;AAAA,MAAC,CAAC,CAAC,EAAE,KAAK,kBAAkB;AAAA,IAClD,CAAC;AAAA,EACH,CAAC;AAED,WAAS,wBAAwB,MAAM;AACrC,SAAK,kCAAkC,MAAM;AAC3C,YAAM,aAAa;AAAA,QACjB,OAAO;AAAA,QACP,oBAAoB;AAClB,iBAAO,CAAC,OAAO;AAAA,QACjB;AAAA,QACA,8BAA8B;AAC5B,iBAAO,CAAC,mBAAmB;AAAA,QAC7B;AAAA,MACF;AACA,aAAO,OAAO,UAAU,CAAC,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,OAK/B;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAED,WAAS,iBAAiB,MAAM;AAC9B,SAAK,4BAA4B,MAAM;AACrC,YAAM,cAAc,uBAAO,OAAO,IAAI;AACtC,kBAAY,QAAQ;AACpB,aAAO,OAAO,WAAW,CAAC,EAAE,KAAK,qBAAqB;AAAA,IACxD,CAAC;AAAA,EACH,CAAC;AACH,CAAC;", "names": []}