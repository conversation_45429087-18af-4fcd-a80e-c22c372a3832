{"version": 3, "sources": ["../../../src/utils/geo.tsx"], "sourcesContent": ["\nimport * as yup from \"yup\";\nimport { yupNumber, yupObject, yupString } from \"../schema-fields\";\n\nexport const geoInfoSchema = yupObject({\n  ip: yupString().defined(),\n  countryCode: yupString().nullable(),\n  regionCode: yupString().nullable(),\n  cityName: yupString().nullable(),\n  latitude: yupNumber().nullable(),\n  longitude: yupNumber().nullable(),\n  tzIdentifier: yupString().nullable(),\n});\n\nexport type GeoInfo = yup.InferType<typeof geoInfoSchema>;\n\n"], "mappings": ";AAEA,SAAS,WAAW,WAAW,iBAAiB;AAEzC,IAAM,gBAAgB,UAAU;AAAA,EACrC,IAAI,UAAU,EAAE,QAAQ;AAAA,EACxB,aAAa,UAAU,EAAE,SAAS;AAAA,EAClC,YAAY,UAAU,EAAE,SAAS;AAAA,EACjC,UAAU,UAAU,EAAE,SAAS;AAAA,EAC/B,UAAU,UAAU,EAAE,SAAS;AAAA,EAC/B,WAAW,UAAU,EAAE,SAAS;AAAA,EAChC,cAAc,UAAU,EAAE,SAAS;AACrC,CAAC;", "names": []}