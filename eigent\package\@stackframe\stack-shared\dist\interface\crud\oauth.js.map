{"version": 3, "sources": ["../../../src/interface/crud/oauth.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport { yupObject, yupString } from \"../../schema-fields\";\n\nexport const connectedAccountAccessTokenReadSchema = yupObject({\n  access_token: yupString().defined(),\n}).defined();\n\nexport const connectedAccountAccessTokenCreateSchema = yupObject({\n  scope: yupString().optional(),\n}).defined();\n\nexport const connectedAccountAccessTokenCrud = createCrud({\n  clientReadSchema: connectedAccountAccessTokenReadSchema,\n  clientCreateSchema: connectedAccountAccessTokenCreateSchema,\n  docs: {\n    clientCreate: {\n      hidden: true,\n    }\n  },\n});\nexport type ConnectedAccountAccessTokenCrud = CrudTypeOf<typeof connectedAccountAccessTokenCrud>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAuC;AACvC,2BAAqC;AAE9B,IAAM,4CAAwC,gCAAU;AAAA,EAC7D,kBAAc,gCAAU,EAAE,QAAQ;AACpC,CAAC,EAAE,QAAQ;AAEJ,IAAM,8CAA0C,gCAAU;AAAA,EAC/D,WAAO,gCAAU,EAAE,SAAS;AAC9B,CAAC,EAAE,QAAQ;AAEJ,IAAM,sCAAkC,wBAAW;AAAA,EACxD,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,MAAM;AAAA,IACJ,cAAc;AAAA,MACZ,QAAQ;AAAA,IACV;AAAA,EACF;AACF,CAAC;", "names": []}