{"version": 3, "sources": ["../../../../../src/lib/stack-app/projects/index.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { ProductionModeError } from \"@stackframe/stack-shared/dist/helpers/production-mode\";\nimport { AdminUserProjectsCrud, ProjectsCrud } from \"@stackframe/stack-shared/dist/interface/crud/projects\";\n\nimport { StackAdminApp } from \"../apps/interfaces/admin-app\";\nimport { AdminProjectConfig, AdminProjectConfigUpdateOptions, ProjectConfig } from \"../project-configs\";\n\n\nexport type Project = {\n  readonly id: string,\n  readonly displayName: string,\n  readonly config: ProjectConfig,\n};\n\nexport type AdminProject = {\n  readonly id: string,\n  readonly displayName: string,\n  readonly description: string | null,\n  readonly createdAt: Date,\n  readonly userCount: number,\n  readonly isProductionMode: boolean,\n  readonly config: AdminProjectConfig,\n\n  update(this: AdminProject, update: AdminProjectUpdateOptions): Promise<void>,\n  delete(this: AdminProject): Promise<void>,\n\n  getProductionModeErrors(this: AdminProject): Promise<ProductionModeError[]>,\n  useProductionModeErrors(this: AdminProject): ProductionModeError[],\n} & Project;\n\nexport type AdminOwnedProject = {\n  readonly app: StackAdminApp<false>,\n} & AdminProject;\n\nexport type AdminProjectUpdateOptions = {\n  displayName?: string,\n  description?: string,\n  isProductionMode?: boolean,\n  config?: AdminProjectConfigUpdateOptions,\n};\nexport function adminProjectUpdateOptionsToCrud(options: AdminProjectUpdateOptions): ProjectsCrud[\"Admin\"][\"Update\"] {\n  return {\n    display_name: options.displayName,\n    description: options.description,\n    is_production_mode: options.isProductionMode,\n    config: {\n      domains: options.config?.domains?.map((d) => ({\n        domain: d.domain,\n        handler_path: d.handlerPath\n      })),\n      oauth_providers: options.config?.oauthProviders?.map((p) => ({\n        id: p.id as any,\n        type: p.type,\n        ...(p.type === 'standard' && {\n          client_id: p.clientId,\n          client_secret: p.clientSecret,\n          facebook_config_id: p.facebookConfigId,\n          microsoft_tenant_id: p.microsoftTenantId,\n        }),\n      })),\n      email_config: options.config?.emailConfig && (\n        options.config.emailConfig.type === 'shared' ? {\n          type: 'shared',\n        } : {\n          type: 'standard',\n          host: options.config.emailConfig.host,\n          port: options.config.emailConfig.port,\n          username: options.config.emailConfig.username,\n          password: options.config.emailConfig.password,\n          sender_name: options.config.emailConfig.senderName,\n          sender_email: options.config.emailConfig.senderEmail,\n        }\n      ),\n      sign_up_enabled: options.config?.signUpEnabled,\n      credential_enabled: options.config?.credentialEnabled,\n      magic_link_enabled: options.config?.magicLinkEnabled,\n      passkey_enabled: options.config?.passkeyEnabled,\n      allow_localhost: options.config?.allowLocalhost,\n      create_team_on_sign_up: options.config?.createTeamOnSignUp,\n      client_team_creation_enabled: options.config?.clientTeamCreationEnabled,\n      client_user_deletion_enabled: options.config?.clientUserDeletionEnabled,\n      team_creator_default_permissions: options.config?.teamCreatorDefaultPermissions,\n      team_member_default_permissions: options.config?.teamMemberDefaultPermissions,\n      user_default_permissions: options.config?.userDefaultPermissions,\n      oauth_account_merge_strategy: options.config?.oauthAccountMergeStrategy,\n      allow_user_api_keys: options.config?.allowUserApiKeys,\n      allow_team_api_keys: options.config?.allowTeamApiKeys,\n    },\n  };\n}\n\nexport type AdminProjectCreateOptions = Omit<AdminProjectUpdateOptions, 'displayName'> & {\n  displayName: string,\n};\nexport function adminProjectCreateOptionsToCrud(options: AdminProjectCreateOptions): AdminUserProjectsCrud[\"Server\"][\"Create\"] {\n  return {\n    ...adminProjectUpdateOptionsToCrud(options),\n    display_name: options.displayName,\n  };\n}\n"], "mappings": ";AA2CO,SAAS,gCAAgC,SAAqE;AACnH,SAAO;AAAA,IACL,cAAc,QAAQ;AAAA,IACtB,aAAa,QAAQ;AAAA,IACrB,oBAAoB,QAAQ;AAAA,IAC5B,QAAQ;AAAA,MACN,SAAS,QAAQ,QAAQ,SAAS,IAAI,CAAC,OAAO;AAAA,QAC5C,QAAQ,EAAE;AAAA,QACV,cAAc,EAAE;AAAA,MAClB,EAAE;AAAA,MACF,iBAAiB,QAAQ,QAAQ,gBAAgB,IAAI,CAAC,OAAO;AAAA,QAC3D,IAAI,EAAE;AAAA,QACN,MAAM,EAAE;AAAA,QACR,GAAI,EAAE,SAAS,cAAc;AAAA,UAC3B,WAAW,EAAE;AAAA,UACb,eAAe,EAAE;AAAA,UACjB,oBAAoB,EAAE;AAAA,UACtB,qBAAqB,EAAE;AAAA,QACzB;AAAA,MACF,EAAE;AAAA,MACF,cAAc,QAAQ,QAAQ,gBAC5B,QAAQ,OAAO,YAAY,SAAS,WAAW;AAAA,QAC7C,MAAM;AAAA,MACR,IAAI;AAAA,QACF,MAAM;AAAA,QACN,MAAM,QAAQ,OAAO,YAAY;AAAA,QACjC,MAAM,QAAQ,OAAO,YAAY;AAAA,QACjC,UAAU,QAAQ,OAAO,YAAY;AAAA,QACrC,UAAU,QAAQ,OAAO,YAAY;AAAA,QACrC,aAAa,QAAQ,OAAO,YAAY;AAAA,QACxC,cAAc,QAAQ,OAAO,YAAY;AAAA,MAC3C;AAAA,MAEF,iBAAiB,QAAQ,QAAQ;AAAA,MACjC,oBAAoB,QAAQ,QAAQ;AAAA,MACpC,oBAAoB,QAAQ,QAAQ;AAAA,MACpC,iBAAiB,QAAQ,QAAQ;AAAA,MACjC,iBAAiB,QAAQ,QAAQ;AAAA,MACjC,wBAAwB,QAAQ,QAAQ;AAAA,MACxC,8BAA8B,QAAQ,QAAQ;AAAA,MAC9C,8BAA8B,QAAQ,QAAQ;AAAA,MAC9C,kCAAkC,QAAQ,QAAQ;AAAA,MAClD,iCAAiC,QAAQ,QAAQ;AAAA,MACjD,0BAA0B,QAAQ,QAAQ;AAAA,MAC1C,8BAA8B,QAAQ,QAAQ;AAAA,MAC9C,qBAAqB,QAAQ,QAAQ;AAAA,MACrC,qBAAqB,QAAQ,QAAQ;AAAA,IACvC;AAAA,EACF;AACF;AAKO,SAAS,gCAAgC,SAA+E;AAC7H,SAAO;AAAA,IACL,GAAG,gCAAgC,OAAO;AAAA,IAC1C,cAAc,QAAQ;AAAA,EACxB;AACF;", "names": []}