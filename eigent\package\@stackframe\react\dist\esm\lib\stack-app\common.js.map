{"version": 3, "sources": ["../../../../src/lib/stack-app/common.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { ProviderType } from \"@stackframe/stack-shared/dist/utils/oauth\";\n\nexport type RedirectToOptions = {\n  replace?: boolean,\n  noRedirectBack?: boolean,\n};\n\nexport type AsyncStoreProperty<Name extends string, Args extends any[], Value, IsMultiple extends boolean> =\n  & { [key in `${IsMultiple extends true ? \"list\" : \"get\"}${Capitalize<Name>}`]: (...args: Args) => Promise<Value> }\n  & { [key in `use${Capitalize<Name>}`]: (...args: Args) => Value } // THIS_LINE_PLATFORM react-like\n\nexport type EmailConfig = {\n  host: string,\n  port: number,\n  username: string,\n  password: string,\n  senderEmail: string,\n  senderName: string,\n}\n\nexport type RedirectMethod = \"window\"\n  | \"none\"\n  | {\n    useNavigate: () => (to: string) => void,\n    navigate?: (to: string) => void,\n  }\n\n\nexport type GetUserOptions<HasTokenStore> =\n  & {\n    or?: 'redirect' | 'throw' | 'return-null' | 'anonymous' | 'anonymous-if-exists',\n    tokenStore?: TokenStoreInit,\n  }\n  & (HasTokenStore extends false ? {\n    tokenStore: TokenStoreInit,\n  } : {});\n\nexport type RequestLike = {\n  headers: {\n    get: (name: string) => string | null,\n  },\n};\n\nexport type TokenStoreInit<HasTokenStore extends boolean = boolean> =\n  HasTokenStore extends true ? (\n    | \"cookie\"\n    | \"nextjs-cookie\"\n    | \"memory\"\n    | RequestLike\n    | { accessToken: string, refreshToken: string }\n  )\n  : HasTokenStore extends false ? null\n  : TokenStoreInit<true> | TokenStoreInit<false>;\n\nexport type HandlerUrls = {\n  handler: string,\n  signIn: string,\n  signUp: string,\n  afterSignIn: string,\n  afterSignUp: string,\n  signOut: string,\n  afterSignOut: string,\n  emailVerification: string,\n  passwordReset: string,\n  forgotPassword: string,\n  home: string,\n  oauthCallback: string,\n  magicLinkCallback: string,\n  accountSettings: string,\n  teamInvitation: string,\n  error: string,\n}\n\nexport type OAuthScopesOnSignIn = {\n  [key in ProviderType]: string[];\n};\n\n/** @internal */\nexport const stackAppInternalsSymbol = Symbol.for(\"StackAuth--DO-NOT-USE-OR-YOU-WILL-BE-FIRED--StackAppInternals\");\n"], "mappings": ";AAkFO,IAAM,0BAA0B,OAAO,IAAI,+DAA+D;", "names": []}