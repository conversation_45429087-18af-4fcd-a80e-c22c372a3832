{"version": 3, "sources": ["../../../src/components-page/magic-link-callback.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { KnownErrors } from \"@stackframe/stack-shared\";\nimport { cacheFunction } from \"@stackframe/stack-shared/dist/utils/caches\";\nimport { throwErr } from \"@stackframe/stack-shared/dist/utils/errors\";\nimport React from \"react\";\nimport { StackClientApp, useStackApp, useUser } from \"..\";\nimport { MessageCard } from \"../components/message-cards/message-card\";\nimport { PredefinedMessageCard } from \"../components/message-cards/predefined-message-card\";\nimport { useTranslation } from \"../lib/translations\";\n\nconst cacheSignInWithMagicLink = cacheFunction(async (stackApp: StackClientApp<true>, code: string) => {\n  return await stackApp.signInWithMagicLink(code);\n});\n\nexport function MagicLinkCallback(props: {\n  searchParams?: Record<string, string>,\n  fullPage?: boolean,\n}) {\n  const { t } = useTranslation();\n  const stackApp = useStackApp();\n  const user = useUser();\n  const [result, setResult] = React.useState<Awaited<ReturnType<typeof stackApp.signInWithMagicLink>> | null>(null);\n\n  if (user) {\n    return <PredefinedMessageCard type='signedIn' fullPage={!!props.fullPage} />;\n  }\n\n  const invalidJsx = (\n    <MessageCard title={t(\"Invalid Magic Link\")} fullPage={!!props.fullPage}>\n      <p>{t(\"Please check if you have the correct link. If you continue to have issues, please contact support.\")}</p>\n    </MessageCard>\n  );\n\n  const expiredJsx = (\n    <MessageCard title={t(\"Expired Magic Link\")} fullPage={!!props.fullPage}>\n      <p>{t(\"Your magic link has expired. Please request a new magic link if you need to sign-in.\")}</p>\n    </MessageCard>\n  );\n\n  const alreadyUsedJsx = (\n    <MessageCard title={t(\"Magic Link Already Used\")} fullPage={!!props.fullPage}>\n      <p>{t(\"The magic link has already been used. The link can only be used once. Please request a new magic link if you need to sign-in again.\")}</p>\n    </MessageCard>\n  );\n\n  if (!props.searchParams?.code) {\n    return invalidJsx;\n  }\n\n  if (!result) {\n    return <MessageCard\n      title={t(\"Do you want to sign in?\")}\n      fullPage={!!props.fullPage}\n      primaryButtonText={t(\"Sign in\")}\n      primaryAction={async () => {\n        const result = await stackApp.signInWithMagicLink(props.searchParams?.code || throwErr(\"No magic link provided\"));\n        setResult(result);\n      }}\n      secondaryButtonText={t(\"Cancel\")}\n      secondaryAction={async () => {\n        await stackApp.redirectToHome();\n      }}\n    />;\n  } else {\n    if (result.status === 'error') {\n      if (KnownErrors.VerificationCodeNotFound.isInstance(result.error)) {\n        return invalidJsx;\n      } else if (KnownErrors.VerificationCodeExpired.isInstance(result.error)) {\n        return expiredJsx;\n      } else if (KnownErrors.VerificationCodeAlreadyUsed.isInstance(result.error)) {\n        return alreadyUsedJsx;\n      } else {\n        throw result.error;\n      }\n    }\n\n    return <MessageCard\n      title={t(\"Signed in successfully!\")}\n      fullPage={!!props.fullPage}\n      primaryButtonText={t(\"Go home\")}\n      primaryAction={async () => {\n        await stackApp.redirectToHome();\n      }}\n    />;\n  }\n}\n"], "mappings": ";;;AAOA,SAAS,mBAAmB;AAC5B,SAAS,qBAAqB;AAC9B,SAAS,gBAAgB;AACzB,OAAO,WAAW;AAClB,SAAyB,aAAa,eAAe;AACrD,SAAS,mBAAmB;AAC5B,SAAS,6BAA6B;AACtC,SAAS,sBAAsB;AAgBpB;AAdX,IAAM,2BAA2B,cAAc,OAAO,UAAgC,SAAiB;AACrG,SAAO,MAAM,SAAS,oBAAoB,IAAI;AAChD,CAAC;AAEM,SAAS,kBAAkB,OAG/B;AACD,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,WAAW,YAAY;AAC7B,QAAM,OAAO,QAAQ;AACrB,QAAM,CAAC,QAAQ,SAAS,IAAI,MAAM,SAA0E,IAAI;AAEhH,MAAI,MAAM;AACR,WAAO,oBAAC,yBAAsB,MAAK,YAAW,UAAU,CAAC,CAAC,MAAM,UAAU;AAAA,EAC5E;AAEA,QAAM,aACJ,oBAAC,eAAY,OAAO,EAAE,oBAAoB,GAAG,UAAU,CAAC,CAAC,MAAM,UAC7D,8BAAC,OAAG,YAAE,oGAAoG,GAAE,GAC9G;AAGF,QAAM,aACJ,oBAAC,eAAY,OAAO,EAAE,oBAAoB,GAAG,UAAU,CAAC,CAAC,MAAM,UAC7D,8BAAC,OAAG,YAAE,sFAAsF,GAAE,GAChG;AAGF,QAAM,iBACJ,oBAAC,eAAY,OAAO,EAAE,yBAAyB,GAAG,UAAU,CAAC,CAAC,MAAM,UAClE,8BAAC,OAAG,YAAE,qIAAqI,GAAE,GAC/I;AAGF,MAAI,CAAC,MAAM,cAAc,MAAM;AAC7B,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,MAAC;AAAA;AAAA,QACN,OAAO,EAAE,yBAAyB;AAAA,QAClC,UAAU,CAAC,CAAC,MAAM;AAAA,QAClB,mBAAmB,EAAE,SAAS;AAAA,QAC9B,eAAe,YAAY;AACzB,gBAAMA,UAAS,MAAM,SAAS,oBAAoB,MAAM,cAAc,QAAQ,SAAS,wBAAwB,CAAC;AAChH,oBAAUA,OAAM;AAAA,QAClB;AAAA,QACA,qBAAqB,EAAE,QAAQ;AAAA,QAC/B,iBAAiB,YAAY;AAC3B,gBAAM,SAAS,eAAe;AAAA,QAChC;AAAA;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,OAAO,WAAW,SAAS;AAC7B,UAAI,YAAY,yBAAyB,WAAW,OAAO,KAAK,GAAG;AACjE,eAAO;AAAA,MACT,WAAW,YAAY,wBAAwB,WAAW,OAAO,KAAK,GAAG;AACvE,eAAO;AAAA,MACT,WAAW,YAAY,4BAA4B,WAAW,OAAO,KAAK,GAAG;AAC3E,eAAO;AAAA,MACT,OAAO;AACL,cAAM,OAAO;AAAA,MACf;AAAA,IACF;AAEA,WAAO;AAAA,MAAC;AAAA;AAAA,QACN,OAAO,EAAE,yBAAyB;AAAA,QAClC,UAAU,CAAC,CAAC,MAAM;AAAA,QAClB,mBAAmB,EAAE,SAAS;AAAA,QAC9B,eAAe,YAAY;AACzB,gBAAM,SAAS,eAAe;AAAA,QAChC;AAAA;AAAA,IACF;AAAA,EACF;AACF;", "names": ["result"]}