{"version": 3, "sources": ["../../../../src/components-page/account-settings/email-and-auth/otp-section.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { Button, Typography } from \"@stackframe/stack-ui\";\nimport { useState } from \"react\";\nimport { useStackApp, useUser } from \"../../../lib/hooks\";\nimport { useTranslation } from \"../../../lib/translations\";\nimport { Section } from \"../section\";\n\nexport function OtpSection() {\n  const { t } = useTranslation();\n  const user = useUser({ or: \"throw\" });\n  const project = useStackApp().useProject();\n  const contactChannels = user.useContactChannels();\n  const isLastAuth = user.otpAuthEnabled && !user.hasPassword && user.oauthProviders.length === 0 && !user.passkeyAuthEnabled;\n  const [disabling, setDisabling] = useState(false);\n\n  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n  const hasValidEmail = contactChannels.filter(x => x.type === 'email' && x.isVerified && x.usedForAuth).length > 0;\n\n  if (!project.config.magicLinkEnabled) {\n    return null;\n  }\n\n  const handleDisableOTP = async () => {\n    await user.update({ otpAuthEnabled: false });\n    setDisabling(false);\n  };\n\n  return (\n    <Section title={t(\"OTP sign-in\")} description={user.otpAuthEnabled ? t(\"OTP/magic link sign-in is currently enabled.\") : t(\"Enable sign-in via magic link or OTP sent to your sign-in emails.\")}>\n      <div className='flex md:justify-end'>\n        {hasValidEmail ? (\n          user.otpAuthEnabled ? (\n            !isLastAuth ? (\n              !disabling ? (\n                <Button\n                  variant='secondary'\n                  onClick={() => setDisabling(true)}\n                >\n                  {t(\"Disable OTP\")}\n                </Button>\n              ) : (\n                <div className='flex flex-col gap-2'>\n                  <Typography variant='destructive'>\n                    {t(\"Are you sure you want to disable OTP sign-in? You will not be able to sign in with only emails anymore.\")}\n                  </Typography>\n                  <div className='flex gap-2'>\n                    <Button\n                      variant='destructive'\n                      onClick={handleDisableOTP}\n                    >\n                      {t(\"Disable\")}\n                    </Button>\n                    <Button\n                      variant='secondary'\n                      onClick={() => setDisabling(false)}\n                    >\n                      {t(\"Cancel\")}\n                    </Button>\n                  </div>\n                </div>\n              )\n            ) : (\n              <Typography variant='secondary' type='label'>{t(\"OTP sign-in is enabled and cannot be disabled as it is currently the only sign-in method\")}</Typography>\n            )\n          ) : (\n            <Button\n              variant='secondary'\n              onClick={async () => {\n                await user.update({ otpAuthEnabled: true });\n              }}\n            >\n              {t(\"Enable OTP\")}\n            </Button>\n          )\n        ) : (\n          <Typography variant='secondary' type='label'>{t(\"To enable OTP sign-in, please add a verified sign-in email.\")}</Typography>\n        )}\n      </div>\n    </Section>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,sBAAmC;AACnC,mBAAyB;AACzB,mBAAqC;AACrC,0BAA+B;AAC/B,qBAAwB;AA6BR;AA3BT,SAAS,aAAa;AAC3B,QAAM,EAAE,EAAE,QAAI,oCAAe;AAC7B,QAAM,WAAO,sBAAQ,EAAE,IAAI,QAAQ,CAAC;AACpC,QAAM,cAAU,0BAAY,EAAE,WAAW;AACzC,QAAM,kBAAkB,KAAK,mBAAmB;AAChD,QAAM,aAAa,KAAK,kBAAkB,CAAC,KAAK,eAAe,KAAK,eAAe,WAAW,KAAK,CAAC,KAAK;AACzG,QAAM,CAAC,WAAW,YAAY,QAAI,uBAAS,KAAK;AAGhD,QAAM,gBAAgB,gBAAgB,OAAO,OAAK,EAAE,SAAS,WAAW,EAAE,cAAc,EAAE,WAAW,EAAE,SAAS;AAEhH,MAAI,CAAC,QAAQ,OAAO,kBAAkB;AACpC,WAAO;AAAA,EACT;AAEA,QAAM,mBAAmB,YAAY;AACnC,UAAM,KAAK,OAAO,EAAE,gBAAgB,MAAM,CAAC;AAC3C,iBAAa,KAAK;AAAA,EACpB;AAEA,SACE,4CAAC,0BAAQ,OAAO,EAAE,aAAa,GAAG,aAAa,KAAK,iBAAiB,EAAE,8CAA8C,IAAI,EAAE,mEAAmE,GAC5L,sDAAC,SAAI,WAAU,uBACZ,0BACC,KAAK,iBACH,CAAC,aACC,CAAC,YACC;AAAA,IAAC;AAAA;AAAA,MACC,SAAQ;AAAA,MACR,SAAS,MAAM,aAAa,IAAI;AAAA,MAE/B,YAAE,aAAa;AAAA;AAAA,EAClB,IAEA,6CAAC,SAAI,WAAU,uBACb;AAAA,gDAAC,8BAAW,SAAQ,eACjB,YAAE,yGAAyG,GAC9G;AAAA,IACA,6CAAC,SAAI,WAAU,cACb;AAAA;AAAA,QAAC;AAAA;AAAA,UACC,SAAQ;AAAA,UACR,SAAS;AAAA,UAER,YAAE,SAAS;AAAA;AAAA,MACd;AAAA,MACA;AAAA,QAAC;AAAA;AAAA,UACC,SAAQ;AAAA,UACR,SAAS,MAAM,aAAa,KAAK;AAAA,UAEhC,YAAE,QAAQ;AAAA;AAAA,MACb;AAAA,OACF;AAAA,KACF,IAGF,4CAAC,8BAAW,SAAQ,aAAY,MAAK,SAAS,YAAE,0FAA0F,GAAE,IAG9I;AAAA,IAAC;AAAA;AAAA,MACC,SAAQ;AAAA,MACR,SAAS,YAAY;AACnB,cAAM,KAAK,OAAO,EAAE,gBAAgB,KAAK,CAAC;AAAA,MAC5C;AAAA,MAEC,YAAE,YAAY;AAAA;AAAA,EACjB,IAGF,4CAAC,8BAAW,SAAQ,aAAY,MAAK,SAAS,YAAE,6DAA6D,GAAE,GAEnH,GACF;AAEJ;", "names": []}