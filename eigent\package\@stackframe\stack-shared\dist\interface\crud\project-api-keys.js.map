{"version": 3, "sources": ["../../../src/interface/crud/project-api-keys.ts"], "sourcesContent": ["import * as yup from \"yup\";\nimport { CrudTypeOf, createCrud } from \"../../crud\";\nimport { userIdOrMeSchema, yupBoolean, yupNumber, yupObject, yupString } from \"../../schema-fields\";\nimport { typedFromEntries } from \"../../utils/objects\";\n\nfunction createApiKeyCrud<T extends string, IdFieldName extends string, IdSchema extends yup.Schema<any>>(type: T, idFieldName: IdFieldName, idSchema: IdSchema) {\n  const projectApiKeysReadSchema = yupObject({\n    id: yupString().defined(),\n    description: yupString().defined(),\n    expires_at_millis: yupNumber().optional(),\n    manually_revoked_at_millis: yupNumber().optional(),\n    created_at_millis: yupNumber().defined(),\n    is_public: yupBoolean().defined(),\n    value: yupObject({\n      last_four: yupString().defined(),\n    }).defined(),\n    type: yupString().oneOf([type]).defined(),\n    ...typedFromEntries([[idFieldName, idSchema]]),\n  });\n\n  const projectApiKeysUpdateSchema = yupObject({\n    description: yupString().optional(),\n    revoked: yupBoolean().oneOf([true]).optional(),\n  }).defined();\n\n  const projectApiKeysCrud = createCrud({\n    clientReadSchema: projectApiKeysReadSchema,\n    clientUpdateSchema: projectApiKeysUpdateSchema,\n    docs: {\n      clientCreate: {\n        description: `Create a new ${type} API key`,\n        displayName: `Create ${type} API key`,\n        tags: [\"API Keys\"],\n        summary: `Create ${type} API key`,\n      },\n      clientList: {\n        description: `List all ${type} API keys for the project with their metadata and status`,\n        displayName: `List ${type} API keys`,\n        summary: `List ${type} API keys`,\n        tags: [\"API Keys\"],\n      },\n      clientRead: {\n        description: `Get details of a specific ${type} API key`,\n        displayName: `Get ${type} API key`,\n        summary: `Get ${type} API key details`,\n        tags: [\"API Keys\"],\n      },\n      clientUpdate: {\n        description: `Update an ${type} API key`,\n        displayName: `Update ${type} API key`,\n        summary: `Update ${type} API key`,\n        tags: [\"API Keys\"],\n      },\n      serverDelete: {\n        description: `Delete an ${type} API key`,\n        displayName: `Delete ${type} API key`,\n        summary: `Delete ${type} API key`,\n        tags: [\"API Keys\"],\n      },\n    },\n  });\n\n  // Used for the result of the create endpoint\n  const projectApiKeysCreateInputSchema = yupObject({\n    description: yupString().defined(),\n    expires_at_millis: yupNumber().nullable().defined(),\n    is_public: yupBoolean().optional(),\n    /*\n    prefix: yupString().optional().nonEmpty().test(\"prefix\", \"Prefix must contain only alphanumeric characters and underscores\", (value) => {\n      if (!value) return true;\n      return /^[a-zA-Z0-9_]+$/.test(value);\n    }),\n    */\n    ...typedFromEntries([[idFieldName, idSchema]]),\n  });\n  const projectApiKeysCreateOutputSchema = projectApiKeysReadSchema.omit([\"value\"]).concat(yupObject({\n    value: yupString().defined(),\n  }));\n\n  return {\n    crud: projectApiKeysCrud,\n    createInputSchema: projectApiKeysCreateInputSchema,\n    createOutputSchema: projectApiKeysCreateOutputSchema,\n  };\n}\n\n\nexport const {\n  crud: userApiKeysCrud,\n  createInputSchema: userApiKeysCreateInputSchema,\n  createOutputSchema: userApiKeysCreateOutputSchema,\n} = createApiKeyCrud(\"user\", \"user_id\", userIdOrMeSchema.defined());\nexport type UserApiKeysCrud = CrudTypeOf<typeof userApiKeysCrud>;\n\nexport const {\n  crud: teamApiKeysCrud,\n  createInputSchema: teamApiKeysCreateInputSchema,\n  createOutputSchema: teamApiKeysCreateOutputSchema,\n} = createApiKeyCrud(\"team\", \"team_id\", yupString().defined());\nexport type TeamApiKeysCrud = CrudTypeOf<typeof teamApiKeysCrud>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,kBAAuC;AACvC,2BAA8E;AAC9E,qBAAiC;AAEjC,SAAS,iBAAiG,MAAS,aAA0B,UAAoB;AAC/J,QAAM,+BAA2B,gCAAU;AAAA,IACzC,QAAI,gCAAU,EAAE,QAAQ;AAAA,IACxB,iBAAa,gCAAU,EAAE,QAAQ;AAAA,IACjC,uBAAmB,gCAAU,EAAE,SAAS;AAAA,IACxC,gCAA4B,gCAAU,EAAE,SAAS;AAAA,IACjD,uBAAmB,gCAAU,EAAE,QAAQ;AAAA,IACvC,eAAW,iCAAW,EAAE,QAAQ;AAAA,IAChC,WAAO,gCAAU;AAAA,MACf,eAAW,gCAAU,EAAE,QAAQ;AAAA,IACjC,CAAC,EAAE,QAAQ;AAAA,IACX,UAAM,gCAAU,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,QAAQ;AAAA,IACxC,OAAG,iCAAiB,CAAC,CAAC,aAAa,QAAQ,CAAC,CAAC;AAAA,EAC/C,CAAC;AAED,QAAM,iCAA6B,gCAAU;AAAA,IAC3C,iBAAa,gCAAU,EAAE,SAAS;AAAA,IAClC,aAAS,iCAAW,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS;AAAA,EAC/C,CAAC,EAAE,QAAQ;AAEX,QAAM,yBAAqB,wBAAW;AAAA,IACpC,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,MAAM;AAAA,MACJ,cAAc;AAAA,QACZ,aAAa,gBAAgB,IAAI;AAAA,QACjC,aAAa,UAAU,IAAI;AAAA,QAC3B,MAAM,CAAC,UAAU;AAAA,QACjB,SAAS,UAAU,IAAI;AAAA,MACzB;AAAA,MACA,YAAY;AAAA,QACV,aAAa,YAAY,IAAI;AAAA,QAC7B,aAAa,QAAQ,IAAI;AAAA,QACzB,SAAS,QAAQ,IAAI;AAAA,QACrB,MAAM,CAAC,UAAU;AAAA,MACnB;AAAA,MACA,YAAY;AAAA,QACV,aAAa,6BAA6B,IAAI;AAAA,QAC9C,aAAa,OAAO,IAAI;AAAA,QACxB,SAAS,OAAO,IAAI;AAAA,QACpB,MAAM,CAAC,UAAU;AAAA,MACnB;AAAA,MACA,cAAc;AAAA,QACZ,aAAa,aAAa,IAAI;AAAA,QAC9B,aAAa,UAAU,IAAI;AAAA,QAC3B,SAAS,UAAU,IAAI;AAAA,QACvB,MAAM,CAAC,UAAU;AAAA,MACnB;AAAA,MACA,cAAc;AAAA,QACZ,aAAa,aAAa,IAAI;AAAA,QAC9B,aAAa,UAAU,IAAI;AAAA,QAC3B,SAAS,UAAU,IAAI;AAAA,QACvB,MAAM,CAAC,UAAU;AAAA,MACnB;AAAA,IACF;AAAA,EACF,CAAC;AAGD,QAAM,sCAAkC,gCAAU;AAAA,IAChD,iBAAa,gCAAU,EAAE,QAAQ;AAAA,IACjC,uBAAmB,gCAAU,EAAE,SAAS,EAAE,QAAQ;AAAA,IAClD,eAAW,iCAAW,EAAE,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOjC,OAAG,iCAAiB,CAAC,CAAC,aAAa,QAAQ,CAAC,CAAC;AAAA,EAC/C,CAAC;AACD,QAAM,mCAAmC,yBAAyB,KAAK,CAAC,OAAO,CAAC,EAAE,WAAO,gCAAU;AAAA,IACjG,WAAO,gCAAU,EAAE,QAAQ;AAAA,EAC7B,CAAC,CAAC;AAEF,SAAO;AAAA,IACL,MAAM;AAAA,IACN,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,EACtB;AACF;AAGO,IAAM;AAAA,EACX,MAAM;AAAA,EACN,mBAAmB;AAAA,EACnB,oBAAoB;AACtB,IAAI,iBAAiB,QAAQ,WAAW,sCAAiB,QAAQ,CAAC;AAG3D,IAAM;AAAA,EACX,MAAM;AAAA,EACN,mBAAmB;AAAA,EACnB,oBAAoB;AACtB,IAAI,iBAAiB,QAAQ,eAAW,gCAAU,EAAE,QAAQ,CAAC;", "names": []}