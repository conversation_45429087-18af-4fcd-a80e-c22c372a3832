{"version": 3, "sources": ["../../../../../src/lib/stack-app/teams/index.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { TeamsCrud } from \"@stackframe/stack-shared/dist/interface/crud/teams\";\nimport { ReadonlyJson } from \"@stackframe/stack-shared/dist/utils/json\";\n\nimport { ApiKeyCreationOptions, TeamApiKey, TeamApiKeyFirstView } from \"../api-keys\";\nimport { AsyncStoreProperty } from \"../common\";\nimport { ServerUser } from \"../users\";\n\n\nexport type TeamMemberProfile = {\n  displayName: string | null,\n  profileImageUrl: string | null,\n}\n\nexport type TeamMemberProfileUpdateOptions = {\n  displayName?: string,\n  profileImageUrl?: string | null,\n};\n\nexport type EditableTeamMemberProfile = TeamMemberProfile & {\n  update(update: TeamMemberProfileUpdateOptions): Promise<void>,\n}\n\nexport type TeamUser = {\n  id: string,\n  teamProfile: TeamMemberProfile,\n}\n\nexport type TeamInvitation = {\n  id: string,\n  recipientEmail: string | null,\n  expiresAt: Date,\n  revoke(): Promise<void>,\n}\n\nexport type Team = {\n  id: string,\n  displayName: string,\n  profileImageUrl: string | null,\n  clientMetadata: any,\n  clientReadOnlyMetadata: any,\n  inviteUser(options: { email: string, callbackUrl?: string }): Promise<void>,\n  listUsers(): Promise<TeamUser[]>,\n  useUsers(): TeamUser[], // THIS_LINE_PLATFORM react-like\n  listInvitations(): Promise<TeamInvitation[]>,\n  useInvitations(): TeamInvitation[], // THIS_LINE_PLATFORM react-like\n  update(update: TeamUpdateOptions): Promise<void>,\n  delete(): Promise<void>,\n  createApiKey(options: ApiKeyCreationOptions<\"team\">): Promise<TeamApiKeyFirstView>,\n} & AsyncStoreProperty<\"apiKeys\", [], TeamApiKey[], true>;\n\nexport type TeamUpdateOptions = {\n  displayName?: string,\n  profileImageUrl?: string | null,\n  clientMetadata?: ReadonlyJson,\n};\nexport function teamUpdateOptionsToCrud(options: TeamUpdateOptions): TeamsCrud[\"Client\"][\"Update\"] {\n  return {\n    display_name: options.displayName,\n    profile_image_url: options.profileImageUrl,\n    client_metadata: options.clientMetadata,\n  };\n}\n\nexport type TeamCreateOptions = {\n  displayName: string,\n  profileImageUrl?: string,\n}\nexport function teamCreateOptionsToCrud(options: TeamCreateOptions, creatorUserId: string): TeamsCrud[\"Client\"][\"Create\"] {\n  return {\n    display_name: options.displayName,\n    profile_image_url: options.profileImageUrl,\n    creator_user_id: creatorUserId,\n  };\n}\n\n\nexport type ServerTeamMemberProfile = TeamMemberProfile;\n\nexport type ServerTeamUser = ServerUser & {\n  teamProfile: ServerTeamMemberProfile,\n}\n\nexport type ServerTeam = {\n  createdAt: Date,\n  serverMetadata: any,\n  listUsers(): Promise<ServerTeamUser[]>,\n  useUsers(): ServerUser[], // THIS_LINE_PLATFORM react-like\n  update(update: ServerTeamUpdateOptions): Promise<void>,\n  delete(): Promise<void>,\n  addUser(userId: string): Promise<void>,\n  inviteUser(options: { email: string, callbackUrl?: string }): Promise<void>,\n  removeUser(userId: string): Promise<void>,\n} & Team;\n\nexport type ServerListUsersOptions = {\n  cursor?: string,\n  limit?: number,\n  orderBy?: 'signedUpAt',\n  desc?: boolean,\n  query?: string,\n};\n\nexport type ServerTeamCreateOptions = TeamCreateOptions & {\n  creatorUserId?: string,\n};\nexport function serverTeamCreateOptionsToCrud(options: ServerTeamCreateOptions): TeamsCrud[\"Server\"][\"Create\"] {\n  return {\n    display_name: options.displayName,\n    profile_image_url: options.profileImageUrl,\n    creator_user_id: options.creatorUserId,\n  };\n}\n\nexport type ServerTeamUpdateOptions = TeamUpdateOptions & {\n  clientReadOnlyMetadata?: ReadonlyJson,\n  serverMetadata?: ReadonlyJson,\n};\nexport function serverTeamUpdateOptionsToCrud(options: ServerTeamUpdateOptions): TeamsCrud[\"Server\"][\"Update\"] {\n  return {\n    display_name: options.displayName,\n    profile_image_url: options.profileImageUrl,\n    client_metadata: options.clientMetadata,\n    client_read_only_metadata: options.clientReadOnlyMetadata,\n    server_metadata: options.serverMetadata,\n  };\n}\n"], "mappings": ";AA2DO,SAAS,wBAAwB,SAA2D;AACjG,SAAO;AAAA,IACL,cAAc,QAAQ;AAAA,IACtB,mBAAmB,QAAQ;AAAA,IAC3B,iBAAiB,QAAQ;AAAA,EAC3B;AACF;AAMO,SAAS,wBAAwB,SAA4B,eAAsD;AACxH,SAAO;AAAA,IACL,cAAc,QAAQ;AAAA,IACtB,mBAAmB,QAAQ;AAAA,IAC3B,iBAAiB;AAAA,EACnB;AACF;AAgCO,SAAS,8BAA8B,SAAiE;AAC7G,SAAO;AAAA,IACL,cAAc,QAAQ;AAAA,IACtB,mBAAmB,QAAQ;AAAA,IAC3B,iBAAiB,QAAQ;AAAA,EAC3B;AACF;AAMO,SAAS,8BAA8B,SAAiE;AAC7G,SAAO;AAAA,IACL,cAAc,QAAQ;AAAA,IACtB,mBAAmB,QAAQ;AAAA,IAC3B,iBAAiB,QAAQ;AAAA,IACzB,2BAA2B,QAAQ;AAAA,IACnC,iBAAiB,QAAQ;AAAA,EAC3B;AACF;", "names": []}