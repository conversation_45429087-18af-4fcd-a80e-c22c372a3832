{"version": 3, "sources": ["../../src/utils/maps.tsx"], "sourcesContent": ["import { Result } from \"./results\";\n\nexport class WeakRefIfAvailable<T extends object> {\n  private readonly _ref: { deref: () => T | undefined };\n\n  constructor(value: T) {\n    if (typeof WeakRef === \"undefined\") {\n      this._ref = { deref: () => value };\n    } else {\n      this._ref = new WeakRef<T>(value);\n    }\n  }\n\n  deref(): T | undefined {\n    return this._ref.deref();\n  }\n}\nundefined?.test(\"WeakRefIfAvailable\", ({ expect }) => {\n  // Test with an object\n  const obj = { id: 1, name: \"test\" };\n  const weakRef = new WeakRefIfAvailable(obj);\n\n  // Test deref returns the original object\n  expect(weakRef.deref()).toBe(obj);\n\n  // Test with a different object\n  const obj2 = { id: 2, name: \"test2\" };\n  const weakRef2 = new WeakRefIfAvailable(obj2);\n  expect(weakRef2.deref()).toBe(obj2);\n  expect(weakRef2.deref()).not.toBe(obj);\n\n  // We can't easily test garbage collection in this environment,\n  // but we can verify the basic functionality works\n});\n\n\n/**\n * A WeakMap-like object that can be iterated over.\n *\n * Note that it relies on WeakRef, and always falls back to the regular Map behavior (ie. no GC) in browsers that don't support it.\n */\nexport class IterableWeakMap<K extends object, V> {\n  private readonly _weakMap: WeakMap<K & WeakKey, { value: V, keyRef: WeakRefIfAvailable<K & WeakKey> }>;\n  private readonly _keyRefs: Set<WeakRefIfAvailable<K & WeakKey>>;\n\n  constructor(entries?: readonly (readonly [K, V])[] | null) {\n    const mappedEntries = entries?.map((e) => [e[0], { value: e[1], keyRef: new WeakRefIfAvailable(e[0]) }] as const);\n    this._weakMap = new WeakMap(mappedEntries ?? []);\n    this._keyRefs = new Set(mappedEntries?.map((e) => e[1].keyRef) ?? []);\n  }\n\n  get(key: K): V | undefined {\n    return this._weakMap.get(key)?.value;\n  }\n\n  set(key: K, value: V): this {\n    const existing = this._weakMap.get(key);\n    const updated = { value, keyRef: existing?.keyRef ?? new WeakRefIfAvailable(key) };\n    this._weakMap.set(key, updated);\n    this._keyRefs.add(updated.keyRef);\n    return this;\n  }\n\n  delete(key: K): boolean {\n    const res = this._weakMap.get(key);\n    if (res) {\n      this._weakMap.delete(key);\n      this._keyRefs.delete(res.keyRef);\n      return true;\n    }\n    return false;\n  }\n\n  has(key: K): boolean {\n    return this._weakMap.has(key) && this._keyRefs.has(this._weakMap.get(key)!.keyRef);\n  }\n\n  *[Symbol.iterator](): IterableIterator<[K, V]> {\n    for (const keyRef of this._keyRefs) {\n      const key = keyRef.deref();\n      const existing = key ? this._weakMap.get(key) : undefined;\n      if (!key) {\n        // This can happen if the key was GCed. Remove it so the next iteration is faster.\n        this._keyRefs.delete(keyRef);\n      } else if (existing) {\n        yield [key, existing.value];\n      }\n    }\n  }\n\n  [Symbol.toStringTag] = \"IterableWeakMap\";\n}\nundefined?.test(\"IterableWeakMap\", ({ expect }) => {\n  // Test basic functionality\n  const map = new IterableWeakMap<{ id: number }, string>();\n\n  // Create object keys\n  const obj1 = { id: 1 };\n  const obj2 = { id: 2 };\n\n  // Test set and get\n  map.set(obj1, \"value1\");\n  expect(map.get(obj1)).toBe(\"value1\");\n\n  // Test has\n  expect(map.has(obj1)).toBe(true);\n  expect(map.has(obj2)).toBe(false);\n  expect(map.has({ id: 1 })).toBe(false); // Different object with same content\n\n  // Test with multiple keys\n  map.set(obj2, \"value2\");\n  expect(map.get(obj2)).toBe(\"value2\");\n  expect(map.get(obj1)).toBe(\"value1\"); // Original still exists\n\n  // Test delete\n  expect(map.delete(obj1)).toBe(true);\n  expect(map.has(obj1)).toBe(false);\n  expect(map.get(obj1)).toBeUndefined();\n  expect(map.has(obj2)).toBe(true); // Other key still exists\n\n  // Test delete non-existent key\n  expect(map.delete({ id: 3 })).toBe(false);\n\n  // Test iteration\n  const iterMap = new IterableWeakMap<{ id: number }, number>();\n  const iterObj1 = { id: 1 };\n  const iterObj2 = { id: 2 };\n  const iterObj3 = { id: 3 };\n\n  iterMap.set(iterObj1, 1);\n  iterMap.set(iterObj2, 2);\n  iterMap.set(iterObj3, 3);\n\n  const entries = Array.from(iterMap);\n  expect(entries.length).toBe(3);\n\n  // Find entries by their values since we can't directly compare objects in the array\n  const values = entries.map(entry => entry[1]);\n  expect(values).toContain(1);\n  expect(values).toContain(2);\n  expect(values).toContain(3);\n\n  // Test constructor with entries\n  const initialEntries: [{ id: number }, string][] = [\n    [{ id: 4 }, \"initial1\"],\n    [{ id: 5 }, \"initial2\"]\n  ];\n  const mapWithEntries = new IterableWeakMap(initialEntries);\n\n  // We can't directly access the initial entries since they're different object references\n  // But we can verify the map has the correct number of entries\n  const entriesFromConstructor = Array.from(mapWithEntries);\n  expect(entriesFromConstructor.length).toBe(2);\n});\n\n/**\n * A map that is a IterableWeakMap for object keys and a regular Map for primitive keys. Also provides iteration over both\n * object and primitive keys.\n *\n * Note that, just like IterableWeakMap, older browsers without support for WeakRef will use a regular Map for object keys.\n */\nexport class MaybeWeakMap<K, V> {\n  private readonly _primitiveMap: Map<K, V>;\n  private readonly _weakMap: IterableWeakMap<K & WeakKey, V>;\n\n  constructor(entries?: readonly (readonly [K, V])[] | null) {\n    const entriesArray = [...entries ?? []];\n    this._primitiveMap = new Map(entriesArray.filter((e) => !this._isAllowedInWeakMap(e[0])));\n    this._weakMap = new IterableWeakMap(entriesArray.filter((e): e is [K & WeakKey, V] => this._isAllowedInWeakMap(e[0])));\n  }\n\n  private _isAllowedInWeakMap(key: K): key is K & WeakKey {\n    return (typeof key === \"object\" && key !== null) || (typeof key === \"symbol\" && Symbol.keyFor(key) === undefined);\n  }\n\n  get(key: K): V | undefined {\n    if (this._isAllowedInWeakMap(key)) {\n      return this._weakMap.get(key);\n    } else {\n      return this._primitiveMap.get(key);\n    }\n  }\n\n  set(key: K, value: V): this {\n    if (this._isAllowedInWeakMap(key)) {\n      this._weakMap.set(key, value);\n    } else {\n      this._primitiveMap.set(key, value);\n    }\n    return this;\n  }\n\n  delete(key: K): boolean {\n    if (this._isAllowedInWeakMap(key)) {\n      return this._weakMap.delete(key);\n    } else {\n      return this._primitiveMap.delete(key);\n    }\n  }\n\n  has(key: K): boolean {\n    if (this._isAllowedInWeakMap(key)) {\n      return this._weakMap.has(key);\n    } else {\n      return this._primitiveMap.has(key);\n    }\n  }\n\n  *[Symbol.iterator](): IterableIterator<[K, V]> {\n    yield* this._primitiveMap;\n    yield* this._weakMap;\n  }\n\n  [Symbol.toStringTag] = \"MaybeWeakMap\";\n}\nundefined?.test(\"MaybeWeakMap\", ({ expect }) => {\n  // Test with primitive keys\n  const map = new MaybeWeakMap<string | object, number>();\n\n  // Test with string keys\n  map.set(\"key1\", 1);\n  map.set(\"key2\", 2);\n  expect(map.get(\"key1\")).toBe(1);\n  expect(map.get(\"key2\")).toBe(2);\n  expect(map.has(\"key1\")).toBe(true);\n  expect(map.has(\"nonexistent\")).toBe(false);\n\n  // Test with object keys\n  const obj1 = { id: 1 };\n  const obj2 = { id: 2 };\n  map.set(obj1, 3);\n  map.set(obj2, 4);\n  expect(map.get(obj1)).toBe(3);\n  expect(map.get(obj2)).toBe(4);\n  expect(map.has(obj1)).toBe(true);\n\n  // Test delete with primitive key\n  expect(map.delete(\"key1\")).toBe(true);\n  expect(map.has(\"key1\")).toBe(false);\n  expect(map.delete(\"nonexistent\")).toBe(false);\n\n  // Test delete with object key\n  expect(map.delete(obj1)).toBe(true);\n  expect(map.has(obj1)).toBe(false);\n\n  // Test iteration\n  const entries = Array.from(map);\n  expect(entries.length).toBe(2);\n  expect(entries).toContainEqual([\"key2\", 2]);\n  expect(entries).toContainEqual([obj2, 4]);\n\n  // Test constructor with entries\n  const initialEntries: [string | object, number][] = [\n    [\"initial1\", 10],\n    [{ id: 3 }, 20]\n  ];\n  const mapWithEntries = new MaybeWeakMap(initialEntries);\n  expect(mapWithEntries.get(\"initial1\")).toBe(10);\n  expect(mapWithEntries.get(initialEntries[1][0])).toBe(20);\n});\n\n\ntype DependenciesMapInner<V> = (\n  & { map: MaybeWeakMap<unknown, DependenciesMapInner<V>> }\n  & (\n    | { hasValue: true, value: V }\n    | { hasValue: false, value: undefined }\n  )\n);\n\n/**\n * A map that stores values indexed by an array of keys. If the keys are objects and the environment supports WeakRefs,\n * they are stored in a WeakMap.\n */\nexport class DependenciesMap<K extends any[], V> {\n  private _inner: DependenciesMapInner<V> = { map: new MaybeWeakMap(), hasValue: false, value: undefined };\n\n  private _valueToResult(inner: DependenciesMapInner<V>): Result<V, void> {\n    if (inner.hasValue) {\n      return Result.ok(inner.value);\n    } else {\n      return Result.error(undefined);\n    }\n  }\n\n\n  private _unwrapFromInner(dependencies: any[], inner: DependenciesMapInner<V>): Result<V, void> {\n    if ((dependencies.length === 0)) {\n      return this._valueToResult(inner);\n    } else {\n      const [key, ...rest] = dependencies;\n      const newInner = inner.map.get(key);\n      if (!newInner) {\n        return Result.error(undefined);\n      }\n      return this._unwrapFromInner(rest, newInner);\n    }\n  }\n\n  private _setInInner(dependencies: any[], value: Result<V, void>, inner: DependenciesMapInner<V>): Result<V, void> {\n    if (dependencies.length === 0) {\n      const res = this._valueToResult(inner);\n      if (value.status === \"ok\") {\n        inner.hasValue = true;\n        inner.value = value.data;\n      } else {\n        inner.hasValue = false;\n        inner.value = undefined;\n      }\n      return res;\n    } else {\n      const [key, ...rest] = dependencies;\n      let newInner = inner.map.get(key);\n      if (!newInner) {\n        inner.map.set(key, newInner = { map: new MaybeWeakMap(), hasValue: false, value: undefined });\n      }\n      return this._setInInner(rest, value, newInner);\n    }\n  }\n\n  private *_iterateInner(dependencies: any[], inner: DependenciesMapInner<V>): IterableIterator<[K, V]> {\n    if (inner.hasValue) {\n      yield [dependencies as K, inner.value];\n    }\n    for (const [key, value] of inner.map) {\n      yield* this._iterateInner([...dependencies, key], value);\n    }\n  }\n\n  get(dependencies: K): V | undefined {\n    return Result.or(this._unwrapFromInner(dependencies, this._inner), undefined);\n  }\n\n  set(dependencies: K, value: V): this {\n    this._setInInner(dependencies, Result.ok(value), this._inner);\n    return this;\n  }\n\n  delete(dependencies: K): boolean {\n    return this._setInInner(dependencies, Result.error(undefined), this._inner).status === \"ok\";\n  }\n\n  has(dependencies: K): boolean {\n    return this._unwrapFromInner(dependencies, this._inner).status === \"ok\";\n  }\n\n  clear(): void {\n    this._inner = { map: new MaybeWeakMap(), hasValue: false, value: undefined };\n  }\n\n  *[Symbol.iterator](): IterableIterator<[K, V]> {\n    yield* this._iterateInner([], this._inner);\n  }\n\n  [Symbol.toStringTag] = \"DependenciesMap\";\n}\nundefined?.test(\"DependenciesMap\", ({ expect }) => {\n  // Test basic functionality\n  const map = new DependenciesMap<[string, number], string>();\n\n  // Test set and get\n  map.set([\"key\", 1], \"value1\");\n  expect(map.get([\"key\", 1])).toBe(\"value1\");\n\n  // Test has\n  expect(map.has([\"key\", 1])).toBe(true);\n  expect(map.has([\"key\", 2])).toBe(false);\n\n  // Test with different dependencies\n  map.set([\"key\", 2], \"value2\");\n  expect(map.get([\"key\", 2])).toBe(\"value2\");\n  expect(map.get([\"key\", 1])).toBe(\"value1\"); // Original still exists\n\n  // Test delete\n  expect(map.delete([\"key\", 1])).toBe(true);\n  expect(map.has([\"key\", 1])).toBe(false);\n  expect(map.get([\"key\", 1])).toBeUndefined();\n  expect(map.has([\"key\", 2])).toBe(true); // Other key still exists\n\n  // Test delete non-existent key\n  expect(map.delete([\"nonexistent\", 1])).toBe(false);\n\n  // Test clear\n  map.clear();\n  expect(map.has([\"key\", 2])).toBe(false);\n\n  // Test with object keys\n  const objMap = new DependenciesMap<[object, number], string>();\n  const obj1 = { id: 1 };\n  const obj2 = { id: 2 };\n  objMap.set([obj1, 1], \"object1\");\n  objMap.set([obj2, 2], \"object2\");\n  expect(objMap.get([obj1, 1])).toBe(\"object1\");\n  expect(objMap.get([obj2, 2])).toBe(\"object2\");\n\n  // Test iteration\n  const iterMap = new DependenciesMap<[string], number>();\n  iterMap.set([\"a\"], 1);\n  iterMap.set([\"b\"], 2);\n  iterMap.set([\"c\"], 3);\n\n  const entries = Array.from(iterMap);\n  expect(entries.length).toBe(3);\n  expect(entries).toContainEqual([[\"a\"], 1]);\n  expect(entries).toContainEqual([[\"b\"], 2]);\n  expect(entries).toContainEqual([[\"c\"], 3]);\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAAuB;AAEhB,IAAM,qBAAN,MAA2C;AAAA,EAGhD,YAAY,OAAU;AACpB,QAAI,OAAO,YAAY,aAAa;AAClC,WAAK,OAAO,EAAE,OAAO,MAAM,MAAM;AAAA,IACnC,OAAO;AACL,WAAK,OAAO,IAAI,QAAW,KAAK;AAAA,IAClC;AAAA,EACF;AAAA,EAEA,QAAuB;AACrB,WAAO,KAAK,KAAK,MAAM;AAAA,EACzB;AACF;AAhBA;AAyCO,IAAM,kBAAN,MAA2C;AAAA,EAIhD,YAAY,SAA+C;AA6C3D,SAAC,MAAsB;AA5CrB,UAAM,gBAAgB,SAAS,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC,GAAG,QAAQ,IAAI,mBAAmB,EAAE,CAAC,CAAC,EAAE,CAAC,CAAU;AAChH,SAAK,WAAW,IAAI,QAAQ,iBAAiB,CAAC,CAAC;AAC/C,SAAK,WAAW,IAAI,IAAI,eAAe,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC,CAAC;AAAA,EACtE;AAAA,EAEA,IAAI,KAAuB;AACzB,WAAO,KAAK,SAAS,IAAI,GAAG,GAAG;AAAA,EACjC;AAAA,EAEA,IAAI,KAAQ,OAAgB;AAC1B,UAAM,WAAW,KAAK,SAAS,IAAI,GAAG;AACtC,UAAM,UAAU,EAAE,OAAO,QAAQ,UAAU,UAAU,IAAI,mBAAmB,GAAG,EAAE;AACjF,SAAK,SAAS,IAAI,KAAK,OAAO;AAC9B,SAAK,SAAS,IAAI,QAAQ,MAAM;AAChC,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,KAAiB;AACtB,UAAM,MAAM,KAAK,SAAS,IAAI,GAAG;AACjC,QAAI,KAAK;AACP,WAAK,SAAS,OAAO,GAAG;AACxB,WAAK,SAAS,OAAO,IAAI,MAAM;AAC/B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EAEA,IAAI,KAAiB;AACnB,WAAO,KAAK,SAAS,IAAI,GAAG,KAAK,KAAK,SAAS,IAAI,KAAK,SAAS,IAAI,GAAG,EAAG,MAAM;AAAA,EACnF;AAAA,EAEA,GAAE,YAAO,UAaR,YAAO,aAbN,GAAe,IAA8B;AAC7C,eAAW,UAAU,KAAK,UAAU;AAClC,YAAM,MAAM,OAAO,MAAM;AACzB,YAAM,WAAW,MAAM,KAAK,SAAS,IAAI,GAAG,IAAI;AAChD,UAAI,CAAC,KAAK;AAER,aAAK,SAAS,OAAO,MAAM;AAAA,MAC7B,WAAW,UAAU;AACnB,cAAM,CAAC,KAAK,SAAS,KAAK;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAGF;AA3FA,IAAAA,KAAAC;AAiKO,IAAM,eAAN,MAAyB;AAAA,EAI9B,YAAY,SAA+C;AAgD3D,SAACD,OAAsB;AA/CrB,UAAM,eAAe,CAAC,GAAG,WAAW,CAAC,CAAC;AACtC,SAAK,gBAAgB,IAAI,IAAI,aAAa,OAAO,CAAC,MAAM,CAAC,KAAK,oBAAoB,EAAE,CAAC,CAAC,CAAC,CAAC;AACxF,SAAK,WAAW,IAAI,gBAAgB,aAAa,OAAO,CAAC,MAA6B,KAAK,oBAAoB,EAAE,CAAC,CAAC,CAAC,CAAC;AAAA,EACvH;AAAA,EAEQ,oBAAoB,KAA4B;AACtD,WAAQ,OAAO,QAAQ,YAAY,QAAQ,QAAU,OAAO,QAAQ,YAAY,OAAO,OAAO,GAAG,MAAM;AAAA,EACzG;AAAA,EAEA,IAAI,KAAuB;AACzB,QAAI,KAAK,oBAAoB,GAAG,GAAG;AACjC,aAAO,KAAK,SAAS,IAAI,GAAG;AAAA,IAC9B,OAAO;AACL,aAAO,KAAK,cAAc,IAAI,GAAG;AAAA,IACnC;AAAA,EACF;AAAA,EAEA,IAAI,KAAQ,OAAgB;AAC1B,QAAI,KAAK,oBAAoB,GAAG,GAAG;AACjC,WAAK,SAAS,IAAI,KAAK,KAAK;AAAA,IAC9B,OAAO;AACL,WAAK,cAAc,IAAI,KAAK,KAAK;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,KAAiB;AACtB,QAAI,KAAK,oBAAoB,GAAG,GAAG;AACjC,aAAO,KAAK,SAAS,OAAO,GAAG;AAAA,IACjC,OAAO;AACL,aAAO,KAAK,cAAc,OAAO,GAAG;AAAA,IACtC;AAAA,EACF;AAAA,EAEA,IAAI,KAAiB;AACnB,QAAI,KAAK,oBAAoB,GAAG,GAAG;AACjC,aAAO,KAAK,SAAS,IAAI,GAAG;AAAA,IAC9B,OAAO;AACL,aAAO,KAAK,cAAc,IAAI,GAAG;AAAA,IACnC;AAAA,EACF;AAAA,EAEA,GAAEC,MAAA,OAAO,UAKRD,MAAA,OAAO,aALNC,IAAe,IAA8B;AAC7C,WAAO,KAAK;AACZ,WAAO,KAAK;AAAA,EACd;AAGF;AAtNA,IAAAD,KAAAC;AAkRO,IAAM,kBAAN,MAA0C;AAAA,EAA1C;AACL,SAAQ,SAAkC,EAAE,KAAK,IAAI,aAAa,GAAG,UAAU,OAAO,OAAO,OAAU;AA+EvG,SAACD,OAAsB;AAAA;AAAA,EA7Ef,eAAe,OAAiD;AACtE,QAAI,MAAM,UAAU;AAClB,aAAO,sBAAO,GAAG,MAAM,KAAK;AAAA,IAC9B,OAAO;AACL,aAAO,sBAAO,MAAM,MAAS;AAAA,IAC/B;AAAA,EACF;AAAA,EAGQ,iBAAiB,cAAqB,OAAiD;AAC7F,QAAK,aAAa,WAAW,GAAI;AAC/B,aAAO,KAAK,eAAe,KAAK;AAAA,IAClC,OAAO;AACL,YAAM,CAAC,KAAK,GAAG,IAAI,IAAI;AACvB,YAAM,WAAW,MAAM,IAAI,IAAI,GAAG;AAClC,UAAI,CAAC,UAAU;AACb,eAAO,sBAAO,MAAM,MAAS;AAAA,MAC/B;AACA,aAAO,KAAK,iBAAiB,MAAM,QAAQ;AAAA,IAC7C;AAAA,EACF;AAAA,EAEQ,YAAY,cAAqB,OAAwB,OAAiD;AAChH,QAAI,aAAa,WAAW,GAAG;AAC7B,YAAM,MAAM,KAAK,eAAe,KAAK;AACrC,UAAI,MAAM,WAAW,MAAM;AACzB,cAAM,WAAW;AACjB,cAAM,QAAQ,MAAM;AAAA,MACtB,OAAO;AACL,cAAM,WAAW;AACjB,cAAM,QAAQ;AAAA,MAChB;AACA,aAAO;AAAA,IACT,OAAO;AACL,YAAM,CAAC,KAAK,GAAG,IAAI,IAAI;AACvB,UAAI,WAAW,MAAM,IAAI,IAAI,GAAG;AAChC,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,IAAI,KAAK,WAAW,EAAE,KAAK,IAAI,aAAa,GAAG,UAAU,OAAO,OAAO,OAAU,CAAC;AAAA,MAC9F;AACA,aAAO,KAAK,YAAY,MAAM,OAAO,QAAQ;AAAA,IAC/C;AAAA,EACF;AAAA,EAEA,CAAS,cAAc,cAAqB,OAA0D;AACpG,QAAI,MAAM,UAAU;AAClB,YAAM,CAAC,cAAmB,MAAM,KAAK;AAAA,IACvC;AACA,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM,KAAK;AACpC,aAAO,KAAK,cAAc,CAAC,GAAG,cAAc,GAAG,GAAG,KAAK;AAAA,IACzD;AAAA,EACF;AAAA,EAEA,IAAI,cAAgC;AAClC,WAAO,sBAAO,GAAG,KAAK,iBAAiB,cAAc,KAAK,MAAM,GAAG,MAAS;AAAA,EAC9E;AAAA,EAEA,IAAI,cAAiB,OAAgB;AACnC,SAAK,YAAY,cAAc,sBAAO,GAAG,KAAK,GAAG,KAAK,MAAM;AAC5D,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,cAA0B;AAC/B,WAAO,KAAK,YAAY,cAAc,sBAAO,MAAM,MAAS,GAAG,KAAK,MAAM,EAAE,WAAW;AAAA,EACzF;AAAA,EAEA,IAAI,cAA0B;AAC5B,WAAO,KAAK,iBAAiB,cAAc,KAAK,MAAM,EAAE,WAAW;AAAA,EACrE;AAAA,EAEA,QAAc;AACZ,SAAK,SAAS,EAAE,KAAK,IAAI,aAAa,GAAG,UAAU,OAAO,OAAO,OAAU;AAAA,EAC7E;AAAA,EAEA,GAAEC,MAAA,OAAO,UAIRD,MAAA,OAAO,aAJNC,IAAe,IAA8B;AAC7C,WAAO,KAAK,cAAc,CAAC,GAAG,KAAK,MAAM;AAAA,EAC3C;AAGF;", "names": ["_a", "_b"]}