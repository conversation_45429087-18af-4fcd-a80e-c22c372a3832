{"version": 3, "sources": ["../../../../src/interface/crud/contact-channels.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport { contactChannelIdSchema, contactChannelIsPrimarySchema, contactChannelIsVerifiedSchema, contactChannelTypeSchema, contactChannelUsedForAuthSchema, contactChannelValueSchema, userIdOrMeSchema, userIdSchema, yupMixed, yupObject } from \"../../schema-fields\";\n\nexport const contactChannelsClientReadSchema = yupObject({\n  user_id: userIdSchema.defined(),\n  id: contactChannelIdSchema.defined(),\n  value: contactChannelValueSchema.defined(),\n  type: contactChannelTypeSchema.defined(),\n  used_for_auth: contactChannelUsedForAuthSchema.defined(),\n  is_verified: contactChannelIsVerifiedSchema.defined(),\n  is_primary: contactChannelIsPrimarySchema.defined(),\n}).defined();\n\nexport const contactChannelsCrudClientUpdateSchema = yupObject({\n  value: contactChannelValueSchema.optional(),\n  type: contactChannelTypeSchema.optional(),\n  used_for_auth: contactChannelUsedForAuthSchema.optional(),\n  is_primary: contactChannelIsPrimarySchema.optional(),\n}).defined();\n\nexport const contactChannelsCrudServerUpdateSchema = contactChannelsCrudClientUpdateSchema.concat(yupObject({\n  is_verified: contactChannelIsVerifiedSchema.optional(),\n}));\n\nexport const contactChannelsCrudClientCreateSchema = yupObject({\n  user_id: userIdOrMeSchema.defined(),\n  value: contactChannelValueSchema.defined(),\n  type: contactChannelTypeSchema.defined(),\n  used_for_auth: contactChannelUsedForAuthSchema.defined(),\n  is_primary: contactChannelIsPrimarySchema.optional(),\n}).defined();\n\nexport const contactChannelsCrudServerCreateSchema = contactChannelsCrudClientCreateSchema.concat(yupObject({\n  is_verified: contactChannelIsVerifiedSchema.optional(),\n}));\n\nexport const contactChannelsCrudClientDeleteSchema = yupMixed();\n\nexport const contactChannelsCrud = createCrud({\n  clientReadSchema: contactChannelsClientReadSchema,\n  clientUpdateSchema: contactChannelsCrudClientUpdateSchema,\n  clientCreateSchema: contactChannelsCrudClientCreateSchema,\n  clientDeleteSchema: contactChannelsCrudClientDeleteSchema,\n  serverUpdateSchema: contactChannelsCrudServerUpdateSchema,\n  serverCreateSchema: contactChannelsCrudServerCreateSchema,\n  docs: {\n    clientRead: {\n      summary: \"Get a contact channel\",\n      description: \"Retrieves a specific contact channel by the user ID and the contact channel ID.\",\n      tags: [\"Contact Channels\"],\n    },\n    clientCreate: {\n      summary: \"Create a contact channel\",\n      description: \"Add a new contact channel for a user.\",\n      tags: [\"Contact Channels\"],\n    },\n    clientUpdate: {\n      summary: \"Update a contact channel\",\n      description: \"Updates an existing contact channel. Only the values provided will be updated.\",\n      tags: [\"Contact Channels\"],\n    },\n    clientDelete: {\n      summary: \"Delete a contact channel\",\n      description: \"Removes a contact channel for a given user.\",\n      tags: [\"Contact Channels\"],\n    },\n    clientList: {\n      summary: \"List contact channels\",\n      description: \"Retrieves a list of all contact channels for a user.\",\n      tags: [\"Contact Channels\"],\n    }\n  }\n});\nexport type ContactChannelsCrud = CrudTypeOf<typeof contactChannelsCrud>;\n"], "mappings": ";AAAA,SAAqB,kBAAkB;AACvC,SAAS,wBAAwB,+BAA+B,gCAAgC,0BAA0B,iCAAiC,2BAA2B,kBAAkB,cAAc,UAAU,iBAAiB;AAE1O,IAAM,kCAAkC,UAAU;AAAA,EACvD,SAAS,aAAa,QAAQ;AAAA,EAC9B,IAAI,uBAAuB,QAAQ;AAAA,EACnC,OAAO,0BAA0B,QAAQ;AAAA,EACzC,MAAM,yBAAyB,QAAQ;AAAA,EACvC,eAAe,gCAAgC,QAAQ;AAAA,EACvD,aAAa,+BAA+B,QAAQ;AAAA,EACpD,YAAY,8BAA8B,QAAQ;AACpD,CAAC,EAAE,QAAQ;AAEJ,IAAM,wCAAwC,UAAU;AAAA,EAC7D,OAAO,0BAA0B,SAAS;AAAA,EAC1C,MAAM,yBAAyB,SAAS;AAAA,EACxC,eAAe,gCAAgC,SAAS;AAAA,EACxD,YAAY,8BAA8B,SAAS;AACrD,CAAC,EAAE,QAAQ;AAEJ,IAAM,wCAAwC,sCAAsC,OAAO,UAAU;AAAA,EAC1G,aAAa,+BAA+B,SAAS;AACvD,CAAC,CAAC;AAEK,IAAM,wCAAwC,UAAU;AAAA,EAC7D,SAAS,iBAAiB,QAAQ;AAAA,EAClC,OAAO,0BAA0B,QAAQ;AAAA,EACzC,MAAM,yBAAyB,QAAQ;AAAA,EACvC,eAAe,gCAAgC,QAAQ;AAAA,EACvD,YAAY,8BAA8B,SAAS;AACrD,CAAC,EAAE,QAAQ;AAEJ,IAAM,wCAAwC,sCAAsC,OAAO,UAAU;AAAA,EAC1G,aAAa,+BAA+B,SAAS;AACvD,CAAC,CAAC;AAEK,IAAM,wCAAwC,SAAS;AAEvD,IAAM,sBAAsB,WAAW;AAAA,EAC5C,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,MAAM;AAAA,IACJ,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,kBAAkB;AAAA,IAC3B;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,kBAAkB;AAAA,IAC3B;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,kBAAkB;AAAA,IAC3B;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,kBAAkB;AAAA,IAC3B;AAAA,IACA,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,kBAAkB;AAAA,IAC3B;AAAA,EACF;AACF,CAAC;", "names": []}