"""
🐾 数据库连接和会话管理
Database Connection and Session Management

提供数据库连接、会话管理和基础操作功能
"""

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
import os
from typing import Generator

from app.core.config import settings

# 创建数据库引擎
if settings.DATABASE_URL.startswith("sqlite"):
    # SQLite配置
    engine = create_engine(
        settings.DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        echo=settings.DEBUG
    )
else:
    # PostgreSQL配置
    engine = create_engine(
        settings.DATABASE_URL,
        pool_pre_ping=True,
        echo=settings.DEBUG
    )

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()

def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话
    Database session dependency
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_tables():
    """
    创建所有数据库表
    Create all database tables
    """
    Base.metadata.create_all(bind=engine)

def drop_tables():
    """
    删除所有数据库表
    Drop all database tables
    """
    Base.metadata.drop_all(bind=engine)

def init_db():
    """
    初始化数据库
    Initialize database with sample data
    """
    from app.models.user import User
    from app.models.stock import Stock
    from app.models.order import Order, Transaction, Position
    
    # 创建表
    create_tables()
    
    # 创建示例数据
    db = SessionLocal()
    try:
        # 检查是否已有数据
        if db.query(User).first() is None:
            # 创建示例用户
            demo_user = User(
                username="demo",
                email="<EMAIL>",
                nickname="演示用户",
                balance=100000.0,  # 10万初始资金
                level=1
            )
            demo_user.set_password("demo123")
            db.add(demo_user)
            
        if db.query(Stock).first() is None:
            # 创建示例股票
            sample_stocks = [
                Stock(
                    code="000001",
                    name="平安银行",
                    industry="银行",
                    current_price=12.50,
                    opening_price=12.30,
                    previous_close=12.40,
                    highest_price=12.80,
                    lowest_price=12.20,
                    volume=1500000,
                    turnover=18750000.0,
                    is_active=True
                ),
                Stock(
                    code="000002",
                    name="万科A",
                    industry="房地产",
                    current_price=18.90,
                    opening_price=18.50,
                    previous_close=18.60,
                    highest_price=19.20,
                    lowest_price=18.30,
                    volume=2300000,
                    turnover=43470000.0,
                    is_active=True
                ),
                Stock(
                    code="600036",
                    name="招商银行",
                    industry="银行",
                    current_price=45.80,
                    opening_price=45.20,
                    previous_close=45.50,
                    highest_price=46.10,
                    lowest_price=44.90,
                    volume=1800000,
                    turnover=82440000.0,
                    is_active=True
                ),
                Stock(
                    code="600519",
                    name="贵州茅台",
                    industry="食品饮料",
                    current_price=1680.00,
                    opening_price=1650.00,
                    previous_close=1670.00,
                    highest_price=1690.00,
                    lowest_price=1640.00,
                    volume=120000,
                    turnover=201600000.0,
                    is_active=True
                ),
                Stock(
                    code="000858",
                    name="五粮液",
                    industry="食品饮料",
                    current_price=158.50,
                    opening_price=156.00,
                    previous_close=157.20,
                    highest_price=160.00,
                    lowest_price=155.50,
                    volume=890000,
                    turnover=141065000.0,
                    is_active=True
                )
            ]
            
            for stock in sample_stocks:
                # 计算涨跌幅
                stock.change_amount = stock.current_price - stock.previous_close
                stock.change_percent = (stock.change_amount / stock.previous_close) * 100
                stock.limit_up = stock.previous_close * 1.1
                stock.limit_down = stock.previous_close * 0.9
                db.add(stock)
        
        db.commit()
        print("✅ 数据库初始化完成")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    # 直接运行此文件时初始化数据库
    init_db()
