import csv
import time
import requests

# 调用AI输出SEO文章
url1 = 'https://fkhd2024.jz.fkw.com/en/ajax/statistics_h.jsp?_v=1753064079506&_TOKEN=981e7fb9598bc8293ebf2ce596cc704d'
headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36',
    'cookie': '_cliid=DOawg7ksYG5LGNtS; first_ta=207; _ta=207; _tp=-; _newUnion=0; _kw=0; _vid_url=https%3A%2F%2Fwww.fkw.com%2F; _s_pro=www.fkw.com%2F; _c_pro=www.fkw.com%2F; reg_sid=0; innerFlag=1; _faiHeDistictId=660ab08970cba0aa; Hm_lvt_26126ee052ae4ad30a36da928aff7654=**********; Hm_lpvt_26126ee052ae4ad30a36da928aff7654=**********; HMACCOUNT=634022B4DF6C6D86; _pykey_=7b2e33a6-26ec-500c-93e3-2a3477e62001; loginReferer=https://www.fkw.com/; loginComeForm=fkjz; wxRegBiz=none; grayUrl=; loginCacct=fkhd2024; loginCaid=********; loginSacct=boss; loginUseSacct=0; _FSESSIONID=9obYC2XHl2caIiNL; loginSign=; _jzmFirstLogin=false; loginTimeInMills=*************; _hasClosePlatinumAd_=false; _hasClosePlatinum_=false; _hasCloseFlyerAd_=false; _hasCloseHdGG_=false; faiscoAd=true; _whereToPortal_=login; _readAllOrderTab=0; _new_reg_home=8; adImg_module_********=0_12_**********000; _isFirstLoginPc=false; _isFirstLoginPc_7=false'
}
data = {
    "cmd": "getWafNotCk_Detail",
    "type": 3,
    "wid": 1,
    "isMobile": 0,
    "typeQy": 0,
    "mapId": -1,
    'startTime': **********,
    'endTime': **********,
}
res = requests.post(url1, headers=headers, data=data)
if res.status_code != 200:
    print("输出失败")
else:
    tree = res.json()
    a = tree['detailData']['sa']
    num = 0
    pv = 0
    for k in a:
        if k['pv'] > 0:
            num += 1
            pv += k['pv']
    print("流量文章总数:",num,"   总访问量:",pv)
    b = tree['detailData']['st']
    for k in b:
        print(int(k['pv']))