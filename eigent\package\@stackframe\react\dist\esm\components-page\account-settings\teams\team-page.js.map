{"version": 3, "sources": ["../../../../../src/components-page/account-settings/teams/team-page.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { Team } from \"../../..\";\nimport { PageLayout } from \"../page-layout\";\nimport { LeaveTeamSection } from \"./leave-team-section\";\nimport { TeamApiKeysSection } from \"./team-api-keys-section\";\nimport { TeamDisplayNameSection } from \"./team-display-name-section\";\nimport { TeamMemberInvitationSection } from \"./team-member-invitation-section\";\nimport { TeamMemberListSection } from \"./team-member-list-section\";\nimport { TeamProfileImageSection } from \"./team-profile-image-section\";\nimport { TeamUserProfileSection } from \"./team-profile-user-section\";\n\n\nexport function TeamPage(props: { team: Team }) {\n  return (\n    <PageLayout>\n      <TeamUserProfileSection key={`user-profile-${props.team.id}`} team={props.team} />\n      <TeamProfileImageSection key={`profile-image-${props.team.id}`} team={props.team} />\n      <TeamDisplayNameSection key={`display-name-${props.team.id}`} team={props.team} />\n      <TeamMemberListSection key={`member-list-${props.team.id}`} team={props.team} />\n      <TeamMemberInvitationSection key={`member-invitation-${props.team.id}`} team={props.team} />\n      <TeamApiKeysSection key={`api-keys-${props.team.id}`} team={props.team} />\n      <LeaveTeamSection key={`leave-team-${props.team.id}`} team={props.team} />\n    </PageLayout>\n  );\n}\n"], "mappings": ";AAKA,SAAS,kBAAkB;AAC3B,SAAS,wBAAwB;AACjC,SAAS,0BAA0B;AACnC,SAAS,8BAA8B;AACvC,SAAS,mCAAmC;AAC5C,SAAS,6BAA6B;AACtC,SAAS,+BAA+B;AACxC,SAAS,8BAA8B;AAKnC,SACE,KADF;AAFG,SAAS,SAAS,OAAuB;AAC9C,SACE,qBAAC,cACC;AAAA,wBAAC,0BAA6D,MAAM,MAAM,QAA7C,gBAAgB,MAAM,KAAK,EAAE,EAAsB;AAAA,IAChF,oBAAC,2BAA+D,MAAM,MAAM,QAA9C,iBAAiB,MAAM,KAAK,EAAE,EAAsB;AAAA,IAClF,oBAAC,0BAA6D,MAAM,MAAM,QAA7C,gBAAgB,MAAM,KAAK,EAAE,EAAsB;AAAA,IAChF,oBAAC,yBAA2D,MAAM,MAAM,QAA5C,eAAe,MAAM,KAAK,EAAE,EAAsB;AAAA,IAC9E,oBAAC,+BAAuE,MAAM,MAAM,QAAlD,qBAAqB,MAAM,KAAK,EAAE,EAAsB;AAAA,IAC1F,oBAAC,sBAAqD,MAAM,MAAM,QAAzC,YAAY,MAAM,KAAK,EAAE,EAAsB;AAAA,IACxE,oBAAC,oBAAqD,MAAM,MAAM,QAA3C,cAAc,MAAM,KAAK,EAAE,EAAsB;AAAA,KAC1E;AAEJ;", "names": []}