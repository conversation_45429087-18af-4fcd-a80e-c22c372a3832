{"version": 3, "sources": ["../../../src/components/user-button.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { runAsynchronouslyWithAlert } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger, Skeleton, Typography } from \"@stackframe/stack-ui\";\nimport { CircleUser, LogIn, LogOut, SunMoon, UserPlus } from \"lucide-react\";\nimport React, { Suspense } from \"react\";\nimport { CurrentUser, useStackApp, useUser } from \"..\";\nimport { useTranslation } from \"../lib/translations\";\nimport { UserAvatar } from \"./elements/user-avatar\";\n\nfunction Item(props: { text: string, icon: React.ReactNode, onClick: () => void | Promise<void> }) {\n  return (\n    <DropdownMenuItem onClick={() => runAsynchronouslyWithAlert(props.onClick)}>\n      <div className=\"flex gap-2 items-center\">\n        {props.icon}\n        <Typography>{props.text}</Typography>\n      </div>\n    </DropdownMenuItem>\n  );\n}\n\ntype UserButtonProps = {\n  showUserInfo?: boolean,\n  colorModeToggle?: () => void | Promise<void>,\n  extraItems?: {\n    text: string,\n    icon: React.ReactNode,\n    onClick: () => void | Promise<void>,\n  }[],\n};\n\nexport function UserButton(props: UserButtonProps) {\n  return (\n    <Suspense fallback={<Skeleton className=\"h-[34px] w-[34px] rounded-full stack-scope\" />}>\n      <UserButtonInner {...props} />\n    </Suspense>\n  );\n}\n\nfunction UserButtonInner(props: UserButtonProps) {\n  const user = useUser();\n  return <UserButtonInnerInner {...props} user={user} />;\n}\n\n\nfunction UserButtonInnerInner(props: UserButtonProps & { user: CurrentUser | null }) {\n  const { t } = useTranslation();\n  const user = props.user;\n  const app = useStackApp();\n\n  const iconProps = { size: 20, className: 'h-4 w-4' };\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger className=\"outline-none stack-scope\">\n        <div className=\"flex gap-2 items-center\">\n          <UserAvatar user={user} />\n          {user && props.showUserInfo &&\n            <div className=\"flex flex-col justify-center text-left\">\n              <Typography className=\"max-w-40 truncate\">{user.displayName}</Typography>\n              <Typography className=\"max-w-40 truncate\" variant=\"secondary\" type='label'>{user.primaryEmail}</Typography>\n            </div>\n          }\n        </div>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent className=\"stack-scope\">\n        <DropdownMenuLabel>\n          <div className=\"flex gap-2 items-center\">\n            <UserAvatar user={user} />\n            <div>\n              {user && <Typography className=\"max-w-40 truncate\">{user.displayName}</Typography>}\n              {user && <Typography className=\"max-w-40 truncate\" variant=\"secondary\" type='label'>{user.primaryEmail}</Typography>}\n              {!user && <Typography>{t('Not signed in')}</Typography>}\n            </div>\n          </div>\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        {user && <Item\n          text={t('Account settings')}\n          onClick={async () => await app.redirectToAccountSettings()}\n          icon={<CircleUser {...iconProps} />}\n        />}\n        {!user && <Item\n          text={t('Sign in')}\n          onClick={async () => await app.redirectToSignIn()}\n          icon={<LogIn {...iconProps} />}\n        />}\n        {!user && <Item\n          text={t('Sign up')}\n          onClick={async () => await app.redirectToSignUp()}\n          icon={<UserPlus {...iconProps}/> }\n        />}\n        {user && props.extraItems && props.extraItems.map((item, index) => (\n          <Item key={index} {...item} />\n        ))}\n        {props.colorModeToggle && (\n          <Item\n            text={t('Toggle theme')}\n            onClick={props.colorModeToggle}\n            icon={<SunMoon {...iconProps} />}\n          />\n        )}\n        {user && <Item\n          text={t('Sign out')}\n          onClick={() => user.signOut()}\n          icon={<LogOut {...iconProps} />}\n        />}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  );\n}\n"], "mappings": ";;;AAOA,SAAS,kCAAkC;AAC3C,SAAS,cAAc,qBAAqB,kBAAkB,mBAAmB,uBAAuB,qBAAqB,UAAU,kBAAkB;AACzJ,SAAS,YAAY,OAAO,QAAQ,SAAS,gBAAgB;AAC7D,SAAgB,gBAAgB;AAChC,SAAsB,aAAa,eAAe;AAClD,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAKrB,SAEE,KAFF;AAHN,SAAS,KAAK,OAAqF;AACjG,SACE,oBAAC,oBAAiB,SAAS,MAAM,2BAA2B,MAAM,OAAO,GACvE,+BAAC,SAAI,WAAU,2BACZ;AAAA,UAAM;AAAA,IACP,oBAAC,cAAY,gBAAM,MAAK;AAAA,KAC1B,GACF;AAEJ;AAYO,SAAS,WAAW,OAAwB;AACjD,SACE,oBAAC,YAAS,UAAU,oBAAC,YAAS,WAAU,8CAA6C,GACnF,8BAAC,mBAAiB,GAAG,OAAO,GAC9B;AAEJ;AAEA,SAAS,gBAAgB,OAAwB;AAC/C,QAAM,OAAO,QAAQ;AACrB,SAAO,oBAAC,wBAAsB,GAAG,OAAO,MAAY;AACtD;AAGA,SAAS,qBAAqB,OAAuD;AACnF,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,OAAO,MAAM;AACnB,QAAM,MAAM,YAAY;AAExB,QAAM,YAAY,EAAE,MAAM,IAAI,WAAW,UAAU;AAEnD,SACE,qBAAC,gBACC;AAAA,wBAAC,uBAAoB,WAAU,4BAC7B,+BAAC,SAAI,WAAU,2BACb;AAAA,0BAAC,cAAW,MAAY;AAAA,MACvB,QAAQ,MAAM,gBACb,qBAAC,SAAI,WAAU,0CACb;AAAA,4BAAC,cAAW,WAAU,qBAAqB,eAAK,aAAY;AAAA,QAC5D,oBAAC,cAAW,WAAU,qBAAoB,SAAQ,aAAY,MAAK,SAAS,eAAK,cAAa;AAAA,SAChG;AAAA,OAEJ,GACF;AAAA,IACA,qBAAC,uBAAoB,WAAU,eAC7B;AAAA,0BAAC,qBACC,+BAAC,SAAI,WAAU,2BACb;AAAA,4BAAC,cAAW,MAAY;AAAA,QACxB,qBAAC,SACE;AAAA,kBAAQ,oBAAC,cAAW,WAAU,qBAAqB,eAAK,aAAY;AAAA,UACpE,QAAQ,oBAAC,cAAW,WAAU,qBAAoB,SAAQ,aAAY,MAAK,SAAS,eAAK,cAAa;AAAA,UACtG,CAAC,QAAQ,oBAAC,cAAY,YAAE,eAAe,GAAE;AAAA,WAC5C;AAAA,SACF,GACF;AAAA,MACA,oBAAC,yBAAsB;AAAA,MACtB,QAAQ;AAAA,QAAC;AAAA;AAAA,UACR,MAAM,EAAE,kBAAkB;AAAA,UAC1B,SAAS,YAAY,MAAM,IAAI,0BAA0B;AAAA,UACzD,MAAM,oBAAC,cAAY,GAAG,WAAW;AAAA;AAAA,MACnC;AAAA,MACC,CAAC,QAAQ;AAAA,QAAC;AAAA;AAAA,UACT,MAAM,EAAE,SAAS;AAAA,UACjB,SAAS,YAAY,MAAM,IAAI,iBAAiB;AAAA,UAChD,MAAM,oBAAC,SAAO,GAAG,WAAW;AAAA;AAAA,MAC9B;AAAA,MACC,CAAC,QAAQ;AAAA,QAAC;AAAA;AAAA,UACT,MAAM,EAAE,SAAS;AAAA,UACjB,SAAS,YAAY,MAAM,IAAI,iBAAiB;AAAA,UAChD,MAAM,oBAAC,YAAU,GAAG,WAAU;AAAA;AAAA,MAChC;AAAA,MACC,QAAQ,MAAM,cAAc,MAAM,WAAW,IAAI,CAAC,MAAM,UACvD,oBAAC,QAAkB,GAAG,QAAX,KAAiB,CAC7B;AAAA,MACA,MAAM,mBACL;AAAA,QAAC;AAAA;AAAA,UACC,MAAM,EAAE,cAAc;AAAA,UACtB,SAAS,MAAM;AAAA,UACf,MAAM,oBAAC,WAAS,GAAG,WAAW;AAAA;AAAA,MAChC;AAAA,MAED,QAAQ;AAAA,QAAC;AAAA;AAAA,UACR,MAAM,EAAE,UAAU;AAAA,UAClB,SAAS,MAAM,KAAK,QAAQ;AAAA,UAC5B,MAAM,oBAAC,UAAQ,GAAG,WAAW;AAAA;AAAA,MAC/B;AAAA,OACF;AAAA,KACF;AAEJ;", "names": []}