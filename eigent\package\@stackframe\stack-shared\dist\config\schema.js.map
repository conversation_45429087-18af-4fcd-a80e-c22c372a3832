{"version": 3, "sources": ["../../src/config/schema.ts"], "sourcesContent": ["import * as yup from \"yup\";\nimport * as schemaFields from \"../schema-fields\";\nimport { yupBoolean, yupObject, yupRecord, yupString } from \"../schema-fields\";\nimport { allProviders } from \"../utils/oauth\";\nimport { DeepMerge, DeepPartial, get, has, isObjectLike, mapValues, set } from \"../utils/objects\";\nimport { PrettifyType } from \"../utils/types\";\nimport { Config, NormalizesTo } from \"./format\";\n\n// NOTE: The validation schemas in here are all schematic validators, not sanity-check validators.\n// For more info, see ./README.md\n\n\nexport const configLevels = ['project', 'branch', 'environment', 'organization'] as const;\nexport type ConfigLevel = typeof configLevels[number];\nconst permissionRegex = /^\\$?[a-z0-9_:]+$/;\nconst customPermissionRegex = /^[a-z0-9_:]+$/;\n\n/**\n * All fields that can be overridden at this level.\n */\nexport const projectConfigSchema = yupObject({});\n\n// --- NEW RBAC Schema ---\nconst branchRbacDefaultPermissions = yupRecord(\n  yupString().optional().matches(permissionRegex),\n  yupBoolean().isTrue().optional(),\n).optional();\n\nconst branchRbacSchema = yupObject({\n  permissions: yupRecord(\n    yupString().optional().matches(customPermissionRegex),\n    yupObject({\n      description: yupString().optional(),\n      scope: yupString().oneOf(['team', 'project']).optional(),\n      containedPermissionIds: yupRecord(\n        yupString().optional().matches(permissionRegex),\n        yupBoolean().isTrue().optional()\n      ).optional(),\n    }).optional(),\n  ).optional(),\n  defaultPermissions: yupObject({\n    teamCreator: branchRbacDefaultPermissions,\n    teamMember: branchRbacDefaultPermissions,\n    signUp: branchRbacDefaultPermissions,\n  }).optional(),\n}).optional();\n// --- END NEW RBAC Schema ---\n\n// --- NEW API Keys Schema ---\nconst branchApiKeysSchema = yupObject({\n  enabled: yupObject({\n    team: yupBoolean().optional(),\n    user: yupBoolean().optional(),\n  }).optional(),\n}).optional();\n// --- END NEW API Keys Schema ---\n\n\nconst branchAuthSchema = yupObject({\n  allowSignUp: yupBoolean().optional(),\n  password: yupObject({\n    allowSignIn: yupBoolean().optional(),\n  }).optional(),\n  otp: yupObject({\n    allowSignIn: yupBoolean().optional(),\n  }).optional(),\n  passkey: yupObject({\n    allowSignIn: yupBoolean().optional(),\n  }).optional(),\n  oauth: yupObject({\n    accountMergeStrategy: yupString().oneOf(['link_method', 'raise_error', 'allow_duplicates']).optional(),\n    providers: yupRecord(\n      yupString().optional().matches(permissionRegex),\n      yupObject({\n        type: yupString().oneOf(allProviders).optional(),\n        allowSignIn: yupBoolean().optional(),\n        allowConnectedAccounts: yupBoolean().optional(),\n      }).defined(),\n    ).optional(),\n  }).optional(),\n}).optional();\n\nconst branchDomain = yupObject({\n  allowLocalhost: yupBoolean().optional(),\n}).optional();\n\nexport const branchConfigSchema = projectConfigSchema.concat(yupObject({\n  rbac: branchRbacSchema,\n\n  teams: yupObject({\n    createPersonalTeamOnSignUp: yupBoolean().optional(),\n    allowClientTeamCreation: yupBoolean().optional(),\n  }).optional(),\n\n  users: yupObject({\n    allowClientUserDeletion: yupBoolean().optional(),\n  }).optional(),\n\n  apiKeys: branchApiKeysSchema,\n\n  domains: branchDomain,\n\n  auth: branchAuthSchema,\n\n  emails: yupObject({}),\n}));\n\n\nexport const environmentConfigSchema = branchConfigSchema.concat(yupObject({\n  auth: branchConfigSchema.getNested(\"auth\").concat(yupObject({\n    oauth: branchConfigSchema.getNested(\"auth\").getNested(\"oauth\").concat(yupObject({\n      providers: yupRecord(\n        yupString().optional().matches(permissionRegex),\n        yupObject({\n          type: yupString().oneOf(allProviders).optional(),\n          isShared: yupBoolean().optional(),\n          clientId: schemaFields.oauthClientIdSchema.optional(),\n          clientSecret: schemaFields.oauthClientSecretSchema.optional(),\n          facebookConfigId: schemaFields.oauthFacebookConfigIdSchema.optional(),\n          microsoftTenantId: schemaFields.oauthMicrosoftTenantIdSchema.optional(),\n          allowSignIn: yupBoolean().optional(),\n          allowConnectedAccounts: yupBoolean().optional(),\n        }),\n      ).optional(),\n    }).optional()),\n  })),\n\n  emails: branchConfigSchema.getNested(\"emails\").concat(yupObject({\n    server: yupObject({\n      isShared: yupBoolean().optional(),\n      host: schemaFields.emailHostSchema.optional().nonEmpty(),\n      port: schemaFields.emailPortSchema.optional(),\n      username: schemaFields.emailUsernameSchema.optional().nonEmpty(),\n      password: schemaFields.emailPasswordSchema.optional().nonEmpty(),\n      senderName: schemaFields.emailSenderNameSchema.optional().nonEmpty(),\n      senderEmail: schemaFields.emailSenderEmailSchema.optional().nonEmpty(),\n    }),\n  }).optional()),\n\n  domains: branchConfigSchema.getNested(\"domains\").concat(yupObject({\n    trustedDomains: yupRecord(\n      yupString().uuid().optional(),\n      yupObject({\n        baseUrl: schemaFields.urlSchema.optional(),\n        handlerPath: schemaFields.handlerPathSchema.optional(),\n      }),\n    ).optional(),\n  })),\n}));\n\nexport const organizationConfigSchema = environmentConfigSchema.concat(yupObject({}));\n\n\n// Defaults\n// these are objects that are merged together to form the rendered config (see ./README.md)\n// Wherever an object could be used as a value, a function can instead be used to generate the default values on a per-key basis\nexport const projectConfigDefaults = {} satisfies DeepReplaceAllowFunctionsForObjects<ProjectConfigStrippedNormalizedOverride>;\n\nexport const branchConfigDefaults = {} satisfies DeepReplaceAllowFunctionsForObjects<BranchConfigStrippedNormalizedOverride>;\n\nexport const environmentConfigDefaults = {} satisfies DeepReplaceAllowFunctionsForObjects<EnvironmentConfigStrippedNormalizedOverride>;\n\nexport const organizationConfigDefaults = {\n  rbac: {\n    permissions: (key: string) => ({}),\n    defaultPermissions: {\n      teamCreator: {},\n      teamMember: {},\n      signUp: {},\n    },\n  },\n\n  apiKeys: {\n    enabled: {\n      team: false,\n      user: false,\n    },\n  },\n\n  teams: {\n    createPersonalTeamOnSignUp: false,\n    allowClientTeamCreation: false,\n  },\n\n  users: {\n    allowClientUserDeletion: false,\n  },\n\n  domains: {\n    allowLocalhost: false,\n    trustedDomains: (key: string) => ({\n      handlerPath: '/handler',\n    }),\n  },\n\n  auth: {\n    allowSignUp: true,\n    password: {\n      allowSignIn: false,\n    },\n    otp: {\n      allowSignIn: false,\n    },\n    passkey: {\n      allowSignIn: false,\n    },\n    oauth: {\n      accountMergeStrategy: 'link_method',\n      providers: (key: string) => ({\n        isShared: true,\n        allowSignIn: false,\n        allowConnectedAccounts: false,\n      }),\n    },\n  },\n\n  emails: {\n    server: {\n      isShared: true,\n    },\n  },\n} satisfies DeepReplaceAllowFunctionsForObjects<OrganizationConfigStrippedNormalizedOverride>;\n\nexport type DeepReplaceAllowFunctionsForObjects<T> = T extends object ? { [K in keyof T]: DeepReplaceAllowFunctionsForObjects<T[K]> } | ((arg: keyof T) => DeepReplaceAllowFunctionsForObjects<T[keyof T]>) : T;\nexport type DeepReplaceFunctionsWithObjects<T> = T extends (arg: infer K extends string) => infer R ? DeepReplaceFunctionsWithObjects<Record<K, R>> : (T extends object ? { [K in keyof T]: DeepReplaceFunctionsWithObjects<T[K]> } : T);\nexport type ApplyDefaults<D extends object | ((key: string) => unknown), C extends object> = DeepMerge<DeepReplaceFunctionsWithObjects<D>, C>;\nexport function applyDefaults<D extends object | ((key: string) => unknown), C extends object>(defaults: D, config: C): ApplyDefaults<D, C> {\n  const res: any = typeof defaults === 'function' ? {} : mapValues(defaults, v => typeof v === 'function' ? {} : (typeof v === 'object' ? applyDefaults(v as any, {}) : v));\n  for (const [key, mergeValue] of Object.entries(config)) {\n    const baseValue = typeof defaults === 'function' ? defaults(key) : (has(defaults, key as any) ? get(defaults, key as any) : undefined);\n    if (baseValue !== undefined) {\n      if (isObjectLike(baseValue) && isObjectLike(mergeValue)) {\n        set(res, key, applyDefaults(baseValue, mergeValue));\n        continue;\n      }\n    }\n    set(res, key, mergeValue);\n  }\n  return res as any;\n}\nundefined?.test(\"applyDefaults\", ({ expect }) => {\n  expect(applyDefaults({ a: 1 }, { a: 2 })).toEqual({ a: 2 });\n  expect(applyDefaults({ a: { b: 1 } }, { a: { c: 2 } })).toEqual({ a: { b: 1, c: 2 } });\n  expect(applyDefaults((key: string) => ({ b: key }), { a: {} })).toEqual({ a: { b: \"a\" } });\n  expect(applyDefaults({ a: (key: string) => ({ b: key }) }, { a: { c: { d: 1 } } })).toEqual({ a: { c: { b: \"c\", d: 1 } } });\n  expect(applyDefaults({ a: (key: string) => ({ b: key }) }, {})).toEqual({ a: {} });\n  expect(applyDefaults({ a: { b: (key: string) => ({ b: key }) } }, {})).toEqual({ a: { b: {} } });\n});\n\n// Normalized overrides\nexport type ProjectConfigNormalizedOverride = yup.InferType<typeof projectConfigSchema>;\nexport type BranchConfigNormalizedOverride = yup.InferType<typeof branchConfigSchema>;\nexport type EnvironmentConfigNormalizedOverride = yup.InferType<typeof environmentConfigSchema>;\nexport type OrganizationConfigNormalizedOverride = yup.InferType<typeof organizationConfigSchema>;\n\n// Normalized overrides, without the properties that may be overridden still\nexport type ProjectConfigStrippedNormalizedOverride = Omit<ProjectConfigNormalizedOverride,\n  | keyof BranchConfigNormalizedOverride\n  | keyof EnvironmentConfigNormalizedOverride\n  | keyof OrganizationConfigNormalizedOverride\n>;\nexport type BranchConfigStrippedNormalizedOverride = Omit<BranchConfigNormalizedOverride,\n  | keyof EnvironmentConfigNormalizedOverride\n  | keyof OrganizationConfigNormalizedOverride\n>;\nexport type EnvironmentConfigStrippedNormalizedOverride = Omit<EnvironmentConfigNormalizedOverride,\n  | keyof OrganizationConfigNormalizedOverride\n>;\nexport type OrganizationConfigStrippedNormalizedOverride = OrganizationConfigNormalizedOverride;\n\n// Overrides\nexport type ProjectConfigOverride = NormalizesTo<ProjectConfigNormalizedOverride>;\nexport type BranchConfigOverride = NormalizesTo<BranchConfigNormalizedOverride>;\nexport type EnvironmentConfigOverride = NormalizesTo<EnvironmentConfigNormalizedOverride>;\nexport type OrganizationConfigOverride = NormalizesTo<OrganizationConfigNormalizedOverride>;\n\n// Override overrides (used to update the overrides)\nexport type ProjectConfigOverrideOverride = Config & DeepPartial<ProjectConfigOverride>;\nexport type BranchConfigOverrideOverride = Config & DeepPartial<BranchConfigOverride>;\nexport type EnvironmentConfigOverrideOverride = Config & DeepPartial<EnvironmentConfigOverride>;\nexport type OrganizationConfigOverrideOverride = Config & DeepPartial<OrganizationConfigOverride>;\n\n// Incomplete configs\nexport type ProjectIncompleteConfig = ProjectConfigNormalizedOverride;\nexport type BranchIncompleteConfig = ProjectIncompleteConfig & BranchConfigNormalizedOverride;\nexport type EnvironmentIncompleteConfig = BranchIncompleteConfig & EnvironmentConfigNormalizedOverride;\nexport type OrganizationIncompleteConfig = EnvironmentIncompleteConfig & OrganizationConfigNormalizedOverride;\n\n// Rendered configs\nexport type ProjectRenderedConfig = PrettifyType<ApplyDefaults<typeof projectConfigDefaults, ProjectConfigStrippedNormalizedOverride>>;\nexport type BranchRenderedConfig = PrettifyType<ProjectRenderedConfig & ApplyDefaults<typeof branchConfigDefaults, BranchConfigStrippedNormalizedOverride>>;\nexport type EnvironmentRenderedConfig = PrettifyType<BranchRenderedConfig & ApplyDefaults<typeof environmentConfigDefaults, EnvironmentConfigStrippedNormalizedOverride>>;\nexport type OrganizationRenderedConfig = PrettifyType<EnvironmentRenderedConfig & ApplyDefaults<typeof organizationConfigDefaults, OrganizationConfigStrippedNormalizedOverride>>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,mBAA8B;AAC9B,2BAA4D;AAC5D,mBAA6B;AAC7B,qBAA+E;AAQxE,IAAM,eAAe,CAAC,WAAW,UAAU,eAAe,cAAc;AAE/E,IAAM,kBAAkB;AACxB,IAAM,wBAAwB;AAKvB,IAAM,0BAAsB,gCAAU,CAAC,CAAC;AAG/C,IAAM,mCAA+B;AAAA,MACnC,gCAAU,EAAE,SAAS,EAAE,QAAQ,eAAe;AAAA,MAC9C,iCAAW,EAAE,OAAO,EAAE,SAAS;AACjC,EAAE,SAAS;AAEX,IAAM,uBAAmB,gCAAU;AAAA,EACjC,iBAAa;AAAA,QACX,gCAAU,EAAE,SAAS,EAAE,QAAQ,qBAAqB;AAAA,QACpD,gCAAU;AAAA,MACR,iBAAa,gCAAU,EAAE,SAAS;AAAA,MAClC,WAAO,gCAAU,EAAE,MAAM,CAAC,QAAQ,SAAS,CAAC,EAAE,SAAS;AAAA,MACvD,4BAAwB;AAAA,YACtB,gCAAU,EAAE,SAAS,EAAE,QAAQ,eAAe;AAAA,YAC9C,iCAAW,EAAE,OAAO,EAAE,SAAS;AAAA,MACjC,EAAE,SAAS;AAAA,IACb,CAAC,EAAE,SAAS;AAAA,EACd,EAAE,SAAS;AAAA,EACX,wBAAoB,gCAAU;AAAA,IAC5B,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,QAAQ;AAAA,EACV,CAAC,EAAE,SAAS;AACd,CAAC,EAAE,SAAS;AAIZ,IAAM,0BAAsB,gCAAU;AAAA,EACpC,aAAS,gCAAU;AAAA,IACjB,UAAM,iCAAW,EAAE,SAAS;AAAA,IAC5B,UAAM,iCAAW,EAAE,SAAS;AAAA,EAC9B,CAAC,EAAE,SAAS;AACd,CAAC,EAAE,SAAS;AAIZ,IAAM,uBAAmB,gCAAU;AAAA,EACjC,iBAAa,iCAAW,EAAE,SAAS;AAAA,EACnC,cAAU,gCAAU;AAAA,IAClB,iBAAa,iCAAW,EAAE,SAAS;AAAA,EACrC,CAAC,EAAE,SAAS;AAAA,EACZ,SAAK,gCAAU;AAAA,IACb,iBAAa,iCAAW,EAAE,SAAS;AAAA,EACrC,CAAC,EAAE,SAAS;AAAA,EACZ,aAAS,gCAAU;AAAA,IACjB,iBAAa,iCAAW,EAAE,SAAS;AAAA,EACrC,CAAC,EAAE,SAAS;AAAA,EACZ,WAAO,gCAAU;AAAA,IACf,0BAAsB,gCAAU,EAAE,MAAM,CAAC,eAAe,eAAe,kBAAkB,CAAC,EAAE,SAAS;AAAA,IACrG,eAAW;AAAA,UACT,gCAAU,EAAE,SAAS,EAAE,QAAQ,eAAe;AAAA,UAC9C,gCAAU;AAAA,QACR,UAAM,gCAAU,EAAE,MAAM,yBAAY,EAAE,SAAS;AAAA,QAC/C,iBAAa,iCAAW,EAAE,SAAS;AAAA,QACnC,4BAAwB,iCAAW,EAAE,SAAS;AAAA,MAChD,CAAC,EAAE,QAAQ;AAAA,IACb,EAAE,SAAS;AAAA,EACb,CAAC,EAAE,SAAS;AACd,CAAC,EAAE,SAAS;AAEZ,IAAM,mBAAe,gCAAU;AAAA,EAC7B,oBAAgB,iCAAW,EAAE,SAAS;AACxC,CAAC,EAAE,SAAS;AAEL,IAAM,qBAAqB,oBAAoB,WAAO,gCAAU;AAAA,EACrE,MAAM;AAAA,EAEN,WAAO,gCAAU;AAAA,IACf,gCAA4B,iCAAW,EAAE,SAAS;AAAA,IAClD,6BAAyB,iCAAW,EAAE,SAAS;AAAA,EACjD,CAAC,EAAE,SAAS;AAAA,EAEZ,WAAO,gCAAU;AAAA,IACf,6BAAyB,iCAAW,EAAE,SAAS;AAAA,EACjD,CAAC,EAAE,SAAS;AAAA,EAEZ,SAAS;AAAA,EAET,SAAS;AAAA,EAET,MAAM;AAAA,EAEN,YAAQ,gCAAU,CAAC,CAAC;AACtB,CAAC,CAAC;AAGK,IAAM,0BAA0B,mBAAmB,WAAO,gCAAU;AAAA,EACzE,MAAM,mBAAmB,UAAU,MAAM,EAAE,WAAO,gCAAU;AAAA,IAC1D,OAAO,mBAAmB,UAAU,MAAM,EAAE,UAAU,OAAO,EAAE,WAAO,gCAAU;AAAA,MAC9E,eAAW;AAAA,YACT,gCAAU,EAAE,SAAS,EAAE,QAAQ,eAAe;AAAA,YAC9C,gCAAU;AAAA,UACR,UAAM,gCAAU,EAAE,MAAM,yBAAY,EAAE,SAAS;AAAA,UAC/C,cAAU,iCAAW,EAAE,SAAS;AAAA,UAChC,UAAuB,iCAAoB,SAAS;AAAA,UACpD,cAA2B,qCAAwB,SAAS;AAAA,UAC5D,kBAA+B,yCAA4B,SAAS;AAAA,UACpE,mBAAgC,0CAA6B,SAAS;AAAA,UACtE,iBAAa,iCAAW,EAAE,SAAS;AAAA,UACnC,4BAAwB,iCAAW,EAAE,SAAS;AAAA,QAChD,CAAC;AAAA,MACH,EAAE,SAAS;AAAA,IACb,CAAC,EAAE,SAAS,CAAC;AAAA,EACf,CAAC,CAAC;AAAA,EAEF,QAAQ,mBAAmB,UAAU,QAAQ,EAAE,WAAO,gCAAU;AAAA,IAC9D,YAAQ,gCAAU;AAAA,MAChB,cAAU,iCAAW,EAAE,SAAS;AAAA,MAChC,MAAmB,6BAAgB,SAAS,EAAE,SAAS;AAAA,MACvD,MAAmB,6BAAgB,SAAS;AAAA,MAC5C,UAAuB,iCAAoB,SAAS,EAAE,SAAS;AAAA,MAC/D,UAAuB,iCAAoB,SAAS,EAAE,SAAS;AAAA,MAC/D,YAAyB,mCAAsB,SAAS,EAAE,SAAS;AAAA,MACnE,aAA0B,oCAAuB,SAAS,EAAE,SAAS;AAAA,IACvE,CAAC;AAAA,EACH,CAAC,EAAE,SAAS,CAAC;AAAA,EAEb,SAAS,mBAAmB,UAAU,SAAS,EAAE,WAAO,gCAAU;AAAA,IAChE,oBAAgB;AAAA,UACd,gCAAU,EAAE,KAAK,EAAE,SAAS;AAAA,UAC5B,gCAAU;AAAA,QACR,SAAsB,uBAAU,SAAS;AAAA,QACzC,aAA0B,+BAAkB,SAAS;AAAA,MACvD,CAAC;AAAA,IACH,EAAE,SAAS;AAAA,EACb,CAAC,CAAC;AACJ,CAAC,CAAC;AAEK,IAAM,2BAA2B,wBAAwB,WAAO,gCAAU,CAAC,CAAC,CAAC;AAM7E,IAAM,wBAAwB,CAAC;AAE/B,IAAM,uBAAuB,CAAC;AAE9B,IAAM,4BAA4B,CAAC;AAEnC,IAAM,6BAA6B;AAAA,EACxC,MAAM;AAAA,IACJ,aAAa,CAAC,SAAiB,CAAC;AAAA,IAChC,oBAAoB;AAAA,MAClB,aAAa,CAAC;AAAA,MACd,YAAY,CAAC;AAAA,MACb,QAAQ,CAAC;AAAA,IACX;AAAA,EACF;AAAA,EAEA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,OAAO;AAAA,IACL,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,EAC3B;AAAA,EAEA,OAAO;AAAA,IACL,yBAAyB;AAAA,EAC3B;AAAA,EAEA,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,gBAAgB,CAAC,SAAiB;AAAA,MAChC,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EAEA,MAAM;AAAA,IACJ,aAAa;AAAA,IACb,UAAU;AAAA,MACR,aAAa;AAAA,IACf;AAAA,IACA,KAAK;AAAA,MACH,aAAa;AAAA,IACf;AAAA,IACA,SAAS;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,WAAW,CAAC,SAAiB;AAAA,QAC3B,UAAU;AAAA,QACV,aAAa;AAAA,QACb,wBAAwB;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAAA,EAEA,QAAQ;AAAA,IACN,QAAQ;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,EACF;AACF;AAKO,SAAS,cAA+E,UAAa,QAAgC;AAC1I,QAAM,MAAW,OAAO,aAAa,aAAa,CAAC,QAAI,0BAAU,UAAU,OAAK,OAAO,MAAM,aAAa,CAAC,IAAK,OAAO,MAAM,WAAW,cAAc,GAAU,CAAC,CAAC,IAAI,CAAE;AACxK,aAAW,CAAC,KAAK,UAAU,KAAK,OAAO,QAAQ,MAAM,GAAG;AACtD,UAAM,YAAY,OAAO,aAAa,aAAa,SAAS,GAAG,QAAK,oBAAI,UAAU,GAAU,QAAI,oBAAI,UAAU,GAAU,IAAI;AAC5H,QAAI,cAAc,QAAW;AAC3B,cAAI,6BAAa,SAAS,SAAK,6BAAa,UAAU,GAAG;AACvD,gCAAI,KAAK,KAAK,cAAc,WAAW,UAAU,CAAC;AAClD;AAAA,MACF;AAAA,IACF;AACA,4BAAI,KAAK,KAAK,UAAU;AAAA,EAC1B;AACA,SAAO;AACT;", "names": []}