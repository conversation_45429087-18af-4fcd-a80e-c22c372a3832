{"version": 3, "sources": ["../../src/generated/global-css.ts"], "sourcesContent": ["export const globalCSS = \".stack-scope *, .stack-scope ::before, .stack-scope ::after, *.stack-scope, .stack-scope::before, .stack-scope::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n.stack-scope ::backdrop, .stack-scope::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n/*\\n! tailwindcss v3.4.14 | MIT License | https://tailwindcss.com\\n*/\\n/*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n.stack-scope *,\\n.stack-scope ::before,\\n.stack-scope ::after,\\n*.stack-scope,\\n.stack-scope::before,\\n.stack-scope::after {\\n  box-sizing: border-box;\\n  /* 1 */\\n  border-width: 0;\\n  /* 2 */\\n  border-style: solid;\\n  /* 2 */\\n  border-color: #e5e7eb;\\n  /* 2 */\\n}\\n.stack-scope ::before,\\n.stack-scope ::after,\\n.stack-scope::before,\\n.stack-scope::after {\\n  --tw-content: '';\\n}\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n.stack-scope html,\\n.stack-scope :host,\\nhtml.stack-scope,\\n.stack-scope:host {\\n  line-height: 1.5;\\n  /* 1 */\\n  -webkit-text-size-adjust: 100%;\\n  /* 2 */\\n  -moz-tab-size: 4;\\n  /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4;\\n  /* 3 */\\n  font-family: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\";\\n  /* 4 */\\n  font-feature-settings: normal;\\n  /* 5 */\\n  font-variation-settings: normal;\\n  /* 6 */\\n  -webkit-tap-highlight-color: transparent;\\n  /* 7 */\\n}\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n.stack-scope body, body.stack-scope {\\n  margin: 0;\\n  /* 1 */\\n  line-height: inherit;\\n  /* 2 */\\n}\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n.stack-scope hr, hr.stack-scope {\\n  height: 0;\\n  /* 1 */\\n  color: inherit;\\n  /* 2 */\\n  border-top-width: 1px;\\n  /* 3 */\\n}\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n.stack-scope abbr:where([title]), abbr.stack-scope:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n.stack-scope h1,\\n.stack-scope h2,\\n.stack-scope h3,\\n.stack-scope h4,\\n.stack-scope h5,\\n.stack-scope h6,\\nh1.stack-scope,\\nh2.stack-scope,\\nh3.stack-scope,\\nh4.stack-scope,\\nh5.stack-scope,\\nh6.stack-scope {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n.stack-scope a, a.stack-scope {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n.stack-scope b,\\n.stack-scope strong,\\nb.stack-scope,\\nstrong.stack-scope {\\n  font-weight: bolder;\\n}\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n.stack-scope code,\\n.stack-scope kbd,\\n.stack-scope samp,\\n.stack-scope pre,\\ncode.stack-scope,\\nkbd.stack-scope,\\nsamp.stack-scope,\\npre.stack-scope {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace;\\n  /* 1 */\\n  font-feature-settings: normal;\\n  /* 2 */\\n  font-variation-settings: normal;\\n  /* 3 */\\n  font-size: 1em;\\n  /* 4 */\\n}\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n.stack-scope small, small.stack-scope {\\n  font-size: 80%;\\n}\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n.stack-scope sub,\\n.stack-scope sup,\\nsub.stack-scope,\\nsup.stack-scope {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n.stack-scope sub, sub.stack-scope {\\n  bottom: -0.25em;\\n}\\n.stack-scope sup, sup.stack-scope {\\n  top: -0.5em;\\n}\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n.stack-scope table, table.stack-scope {\\n  text-indent: 0;\\n  /* 1 */\\n  border-color: inherit;\\n  /* 2 */\\n  border-collapse: collapse;\\n  /* 3 */\\n}\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n.stack-scope button,\\n.stack-scope input,\\n.stack-scope optgroup,\\n.stack-scope select,\\n.stack-scope textarea,\\nbutton.stack-scope,\\ninput.stack-scope,\\noptgroup.stack-scope,\\nselect.stack-scope,\\ntextarea.stack-scope {\\n  font-family: inherit;\\n  /* 1 */\\n  font-feature-settings: inherit;\\n  /* 1 */\\n  font-variation-settings: inherit;\\n  /* 1 */\\n  font-size: 100%;\\n  /* 1 */\\n  font-weight: inherit;\\n  /* 1 */\\n  line-height: inherit;\\n  /* 1 */\\n  letter-spacing: inherit;\\n  /* 1 */\\n  color: inherit;\\n  /* 1 */\\n  margin: 0;\\n  /* 2 */\\n  padding: 0;\\n  /* 3 */\\n}\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n.stack-scope button,\\n.stack-scope select,\\nbutton.stack-scope,\\nselect.stack-scope {\\n  text-transform: none;\\n}\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n.stack-scope button,\\n.stack-scope input:where([type='button']),\\n.stack-scope input:where([type='reset']),\\n.stack-scope input:where([type='submit']),\\nbutton.stack-scope,\\ninput.stack-scope:where([type='button']),\\ninput.stack-scope:where([type='reset']),\\ninput.stack-scope:where([type='submit']) {\\n  -webkit-appearance: button;\\n  /* 1 */\\n  background-color: transparent;\\n  /* 2 */\\n  background-image: none;\\n  /* 2 */\\n}\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n.stack-scope :-moz-focusring, .stack-scope:-moz-focusring {\\n  outline: auto;\\n}\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n.stack-scope :-moz-ui-invalid, .stack-scope:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n.stack-scope progress, progress.stack-scope {\\n  vertical-align: baseline;\\n}\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n.stack-scope ::-webkit-inner-spin-button,\\n.stack-scope ::-webkit-outer-spin-button,\\n.stack-scope::-webkit-inner-spin-button,\\n.stack-scope::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n.stack-scope [type='search'], [type='search'] .stack-scope {\\n  -webkit-appearance: textfield;\\n  /* 1 */\\n  outline-offset: -2px;\\n  /* 2 */\\n}\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n.stack-scope ::-webkit-search-decoration, .stack-scope::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n.stack-scope ::-webkit-file-upload-button, .stack-scope::-webkit-file-upload-button {\\n  -webkit-appearance: button;\\n  /* 1 */\\n  font: inherit;\\n  /* 2 */\\n}\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n.stack-scope summary, summary.stack-scope {\\n  display: list-item;\\n}\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n.stack-scope blockquote,\\n.stack-scope dl,\\n.stack-scope dd,\\n.stack-scope h1,\\n.stack-scope h2,\\n.stack-scope h3,\\n.stack-scope h4,\\n.stack-scope h5,\\n.stack-scope h6,\\n.stack-scope hr,\\n.stack-scope figure,\\n.stack-scope p,\\n.stack-scope pre,\\nblockquote.stack-scope,\\ndl.stack-scope,\\ndd.stack-scope,\\nh1.stack-scope,\\nh2.stack-scope,\\nh3.stack-scope,\\nh4.stack-scope,\\nh5.stack-scope,\\nh6.stack-scope,\\nhr.stack-scope,\\nfigure.stack-scope,\\np.stack-scope,\\npre.stack-scope {\\n  margin: 0;\\n}\\n.stack-scope fieldset, fieldset.stack-scope {\\n  margin: 0;\\n  padding: 0;\\n}\\n.stack-scope legend, legend.stack-scope {\\n  padding: 0;\\n}\\n.stack-scope ol,\\n.stack-scope ul,\\n.stack-scope menu,\\nol.stack-scope,\\nul.stack-scope,\\nmenu.stack-scope {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n/*\\nReset default styling for dialogs.\\n*/\\n.stack-scope dialog, dialog.stack-scope {\\n  padding: 0;\\n}\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n.stack-scope textarea, textarea.stack-scope {\\n  resize: vertical;\\n}\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n.stack-scope input::-moz-placeholder, .stack-scope textarea::-moz-placeholder, input.stack-scope::-moz-placeholder, textarea.stack-scope::-moz-placeholder {\\n  opacity: 1;\\n  /* 1 */\\n  color: #9ca3af;\\n  /* 2 */\\n}\\n.stack-scope input::placeholder,\\n.stack-scope textarea::placeholder,\\ninput.stack-scope::placeholder,\\ntextarea.stack-scope::placeholder {\\n  opacity: 1;\\n  /* 1 */\\n  color: #9ca3af;\\n  /* 2 */\\n}\\n/*\\nSet the default cursor for buttons.\\n*/\\n.stack-scope button,\\n.stack-scope [role=\\\"button\\\"],\\nbutton.stack-scope,\\n[role=\\\"button\\\"] .stack-scope {\\n  cursor: pointer;\\n}\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n.stack-scope :disabled, .stack-scope:disabled {\\n  cursor: default;\\n}\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n.stack-scope img,\\n.stack-scope svg,\\n.stack-scope video,\\n.stack-scope canvas,\\n.stack-scope audio,\\n.stack-scope iframe,\\n.stack-scope embed,\\n.stack-scope object,\\nimg.stack-scope,\\nsvg.stack-scope,\\nvideo.stack-scope,\\ncanvas.stack-scope,\\naudio.stack-scope,\\niframe.stack-scope,\\nembed.stack-scope,\\nobject.stack-scope {\\n  display: block;\\n  /* 1 */\\n  vertical-align: middle;\\n  /* 2 */\\n}\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n.stack-scope img,\\n.stack-scope video,\\nimg.stack-scope,\\nvideo.stack-scope {\\n  max-width: 100%;\\n  height: auto;\\n}\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n.stack-scope [hidden]:where(:not([hidden=\\\"until-found\\\"])), [hidden] .stack-scope:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n.stack-scope .sr-only, .stack-scope.sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n.stack-scope .pointer-events-none, .stack-scope.pointer-events-none {\\n  pointer-events: none;\\n}\\n.stack-scope .pointer-events-auto, .stack-scope.pointer-events-auto {\\n  pointer-events: auto;\\n}\\n.stack-scope .visible, .stack-scope.visible {\\n  visibility: visible;\\n}\\n.stack-scope .invisible, .stack-scope.invisible {\\n  visibility: hidden;\\n}\\n.stack-scope .collapse, .stack-scope.collapse {\\n  visibility: collapse;\\n}\\n.stack-scope .static, .stack-scope.static {\\n  position: static;\\n}\\n.stack-scope .fixed, .stack-scope.fixed {\\n  position: fixed;\\n}\\n.stack-scope .absolute, .stack-scope.absolute {\\n  position: absolute;\\n}\\n.stack-scope .relative, .stack-scope.relative {\\n  position: relative;\\n}\\n.stack-scope .inset-0, .stack-scope.inset-0 {\\n  inset: 0px;\\n}\\n.stack-scope .inset-x-0, .stack-scope.inset-x-0 {\\n  left: 0px;\\n  right: 0px;\\n}\\n.stack-scope .inset-y-0, .stack-scope.inset-y-0 {\\n  top: 0px;\\n  bottom: 0px;\\n}\\n.stack-scope .-right-2, .stack-scope.-right-2 {\\n  right: -0.5rem;\\n}\\n.stack-scope .-top-2, .stack-scope.-top-2 {\\n  top: -0.5rem;\\n}\\n.stack-scope .bottom-0, .stack-scope.bottom-0 {\\n  bottom: 0px;\\n}\\n.stack-scope .left-0, .stack-scope.left-0 {\\n  left: 0px;\\n}\\n.stack-scope .left-1, .stack-scope.left-1 {\\n  left: 0.25rem;\\n}\\n.stack-scope .left-2, .stack-scope.left-2 {\\n  left: 0.5rem;\\n}\\n.stack-scope .left-4, .stack-scope.left-4 {\\n  left: 1rem;\\n}\\n.stack-scope .left-\\\\[50\\\\%\\\\], .stack-scope.left-\\\\[50\\\\%\\\\] {\\n  left: 50%;\\n}\\n.stack-scope .right-0, .stack-scope.right-0 {\\n  right: 0px;\\n}\\n.stack-scope .right-1, .stack-scope.right-1 {\\n  right: 0.25rem;\\n}\\n.stack-scope .right-2, .stack-scope.right-2 {\\n  right: 0.5rem;\\n}\\n.stack-scope .right-4, .stack-scope.right-4 {\\n  right: 1rem;\\n}\\n.stack-scope .top-0, .stack-scope.top-0 {\\n  top: 0px;\\n}\\n.stack-scope .top-1, .stack-scope.top-1 {\\n  top: 0.25rem;\\n}\\n.stack-scope .top-2, .stack-scope.top-2 {\\n  top: 0.5rem;\\n}\\n.stack-scope .top-4, .stack-scope.top-4 {\\n  top: 1rem;\\n}\\n.stack-scope .top-\\\\[1px\\\\], .stack-scope.top-\\\\[1px\\\\] {\\n  top: 1px;\\n}\\n.stack-scope .top-\\\\[50\\\\%\\\\], .stack-scope.top-\\\\[50\\\\%\\\\] {\\n  top: 50%;\\n}\\n.stack-scope .top-\\\\[60\\\\%\\\\], .stack-scope.top-\\\\[60\\\\%\\\\] {\\n  top: 60%;\\n}\\n.stack-scope .top-auto, .stack-scope.top-auto {\\n  top: auto;\\n}\\n.stack-scope .top-full, .stack-scope.top-full {\\n  top: 100%;\\n}\\n.stack-scope .z-10, .stack-scope.z-10 {\\n  z-index: 10;\\n}\\n.stack-scope .z-50, .stack-scope.z-50 {\\n  z-index: 50;\\n}\\n.stack-scope .z-\\\\[100\\\\], .stack-scope.z-\\\\[100\\\\] {\\n  z-index: 100;\\n}\\n.stack-scope .z-\\\\[1\\\\], .stack-scope.z-\\\\[1\\\\] {\\n  z-index: 1;\\n}\\n.stack-scope .-mx-1, .stack-scope.-mx-1 {\\n  margin-left: -0.25rem;\\n  margin-right: -0.25rem;\\n}\\n.stack-scope .-mx-6, .stack-scope.-mx-6 {\\n  margin-left: -1.5rem;\\n  margin-right: -1.5rem;\\n}\\n.stack-scope .mx-2, .stack-scope.mx-2 {\\n  margin-left: 0.5rem;\\n  margin-right: 0.5rem;\\n}\\n.stack-scope .my-1, .stack-scope.my-1 {\\n  margin-top: 0.25rem;\\n  margin-bottom: 0.25rem;\\n}\\n.stack-scope .my-2, .stack-scope.my-2 {\\n  margin-top: 0.5rem;\\n  margin-bottom: 0.5rem;\\n}\\n.stack-scope .my-5, .stack-scope.my-5 {\\n  margin-top: 1.25rem;\\n  margin-bottom: 1.25rem;\\n}\\n.stack-scope .my-6, .stack-scope.my-6 {\\n  margin-top: 1.5rem;\\n  margin-bottom: 1.5rem;\\n}\\n.stack-scope .-ml-3, .stack-scope.-ml-3 {\\n  margin-left: -0.75rem;\\n}\\n.stack-scope .mb-1, .stack-scope.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\n.stack-scope .mb-2, .stack-scope.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\n.stack-scope .mb-4, .stack-scope.mb-4 {\\n  margin-bottom: 1rem;\\n}\\n.stack-scope .mb-6, .stack-scope.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\n.stack-scope .ml-1, .stack-scope.ml-1 {\\n  margin-left: 0.25rem;\\n}\\n.stack-scope .ml-2, .stack-scope.ml-2 {\\n  margin-left: 0.5rem;\\n}\\n.stack-scope .ml-auto, .stack-scope.ml-auto {\\n  margin-left: auto;\\n}\\n.stack-scope .mr-1, .stack-scope.mr-1 {\\n  margin-right: 0.25rem;\\n}\\n.stack-scope .mr-1\\\\.5, .stack-scope.mr-1\\\\.5 {\\n  margin-right: 0.375rem;\\n}\\n.stack-scope .mr-2, .stack-scope.mr-2 {\\n  margin-right: 0.5rem;\\n}\\n.stack-scope .mr-4, .stack-scope.mr-4 {\\n  margin-right: 1rem;\\n}\\n.stack-scope .mt-1, .stack-scope.mt-1 {\\n  margin-top: 0.25rem;\\n}\\n.stack-scope .mt-1\\\\.5, .stack-scope.mt-1\\\\.5 {\\n  margin-top: 0.375rem;\\n}\\n.stack-scope .mt-2, .stack-scope.mt-2 {\\n  margin-top: 0.5rem;\\n}\\n.stack-scope .mt-4, .stack-scope.mt-4 {\\n  margin-top: 1rem;\\n}\\n.stack-scope .mt-6, .stack-scope.mt-6 {\\n  margin-top: 1.5rem;\\n}\\n.stack-scope .mt-8, .stack-scope.mt-8 {\\n  margin-top: 2rem;\\n}\\n.stack-scope .box-border, .stack-scope.box-border {\\n  box-sizing: border-box;\\n}\\n.stack-scope .line-clamp-1, .stack-scope.line-clamp-1 {\\n  overflow: hidden;\\n  display: -webkit-box;\\n  -webkit-box-orient: vertical;\\n  -webkit-line-clamp: 1;\\n}\\n.stack-scope .block, .stack-scope.block {\\n  display: block;\\n}\\n.stack-scope .inline, .stack-scope.inline {\\n  display: inline;\\n}\\n.stack-scope .flex, .stack-scope.flex {\\n  display: flex;\\n}\\n.stack-scope .inline-flex, .stack-scope.inline-flex {\\n  display: inline-flex;\\n}\\n.stack-scope .\\\\!table, .stack-scope.\\\\!table {\\n  display: table !important;\\n}\\n.stack-scope .table, .stack-scope.table {\\n  display: table;\\n}\\n.stack-scope .grid, .stack-scope.grid {\\n  display: grid;\\n}\\n.stack-scope .contents, .stack-scope.contents {\\n  display: contents;\\n}\\n.stack-scope .hidden, .stack-scope.hidden {\\n  display: none;\\n}\\n.stack-scope .aspect-square, .stack-scope.aspect-square {\\n  aspect-ratio: 1 / 1;\\n}\\n.stack-scope .size-3, .stack-scope.size-3 {\\n  width: 0.75rem;\\n  height: 0.75rem;\\n}\\n.stack-scope .h-0, .stack-scope.h-0 {\\n  height: 0px;\\n}\\n.stack-scope .h-0\\\\.5, .stack-scope.h-0\\\\.5 {\\n  height: 0.125rem;\\n}\\n.stack-scope .h-1, .stack-scope.h-1 {\\n  height: 0.25rem;\\n}\\n.stack-scope .h-1\\\\.5, .stack-scope.h-1\\\\.5 {\\n  height: 0.375rem;\\n}\\n.stack-scope .h-10, .stack-scope.h-10 {\\n  height: 2.5rem;\\n}\\n.stack-scope .h-12, .stack-scope.h-12 {\\n  height: 3rem;\\n}\\n.stack-scope .h-2, .stack-scope.h-2 {\\n  height: 0.5rem;\\n}\\n.stack-scope .h-2\\\\.5, .stack-scope.h-2\\\\.5 {\\n  height: 0.625rem;\\n}\\n.stack-scope .h-24, .stack-scope.h-24 {\\n  height: 6rem;\\n}\\n.stack-scope .h-3, .stack-scope.h-3 {\\n  height: 0.75rem;\\n}\\n.stack-scope .h-3\\\\.5, .stack-scope.h-3\\\\.5 {\\n  height: 0.875rem;\\n}\\n.stack-scope .h-4, .stack-scope.h-4 {\\n  height: 1rem;\\n}\\n.stack-scope .h-5, .stack-scope.h-5 {\\n  height: 1.25rem;\\n}\\n.stack-scope .h-6, .stack-scope.h-6 {\\n  height: 1.5rem;\\n}\\n.stack-scope .h-7, .stack-scope.h-7 {\\n  height: 1.75rem;\\n}\\n.stack-scope .h-8, .stack-scope.h-8 {\\n  height: 2rem;\\n}\\n.stack-scope .h-9, .stack-scope.h-9 {\\n  height: 2.25rem;\\n}\\n.stack-scope .h-\\\\[1px\\\\], .stack-scope.h-\\\\[1px\\\\] {\\n  height: 1px;\\n}\\n.stack-scope .h-\\\\[200px\\\\], .stack-scope.h-\\\\[200px\\\\] {\\n  height: 200px;\\n}\\n.stack-scope .h-\\\\[300px\\\\], .stack-scope.h-\\\\[300px\\\\] {\\n  height: 300px;\\n}\\n.stack-scope .h-\\\\[34px\\\\], .stack-scope.h-\\\\[34px\\\\] {\\n  height: 34px;\\n}\\n.stack-scope .h-\\\\[60px\\\\], .stack-scope.h-\\\\[60px\\\\] {\\n  height: 60px;\\n}\\n.stack-scope .h-\\\\[var\\\\(--radix-navigation-menu-viewport-height\\\\)\\\\], .stack-scope.h-\\\\[var\\\\(--radix-navigation-menu-viewport-height\\\\)\\\\] {\\n  height: var(--radix-navigation-menu-viewport-height);\\n}\\n.stack-scope .h-\\\\[var\\\\(--radix-select-trigger-height\\\\)\\\\], .stack-scope.h-\\\\[var\\\\(--radix-select-trigger-height\\\\)\\\\] {\\n  height: var(--radix-select-trigger-height);\\n}\\n.stack-scope .h-full, .stack-scope.h-full {\\n  height: 100%;\\n}\\n.stack-scope .h-px, .stack-scope.h-px {\\n  height: 1px;\\n}\\n.stack-scope .max-h-6, .stack-scope.max-h-6 {\\n  max-height: 1.5rem;\\n}\\n.stack-scope .max-h-96, .stack-scope.max-h-96 {\\n  max-height: 24rem;\\n}\\n.stack-scope .max-h-\\\\[300px\\\\], .stack-scope.max-h-\\\\[300px\\\\] {\\n  max-height: 300px;\\n}\\n.stack-scope .max-h-screen, .stack-scope.max-h-screen {\\n  max-height: 100vh;\\n}\\n.stack-scope .min-h-6, .stack-scope.min-h-6 {\\n  min-height: 1.5rem;\\n}\\n.stack-scope .min-h-\\\\[60px\\\\], .stack-scope.min-h-\\\\[60px\\\\] {\\n  min-height: 60px;\\n}\\n.stack-scope .w-0, .stack-scope.w-0 {\\n  width: 0px;\\n}\\n.stack-scope .w-10, .stack-scope.w-10 {\\n  width: 2.5rem;\\n}\\n.stack-scope .w-16, .stack-scope.w-16 {\\n  width: 4rem;\\n}\\n.stack-scope .w-2, .stack-scope.w-2 {\\n  width: 0.5rem;\\n}\\n.stack-scope .w-2\\\\.5, .stack-scope.w-2\\\\.5 {\\n  width: 0.625rem;\\n}\\n.stack-scope .w-2\\\\/3, .stack-scope.w-2\\\\/3 {\\n  width: 66.666667%;\\n}\\n.stack-scope .w-24, .stack-scope.w-24 {\\n  width: 6rem;\\n}\\n.stack-scope .w-3, .stack-scope.w-3 {\\n  width: 0.75rem;\\n}\\n.stack-scope .w-3\\\\.5, .stack-scope.w-3\\\\.5 {\\n  width: 0.875rem;\\n}\\n.stack-scope .w-3\\\\/4, .stack-scope.w-3\\\\/4 {\\n  width: 75%;\\n}\\n.stack-scope .w-4, .stack-scope.w-4 {\\n  width: 1rem;\\n}\\n.stack-scope .w-48, .stack-scope.w-48 {\\n  width: 12rem;\\n}\\n.stack-scope .w-5, .stack-scope.w-5 {\\n  width: 1.25rem;\\n}\\n.stack-scope .w-6, .stack-scope.w-6 {\\n  width: 1.5rem;\\n}\\n.stack-scope .w-64, .stack-scope.w-64 {\\n  width: 16rem;\\n}\\n.stack-scope .w-7, .stack-scope.w-7 {\\n  width: 1.75rem;\\n}\\n.stack-scope .w-72, .stack-scope.w-72 {\\n  width: 18rem;\\n}\\n.stack-scope .w-8, .stack-scope.w-8 {\\n  width: 2rem;\\n}\\n.stack-scope .w-9, .stack-scope.w-9 {\\n  width: 2.25rem;\\n}\\n.stack-scope .w-\\\\[100px\\\\], .stack-scope.w-\\\\[100px\\\\] {\\n  width: 100px;\\n}\\n.stack-scope .w-\\\\[150px\\\\], .stack-scope.w-\\\\[150px\\\\] {\\n  width: 150px;\\n}\\n.stack-scope .w-\\\\[1px\\\\], .stack-scope.w-\\\\[1px\\\\] {\\n  width: 1px;\\n}\\n.stack-scope .w-\\\\[200px\\\\], .stack-scope.w-\\\\[200px\\\\] {\\n  width: 200px;\\n}\\n.stack-scope .w-\\\\[250px\\\\], .stack-scope.w-\\\\[250px\\\\] {\\n  width: 250px;\\n}\\n.stack-scope .w-\\\\[34px\\\\], .stack-scope.w-\\\\[34px\\\\] {\\n  width: 34px;\\n}\\n.stack-scope .w-\\\\[36px\\\\], .stack-scope.w-\\\\[36px\\\\] {\\n  width: 36px;\\n}\\n.stack-scope .w-\\\\[60px\\\\], .stack-scope.w-\\\\[60px\\\\] {\\n  width: 60px;\\n}\\n.stack-scope .w-\\\\[70px\\\\], .stack-scope.w-\\\\[70px\\\\] {\\n  width: 70px;\\n}\\n.stack-scope .w-\\\\[800px\\\\], .stack-scope.w-\\\\[800px\\\\] {\\n  width: 800px;\\n}\\n.stack-scope .w-\\\\[80px\\\\], .stack-scope.w-\\\\[80px\\\\] {\\n  width: 80px;\\n}\\n.stack-scope .w-\\\\[calc\\\\(100\\\\%\\\\+3rem\\\\)\\\\], .stack-scope.w-\\\\[calc\\\\(100\\\\%\\\\+3rem\\\\)\\\\] {\\n  width: calc(100% + 3rem);\\n}\\n.stack-scope .w-auto, .stack-scope.w-auto {\\n  width: auto;\\n}\\n.stack-scope .w-fit, .stack-scope.w-fit {\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n.stack-scope .w-full, .stack-scope.w-full {\\n  width: 100%;\\n}\\n.stack-scope .w-max, .stack-scope.w-max {\\n  width: -moz-max-content;\\n  width: max-content;\\n}\\n.stack-scope .w-px, .stack-scope.w-px {\\n  width: 1px;\\n}\\n.stack-scope .min-w-6, .stack-scope.min-w-6 {\\n  min-width: 1.5rem;\\n}\\n.stack-scope .min-w-\\\\[12rem\\\\], .stack-scope.min-w-\\\\[12rem\\\\] {\\n  min-width: 12rem;\\n}\\n.stack-scope .min-w-\\\\[150px\\\\], .stack-scope.min-w-\\\\[150px\\\\] {\\n  min-width: 150px;\\n}\\n.stack-scope .min-w-\\\\[200px\\\\], .stack-scope.min-w-\\\\[200px\\\\] {\\n  min-width: 200px;\\n}\\n.stack-scope .min-w-\\\\[8rem\\\\], .stack-scope.min-w-\\\\[8rem\\\\] {\\n  min-width: 8rem;\\n}\\n.stack-scope .min-w-\\\\[var\\\\(--radix-select-trigger-width\\\\)\\\\], .stack-scope.min-w-\\\\[var\\\\(--radix-select-trigger-width\\\\)\\\\] {\\n  min-width: var(--radix-select-trigger-width);\\n}\\n.stack-scope .max-w-40, .stack-scope.max-w-40 {\\n  max-width: 10rem;\\n}\\n.stack-scope .max-w-6, .stack-scope.max-w-6 {\\n  max-width: 1.5rem;\\n}\\n.stack-scope .max-w-60, .stack-scope.max-w-60 {\\n  max-width: 15rem;\\n}\\n.stack-scope .max-w-64, .stack-scope.max-w-64 {\\n  max-width: 16rem;\\n}\\n.stack-scope .max-w-\\\\[200px\\\\], .stack-scope.max-w-\\\\[200px\\\\] {\\n  max-width: 200px;\\n}\\n.stack-scope .max-w-\\\\[300px\\\\], .stack-scope.max-w-\\\\[300px\\\\] {\\n  max-width: 300px;\\n}\\n.stack-scope .max-w-\\\\[320px\\\\], .stack-scope.max-w-\\\\[320px\\\\] {\\n  max-width: 320px;\\n}\\n.stack-scope .max-w-\\\\[36px\\\\], .stack-scope.max-w-\\\\[36px\\\\] {\\n  max-width: 36px;\\n}\\n.stack-scope .max-w-\\\\[380px\\\\], .stack-scope.max-w-\\\\[380px\\\\] {\\n  max-width: 380px;\\n}\\n.stack-scope .max-w-\\\\[800px\\\\], .stack-scope.max-w-\\\\[800px\\\\] {\\n  max-width: 800px;\\n}\\n.stack-scope .max-w-full, .stack-scope.max-w-full {\\n  max-width: 100%;\\n}\\n.stack-scope .max-w-lg, .stack-scope.max-w-lg {\\n  max-width: 32rem;\\n}\\n.stack-scope .max-w-max, .stack-scope.max-w-max {\\n  max-width: -moz-max-content;\\n  max-width: max-content;\\n}\\n.stack-scope .max-w-sm, .stack-scope.max-w-sm {\\n  max-width: 24rem;\\n}\\n.stack-scope .flex-1, .stack-scope.flex-1 {\\n  flex: 1 1 0%;\\n}\\n.stack-scope .flex-shrink, .stack-scope.flex-shrink {\\n  flex-shrink: 1;\\n}\\n.stack-scope .flex-shrink-0, .stack-scope.flex-shrink-0 {\\n  flex-shrink: 0;\\n}\\n.stack-scope .shrink-0, .stack-scope.shrink-0 {\\n  flex-shrink: 0;\\n}\\n.stack-scope .flex-grow, .stack-scope.flex-grow {\\n  flex-grow: 1;\\n}\\n.stack-scope .grow, .stack-scope.grow {\\n  flex-grow: 1;\\n}\\n.stack-scope .caption-bottom, .stack-scope.caption-bottom {\\n  caption-side: bottom;\\n}\\n.stack-scope .border-collapse, .stack-scope.border-collapse {\\n  border-collapse: collapse;\\n}\\n.stack-scope .translate-x-\\\\[-50\\\\%\\\\], .stack-scope.translate-x-\\\\[-50\\\\%\\\\] {\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.stack-scope .translate-y-\\\\[-50\\\\%\\\\], .stack-scope.translate-y-\\\\[-50\\\\%\\\\] {\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.stack-scope .rotate-180, .stack-scope.rotate-180 {\\n  --tw-rotate: 180deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.stack-scope .rotate-45, .stack-scope.rotate-45 {\\n  --tw-rotate: 45deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.stack-scope .transform, .stack-scope.transform {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n@keyframes pulse {\\n  50% {\\n    opacity: .5;\\n  }\\n}\\n.stack-scope .animate-pulse, .stack-scope.animate-pulse {\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\n@keyframes spin {\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.stack-scope .animate-spin, .stack-scope.animate-spin {\\n  animation: spin 1s linear infinite;\\n}\\n.stack-scope .cursor-default, .stack-scope.cursor-default {\\n  cursor: default;\\n}\\n.stack-scope .cursor-not-allowed, .stack-scope.cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\n.stack-scope .cursor-pointer, .stack-scope.cursor-pointer {\\n  cursor: pointer;\\n}\\n.stack-scope .cursor-wait, .stack-scope.cursor-wait {\\n  cursor: wait;\\n}\\n.stack-scope .touch-none, .stack-scope.touch-none {\\n  touch-action: none;\\n}\\n.stack-scope .select-none, .stack-scope.select-none {\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n}\\n.stack-scope .resize, .stack-scope.resize {\\n  resize: both;\\n}\\n.stack-scope .list-none, .stack-scope.list-none {\\n  list-style-type: none;\\n}\\n.stack-scope .flex-row, .stack-scope.flex-row {\\n  flex-direction: row;\\n}\\n.stack-scope .flex-row-reverse, .stack-scope.flex-row-reverse {\\n  flex-direction: row-reverse;\\n}\\n.stack-scope .flex-col, .stack-scope.flex-col {\\n  flex-direction: column;\\n}\\n.stack-scope .flex-col-reverse, .stack-scope.flex-col-reverse {\\n  flex-direction: column-reverse;\\n}\\n.stack-scope .flex-wrap, .stack-scope.flex-wrap {\\n  flex-wrap: wrap;\\n}\\n.stack-scope .items-end, .stack-scope.items-end {\\n  align-items: flex-end;\\n}\\n.stack-scope .items-center, .stack-scope.items-center {\\n  align-items: center;\\n}\\n.stack-scope .items-stretch, .stack-scope.items-stretch {\\n  align-items: stretch;\\n}\\n.stack-scope .justify-start, .stack-scope.justify-start {\\n  justify-content: flex-start;\\n}\\n.stack-scope .justify-end, .stack-scope.justify-end {\\n  justify-content: flex-end;\\n}\\n.stack-scope .justify-center, .stack-scope.justify-center {\\n  justify-content: center;\\n}\\n.stack-scope .justify-between, .stack-scope.justify-between {\\n  justify-content: space-between;\\n}\\n.stack-scope .justify-evenly, .stack-scope.justify-evenly {\\n  justify-content: space-evenly;\\n}\\n.stack-scope .gap-1, .stack-scope.gap-1 {\\n  gap: 0.25rem;\\n}\\n.stack-scope .gap-1\\\\.5, .stack-scope.gap-1\\\\.5 {\\n  gap: 0.375rem;\\n}\\n.stack-scope .gap-2, .stack-scope.gap-2 {\\n  gap: 0.5rem;\\n}\\n.stack-scope .gap-4, .stack-scope.gap-4 {\\n  gap: 1rem;\\n}\\n.stack-scope .gap-6, .stack-scope.gap-6 {\\n  gap: 1.5rem;\\n}\\n.stack-scope .gap-x-6, .stack-scope.gap-x-6 {\\n  -moz-column-gap: 1.5rem;\\n       column-gap: 1.5rem;\\n}\\n.stack-scope .gap-x-8, .stack-scope.gap-x-8 {\\n  -moz-column-gap: 2rem;\\n       column-gap: 2rem;\\n}\\n.stack-scope .gap-y-0, .stack-scope.gap-y-0 {\\n  row-gap: 0px;\\n}\\n.stack-scope .gap-y-4, .stack-scope.gap-y-4 {\\n  row-gap: 1rem;\\n}\\n.stack-scope .space-x-1 > :not([hidden]) ~ :not([hidden]), .stack-scope.space-x-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.stack-scope .space-x-2 > :not([hidden]) ~ :not([hidden]), .stack-scope.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.stack-scope .space-x-4 > :not([hidden]) ~ :not([hidden]), .stack-scope.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.stack-scope .space-y-0 > :not([hidden]) ~ :not([hidden]), .stack-scope.space-y-0 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0px * var(--tw-space-y-reverse));\\n}\\n.stack-scope .space-y-1 > :not([hidden]) ~ :not([hidden]), .stack-scope.space-y-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\n.stack-scope .space-y-1\\\\.5 > :not([hidden]) ~ :not([hidden]), .stack-scope.space-y-1\\\\.5 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\\n}\\n.stack-scope .space-y-2 > :not([hidden]) ~ :not([hidden]), .stack-scope.space-y-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\n.stack-scope .space-y-4 > :not([hidden]) ~ :not([hidden]), .stack-scope.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.stack-scope .self-center, .stack-scope.self-center {\\n  align-self: center;\\n}\\n.stack-scope .self-stretch, .stack-scope.self-stretch {\\n  align-self: stretch;\\n}\\n.stack-scope .overflow-auto, .stack-scope.overflow-auto {\\n  overflow: auto;\\n}\\n.stack-scope .overflow-hidden, .stack-scope.overflow-hidden {\\n  overflow: hidden;\\n}\\n.stack-scope .overflow-y-auto, .stack-scope.overflow-y-auto {\\n  overflow-y: auto;\\n}\\n.stack-scope .overflow-x-hidden, .stack-scope.overflow-x-hidden {\\n  overflow-x: hidden;\\n}\\n.stack-scope .truncate, .stack-scope.truncate {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.stack-scope .overflow-ellipsis, .stack-scope.overflow-ellipsis {\\n  text-overflow: ellipsis;\\n}\\n.stack-scope .text-ellipsis, .stack-scope.text-ellipsis {\\n  text-overflow: ellipsis;\\n}\\n.stack-scope .whitespace-nowrap, .stack-scope.whitespace-nowrap {\\n  white-space: nowrap;\\n}\\n.stack-scope .whitespace-pre-wrap, .stack-scope.whitespace-pre-wrap {\\n  white-space: pre-wrap;\\n}\\n.stack-scope .text-wrap, .stack-scope.text-wrap {\\n  text-wrap: wrap;\\n}\\n.stack-scope .text-nowrap, .stack-scope.text-nowrap {\\n  text-wrap: nowrap;\\n}\\n.stack-scope .break-words, .stack-scope.break-words {\\n  overflow-wrap: break-word;\\n}\\n.stack-scope .rounded, .stack-scope.rounded {\\n  border-radius: 0.25rem;\\n}\\n.stack-scope .rounded-\\\\[inherit\\\\], .stack-scope.rounded-\\\\[inherit\\\\] {\\n  border-radius: inherit;\\n}\\n.stack-scope .rounded-full, .stack-scope.rounded-full {\\n  border-radius: 9999px;\\n}\\n.stack-scope .rounded-lg, .stack-scope.rounded-lg {\\n  border-radius: var(--radius);\\n}\\n.stack-scope .rounded-md, .stack-scope.rounded-md {\\n  border-radius: calc(var(--radius) - 2px);\\n}\\n.stack-scope .rounded-sm, .stack-scope.rounded-sm {\\n  border-radius: calc(var(--radius) - 4px);\\n}\\n.stack-scope .rounded-xl, .stack-scope.rounded-xl {\\n  border-radius: 0.75rem;\\n}\\n.stack-scope .rounded-b-md, .stack-scope.rounded-b-md {\\n  border-bottom-right-radius: calc(var(--radius) - 2px);\\n  border-bottom-left-radius: calc(var(--radius) - 2px);\\n}\\n.stack-scope .rounded-l-md, .stack-scope.rounded-l-md {\\n  border-top-left-radius: calc(var(--radius) - 2px);\\n  border-bottom-left-radius: calc(var(--radius) - 2px);\\n}\\n.stack-scope .rounded-l-none, .stack-scope.rounded-l-none {\\n  border-top-left-radius: 0px;\\n  border-bottom-left-radius: 0px;\\n}\\n.stack-scope .rounded-tl-sm, .stack-scope.rounded-tl-sm {\\n  border-top-left-radius: calc(var(--radius) - 4px);\\n}\\n.stack-scope .border, .stack-scope.border {\\n  border-width: 1px;\\n}\\n.stack-scope .border-0, .stack-scope.border-0 {\\n  border-width: 0px;\\n}\\n.stack-scope .border-2, .stack-scope.border-2 {\\n  border-width: 2px;\\n}\\n.stack-scope .border-b, .stack-scope.border-b {\\n  border-bottom-width: 1px;\\n}\\n.stack-scope .border-l, .stack-scope.border-l {\\n  border-left-width: 1px;\\n}\\n.stack-scope .border-r, .stack-scope.border-r {\\n  border-right-width: 1px;\\n}\\n.stack-scope .border-t, .stack-scope.border-t {\\n  border-top-width: 1px;\\n}\\n.stack-scope .border-border, .stack-scope.border-border {\\n  border-color: hsl(var(--border));\\n}\\n.stack-scope .border-current, .stack-scope.border-current {\\n  border-color: currentColor;\\n}\\n.stack-scope .border-destructive, .stack-scope.border-destructive {\\n  border-color: hsl(var(--destructive));\\n}\\n.stack-scope .border-destructive\\\\/50, .stack-scope.border-destructive\\\\/50 {\\n  border-color: hsl(var(--destructive) / 0.5);\\n}\\n.stack-scope .border-input, .stack-scope.border-input {\\n  border-color: hsl(var(--input));\\n}\\n.stack-scope .border-muted, .stack-scope.border-muted {\\n  border-color: hsl(var(--muted));\\n}\\n.stack-scope .border-primary, .stack-scope.border-primary {\\n  border-color: hsl(var(--primary));\\n}\\n.stack-scope .border-primary\\\\/50, .stack-scope.border-primary\\\\/50 {\\n  border-color: hsl(var(--primary) / 0.5);\\n}\\n.stack-scope .border-success, .stack-scope.border-success {\\n  border-color: hsl(var(--success));\\n}\\n.stack-scope .border-success\\\\/50, .stack-scope.border-success\\\\/50 {\\n  border-color: hsl(var(--success) / 0.5);\\n}\\n.stack-scope .border-transparent, .stack-scope.border-transparent {\\n  border-color: transparent;\\n}\\n.stack-scope .border-l-transparent, .stack-scope.border-l-transparent {\\n  border-left-color: transparent;\\n}\\n.stack-scope .border-t-transparent, .stack-scope.border-t-transparent {\\n  border-top-color: transparent;\\n}\\n.stack-scope .bg-accent, .stack-scope.bg-accent {\\n  background-color: hsl(var(--accent));\\n}\\n.stack-scope .bg-background, .stack-scope.bg-background {\\n  background-color: hsl(var(--background));\\n}\\n.stack-scope .bg-black, .stack-scope.bg-black {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity));\\n}\\n.stack-scope .bg-black\\\\/80, .stack-scope.bg-black\\\\/80 {\\n  background-color: rgb(0 0 0 / 0.8);\\n}\\n.stack-scope .bg-blue-500, .stack-scope.bg-blue-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity));\\n}\\n.stack-scope .bg-border, .stack-scope.bg-border {\\n  background-color: hsl(var(--border));\\n}\\n.stack-scope .bg-card, .stack-scope.bg-card {\\n  background-color: hsl(var(--card));\\n}\\n.stack-scope .bg-destructive, .stack-scope.bg-destructive {\\n  background-color: hsl(var(--destructive));\\n}\\n.stack-scope .bg-destructive\\\\/5, .stack-scope.bg-destructive\\\\/5 {\\n  background-color: hsl(var(--destructive) / 0.05);\\n}\\n.stack-scope .bg-foreground, .stack-scope.bg-foreground {\\n  background-color: hsl(var(--foreground));\\n}\\n.stack-scope .bg-gray-200, .stack-scope.bg-gray-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity));\\n}\\n.stack-scope .bg-gray-400, .stack-scope.bg-gray-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity));\\n}\\n.stack-scope .bg-gray-500, .stack-scope.bg-gray-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity));\\n}\\n.stack-scope .bg-gray-500\\\\/20, .stack-scope.bg-gray-500\\\\/20 {\\n  background-color: rgb(107 114 128 / 0.2);\\n}\\n.stack-scope .bg-green-500, .stack-scope.bg-green-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity));\\n}\\n.stack-scope .bg-muted, .stack-scope.bg-muted {\\n  background-color: hsl(var(--muted));\\n}\\n.stack-scope .bg-muted\\\\/50, .stack-scope.bg-muted\\\\/50 {\\n  background-color: hsl(var(--muted) / 0.5);\\n}\\n.stack-scope .bg-muted\\\\/70, .stack-scope.bg-muted\\\\/70 {\\n  background-color: hsl(var(--muted) / 0.7);\\n}\\n.stack-scope .bg-popover, .stack-scope.bg-popover {\\n  background-color: hsl(var(--popover));\\n}\\n.stack-scope .bg-primary, .stack-scope.bg-primary {\\n  background-color: hsl(var(--primary));\\n}\\n.stack-scope .bg-primary\\\\/10, .stack-scope.bg-primary\\\\/10 {\\n  background-color: hsl(var(--primary) / 0.1);\\n}\\n.stack-scope .bg-primary\\\\/20, .stack-scope.bg-primary\\\\/20 {\\n  background-color: hsl(var(--primary) / 0.2);\\n}\\n.stack-scope .bg-red-500, .stack-scope.bg-red-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity));\\n}\\n.stack-scope .bg-secondary, .stack-scope.bg-secondary {\\n  background-color: hsl(var(--secondary));\\n}\\n.stack-scope .bg-success, .stack-scope.bg-success {\\n  background-color: hsl(var(--success));\\n}\\n.stack-scope .bg-success\\\\/5, .stack-scope.bg-success\\\\/5 {\\n  background-color: hsl(var(--success) / 0.05);\\n}\\n.stack-scope .bg-transparent, .stack-scope.bg-transparent {\\n  background-color: transparent;\\n}\\n.stack-scope .bg-white, .stack-scope.bg-white {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity));\\n}\\n.stack-scope .bg-yellow-500, .stack-scope.bg-yellow-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity));\\n}\\n.stack-scope .bg-zinc-200, .stack-scope.bg-zinc-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(228 228 231 / var(--tw-bg-opacity));\\n}\\n.stack-scope .fill-current, .stack-scope.fill-current {\\n  fill: currentColor;\\n}\\n.stack-scope .fill-primary, .stack-scope.fill-primary {\\n  fill: hsl(var(--primary));\\n}\\n.stack-scope .p-0, .stack-scope.p-0 {\\n  padding: 0px;\\n}\\n.stack-scope .p-1, .stack-scope.p-1 {\\n  padding: 0.25rem;\\n}\\n.stack-scope .p-2, .stack-scope.p-2 {\\n  padding: 0.5rem;\\n}\\n.stack-scope .p-3, .stack-scope.p-3 {\\n  padding: 0.75rem;\\n}\\n.stack-scope .p-4, .stack-scope.p-4 {\\n  padding: 1rem;\\n}\\n.stack-scope .p-6, .stack-scope.p-6 {\\n  padding: 1.5rem;\\n}\\n.stack-scope .p-\\\\[1px\\\\], .stack-scope.p-\\\\[1px\\\\] {\\n  padding: 1px;\\n}\\n.stack-scope .px-1, .stack-scope.px-1 {\\n  padding-left: 0.25rem;\\n  padding-right: 0.25rem;\\n}\\n.stack-scope .px-2, .stack-scope.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.stack-scope .px-2\\\\.5, .stack-scope.px-2\\\\.5 {\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n}\\n.stack-scope .px-3, .stack-scope.px-3 {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\n.stack-scope .px-4, .stack-scope.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.stack-scope .px-6, .stack-scope.px-6 {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\n.stack-scope .px-8, .stack-scope.px-8 {\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n}\\n.stack-scope .py-0, .stack-scope.py-0 {\\n  padding-top: 0px;\\n  padding-bottom: 0px;\\n}\\n.stack-scope .py-0\\\\.5, .stack-scope.py-0\\\\.5 {\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n}\\n.stack-scope .py-1, .stack-scope.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.stack-scope .py-1\\\\.5, .stack-scope.py-1\\\\.5 {\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\n.stack-scope .py-2, .stack-scope.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.stack-scope .py-3, .stack-scope.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.stack-scope .py-4, .stack-scope.py-4 {\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\n.stack-scope .py-6, .stack-scope.py-6 {\\n  padding-top: 1.5rem;\\n  padding-bottom: 1.5rem;\\n}\\n.stack-scope .pb-0, .stack-scope.pb-0 {\\n  padding-bottom: 0px;\\n}\\n.stack-scope .pb-2, .stack-scope.pb-2 {\\n  padding-bottom: 0.5rem;\\n}\\n.stack-scope .pb-4, .stack-scope.pb-4 {\\n  padding-bottom: 1rem;\\n}\\n.stack-scope .pl-2, .stack-scope.pl-2 {\\n  padding-left: 0.5rem;\\n}\\n.stack-scope .pl-3, .stack-scope.pl-3 {\\n  padding-left: 0.75rem;\\n}\\n.stack-scope .pl-7, .stack-scope.pl-7 {\\n  padding-left: 1.75rem;\\n}\\n.stack-scope .pl-8, .stack-scope.pl-8 {\\n  padding-left: 2rem;\\n}\\n.stack-scope .pr-10, .stack-scope.pr-10 {\\n  padding-right: 2.5rem;\\n}\\n.stack-scope .pr-2, .stack-scope.pr-2 {\\n  padding-right: 0.5rem;\\n}\\n.stack-scope .pr-3, .stack-scope.pr-3 {\\n  padding-right: 0.75rem;\\n}\\n.stack-scope .pr-6, .stack-scope.pr-6 {\\n  padding-right: 1.5rem;\\n}\\n.stack-scope .pr-8, .stack-scope.pr-8 {\\n  padding-right: 2rem;\\n}\\n.stack-scope .pt-0, .stack-scope.pt-0 {\\n  padding-top: 0px;\\n}\\n.stack-scope .pt-1, .stack-scope.pt-1 {\\n  padding-top: 0.25rem;\\n}\\n.stack-scope .pt-4, .stack-scope.pt-4 {\\n  padding-top: 1rem;\\n}\\n.stack-scope .text-left, .stack-scope.text-left {\\n  text-align: left;\\n}\\n.stack-scope .text-center, .stack-scope.text-center {\\n  text-align: center;\\n}\\n.stack-scope .align-middle, .stack-scope.align-middle {\\n  vertical-align: middle;\\n}\\n.stack-scope .text-2xl, .stack-scope.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.stack-scope .text-3xl, .stack-scope.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.stack-scope .text-\\\\[0\\\\.8rem\\\\], .stack-scope.text-\\\\[0\\\\.8rem\\\\] {\\n  font-size: 0.8rem;\\n}\\n.stack-scope .text-lg, .stack-scope.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.stack-scope .text-sm, .stack-scope.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.stack-scope .text-xl, .stack-scope.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n.stack-scope .text-xs, .stack-scope.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.stack-scope .font-bold, .stack-scope.font-bold {\\n  font-weight: 700;\\n}\\n.stack-scope .font-medium, .stack-scope.font-medium {\\n  font-weight: 500;\\n}\\n.stack-scope .font-normal, .stack-scope.font-normal {\\n  font-weight: 400;\\n}\\n.stack-scope .font-semibold, .stack-scope.font-semibold {\\n  font-weight: 600;\\n}\\n.stack-scope .uppercase, .stack-scope.uppercase {\\n  text-transform: uppercase;\\n}\\n.stack-scope .capitalize, .stack-scope.capitalize {\\n  text-transform: capitalize;\\n}\\n.stack-scope .italic, .stack-scope.italic {\\n  font-style: italic;\\n}\\n.stack-scope .leading-6, .stack-scope.leading-6 {\\n  line-height: 1.5rem;\\n}\\n.stack-scope .leading-\\\\[0\\\\], .stack-scope.leading-\\\\[0\\\\] {\\n  line-height: 0;\\n}\\n.stack-scope .leading-none, .stack-scope.leading-none {\\n  line-height: 1;\\n}\\n.stack-scope .leading-relaxed, .stack-scope.leading-relaxed {\\n  line-height: 1.625;\\n}\\n.stack-scope .tracking-tight, .stack-scope.tracking-tight {\\n  letter-spacing: -0.025em;\\n}\\n.stack-scope .tracking-widest, .stack-scope.tracking-widest {\\n  letter-spacing: 0.1em;\\n}\\n.stack-scope .text-accent-foreground, .stack-scope.text-accent-foreground {\\n  color: hsl(var(--accent-foreground));\\n}\\n.stack-scope .text-black, .stack-scope.text-black {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity));\\n}\\n.stack-scope .text-card-foreground, .stack-scope.text-card-foreground {\\n  color: hsl(var(--card-foreground));\\n}\\n.stack-scope .text-current, .stack-scope.text-current {\\n  color: currentColor;\\n}\\n.stack-scope .text-destructive, .stack-scope.text-destructive {\\n  color: hsl(var(--destructive));\\n}\\n.stack-scope .text-destructive-foreground, .stack-scope.text-destructive-foreground {\\n  color: hsl(var(--destructive-foreground));\\n}\\n.stack-scope .text-foreground, .stack-scope.text-foreground {\\n  color: hsl(var(--foreground));\\n}\\n.stack-scope .text-foreground\\\\/50, .stack-scope.text-foreground\\\\/50 {\\n  color: hsl(var(--foreground) / 0.5);\\n}\\n.stack-scope .text-gray-500, .stack-scope.text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity));\\n}\\n.stack-scope .text-gray-600, .stack-scope.text-gray-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity));\\n}\\n.stack-scope .text-gray-700, .stack-scope.text-gray-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity));\\n}\\n.stack-scope .text-green-300, .stack-scope.text-green-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(134 239 172 / var(--tw-text-opacity));\\n}\\n.stack-scope .text-green-500, .stack-scope.text-green-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(34 197 94 / var(--tw-text-opacity));\\n}\\n.stack-scope .text-muted-foreground, .stack-scope.text-muted-foreground {\\n  color: hsl(var(--muted-foreground));\\n}\\n.stack-scope .text-muted-foreground\\\\/70, .stack-scope.text-muted-foreground\\\\/70 {\\n  color: hsl(var(--muted-foreground) / 0.7);\\n}\\n.stack-scope .text-popover-foreground, .stack-scope.text-popover-foreground {\\n  color: hsl(var(--popover-foreground));\\n}\\n.stack-scope .text-primary, .stack-scope.text-primary {\\n  color: hsl(var(--primary));\\n}\\n.stack-scope .text-primary-foreground, .stack-scope.text-primary-foreground {\\n  color: hsl(var(--primary-foreground));\\n}\\n.stack-scope .text-red-300, .stack-scope.text-red-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(252 165 165 / var(--tw-text-opacity));\\n}\\n.stack-scope .text-red-500, .stack-scope.text-red-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(239 68 68 / var(--tw-text-opacity));\\n}\\n.stack-scope .text-red-600, .stack-scope.text-red-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity));\\n}\\n.stack-scope .text-secondary-foreground, .stack-scope.text-secondary-foreground {\\n  color: hsl(var(--secondary-foreground));\\n}\\n.stack-scope .text-success, .stack-scope.text-success {\\n  color: hsl(var(--success));\\n}\\n.stack-scope .text-success-foreground, .stack-scope.text-success-foreground {\\n  color: hsl(var(--success-foreground));\\n}\\n.stack-scope .text-white, .stack-scope.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity));\\n}\\n.stack-scope .text-zinc-500, .stack-scope.text-zinc-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(113 113 122 / var(--tw-text-opacity));\\n}\\n.stack-scope .text-zinc-600, .stack-scope.text-zinc-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(82 82 91 / var(--tw-text-opacity));\\n}\\n.stack-scope .text-zinc-700, .stack-scope.text-zinc-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(63 63 70 / var(--tw-text-opacity));\\n}\\n.stack-scope .text-zinc-800, .stack-scope.text-zinc-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(39 39 42 / var(--tw-text-opacity));\\n}\\n.stack-scope .underline, .stack-scope.underline {\\n  text-decoration-line: underline;\\n}\\n.stack-scope .underline-offset-4, .stack-scope.underline-offset-4 {\\n  text-underline-offset: 4px;\\n}\\n.stack-scope .opacity-0, .stack-scope.opacity-0 {\\n  opacity: 0;\\n}\\n.stack-scope .opacity-100, .stack-scope.opacity-100 {\\n  opacity: 1;\\n}\\n.stack-scope .opacity-50, .stack-scope.opacity-50 {\\n  opacity: 0.5;\\n}\\n.stack-scope .opacity-60, .stack-scope.opacity-60 {\\n  opacity: 0.6;\\n}\\n.stack-scope .opacity-70, .stack-scope.opacity-70 {\\n  opacity: 0.7;\\n}\\n.stack-scope .opacity-90, .stack-scope.opacity-90 {\\n  opacity: 0.9;\\n}\\n.stack-scope .shadow, .stack-scope.shadow {\\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.stack-scope .shadow-2xl, .stack-scope.shadow-2xl {\\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.stack-scope .shadow-lg, .stack-scope.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.stack-scope .shadow-md, .stack-scope.shadow-md {\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.stack-scope .shadow-sm, .stack-scope.shadow-sm {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.stack-scope .outline-none, .stack-scope.outline-none {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.stack-scope .outline, .stack-scope.outline {\\n  outline-style: solid;\\n}\\n.stack-scope .ring, .stack-scope.ring {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.stack-scope .ring-0, .stack-scope.ring-0 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.stack-scope .ring-1, .stack-scope.ring-1 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.stack-scope .ring-ring, .stack-scope.ring-ring {\\n  --tw-ring-color: hsl(var(--ring));\\n}\\n.stack-scope .ring-offset-background, .stack-scope.ring-offset-background {\\n  --tw-ring-offset-color: hsl(var(--background));\\n}\\n.stack-scope .blur, .stack-scope.blur {\\n  --tw-blur: blur(8px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.stack-scope .filter, .stack-scope.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.stack-scope .backdrop-blur-sm, .stack-scope.backdrop-blur-sm {\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.stack-scope .backdrop-filter, .stack-scope.backdrop-filter {\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.stack-scope .transition, .stack-scope.transition {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.stack-scope .transition-all, .stack-scope.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.stack-scope .transition-colors, .stack-scope.transition-colors {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.stack-scope .transition-opacity, .stack-scope.transition-opacity {\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.stack-scope .transition-transform, .stack-scope.transition-transform {\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.stack-scope .duration-100, .stack-scope.duration-100 {\\n  transition-duration: 100ms;\\n}\\n.stack-scope .duration-1000, .stack-scope.duration-1000 {\\n  transition-duration: 1000ms;\\n}\\n.stack-scope .duration-200, .stack-scope.duration-200 {\\n  transition-duration: 200ms;\\n}\\n.stack-scope .duration-300, .stack-scope.duration-300 {\\n  transition-duration: 300ms;\\n}\\n.stack-scope .ease-in-out, .stack-scope.ease-in-out {\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n@keyframes enter {\\n  from {\\n    opacity: var(--tw-enter-opacity, 1);\\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\\n  }\\n}\\n@keyframes exit {\\n  to {\\n    opacity: var(--tw-exit-opacity, 1);\\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\\n  }\\n}\\n.stack-scope .animate-in, .stack-scope.animate-in {\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\n.stack-scope .fade-in-0, .stack-scope.fade-in-0 {\\n  --tw-enter-opacity: 0;\\n}\\n.stack-scope .zoom-in-95, .stack-scope.zoom-in-95 {\\n  --tw-enter-scale: .95;\\n}\\n.stack-scope .slide-in-from-left-1, .stack-scope.slide-in-from-left-1 {\\n  --tw-enter-translate-x: -0.25rem;\\n}\\n.stack-scope .slide-out-to-left-1, .stack-scope.slide-out-to-left-1 {\\n  --tw-exit-translate-x: -0.25rem;\\n}\\n.stack-scope .duration-100, .stack-scope.duration-100 {\\n  animation-duration: 100ms;\\n}\\n.stack-scope .duration-1000, .stack-scope.duration-1000 {\\n  animation-duration: 1000ms;\\n}\\n.stack-scope .duration-200, .stack-scope.duration-200 {\\n  animation-duration: 200ms;\\n}\\n.stack-scope .duration-300, .stack-scope.duration-300 {\\n  animation-duration: 300ms;\\n}\\n.stack-scope .ease-in-out, .stack-scope.ease-in-out {\\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.stack-scope .running, .stack-scope.running {\\n  animation-play-state: running;\\n}\\n.stack-scope *, *.stack-scope {\\n  border-color: hsl(var(--border));\\n}\\n.stack-scope * .hide-password-toggle::-ms-reveal,\\n  .stack-scope * .hide-password-toggle::-ms-clear,\\n  *.stack-scope .hide-password-toggle::-ms-reveal,\\n  *.stack-scope .hide-password-toggle::-ms-clear {\\n    visibility: hidden;\\n    pointer-events: none;\\n    display: none;\\n  }\\n.stack-scope .file\\\\:border-0::file-selector-button, .stack-scope.file\\\\:border-0::file-selector-button {\\n  border-width: 0px;\\n}\\n.stack-scope .file\\\\:bg-transparent::file-selector-button, .stack-scope.file\\\\:bg-transparent::file-selector-button {\\n  background-color: transparent;\\n}\\n.stack-scope .file\\\\:text-sm::file-selector-button, .stack-scope.file\\\\:text-sm::file-selector-button {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.stack-scope .file\\\\:font-medium::file-selector-button, .stack-scope.file\\\\:font-medium::file-selector-button {\\n  font-weight: 500;\\n}\\n.stack-scope .placeholder\\\\:text-muted-foreground::-moz-placeholder, .stack-scope.placeholder\\\\:text-muted-foreground::-moz-placeholder {\\n  color: hsl(var(--muted-foreground));\\n}\\n.stack-scope .placeholder\\\\:text-muted-foreground::placeholder, .stack-scope.placeholder\\\\:text-muted-foreground::placeholder {\\n  color: hsl(var(--muted-foreground));\\n}\\n.stack-scope .focus-within\\\\:relative:focus-within, .stack-scope.focus-within\\\\:relative:focus-within {\\n  position: relative;\\n}\\n.stack-scope .focus-within\\\\:z-20:focus-within, .stack-scope.focus-within\\\\:z-20:focus-within {\\n  z-index: 20;\\n}\\n.stack-scope .hover\\\\:bg-accent:hover, .stack-scope.hover\\\\:bg-accent:hover {\\n  background-color: hsl(var(--accent));\\n}\\n.stack-scope .hover\\\\:bg-destructive\\\\/90:hover, .stack-scope.hover\\\\:bg-destructive\\\\/90:hover {\\n  background-color: hsl(var(--destructive) / 0.9);\\n}\\n.stack-scope .hover\\\\:bg-muted:hover, .stack-scope.hover\\\\:bg-muted:hover {\\n  background-color: hsl(var(--muted));\\n}\\n.stack-scope .hover\\\\:bg-muted\\\\/50:hover, .stack-scope.hover\\\\:bg-muted\\\\/50:hover {\\n  background-color: hsl(var(--muted) / 0.5);\\n}\\n.stack-scope .hover\\\\:bg-primary:hover, .stack-scope.hover\\\\:bg-primary:hover {\\n  background-color: hsl(var(--primary));\\n}\\n.stack-scope .hover\\\\:bg-primary\\\\/90:hover, .stack-scope.hover\\\\:bg-primary\\\\/90:hover {\\n  background-color: hsl(var(--primary) / 0.9);\\n}\\n.stack-scope .hover\\\\:bg-secondary:hover, .stack-scope.hover\\\\:bg-secondary:hover {\\n  background-color: hsl(var(--secondary));\\n}\\n.stack-scope .hover\\\\:bg-secondary\\\\/80:hover, .stack-scope.hover\\\\:bg-secondary\\\\/80:hover {\\n  background-color: hsl(var(--secondary) / 0.8);\\n}\\n.stack-scope .hover\\\\:bg-transparent:hover, .stack-scope.hover\\\\:bg-transparent:hover {\\n  background-color: transparent;\\n}\\n.stack-scope .hover\\\\:text-accent-foreground:hover, .stack-scope.hover\\\\:text-accent-foreground:hover {\\n  color: hsl(var(--accent-foreground));\\n}\\n.stack-scope .hover\\\\:text-foreground:hover, .stack-scope.hover\\\\:text-foreground:hover {\\n  color: hsl(var(--foreground));\\n}\\n.stack-scope .hover\\\\:text-muted-foreground:hover, .stack-scope.hover\\\\:text-muted-foreground:hover {\\n  color: hsl(var(--muted-foreground));\\n}\\n.stack-scope .hover\\\\:text-primary-foreground:hover, .stack-scope.hover\\\\:text-primary-foreground:hover {\\n  color: hsl(var(--primary-foreground));\\n}\\n.stack-scope .hover\\\\:underline:hover, .stack-scope.hover\\\\:underline:hover {\\n  text-decoration-line: underline;\\n}\\n.stack-scope .hover\\\\:opacity-100:hover, .stack-scope.hover\\\\:opacity-100:hover {\\n  opacity: 1;\\n}\\n.stack-scope .focus\\\\:bg-accent:focus, .stack-scope.focus\\\\:bg-accent:focus {\\n  background-color: hsl(var(--accent));\\n}\\n.stack-scope .focus\\\\:bg-primary:focus, .stack-scope.focus\\\\:bg-primary:focus {\\n  background-color: hsl(var(--primary));\\n}\\n.stack-scope .focus\\\\:text-accent-foreground:focus, .stack-scope.focus\\\\:text-accent-foreground:focus {\\n  color: hsl(var(--accent-foreground));\\n}\\n.stack-scope .focus\\\\:text-primary-foreground:focus, .stack-scope.focus\\\\:text-primary-foreground:focus {\\n  color: hsl(var(--primary-foreground));\\n}\\n.stack-scope .focus\\\\:opacity-100:focus, .stack-scope.focus\\\\:opacity-100:focus {\\n  opacity: 1;\\n}\\n.stack-scope .focus\\\\:outline-none:focus, .stack-scope.focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.stack-scope .focus\\\\:ring-1:focus, .stack-scope.focus\\\\:ring-1:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.stack-scope .focus\\\\:ring-2:focus, .stack-scope.focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.stack-scope .focus\\\\:ring-ring:focus, .stack-scope.focus\\\\:ring-ring:focus {\\n  --tw-ring-color: hsl(var(--ring));\\n}\\n.stack-scope .focus\\\\:ring-offset-2:focus, .stack-scope.focus\\\\:ring-offset-2:focus {\\n  --tw-ring-offset-width: 2px;\\n}\\n.stack-scope .focus-visible\\\\:outline-none:focus-visible, .stack-scope.focus-visible\\\\:outline-none:focus-visible {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.stack-scope .focus-visible\\\\:ring-1:focus-visible, .stack-scope.focus-visible\\\\:ring-1:focus-visible {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.stack-scope .focus-visible\\\\:ring-2:focus-visible, .stack-scope.focus-visible\\\\:ring-2:focus-visible {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.stack-scope .focus-visible\\\\:ring-ring:focus-visible, .stack-scope.focus-visible\\\\:ring-ring:focus-visible {\\n  --tw-ring-color: hsl(var(--ring));\\n}\\n.stack-scope .focus-visible\\\\:ring-offset-2:focus-visible, .stack-scope.focus-visible\\\\:ring-offset-2:focus-visible {\\n  --tw-ring-offset-width: 2px;\\n}\\n.stack-scope .focus-visible\\\\:ring-offset-background:focus-visible, .stack-scope.focus-visible\\\\:ring-offset-background:focus-visible {\\n  --tw-ring-offset-color: hsl(var(--background));\\n}\\n.stack-scope .disabled\\\\:pointer-events-none:disabled, .stack-scope.disabled\\\\:pointer-events-none:disabled {\\n  pointer-events: none;\\n}\\n.stack-scope .disabled\\\\:cursor-not-allowed:disabled, .stack-scope.disabled\\\\:cursor-not-allowed:disabled {\\n  cursor: not-allowed;\\n}\\n.stack-scope .disabled\\\\:opacity-50:disabled, .stack-scope.disabled\\\\:opacity-50:disabled {\\n  opacity: 0.5;\\n}\\n.stack-scope .group:hover .group-hover\\\\:opacity-100, .stack-scope.group:hover .group-hover\\\\:opacity-100 {\\n  opacity: 1;\\n}\\n.stack-scope .group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:border-muted\\\\/40, .stack-scope.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:border-muted\\\\/40 {\\n  border-color: hsl(var(--muted) / 0.4);\\n}\\n.stack-scope .group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:text-red-300, .stack-scope.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:text-red-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(252 165 165 / var(--tw-text-opacity));\\n}\\n.stack-scope .group.success .group-\\\\[\\\\.success\\\\]\\\\:text-green-300, .stack-scope.group.success .group-\\\\[\\\\.success\\\\]\\\\:text-green-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(134 239 172 / var(--tw-text-opacity));\\n}\\n.stack-scope .group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:border-destructive\\\\/30:hover, .stack-scope.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:border-destructive\\\\/30:hover {\\n  border-color: hsl(var(--destructive) / 0.3);\\n}\\n.stack-scope .group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:bg-destructive:hover, .stack-scope.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:bg-destructive:hover {\\n  background-color: hsl(var(--destructive));\\n}\\n.stack-scope .group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:text-destructive-foreground:hover, .stack-scope.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:text-destructive-foreground:hover {\\n  color: hsl(var(--destructive-foreground));\\n}\\n.stack-scope .group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:text-red-50:hover, .stack-scope.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:hover\\\\:text-red-50:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(254 242 242 / var(--tw-text-opacity));\\n}\\n.stack-scope .group.success .group-\\\\[\\\\.success\\\\]\\\\:hover\\\\:text-green-50:hover, .stack-scope.group.success .group-\\\\[\\\\.success\\\\]\\\\:hover\\\\:text-green-50:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(240 253 244 / var(--tw-text-opacity));\\n}\\n.stack-scope .group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:focus\\\\:ring-destructive:focus, .stack-scope.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:focus\\\\:ring-destructive:focus {\\n  --tw-ring-color: hsl(var(--destructive));\\n}\\n.stack-scope .group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:focus\\\\:ring-red-400:focus, .stack-scope.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:focus\\\\:ring-red-400:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity));\\n}\\n.stack-scope .group.success .group-\\\\[\\\\.success\\\\]\\\\:focus\\\\:ring-green-400:focus, .stack-scope.group.success .group-\\\\[\\\\.success\\\\]\\\\:focus\\\\:ring-green-400:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(74 222 128 / var(--tw-ring-opacity));\\n}\\n.stack-scope .group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:focus\\\\:ring-offset-red-600:focus, .stack-scope.group.destructive .group-\\\\[\\\\.destructive\\\\]\\\\:focus\\\\:ring-offset-red-600:focus {\\n  --tw-ring-offset-color: #dc2626;\\n}\\n.stack-scope .group.success .group-\\\\[\\\\.success\\\\]\\\\:focus\\\\:ring-offset-green-600:focus, .stack-scope.group.success .group-\\\\[\\\\.success\\\\]\\\\:focus\\\\:ring-offset-green-600:focus {\\n  --tw-ring-offset-color: #16a34a;\\n}\\n.stack-scope .peer:disabled ~ .peer-disabled\\\\:cursor-not-allowed, .stack-scope.peer:disabled ~ .peer-disabled\\\\:cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\n.stack-scope .peer:disabled ~ .peer-disabled\\\\:opacity-70, .stack-scope.peer:disabled ~ .peer-disabled\\\\:opacity-70 {\\n  opacity: 0.7;\\n}\\n.stack-scope .has-\\\\[\\\\:disabled\\\\]\\\\:opacity-50:has(:disabled), .stack-scope.has-\\\\[\\\\:disabled\\\\]\\\\:opacity-50:has(:disabled) {\\n  opacity: 0.5;\\n}\\n.stack-scope .aria-selected\\\\:bg-accent[aria-selected=\\\"true\\\"], .stack-scope.aria-selected\\\\:bg-accent[aria-selected=\\\"true\\\"] {\\n  background-color: hsl(var(--accent));\\n}\\n.stack-scope .aria-selected\\\\:bg-accent\\\\/50[aria-selected=\\\"true\\\"], .stack-scope.aria-selected\\\\:bg-accent\\\\/50[aria-selected=\\\"true\\\"] {\\n  background-color: hsl(var(--accent) / 0.5);\\n}\\n.stack-scope .aria-selected\\\\:text-accent-foreground[aria-selected=\\\"true\\\"], .stack-scope.aria-selected\\\\:text-accent-foreground[aria-selected=\\\"true\\\"] {\\n  color: hsl(var(--accent-foreground));\\n}\\n.stack-scope .aria-selected\\\\:text-muted-foreground[aria-selected=\\\"true\\\"], .stack-scope.aria-selected\\\\:text-muted-foreground[aria-selected=\\\"true\\\"] {\\n  color: hsl(var(--muted-foreground));\\n}\\n.stack-scope .aria-selected\\\\:opacity-100[aria-selected=\\\"true\\\"], .stack-scope.aria-selected\\\\:opacity-100[aria-selected=\\\"true\\\"] {\\n  opacity: 1;\\n}\\n.stack-scope .aria-selected\\\\:opacity-30[aria-selected=\\\"true\\\"], .stack-scope.aria-selected\\\\:opacity-30[aria-selected=\\\"true\\\"] {\\n  opacity: 0.3;\\n}\\n.stack-scope .data-\\\\[disabled\\\\=true\\\\]\\\\:pointer-events-none[data-disabled=\\\"true\\\"], .stack-scope.data-\\\\[disabled\\\\=true\\\\]\\\\:pointer-events-none[data-disabled=\\\"true\\\"] {\\n  pointer-events: none;\\n}\\n.stack-scope .data-\\\\[disabled\\\\]\\\\:pointer-events-none[data-disabled], .stack-scope.data-\\\\[disabled\\\\]\\\\:pointer-events-none[data-disabled] {\\n  pointer-events: none;\\n}\\n.stack-scope .data-\\\\[side\\\\=bottom\\\\]\\\\:translate-y-1[data-side=\\\"bottom\\\"], .stack-scope.data-\\\\[side\\\\=bottom\\\\]\\\\:translate-y-1[data-side=\\\"bottom\\\"] {\\n  --tw-translate-y: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.stack-scope .data-\\\\[side\\\\=left\\\\]\\\\:-translate-x-1[data-side=\\\"left\\\"], .stack-scope.data-\\\\[side\\\\=left\\\\]\\\\:-translate-x-1[data-side=\\\"left\\\"] {\\n  --tw-translate-x: -0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.stack-scope .data-\\\\[side\\\\=right\\\\]\\\\:translate-x-1[data-side=\\\"right\\\"], .stack-scope.data-\\\\[side\\\\=right\\\\]\\\\:translate-x-1[data-side=\\\"right\\\"] {\\n  --tw-translate-x: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.stack-scope .data-\\\\[side\\\\=top\\\\]\\\\:-translate-y-1[data-side=\\\"top\\\"], .stack-scope.data-\\\\[side\\\\=top\\\\]\\\\:-translate-y-1[data-side=\\\"top\\\"] {\\n  --tw-translate-y: -0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.stack-scope .data-\\\\[state\\\\=checked\\\\]\\\\:translate-x-4[data-state=\\\"checked\\\"], .stack-scope.data-\\\\[state\\\\=checked\\\\]\\\\:translate-x-4[data-state=\\\"checked\\\"] {\\n  --tw-translate-x: 1rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.stack-scope .data-\\\\[state\\\\=unchecked\\\\]\\\\:translate-x-0[data-state=\\\"unchecked\\\"], .stack-scope.data-\\\\[state\\\\=unchecked\\\\]\\\\:translate-x-0[data-state=\\\"unchecked\\\"] {\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.stack-scope .data-\\\\[swipe\\\\=cancel\\\\]\\\\:translate-x-0[data-swipe=\\\"cancel\\\"], .stack-scope.data-\\\\[swipe\\\\=cancel\\\\]\\\\:translate-x-0[data-swipe=\\\"cancel\\\"] {\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.stack-scope .data-\\\\[swipe\\\\=end\\\\]\\\\:translate-x-\\\\[var\\\\(--radix-toast-swipe-end-x\\\\)\\\\][data-swipe=\\\"end\\\"], .stack-scope.data-\\\\[swipe\\\\=end\\\\]\\\\:translate-x-\\\\[var\\\\(--radix-toast-swipe-end-x\\\\)\\\\][data-swipe=\\\"end\\\"] {\\n  --tw-translate-x: var(--radix-toast-swipe-end-x);\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.stack-scope .data-\\\\[swipe\\\\=move\\\\]\\\\:translate-x-\\\\[var\\\\(--radix-toast-swipe-move-x\\\\)\\\\][data-swipe=\\\"move\\\"], .stack-scope.data-\\\\[swipe\\\\=move\\\\]\\\\:translate-x-\\\\[var\\\\(--radix-toast-swipe-move-x\\\\)\\\\][data-swipe=\\\"move\\\"] {\\n  --tw-translate-x: var(--radix-toast-swipe-move-x);\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n@keyframes accordion-up {\\n  from {\\n    height: var(--radix-accordion-content-height);\\n  }\\n\\n  to {\\n    height: 0;\\n  }\\n}\\n.stack-scope .data-\\\\[state\\\\=closed\\\\]\\\\:animate-accordion-up[data-state=\\\"closed\\\"], .stack-scope.data-\\\\[state\\\\=closed\\\\]\\\\:animate-accordion-up[data-state=\\\"closed\\\"] {\\n  animation: accordion-up 0.2s ease-out;\\n}\\n@keyframes accordion-down {\\n  from {\\n    height: 0;\\n  }\\n\\n  to {\\n    height: var(--radix-accordion-content-height);\\n  }\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:animate-accordion-down[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:animate-accordion-down[data-state=\\\"open\\\"] {\\n  animation: accordion-down 0.2s ease-out;\\n}\\n.stack-scope .data-\\\\[active\\\\]\\\\:bg-accent\\\\/50[data-active], .stack-scope.data-\\\\[active\\\\]\\\\:bg-accent\\\\/50[data-active] {\\n  background-color: hsl(var(--accent) / 0.5);\\n}\\n.stack-scope .data-\\\\[state\\\\=active\\\\]\\\\:bg-background[data-state=\\\"active\\\"], .stack-scope.data-\\\\[state\\\\=active\\\\]\\\\:bg-background[data-state=\\\"active\\\"] {\\n  background-color: hsl(var(--background));\\n}\\n.stack-scope .data-\\\\[state\\\\=checked\\\\]\\\\:bg-primary[data-state=\\\"checked\\\"], .stack-scope.data-\\\\[state\\\\=checked\\\\]\\\\:bg-primary[data-state=\\\"checked\\\"] {\\n  background-color: hsl(var(--primary));\\n}\\n.stack-scope .data-\\\\[state\\\\=on\\\\]\\\\:bg-accent[data-state=\\\"on\\\"], .stack-scope.data-\\\\[state\\\\=on\\\\]\\\\:bg-accent[data-state=\\\"on\\\"] {\\n  background-color: hsl(var(--accent));\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:bg-accent[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:bg-accent[data-state=\\\"open\\\"] {\\n  background-color: hsl(var(--accent));\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:bg-accent\\\\/50[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:bg-accent\\\\/50[data-state=\\\"open\\\"] {\\n  background-color: hsl(var(--accent) / 0.5);\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:bg-muted[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:bg-muted[data-state=\\\"open\\\"] {\\n  background-color: hsl(var(--muted));\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:bg-secondary[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:bg-secondary[data-state=\\\"open\\\"] {\\n  background-color: hsl(var(--secondary));\\n}\\n.stack-scope .data-\\\\[state\\\\=selected\\\\]\\\\:bg-muted[data-state=\\\"selected\\\"], .stack-scope.data-\\\\[state\\\\=selected\\\\]\\\\:bg-muted[data-state=\\\"selected\\\"] {\\n  background-color: hsl(var(--muted));\\n}\\n.stack-scope .data-\\\\[state\\\\=unchecked\\\\]\\\\:bg-input[data-state=\\\"unchecked\\\"], .stack-scope.data-\\\\[state\\\\=unchecked\\\\]\\\\:bg-input[data-state=\\\"unchecked\\\"] {\\n  background-color: hsl(var(--input));\\n}\\n.stack-scope .data-\\\\[state\\\\=active\\\\]\\\\:text-foreground[data-state=\\\"active\\\"], .stack-scope.data-\\\\[state\\\\=active\\\\]\\\\:text-foreground[data-state=\\\"active\\\"] {\\n  color: hsl(var(--foreground));\\n}\\n.stack-scope .data-\\\\[state\\\\=checked\\\\]\\\\:text-primary-foreground[data-state=\\\"checked\\\"], .stack-scope.data-\\\\[state\\\\=checked\\\\]\\\\:text-primary-foreground[data-state=\\\"checked\\\"] {\\n  color: hsl(var(--primary-foreground));\\n}\\n.stack-scope .data-\\\\[state\\\\=on\\\\]\\\\:text-accent-foreground[data-state=\\\"on\\\"], .stack-scope.data-\\\\[state\\\\=on\\\\]\\\\:text-accent-foreground[data-state=\\\"on\\\"] {\\n  color: hsl(var(--accent-foreground));\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:text-accent-foreground[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:text-accent-foreground[data-state=\\\"open\\\"] {\\n  color: hsl(var(--accent-foreground));\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:text-muted-foreground[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:text-muted-foreground[data-state=\\\"open\\\"] {\\n  color: hsl(var(--muted-foreground));\\n}\\n.stack-scope .data-\\\\[disabled\\\\=true\\\\]\\\\:opacity-50[data-disabled=\\\"true\\\"], .stack-scope.data-\\\\[disabled\\\\=true\\\\]\\\\:opacity-50[data-disabled=\\\"true\\\"] {\\n  opacity: 0.5;\\n}\\n.stack-scope .data-\\\\[disabled\\\\]\\\\:opacity-50[data-disabled], .stack-scope.data-\\\\[disabled\\\\]\\\\:opacity-50[data-disabled] {\\n  opacity: 0.5;\\n}\\n.stack-scope .data-\\\\[state\\\\=active\\\\]\\\\:shadow-sm[data-state=\\\"active\\\"], .stack-scope.data-\\\\[state\\\\=active\\\\]\\\\:shadow-sm[data-state=\\\"active\\\"] {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.stack-scope .data-\\\\[swipe\\\\=move\\\\]\\\\:transition-none[data-swipe=\\\"move\\\"], .stack-scope.data-\\\\[swipe\\\\=move\\\\]\\\\:transition-none[data-swipe=\\\"move\\\"] {\\n  transition-property: none;\\n}\\n.stack-scope .data-\\\\[state\\\\=closed\\\\]\\\\:duration-100[data-state=\\\"closed\\\"], .stack-scope.data-\\\\[state\\\\=closed\\\\]\\\\:duration-100[data-state=\\\"closed\\\"] {\\n  transition-duration: 100ms;\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:duration-100[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:duration-100[data-state=\\\"open\\\"] {\\n  transition-duration: 100ms;\\n}\\n.stack-scope .data-\\\\[motion\\\\^\\\\=from-\\\\]\\\\:animate-in[data-motion^=\\\"from-\\\"], .stack-scope.data-\\\\[motion\\\\^\\\\=from-\\\\]\\\\:animate-in[data-motion^=\\\"from-\\\"] {\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:animate-in[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:animate-in[data-state=\\\"open\\\"] {\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\n.stack-scope .data-\\\\[state\\\\=visible\\\\]\\\\:animate-in[data-state=\\\"visible\\\"], .stack-scope.data-\\\\[state\\\\=visible\\\\]\\\\:animate-in[data-state=\\\"visible\\\"] {\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\n.stack-scope .data-\\\\[motion\\\\^\\\\=to-\\\\]\\\\:animate-out[data-motion^=\\\"to-\\\"], .stack-scope.data-\\\\[motion\\\\^\\\\=to-\\\\]\\\\:animate-out[data-motion^=\\\"to-\\\"] {\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\n.stack-scope .data-\\\\[state\\\\=closed\\\\]\\\\:animate-out[data-state=\\\"closed\\\"], .stack-scope.data-\\\\[state\\\\=closed\\\\]\\\\:animate-out[data-state=\\\"closed\\\"] {\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\n.stack-scope .data-\\\\[state\\\\=hidden\\\\]\\\\:animate-out[data-state=\\\"hidden\\\"], .stack-scope.data-\\\\[state\\\\=hidden\\\\]\\\\:animate-out[data-state=\\\"hidden\\\"] {\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\n.stack-scope .data-\\\\[swipe\\\\=end\\\\]\\\\:animate-out[data-swipe=\\\"end\\\"], .stack-scope.data-\\\\[swipe\\\\=end\\\\]\\\\:animate-out[data-swipe=\\\"end\\\"] {\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\n.stack-scope .data-\\\\[motion\\\\^\\\\=from-\\\\]\\\\:fade-in[data-motion^=\\\"from-\\\"], .stack-scope.data-\\\\[motion\\\\^\\\\=from-\\\\]\\\\:fade-in[data-motion^=\\\"from-\\\"] {\\n  --tw-enter-opacity: 0;\\n}\\n.stack-scope .data-\\\\[motion\\\\^\\\\=to-\\\\]\\\\:fade-out[data-motion^=\\\"to-\\\"], .stack-scope.data-\\\\[motion\\\\^\\\\=to-\\\\]\\\\:fade-out[data-motion^=\\\"to-\\\"] {\\n  --tw-exit-opacity: 0;\\n}\\n.stack-scope .data-\\\\[state\\\\=closed\\\\]\\\\:fade-out-0[data-state=\\\"closed\\\"], .stack-scope.data-\\\\[state\\\\=closed\\\\]\\\\:fade-out-0[data-state=\\\"closed\\\"] {\\n  --tw-exit-opacity: 0;\\n}\\n.stack-scope .data-\\\\[state\\\\=closed\\\\]\\\\:fade-out-80[data-state=\\\"closed\\\"], .stack-scope.data-\\\\[state\\\\=closed\\\\]\\\\:fade-out-80[data-state=\\\"closed\\\"] {\\n  --tw-exit-opacity: 0.8;\\n}\\n.stack-scope .data-\\\\[state\\\\=hidden\\\\]\\\\:fade-out[data-state=\\\"hidden\\\"], .stack-scope.data-\\\\[state\\\\=hidden\\\\]\\\\:fade-out[data-state=\\\"hidden\\\"] {\\n  --tw-exit-opacity: 0;\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:fade-in-0[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:fade-in-0[data-state=\\\"open\\\"] {\\n  --tw-enter-opacity: 0;\\n}\\n.stack-scope .data-\\\\[state\\\\=visible\\\\]\\\\:fade-in[data-state=\\\"visible\\\"], .stack-scope.data-\\\\[state\\\\=visible\\\\]\\\\:fade-in[data-state=\\\"visible\\\"] {\\n  --tw-enter-opacity: 0;\\n}\\n.stack-scope .data-\\\\[state\\\\=closed\\\\]\\\\:zoom-out-95[data-state=\\\"closed\\\"], .stack-scope.data-\\\\[state\\\\=closed\\\\]\\\\:zoom-out-95[data-state=\\\"closed\\\"] {\\n  --tw-exit-scale: .95;\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:zoom-in-90[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:zoom-in-90[data-state=\\\"open\\\"] {\\n  --tw-enter-scale: .9;\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:zoom-in-95[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:zoom-in-95[data-state=\\\"open\\\"] {\\n  --tw-enter-scale: .95;\\n}\\n.stack-scope .data-\\\\[motion\\\\=from-end\\\\]\\\\:slide-in-from-right-52[data-motion=\\\"from-end\\\"], .stack-scope.data-\\\\[motion\\\\=from-end\\\\]\\\\:slide-in-from-right-52[data-motion=\\\"from-end\\\"] {\\n  --tw-enter-translate-x: 13rem;\\n}\\n.stack-scope .data-\\\\[motion\\\\=from-start\\\\]\\\\:slide-in-from-left-52[data-motion=\\\"from-start\\\"], .stack-scope.data-\\\\[motion\\\\=from-start\\\\]\\\\:slide-in-from-left-52[data-motion=\\\"from-start\\\"] {\\n  --tw-enter-translate-x: -13rem;\\n}\\n.stack-scope .data-\\\\[motion\\\\=to-end\\\\]\\\\:slide-out-to-right-52[data-motion=\\\"to-end\\\"], .stack-scope.data-\\\\[motion\\\\=to-end\\\\]\\\\:slide-out-to-right-52[data-motion=\\\"to-end\\\"] {\\n  --tw-exit-translate-x: 13rem;\\n}\\n.stack-scope .data-\\\\[motion\\\\=to-start\\\\]\\\\:slide-out-to-left-52[data-motion=\\\"to-start\\\"], .stack-scope.data-\\\\[motion\\\\=to-start\\\\]\\\\:slide-out-to-left-52[data-motion=\\\"to-start\\\"] {\\n  --tw-exit-translate-x: -13rem;\\n}\\n.stack-scope .data-\\\\[side\\\\=bottom\\\\]\\\\:slide-in-from-top-2[data-side=\\\"bottom\\\"], .stack-scope.data-\\\\[side\\\\=bottom\\\\]\\\\:slide-in-from-top-2[data-side=\\\"bottom\\\"] {\\n  --tw-enter-translate-y: -0.5rem;\\n}\\n.stack-scope .data-\\\\[side\\\\=left\\\\]\\\\:slide-in-from-right-2[data-side=\\\"left\\\"], .stack-scope.data-\\\\[side\\\\=left\\\\]\\\\:slide-in-from-right-2[data-side=\\\"left\\\"] {\\n  --tw-enter-translate-x: 0.5rem;\\n}\\n.stack-scope .data-\\\\[side\\\\=right\\\\]\\\\:slide-in-from-left-2[data-side=\\\"right\\\"], .stack-scope.data-\\\\[side\\\\=right\\\\]\\\\:slide-in-from-left-2[data-side=\\\"right\\\"] {\\n  --tw-enter-translate-x: -0.5rem;\\n}\\n.stack-scope .data-\\\\[side\\\\=top\\\\]\\\\:slide-in-from-bottom-2[data-side=\\\"top\\\"], .stack-scope.data-\\\\[side\\\\=top\\\\]\\\\:slide-in-from-bottom-2[data-side=\\\"top\\\"] {\\n  --tw-enter-translate-y: 0.5rem;\\n}\\n.stack-scope .data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-bottom[data-state=\\\"closed\\\"], .stack-scope.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-bottom[data-state=\\\"closed\\\"] {\\n  --tw-exit-translate-y: 100%;\\n}\\n.stack-scope .data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-left[data-state=\\\"closed\\\"], .stack-scope.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-left[data-state=\\\"closed\\\"] {\\n  --tw-exit-translate-x: -100%;\\n}\\n.stack-scope .data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-left-1\\\\/2[data-state=\\\"closed\\\"], .stack-scope.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-left-1\\\\/2[data-state=\\\"closed\\\"] {\\n  --tw-exit-translate-x: -50%;\\n}\\n.stack-scope .data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-right[data-state=\\\"closed\\\"], .stack-scope.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-right[data-state=\\\"closed\\\"] {\\n  --tw-exit-translate-x: 100%;\\n}\\n.stack-scope .data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-right-full[data-state=\\\"closed\\\"], .stack-scope.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-right-full[data-state=\\\"closed\\\"] {\\n  --tw-exit-translate-x: 100%;\\n}\\n.stack-scope .data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-top[data-state=\\\"closed\\\"], .stack-scope.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-top[data-state=\\\"closed\\\"] {\\n  --tw-exit-translate-y: -100%;\\n}\\n.stack-scope .data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-top-\\\\[48\\\\%\\\\][data-state=\\\"closed\\\"], .stack-scope.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-top-\\\\[48\\\\%\\\\][data-state=\\\"closed\\\"] {\\n  --tw-exit-translate-y: -48%;\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-bottom[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-bottom[data-state=\\\"open\\\"] {\\n  --tw-enter-translate-y: 100%;\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-left[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-left[data-state=\\\"open\\\"] {\\n  --tw-enter-translate-x: -100%;\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-left-1\\\\/2[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-left-1\\\\/2[data-state=\\\"open\\\"] {\\n  --tw-enter-translate-x: -50%;\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-right[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-right[data-state=\\\"open\\\"] {\\n  --tw-enter-translate-x: 100%;\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top[data-state=\\\"open\\\"] {\\n  --tw-enter-translate-y: -100%;\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top-\\\\[48\\\\%\\\\][data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top-\\\\[48\\\\%\\\\][data-state=\\\"open\\\"] {\\n  --tw-enter-translate-y: -48%;\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top-full[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top-full[data-state=\\\"open\\\"] {\\n  --tw-enter-translate-y: -100%;\\n}\\n.stack-scope .data-\\\\[state\\\\=closed\\\\]\\\\:duration-100[data-state=\\\"closed\\\"], .stack-scope.data-\\\\[state\\\\=closed\\\\]\\\\:duration-100[data-state=\\\"closed\\\"] {\\n  animation-duration: 100ms;\\n}\\n.stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:duration-100[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:duration-100[data-state=\\\"open\\\"] {\\n  animation-duration: 100ms;\\n}\\n.stack-scope .group[data-state=\\\"open\\\"] .group-data-\\\\[state\\\\=open\\\\]\\\\:rotate-180, .stack-scope.group[data-state=\\\"open\\\"] .group-data-\\\\[state\\\\=open\\\\]\\\\:rotate-180 {\\n  --tw-rotate: 180deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n@media (min-width: 640px) {\\n  .stack-scope .sm\\\\:bottom-0, .stack-scope.sm\\\\:bottom-0 {\\n    bottom: 0px;\\n  }\\n\\n  .stack-scope .sm\\\\:right-0, .stack-scope.sm\\\\:right-0 {\\n    right: 0px;\\n  }\\n\\n  .stack-scope .sm\\\\:top-auto, .stack-scope.sm\\\\:top-auto {\\n    top: auto;\\n  }\\n\\n  .stack-scope .sm\\\\:flex, .stack-scope.sm\\\\:flex {\\n    display: flex;\\n  }\\n\\n  .stack-scope .sm\\\\:hidden, .stack-scope.sm\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .stack-scope .sm\\\\:max-w-sm, .stack-scope.sm\\\\:max-w-sm {\\n    max-width: 24rem;\\n  }\\n\\n  .stack-scope .sm\\\\:flex-1, .stack-scope.sm\\\\:flex-1 {\\n    flex: 1 1 0%;\\n  }\\n\\n  .stack-scope .sm\\\\:flex-row, .stack-scope.sm\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .stack-scope .sm\\\\:flex-col, .stack-scope.sm\\\\:flex-col {\\n    flex-direction: column;\\n  }\\n\\n  .stack-scope .sm\\\\:items-end, .stack-scope.sm\\\\:items-end {\\n    align-items: flex-end;\\n  }\\n\\n  .stack-scope .sm\\\\:justify-end, .stack-scope.sm\\\\:justify-end {\\n    justify-content: flex-end;\\n  }\\n\\n  .stack-scope .sm\\\\:gap-2\\\\.5, .stack-scope.sm\\\\:gap-2\\\\.5 {\\n    gap: 0.625rem;\\n  }\\n\\n  .stack-scope .sm\\\\:gap-y-0, .stack-scope.sm\\\\:gap-y-0 {\\n    row-gap: 0px;\\n  }\\n\\n  .stack-scope .sm\\\\:space-x-2 > :not([hidden]) ~ :not([hidden]), .stack-scope.sm\\\\:space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-x-reverse: 0;\\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n  }\\n\\n  .stack-scope .sm\\\\:space-x-4 > :not([hidden]) ~ :not([hidden]), .stack-scope.sm\\\\:space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-x-reverse: 0;\\n    margin-right: calc(1rem * var(--tw-space-x-reverse));\\n    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n  }\\n\\n  .stack-scope .sm\\\\:space-y-0 > :not([hidden]) ~ :not([hidden]), .stack-scope.sm\\\\:space-y-0 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-y-reverse: 0;\\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\\n  }\\n\\n  .stack-scope .sm\\\\:rounded-lg, .stack-scope.sm\\\\:rounded-lg {\\n    border-radius: var(--radius);\\n  }\\n\\n  .stack-scope .sm\\\\:bg-muted, .stack-scope.sm\\\\:bg-muted {\\n    background-color: hsl(var(--muted));\\n  }\\n\\n  .stack-scope .sm\\\\:text-left, .stack-scope.sm\\\\:text-left {\\n    text-align: left;\\n  }\\n\\n  .stack-scope .data-\\\\[state\\\\=open\\\\]\\\\:sm\\\\:slide-in-from-bottom-full[data-state=\\\"open\\\"], .stack-scope.data-\\\\[state\\\\=open\\\\]\\\\:sm\\\\:slide-in-from-bottom-full[data-state=\\\"open\\\"] {\\n    --tw-enter-translate-y: 100%;\\n  }\\n}\\n@media (min-width: 768px) {\\n  .stack-scope .md\\\\:absolute, .stack-scope.md\\\\:absolute {\\n    position: absolute;\\n  }\\n\\n  .stack-scope .md\\\\:w-\\\\[90\\\\%\\\\], .stack-scope.md\\\\:w-\\\\[90\\\\%\\\\] {\\n    width: 90%;\\n  }\\n\\n  .stack-scope .md\\\\:w-\\\\[var\\\\(--radix-navigation-menu-viewport-width\\\\)\\\\], .stack-scope.md\\\\:w-\\\\[var\\\\(--radix-navigation-menu-viewport-width\\\\)\\\\] {\\n    width: var(--radix-navigation-menu-viewport-width);\\n  }\\n\\n  .stack-scope .md\\\\:w-auto, .stack-scope.md\\\\:w-auto {\\n    width: auto;\\n  }\\n\\n  .stack-scope .md\\\\:max-w-\\\\[420px\\\\], .stack-scope.md\\\\:max-w-\\\\[420px\\\\] {\\n    max-width: 420px;\\n  }\\n\\n  .stack-scope .md\\\\:flex-row, .stack-scope.md\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .stack-scope .md\\\\:justify-end, .stack-scope.md\\\\:justify-end {\\n    justify-content: flex-end;\\n  }\\n\\n  .stack-scope .md\\\\:gap-4, .stack-scope.md\\\\:gap-4 {\\n    gap: 1rem;\\n  }\\n}\\n@media (min-width: 1024px) {\\n  .stack-scope .lg\\\\:flex, .stack-scope.lg\\\\:flex {\\n    display: flex;\\n  }\\n\\n  .stack-scope .lg\\\\:hidden, .stack-scope.lg\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .stack-scope .lg\\\\:gap-x-8, .stack-scope.lg\\\\:gap-x-8 {\\n    -moz-column-gap: 2rem;\\n         column-gap: 2rem;\\n  }\\n\\n  .stack-scope .lg\\\\:px-3, .stack-scope.lg\\\\:px-3 {\\n    padding-left: 0.75rem;\\n    padding-right: 0.75rem;\\n  }\\n}\\n.stack-scope .dark\\\\:border-destructive:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *), .stack-scope.dark\\\\:border-destructive:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *) {\\n  border-color: hsl(var(--destructive));\\n}\\n.stack-scope .dark\\\\:border-gray-600:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *), .stack-scope.dark\\\\:border-gray-600:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *) {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity));\\n}\\n.stack-scope .dark\\\\:border-success:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *), .stack-scope.dark\\\\:border-success:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *) {\\n  border-color: hsl(var(--success));\\n}\\n.stack-scope .dark\\\\:bg-black:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *), .stack-scope.dark\\\\:bg-black:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity));\\n}\\n.stack-scope .dark\\\\:bg-gray-500:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *), .stack-scope.dark\\\\:bg-gray-500:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity));\\n}\\n.stack-scope .dark\\\\:bg-gray-700:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *), .stack-scope.dark\\\\:bg-gray-700:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity));\\n}\\n.stack-scope .dark\\\\:bg-gray-800:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *), .stack-scope.dark\\\\:bg-gray-800:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity));\\n}\\n.stack-scope .dark\\\\:bg-zinc-800:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *), .stack-scope.dark\\\\:bg-zinc-800:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(39 39 42 / var(--tw-bg-opacity));\\n}\\n.stack-scope .dark\\\\:text-foreground:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *), .stack-scope.dark\\\\:text-foreground:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *) {\\n  color: hsl(var(--foreground));\\n}\\n.stack-scope .dark\\\\:text-gray-300:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *), .stack-scope.dark\\\\:text-gray-300:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity));\\n}\\n.stack-scope .dark\\\\:text-gray-400:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *), .stack-scope.dark\\\\:text-gray-400:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity));\\n}\\n.stack-scope .dark\\\\:text-white:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *), .stack-scope.dark\\\\:text-white:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity));\\n}\\n.stack-scope .dark\\\\:text-zinc-300:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *), .stack-scope.dark\\\\:text-zinc-300:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(212 212 216 / var(--tw-text-opacity));\\n}\\n.stack-scope .dark\\\\:text-zinc-400:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *), .stack-scope.dark\\\\:text-zinc-400:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(161 161 170 / var(--tw-text-opacity));\\n}\\n.stack-scope .dark\\\\:text-zinc-800:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *), .stack-scope.dark\\\\:text-zinc-800:where(html:has(head > [data-stack-theme=\\\"dark\\\"]), html:has(head > [data-stack-theme=\\\"dark\\\"]) *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(39 39 42 / var(--tw-text-opacity));\\n}\\n.stack-scope .\\\\[\\\\&\\\\+div\\\\]\\\\:text-xs+div, .stack-scope.\\\\[\\\\&\\\\+div\\\\]\\\\:text-xs+div {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.stack-scope .\\\\[\\\\&\\\\:has\\\\(\\\\>\\\\.day-range-end\\\\)\\\\]\\\\:rounded-r-md:has(>.day-range-end), .stack-scope.\\\\[\\\\&\\\\:has\\\\(\\\\>\\\\.day-range-end\\\\)\\\\]\\\\:rounded-r-md:has(>.day-range-end) {\\n  border-top-right-radius: calc(var(--radius) - 2px);\\n  border-bottom-right-radius: calc(var(--radius) - 2px);\\n}\\n.stack-scope .\\\\[\\\\&\\\\:has\\\\(\\\\>\\\\.day-range-start\\\\)\\\\]\\\\:rounded-l-md:has(>.day-range-start), .stack-scope.\\\\[\\\\&\\\\:has\\\\(\\\\>\\\\.day-range-start\\\\)\\\\]\\\\:rounded-l-md:has(>.day-range-start) {\\n  border-top-left-radius: calc(var(--radius) - 2px);\\n  border-bottom-left-radius: calc(var(--radius) - 2px);\\n}\\n.stack-scope .\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\)\\\\]\\\\:rounded-md:has([aria-selected]), .stack-scope.\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\)\\\\]\\\\:rounded-md:has([aria-selected]) {\\n  border-radius: calc(var(--radius) - 2px);\\n}\\n.stack-scope .\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\)\\\\]\\\\:bg-accent:has([aria-selected]), .stack-scope.\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\)\\\\]\\\\:bg-accent:has([aria-selected]) {\\n  background-color: hsl(var(--accent));\\n}\\n.stack-scope .first\\\\:\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\)\\\\]\\\\:rounded-l-md:has([aria-selected]):first-child, .stack-scope.first\\\\:\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\)\\\\]\\\\:rounded-l-md:has([aria-selected]):first-child {\\n  border-top-left-radius: calc(var(--radius) - 2px);\\n  border-bottom-left-radius: calc(var(--radius) - 2px);\\n}\\n.stack-scope .last\\\\:\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\)\\\\]\\\\:rounded-r-md:has([aria-selected]):last-child, .stack-scope.last\\\\:\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\)\\\\]\\\\:rounded-r-md:has([aria-selected]):last-child {\\n  border-top-right-radius: calc(var(--radius) - 2px);\\n  border-bottom-right-radius: calc(var(--radius) - 2px);\\n}\\n.stack-scope .\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\.day-outside\\\\)\\\\]\\\\:bg-accent\\\\/50:has([aria-selected].day-outside), .stack-scope.\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\.day-outside\\\\)\\\\]\\\\:bg-accent\\\\/50:has([aria-selected].day-outside) {\\n  background-color: hsl(var(--accent) / 0.5);\\n}\\n.stack-scope .\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\.day-range-end\\\\)\\\\]\\\\:rounded-r-md:has([aria-selected].day-range-end), .stack-scope.\\\\[\\\\&\\\\:has\\\\(\\\\[aria-selected\\\\]\\\\.day-range-end\\\\)\\\\]\\\\:rounded-r-md:has([aria-selected].day-range-end) {\\n  border-top-right-radius: calc(var(--radius) - 2px);\\n  border-bottom-right-radius: calc(var(--radius) - 2px);\\n}\\n.stack-scope .\\\\[\\\\&\\\\:has\\\\(\\\\[role\\\\=checkbox\\\\]\\\\)\\\\]\\\\:pr-0:has([role=checkbox]), .stack-scope.\\\\[\\\\&\\\\:has\\\\(\\\\[role\\\\=checkbox\\\\]\\\\)\\\\]\\\\:pr-0:has([role=checkbox]) {\\n  padding-right: 0px;\\n}\\n.stack-scope .\\\\[\\\\&\\\\>\\\\:not\\\\(\\\\.stack-button-do-not-hide-when-siblings-are\\\\)\\\\]\\\\:invisible>:not(.stack-button-do-not-hide-when-siblings-are), .stack-scope.\\\\[\\\\&\\\\>\\\\:not\\\\(\\\\.stack-button-do-not-hide-when-siblings-are\\\\)\\\\]\\\\:invisible>:not(.stack-button-do-not-hide-when-siblings-are) {\\n  visibility: hidden;\\n}\\n.stack-scope .\\\\[\\\\&\\\\>\\\\[role\\\\=checkbox\\\\]\\\\]\\\\:translate-y-\\\\[2px\\\\]>[role=checkbox], .stack-scope.\\\\[\\\\&\\\\>\\\\[role\\\\=checkbox\\\\]\\\\]\\\\:translate-y-\\\\[2px\\\\]>[role=checkbox] {\\n  --tw-translate-y: 2px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.stack-scope .\\\\[\\\\&\\\\>button\\\\]\\\\:hidden>button, .stack-scope.\\\\[\\\\&\\\\>button\\\\]\\\\:hidden>button {\\n  display: none;\\n}\\n.stack-scope .\\\\[\\\\&\\\\>span\\\\]\\\\:line-clamp-1>span, .stack-scope.\\\\[\\\\&\\\\>span\\\\]\\\\:line-clamp-1>span {\\n  overflow: hidden;\\n  display: -webkit-box;\\n  -webkit-box-orient: vertical;\\n  -webkit-line-clamp: 1;\\n}\\n.stack-scope .\\\\[\\\\&\\\\>svg\\\\+div\\\\]\\\\:translate-y-\\\\[-3px\\\\]>svg+div, .stack-scope.\\\\[\\\\&\\\\>svg\\\\+div\\\\]\\\\:translate-y-\\\\[-3px\\\\]>svg+div {\\n  --tw-translate-y: -3px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.stack-scope .\\\\[\\\\&\\\\>svg\\\\]\\\\:absolute>svg, .stack-scope.\\\\[\\\\&\\\\>svg\\\\]\\\\:absolute>svg {\\n  position: absolute;\\n}\\n.stack-scope .\\\\[\\\\&\\\\>svg\\\\]\\\\:left-4>svg, .stack-scope.\\\\[\\\\&\\\\>svg\\\\]\\\\:left-4>svg {\\n  left: 1rem;\\n}\\n.stack-scope .\\\\[\\\\&\\\\>svg\\\\]\\\\:top-4>svg, .stack-scope.\\\\[\\\\&\\\\>svg\\\\]\\\\:top-4>svg {\\n  top: 1rem;\\n}\\n.stack-scope .\\\\[\\\\&\\\\>svg\\\\]\\\\:size-3\\\\.5>svg, .stack-scope.\\\\[\\\\&\\\\>svg\\\\]\\\\:size-3\\\\.5>svg {\\n  width: 0.875rem;\\n  height: 0.875rem;\\n}\\n.stack-scope .\\\\[\\\\&\\\\>svg\\\\]\\\\:text-destructive>svg, .stack-scope.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-destructive>svg {\\n  color: hsl(var(--destructive));\\n}\\n.stack-scope .\\\\[\\\\&\\\\>svg\\\\]\\\\:text-foreground>svg, .stack-scope.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-foreground>svg {\\n  color: hsl(var(--foreground));\\n}\\n.stack-scope .\\\\[\\\\&\\\\>svg\\\\]\\\\:text-success>svg, .stack-scope.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-success>svg {\\n  color: hsl(var(--success));\\n}\\n.stack-scope .\\\\[\\\\&\\\\>svg\\\\~\\\\*\\\\]\\\\:pl-7>svg~*, .stack-scope.\\\\[\\\\&\\\\>svg\\\\~\\\\*\\\\]\\\\:pl-7>svg~* {\\n  padding-left: 1.75rem;\\n}\\n.stack-scope .\\\\[\\\\&\\\\>tr\\\\]\\\\:last\\\\:border-b-0:last-child>tr, .stack-scope.\\\\[\\\\&\\\\>tr\\\\]\\\\:last\\\\:border-b-0:last-child>tr {\\n  border-bottom-width: 0px;\\n}\\n.stack-scope .\\\\[\\\\&\\\\[data-state\\\\=open\\\\]\\\\>svg\\\\]\\\\:rotate-180[data-state=open]>svg, .stack-scope.\\\\[\\\\&\\\\[data-state\\\\=open\\\\]\\\\>svg\\\\]\\\\:rotate-180[data-state=open]>svg {\\n  --tw-rotate: 180deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.stack-scope .\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:px-2 [cmdk-group-heading], .stack-scope.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:px-2 [cmdk-group-heading] {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.stack-scope .\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:py-1\\\\.5 [cmdk-group-heading], .stack-scope.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:py-1\\\\.5 [cmdk-group-heading] {\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\n.stack-scope .\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:text-xs [cmdk-group-heading], .stack-scope.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:text-xs [cmdk-group-heading] {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.stack-scope .\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:font-medium [cmdk-group-heading], .stack-scope.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:font-medium [cmdk-group-heading] {\\n  font-weight: 500;\\n}\\n.stack-scope .\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:text-muted-foreground [cmdk-group-heading], .stack-scope.\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:text-muted-foreground [cmdk-group-heading] {\\n  color: hsl(var(--muted-foreground));\\n}\\n.stack-scope .\\\\[\\\\&_\\\\[cmdk-group\\\\]\\\\:not\\\\(\\\\[hidden\\\\]\\\\)_\\\\~\\\\[cmdk-group\\\\]\\\\]\\\\:pt-0 [cmdk-group]:not([hidden]) ~[cmdk-group], .stack-scope.\\\\[\\\\&_\\\\[cmdk-group\\\\]\\\\:not\\\\(\\\\[hidden\\\\]\\\\)_\\\\~\\\\[cmdk-group\\\\]\\\\]\\\\:pt-0 [cmdk-group]:not([hidden]) ~[cmdk-group] {\\n  padding-top: 0px;\\n}\\n.stack-scope .\\\\[\\\\&_\\\\[cmdk-group\\\\]\\\\]\\\\:px-2 [cmdk-group], .stack-scope.\\\\[\\\\&_\\\\[cmdk-group\\\\]\\\\]\\\\:px-2 [cmdk-group] {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.stack-scope .\\\\[\\\\&_\\\\[cmdk-input-wrapper\\\\]_svg\\\\]\\\\:h-5 [cmdk-input-wrapper] svg, .stack-scope.\\\\[\\\\&_\\\\[cmdk-input-wrapper\\\\]_svg\\\\]\\\\:h-5 [cmdk-input-wrapper] svg {\\n  height: 1.25rem;\\n}\\n.stack-scope .\\\\[\\\\&_\\\\[cmdk-input-wrapper\\\\]_svg\\\\]\\\\:w-5 [cmdk-input-wrapper] svg, .stack-scope.\\\\[\\\\&_\\\\[cmdk-input-wrapper\\\\]_svg\\\\]\\\\:w-5 [cmdk-input-wrapper] svg {\\n  width: 1.25rem;\\n}\\n.stack-scope .\\\\[\\\\&_\\\\[cmdk-input\\\\]\\\\]\\\\:h-12 [cmdk-input], .stack-scope.\\\\[\\\\&_\\\\[cmdk-input\\\\]\\\\]\\\\:h-12 [cmdk-input] {\\n  height: 3rem;\\n}\\n.stack-scope .\\\\[\\\\&_\\\\[cmdk-item\\\\]\\\\]\\\\:px-2 [cmdk-item], .stack-scope.\\\\[\\\\&_\\\\[cmdk-item\\\\]\\\\]\\\\:px-2 [cmdk-item] {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.stack-scope .\\\\[\\\\&_\\\\[cmdk-item\\\\]\\\\]\\\\:py-3 [cmdk-item], .stack-scope.\\\\[\\\\&_\\\\[cmdk-item\\\\]\\\\]\\\\:py-3 [cmdk-item] {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.stack-scope .\\\\[\\\\&_\\\\[cmdk-item\\\\]_svg\\\\]\\\\:h-5 [cmdk-item] svg, .stack-scope.\\\\[\\\\&_\\\\[cmdk-item\\\\]_svg\\\\]\\\\:h-5 [cmdk-item] svg {\\n  height: 1.25rem;\\n}\\n.stack-scope .\\\\[\\\\&_\\\\[cmdk-item\\\\]_svg\\\\]\\\\:w-5 [cmdk-item] svg, .stack-scope.\\\\[\\\\&_\\\\[cmdk-item\\\\]_svg\\\\]\\\\:w-5 [cmdk-item] svg {\\n  width: 1.25rem;\\n}\\n.stack-scope .\\\\[\\\\&_p\\\\]\\\\:leading-relaxed p, .stack-scope.\\\\[\\\\&_p\\\\]\\\\:leading-relaxed p {\\n  line-height: 1.625;\\n}\\n.stack-scope .\\\\[\\\\&_svg\\\\]\\\\:invisible svg, .stack-scope.\\\\[\\\\&_svg\\\\]\\\\:invisible svg {\\n  visibility: hidden;\\n}\\n.stack-scope .\\\\[\\\\&_tr\\\\:last-child\\\\]\\\\:border-0 tr:last-child, .stack-scope.\\\\[\\\\&_tr\\\\:last-child\\\\]\\\\:border-0 tr:last-child {\\n  border-width: 0px;\\n}\\n.stack-scope .\\\\[\\\\&_tr\\\\]\\\\:border-b tr, .stack-scope.\\\\[\\\\&_tr\\\\]\\\\:border-b tr {\\n  border-bottom-width: 1px;\\n}\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,IAAM,YAAY;", "names": []}