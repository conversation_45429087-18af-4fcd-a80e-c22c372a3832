{"version": 3, "sources": ["../../../src/components/passkey-button.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { Button } from '@stackframe/stack-ui';\nimport { useId } from 'react';\nimport { useStackApp } from '..';\nimport { useTranslation } from '../lib/translations';\nimport { KeyRound } from 'lucide-react';\n\n\nexport function PasskeyButton({\n  type,\n}: {\n  type: 'sign-in' | 'sign-up',\n}) {\n  const { t } = useTranslation();\n  const stackApp = useStackApp();\n  const styleId = useId().replaceAll(':', '-');\n\n\n  return (\n    <>\n      <Button\n        onClick={async () => { await stackApp.signInWithPasskey(); }}\n        className={`stack-oauth-button-${styleId} stack-scope`}\n      >\n        <div className='flex items-center w-full gap-4'>\n          <KeyRound />\n          <span className='flex-1'>\n            {type === 'sign-up' ?\n              t('Sign up with Passkey') :\n              t('Sign in with Passkey')\n            }\n          </span>\n        </div>\n      </Button>\n    </>\n  );\n}\n"], "mappings": ";;;AAOA,SAAS,cAAc;AACvB,SAAS,aAAa;AACtB,SAAS,mBAAmB;AAC5B,SAAS,sBAAsB;AAC/B,SAAS,gBAAgB;AAcrB,mBAMM,KADF,YALJ;AAXG,SAAS,cAAc;AAAA,EAC5B;AACF,GAEG;AACD,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,WAAW,YAAY;AAC7B,QAAM,UAAU,MAAM,EAAE,WAAW,KAAK,GAAG;AAG3C,SACE,gCACE;AAAA,IAAC;AAAA;AAAA,MACC,SAAS,YAAY;AAAE,cAAM,SAAS,kBAAkB;AAAA,MAAG;AAAA,MAC3D,WAAW,sBAAsB,OAAO;AAAA,MAExC,+BAAC,SAAI,WAAU,kCACb;AAAA,4BAAC,YAAS;AAAA,QACV,oBAAC,UAAK,WAAU,UACb,mBAAS,YACR,EAAE,sBAAsB,IACxB,EAAE,sBAAsB,GAE5B;AAAA,SACF;AAAA;AAAA,EACF,GACF;AAEJ;", "names": []}