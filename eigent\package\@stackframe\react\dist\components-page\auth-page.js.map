{"version": 3, "sources": ["../../src/components-page/auth-page.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { runAsynchronously } from '@stackframe/stack-shared/dist/utils/promises';\nimport { Skeleton, Tabs, TabsContent, TabsList, TabsTrigger, Typography, cn } from '@stackframe/stack-ui';\nimport { Suspense, useEffect } from 'react';\nimport { useStackApp, useUser } from '..';\nimport { CredentialSignIn } from '../components/credential-sign-in';\nimport { CredentialSignUp } from '../components/credential-sign-up';\nimport { MaybeFullPage } from '../components/elements/maybe-full-page';\nimport { SeparatorWithText } from '../components/elements/separator-with-text';\nimport { StyledLink } from '../components/link';\nimport { MagicLinkSignIn } from '../components/magic-link-sign-in';\nimport { PredefinedMessageCard } from '../components/message-cards/predefined-message-card';\nimport { OAuthButtonGroup } from '../components/oauth-button-group';\nimport { PasskeyButton } from '../components/passkey-button';\nimport { useTranslation } from '../lib/translations';\n\ntype Props = {\n  noPasswordRepeat?: boolean,\n  firstTab?: 'magic-link' | 'password',\n  fullPage?: boolean,\n  type: 'sign-in' | 'sign-up',\n  automaticRedirect?: boolean,\n  extraInfo?: React.ReactNode,\n  mockProject?: {\n    config: {\n      signUpEnabled: boolean,\n      credentialEnabled: boolean,\n      passkeyEnabled: boolean,\n      magicLinkEnabled: boolean,\n      oauthProviders: {\n        id: string,\n      }[],\n    },\n  },\n}\n\nexport function AuthPage(props: Props) {\n  return <Suspense fallback={<Fallback {...props} />}>\n    <Inner {...props} />\n  </Suspense>;\n}\n\nfunction Fallback(props: Props) {\n  const { t } = useTranslation();\n\n  return (\n    <MaybeFullPage fullPage={!!props.fullPage}>\n      <div className='stack-scope flex flex-col items-stretch' style={{ maxWidth: '380px', flexBasis: '380px', padding: props.fullPage ? '1rem' : 0 }}>\n        <div className=\"text-center mb-6 flex flex-col\">\n          <Skeleton className='h-9 w-2/3 self-center' />\n\n          <Skeleton className='h-3 w-16 mt-8' />\n          <Skeleton className='h-9 w-full mt-1' />\n\n          <Skeleton className='h-3 w-24 mt-2' />\n          <Skeleton className='h-9 w-full mt-1' />\n\n          <Skeleton className='h-9 w-full mt-6' />\n        </div>\n      </div>\n    </MaybeFullPage>\n  );\n}\n\nfunction Inner(props: Props) {\n  const stackApp = useStackApp();\n  const user = useUser();\n  const projectFromHook = stackApp.useProject();\n  const project = props.mockProject || projectFromHook;\n  const { t } = useTranslation();\n\n  useEffect(() => {\n    if (props.automaticRedirect && user && !props.mockProject) {\n      runAsynchronously(props.type === 'sign-in'\n        ? stackApp.redirectToAfterSignIn({ replace: true })\n        : stackApp.redirectToAfterSignUp({ replace: true })\n      );\n    }\n  }, [user, props.mockProject, stackApp, props.automaticRedirect]);\n\n  if (user && !props.mockProject && !props.automaticRedirect) {\n    return <PredefinedMessageCard type='signedIn' fullPage={props.fullPage} />;\n  }\n\n  if (props.type === 'sign-up' && !project.config.signUpEnabled) {\n    return <PredefinedMessageCard type='signUpDisabled' fullPage={props.fullPage} />;\n  }\n\n  const hasOAuthProviders = project.config.oauthProviders.length > 0;\n  const hasPasskey = (project.config.passkeyEnabled === true && props.type === \"sign-in\");\n  const enableSeparator = (project.config.credentialEnabled || project.config.magicLinkEnabled) && (hasOAuthProviders || hasPasskey);\n\n  return (\n    <MaybeFullPage fullPage={!!props.fullPage}>\n      <div className='stack-scope flex flex-col items-stretch' style={{ maxWidth: '380px', flexBasis: '380px', padding: props.fullPage ? '1rem' : 0 }}>\n        <div className=\"text-center mb-6\">\n          <Typography type='h2'>\n            {props.type === 'sign-in' ? t(\"Sign in to your account\") : t(\"Create a new account\")}\n          </Typography>\n          {props.type === 'sign-in' ? (\n            project.config.signUpEnabled && (\n              <Typography>\n                {t(\"Don't have an account?\")}{\" \"}\n                <StyledLink href={stackApp.urls.signUp} onClick={(e) => {\n                  runAsynchronously(stackApp.redirectToSignUp());\n                  e.preventDefault();\n                }}>{t(\"Sign up\")}</StyledLink>\n              </Typography>\n            )\n          ) : (\n            <Typography>\n              {t(\"Already have an account?\")}{\" \"}\n              <StyledLink href={stackApp.urls.signIn} onClick={(e) => {\n                runAsynchronously(stackApp.redirectToSignIn());\n                e.preventDefault();\n              }}>{t(\"Sign in\")}</StyledLink>\n            </Typography>\n          )}\n        </div>\n        {(hasOAuthProviders || hasPasskey) && (\n          <div className='gap-4 flex flex-col items-stretch stack-scope'>\n            {hasOAuthProviders && <OAuthButtonGroup type={props.type} mockProject={props.mockProject} />}\n            {hasPasskey && <PasskeyButton type={props.type} />}\n          </div>\n        )}\n\n        {enableSeparator && <SeparatorWithText text={t('Or continue with')} />}\n        {project.config.credentialEnabled && project.config.magicLinkEnabled ? (\n          <Tabs defaultValue={props.firstTab || 'magic-link'}>\n            <TabsList className={cn('w-full mb-2', {\n              'flex-row-reverse': props.firstTab === 'password'\n            })}>\n              <TabsTrigger value='magic-link' className='flex-1'>{t(\"Email\")}</TabsTrigger>\n              <TabsTrigger value='password' className='flex-1'>{t(\"Email & Password\")}</TabsTrigger>\n            </TabsList>\n            <TabsContent value='magic-link'>\n              <MagicLinkSignIn />\n            </TabsContent>\n            <TabsContent value='password'>\n              {props.type === 'sign-up' ? <CredentialSignUp noPasswordRepeat={props.noPasswordRepeat} /> : <CredentialSignIn />}\n            </TabsContent>\n          </Tabs>\n        ) : project.config.credentialEnabled ? (\n          props.type === 'sign-up' ? <CredentialSignUp noPasswordRepeat={props.noPasswordRepeat} /> : <CredentialSignIn />\n        ) : project.config.magicLinkEnabled ? (\n          <MagicLinkSignIn />\n        ) : !(hasOAuthProviders || hasPasskey) ? <Typography variant={\"destructive\"} className=\"text-center\">{t(\"No authentication method enabled.\")}</Typography> : null}\n        {props.extraInfo && (\n          <div className={cn('flex flex-col items-center text-center text-sm text-gray-500', {\n            'mt-2': project.config.credentialEnabled || project.config.magicLinkEnabled,\n            'mt-6': !(project.config.credentialEnabled || project.config.magicLinkEnabled),\n          })}>\n            <div>{props.extraInfo}</div>\n          </div>\n        )}\n      </div>\n    </MaybeFullPage>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,sBAAkC;AAClC,sBAAmF;AACnF,mBAAoC;AACpC,eAAqC;AACrC,gCAAiC;AACjC,gCAAiC;AACjC,6BAA8B;AAC9B,iCAAkC;AAClC,kBAA2B;AAC3B,gCAAgC;AAChC,qCAAsC;AACtC,gCAAiC;AACjC,4BAA8B;AAC9B,0BAA+B;AAuBF;AADtB,SAAS,SAAS,OAAc;AACrC,SAAO,4CAAC,yBAAS,UAAU,4CAAC,YAAU,GAAG,OAAO,GAC9C,sDAAC,SAAO,GAAG,OAAO,GACpB;AACF;AAEA,SAAS,SAAS,OAAc;AAC9B,QAAM,EAAE,EAAE,QAAI,oCAAe;AAE7B,SACE,4CAAC,wCAAc,UAAU,CAAC,CAAC,MAAM,UAC/B,sDAAC,SAAI,WAAU,2CAA0C,OAAO,EAAE,UAAU,SAAS,WAAW,SAAS,SAAS,MAAM,WAAW,SAAS,EAAE,GAC5I,uDAAC,SAAI,WAAU,kCACb;AAAA,gDAAC,4BAAS,WAAU,yBAAwB;AAAA,IAE5C,4CAAC,4BAAS,WAAU,iBAAgB;AAAA,IACpC,4CAAC,4BAAS,WAAU,mBAAkB;AAAA,IAEtC,4CAAC,4BAAS,WAAU,iBAAgB;AAAA,IACpC,4CAAC,4BAAS,WAAU,mBAAkB;AAAA,IAEtC,4CAAC,4BAAS,WAAU,mBAAkB;AAAA,KACxC,GACF,GACF;AAEJ;AAEA,SAAS,MAAM,OAAc;AAC3B,QAAM,eAAW,sBAAY;AAC7B,QAAM,WAAO,kBAAQ;AACrB,QAAM,kBAAkB,SAAS,WAAW;AAC5C,QAAM,UAAU,MAAM,eAAe;AACrC,QAAM,EAAE,EAAE,QAAI,oCAAe;AAE7B,8BAAU,MAAM;AACd,QAAI,MAAM,qBAAqB,QAAQ,CAAC,MAAM,aAAa;AACzD;AAAA,QAAkB,MAAM,SAAS,YAC7B,SAAS,sBAAsB,EAAE,SAAS,KAAK,CAAC,IAChD,SAAS,sBAAsB,EAAE,SAAS,KAAK,CAAC;AAAA,MACpD;AAAA,IACF;AAAA,EACF,GAAG,CAAC,MAAM,MAAM,aAAa,UAAU,MAAM,iBAAiB,CAAC;AAE/D,MAAI,QAAQ,CAAC,MAAM,eAAe,CAAC,MAAM,mBAAmB;AAC1D,WAAO,4CAAC,wDAAsB,MAAK,YAAW,UAAU,MAAM,UAAU;AAAA,EAC1E;AAEA,MAAI,MAAM,SAAS,aAAa,CAAC,QAAQ,OAAO,eAAe;AAC7D,WAAO,4CAAC,wDAAsB,MAAK,kBAAiB,UAAU,MAAM,UAAU;AAAA,EAChF;AAEA,QAAM,oBAAoB,QAAQ,OAAO,eAAe,SAAS;AACjE,QAAM,aAAc,QAAQ,OAAO,mBAAmB,QAAQ,MAAM,SAAS;AAC7E,QAAM,mBAAmB,QAAQ,OAAO,qBAAqB,QAAQ,OAAO,sBAAsB,qBAAqB;AAEvH,SACE,4CAAC,wCAAc,UAAU,CAAC,CAAC,MAAM,UAC/B,uDAAC,SAAI,WAAU,2CAA0C,OAAO,EAAE,UAAU,SAAS,WAAW,SAAS,SAAS,MAAM,WAAW,SAAS,EAAE,GAC5I;AAAA,iDAAC,SAAI,WAAU,oBACb;AAAA,kDAAC,8BAAW,MAAK,MACd,gBAAM,SAAS,YAAY,EAAE,yBAAyB,IAAI,EAAE,sBAAsB,GACrF;AAAA,MACC,MAAM,SAAS,YACd,QAAQ,OAAO,iBACb,6CAAC,8BACE;AAAA,UAAE,wBAAwB;AAAA,QAAG;AAAA,QAC9B,4CAAC,0BAAW,MAAM,SAAS,KAAK,QAAQ,SAAS,CAAC,MAAM;AACtD,iDAAkB,SAAS,iBAAiB,CAAC;AAC7C,YAAE,eAAe;AAAA,QACnB,GAAI,YAAE,SAAS,GAAE;AAAA,SACnB,IAGF,6CAAC,8BACE;AAAA,UAAE,0BAA0B;AAAA,QAAG;AAAA,QAChC,4CAAC,0BAAW,MAAM,SAAS,KAAK,QAAQ,SAAS,CAAC,MAAM;AACtD,iDAAkB,SAAS,iBAAiB,CAAC;AAC7C,YAAE,eAAe;AAAA,QACnB,GAAI,YAAE,SAAS,GAAE;AAAA,SACnB;AAAA,OAEJ;AAAA,KACE,qBAAqB,eACrB,6CAAC,SAAI,WAAU,iDACZ;AAAA,2BAAqB,4CAAC,8CAAiB,MAAM,MAAM,MAAM,aAAa,MAAM,aAAa;AAAA,MACzF,cAAc,4CAAC,uCAAc,MAAM,MAAM,MAAM;AAAA,OAClD;AAAA,IAGD,mBAAmB,4CAAC,gDAAkB,MAAM,EAAE,kBAAkB,GAAG;AAAA,IACnE,QAAQ,OAAO,qBAAqB,QAAQ,OAAO,mBAClD,6CAAC,wBAAK,cAAc,MAAM,YAAY,cACpC;AAAA,mDAAC,4BAAS,eAAW,oBAAG,eAAe;AAAA,QACrC,oBAAoB,MAAM,aAAa;AAAA,MACzC,CAAC,GACC;AAAA,oDAAC,+BAAY,OAAM,cAAa,WAAU,UAAU,YAAE,OAAO,GAAE;AAAA,QAC/D,4CAAC,+BAAY,OAAM,YAAW,WAAU,UAAU,YAAE,kBAAkB,GAAE;AAAA,SAC1E;AAAA,MACA,4CAAC,+BAAY,OAAM,cACjB,sDAAC,6CAAgB,GACnB;AAAA,MACA,4CAAC,+BAAY,OAAM,YAChB,gBAAM,SAAS,YAAY,4CAAC,8CAAiB,kBAAkB,MAAM,kBAAkB,IAAK,4CAAC,8CAAiB,GACjH;AAAA,OACF,IACE,QAAQ,OAAO,oBACjB,MAAM,SAAS,YAAY,4CAAC,8CAAiB,kBAAkB,MAAM,kBAAkB,IAAK,4CAAC,8CAAiB,IAC5G,QAAQ,OAAO,mBACjB,4CAAC,6CAAgB,IACf,EAAE,qBAAqB,cAAc,4CAAC,8BAAW,SAAS,eAAe,WAAU,eAAe,YAAE,mCAAmC,GAAE,IAAgB;AAAA,IAC5J,MAAM,aACL,4CAAC,SAAI,eAAW,oBAAG,gEAAgE;AAAA,MACjF,QAAQ,QAAQ,OAAO,qBAAqB,QAAQ,OAAO;AAAA,MAC3D,QAAQ,EAAE,QAAQ,OAAO,qBAAqB,QAAQ,OAAO;AAAA,IAC/D,CAAC,GACC,sDAAC,SAAK,gBAAM,WAAU,GACxB;AAAA,KAEJ,GACF;AAEJ;", "names": []}