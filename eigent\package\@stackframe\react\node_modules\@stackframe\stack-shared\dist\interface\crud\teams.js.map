{"version": 3, "sources": ["../../../src/interface/crud/teams.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport * as fieldSchema from \"../../schema-fields\";\nimport { yupObject } from \"../../schema-fields\";\nimport { WebhookEvent } from \"../webhooks\";\n\n// Read\nexport const teamsCrudClientReadSchema = yupObject({\n  id: fieldSchema.teamIdSchema.defined(),\n  display_name: fieldSchema.teamDisplayNameSchema.defined(),\n  profile_image_url: fieldSchema.teamProfileImageUrlSchema.nullable().defined(),\n  client_metadata: fieldSchema.teamClientMetadataSchema.optional(),\n  client_read_only_metadata: fieldSchema.teamClientReadOnlyMetadataSchema.optional(),\n}).defined();\nexport const teamsCrudServerReadSchema = teamsCrudClientReadSchema.concat(yupObject({\n  created_at_millis: fieldSchema.teamCreatedAtMillisSchema.defined(),\n  server_metadata: fieldSchema.teamServerMetadataSchema.optional(),\n}).defined());\n\n// Update\nexport const teamsCrudClientUpdateSchema = yupObject({\n  display_name: fieldSchema.teamDisplayNameSchema.optional(),\n  profile_image_url: fieldSchema.teamProfileImageUrlSchema.nullable().optional(),\n  client_metadata: fieldSchema.teamClientMetadataSchema.optional(),\n}).defined();\nexport const teamsCrudServerUpdateSchema = teamsCrudClientUpdateSchema.concat(yupObject({\n  client_read_only_metadata: fieldSchema.teamClientReadOnlyMetadataSchema.optional(),\n  server_metadata: fieldSchema.teamServerMetadataSchema.optional(),\n}).defined());\n\n// Create\nexport const teamsCrudClientCreateSchema = teamsCrudClientUpdateSchema.concat(yupObject({\n  display_name: fieldSchema.teamDisplayNameSchema.defined(),\n  creator_user_id: fieldSchema.teamCreatorUserIdSchema.optional(),\n}).defined());\nexport const teamsCrudServerCreateSchema = teamsCrudServerUpdateSchema.concat(yupObject({\n  display_name: fieldSchema.teamDisplayNameSchema.defined(),\n  creator_user_id: fieldSchema.teamCreatorUserIdSchema.optional(),\n}).defined());\n\n// Delete\nexport const teamsCrudClientDeleteSchema = fieldSchema.yupMixed();\nexport const teamsCrudServerDeleteSchema = teamsCrudClientDeleteSchema;\n\nexport const teamsCrud = createCrud({\n  // Client\n  clientReadSchema: teamsCrudClientReadSchema,\n  clientUpdateSchema: teamsCrudClientUpdateSchema,\n  clientCreateSchema: teamsCrudClientCreateSchema,\n  clientDeleteSchema: teamsCrudClientDeleteSchema,\n  // Server\n  serverReadSchema: teamsCrudServerReadSchema,\n  serverUpdateSchema: teamsCrudServerUpdateSchema,\n  serverCreateSchema: teamsCrudServerCreateSchema,\n  serverDeleteSchema: teamsCrudServerDeleteSchema,\n  docs: {\n    clientList: {\n      summary: \"List teams\",\n      description: \"List all the teams that the current user is a member of. `user_id=me` must be passed in the query parameters.\",\n      tags: [\"Teams\"],\n    },\n    clientCreate: {\n      summary: \"Create a team\",\n      description: \"Create a new team and optionally add the current user as a member.\",\n      tags: [\"Teams\"],\n    },\n    clientRead: {\n      summary: \"Get a team\",\n      description: \"Get a team that the current user is a member of.\",\n      tags: [\"Teams\"],\n    },\n    clientUpdate: {\n      summary: \"Update a team\",\n      description: \"Update the team information. Only allowed if the current user is a member of the team and has the `$update_team` permission.\",\n      tags: [\"Teams\"],\n    },\n    clientDelete: {\n      summary: \"Delete a team\",\n      description: \"Delete a team. Only allowed if the current user is a member of the team and has the `$delete_team` permission.\",\n      tags: [\"Teams\"],\n    },\n    serverCreate: {\n      summary: \"Create a team\",\n      description: \"Create a new team and optionally add the current user as a member.\",\n      tags: [\"Teams\"],\n    },\n    serverList: {\n      summary: \"List teams\",\n      description: \"List all the teams in the project.\",\n      tags: [\"Teams\"],\n    },\n    serverRead: {\n      summary: \"Get a team\",\n      description: \"Get a team by ID.\",\n      tags: [\"Teams\"],\n    },\n    serverUpdate: {\n      summary: \"Update a team\",\n      description: \"Update the team information by ID.\",\n      tags: [\"Teams\"],\n    },\n    serverDelete: {\n      summary: \"Delete a team\",\n      description: \"Delete a team by ID.\",\n      tags: [\"Teams\"],\n    },\n  },\n});\nexport type TeamsCrud = CrudTypeOf<typeof teamsCrud>;\n\nexport const teamCreatedWebhookEvent = {\n  type: \"team.created\",\n  schema: teamsCrud.server.readSchema,\n  metadata: {\n    summary: \"Team Created\",\n    description: \"This event is triggered when a team is created.\",\n    tags: [\"Teams\"],\n  },\n} satisfies WebhookEvent<typeof teamsCrud.server.readSchema>;\n\nexport const teamUpdatedWebhookEvent = {\n  type: \"team.updated\",\n  schema: teamsCrud.server.readSchema,\n  metadata: {\n    summary: \"Team Updated\",\n    description: \"This event is triggered when a team is updated.\",\n    tags: [\"Teams\"],\n  },\n} satisfies WebhookEvent<typeof teamsCrud.server.readSchema>;\n\nconst webhookTeamDeletedSchema = fieldSchema.yupObject({\n  id: fieldSchema.userIdSchema.defined(),\n}).defined();\n\nexport const teamDeletedWebhookEvent = {\n  type: \"team.deleted\",\n  schema: webhookTeamDeletedSchema,\n  metadata: {\n    summary: \"Team Deleted\",\n    description: \"This event is triggered when a team is deleted.\",\n    tags: [\"Teams\"],\n  },\n} satisfies WebhookEvent<typeof webhookTeamDeletedSchema>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAuC;AACvC,kBAA6B;AAC7B,2BAA0B;AAInB,IAAM,gCAA4B,gCAAU;AAAA,EACjD,IAAgB,yBAAa,QAAQ;AAAA,EACrC,cAA0B,kCAAsB,QAAQ;AAAA,EACxD,mBAA+B,sCAA0B,SAAS,EAAE,QAAQ;AAAA,EAC5E,iBAA6B,qCAAyB,SAAS;AAAA,EAC/D,2BAAuC,6CAAiC,SAAS;AACnF,CAAC,EAAE,QAAQ;AACJ,IAAM,4BAA4B,0BAA0B,WAAO,gCAAU;AAAA,EAClF,mBAA+B,sCAA0B,QAAQ;AAAA,EACjE,iBAA6B,qCAAyB,SAAS;AACjE,CAAC,EAAE,QAAQ,CAAC;AAGL,IAAM,kCAA8B,gCAAU;AAAA,EACnD,cAA0B,kCAAsB,SAAS;AAAA,EACzD,mBAA+B,sCAA0B,SAAS,EAAE,SAAS;AAAA,EAC7E,iBAA6B,qCAAyB,SAAS;AACjE,CAAC,EAAE,QAAQ;AACJ,IAAM,8BAA8B,4BAA4B,WAAO,gCAAU;AAAA,EACtF,2BAAuC,6CAAiC,SAAS;AAAA,EACjF,iBAA6B,qCAAyB,SAAS;AACjE,CAAC,EAAE,QAAQ,CAAC;AAGL,IAAM,8BAA8B,4BAA4B,WAAO,gCAAU;AAAA,EACtF,cAA0B,kCAAsB,QAAQ;AAAA,EACxD,iBAA6B,oCAAwB,SAAS;AAChE,CAAC,EAAE,QAAQ,CAAC;AACL,IAAM,8BAA8B,4BAA4B,WAAO,gCAAU;AAAA,EACtF,cAA0B,kCAAsB,QAAQ;AAAA,EACxD,iBAA6B,oCAAwB,SAAS;AAChE,CAAC,EAAE,QAAQ,CAAC;AAGL,IAAM,8BAA0C,qBAAS;AACzD,IAAM,8BAA8B;AAEpC,IAAM,gBAAY,wBAAW;AAAA;AAAA,EAElC,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,MAAM;AAAA,IACJ,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,EACF;AACF,CAAC;AAGM,IAAM,0BAA0B;AAAA,EACrC,MAAM;AAAA,EACN,QAAQ,UAAU,OAAO;AAAA,EACzB,UAAU;AAAA,IACR,SAAS;AAAA,IACT,aAAa;AAAA,IACb,MAAM,CAAC,OAAO;AAAA,EAChB;AACF;AAEO,IAAM,0BAA0B;AAAA,EACrC,MAAM;AAAA,EACN,QAAQ,UAAU,OAAO;AAAA,EACzB,UAAU;AAAA,IACR,SAAS;AAAA,IACT,aAAa;AAAA,IACb,MAAM,CAAC,OAAO;AAAA,EAChB;AACF;AAEA,IAAM,2BAAuC,sBAAU;AAAA,EACrD,IAAgB,yBAAa,QAAQ;AACvC,CAAC,EAAE,QAAQ;AAEJ,IAAM,0BAA0B;AAAA,EACrC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,UAAU;AAAA,IACR,SAAS;AAAA,IACT,aAAa;AAAA,IACb,MAAM,CAAC,OAAO;AAAA,EAChB;AACF;", "names": []}