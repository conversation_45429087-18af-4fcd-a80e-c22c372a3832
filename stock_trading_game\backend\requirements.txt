# Web框架和API
fastapi==0.104.1
uvicorn[standard]==0.24.0
websockets==12.0

# 数据库相关
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# Redis和缓存
redis==5.0.1
aioredis==2.0.1

# 异步任务处理
celery==5.3.4
flower==2.0.1

# 数据处理和科学计算
pandas==2.1.3
numpy==1.25.2
scipy==1.11.4

# 金融数据和技术指标
yfinance==0.2.28
ta-lib==0.4.28
talib-binary==0.4.19

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 配置管理
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1

# 日志和监控
loguru==0.7.2
prometheus-client==0.19.0

# 测试框架
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# 代码质量工具
black==23.11.0
isort==5.12.0
flake8==6.1.0
pylint==3.0.3
mypy==1.7.1

# 开发工具
pre-commit==3.6.0
bandit==1.7.5

# 时间处理
python-dateutil==2.8.2
pytz==2023.3

# JSON和序列化
orjson==3.9.10
ujson==5.8.0

# 环境变量和配置
environs==10.0.0

# 数据验证
marshmallow==3.20.1

# 图像处理（用于生成图表）
pillow==10.1.0
matplotlib==3.8.2
plotly==5.17.0

# 机器学习（用于AI交易策略）
scikit-learn==1.3.2
joblib==1.3.2

# 随机数生成
faker==20.1.0

# 文档生成
mkdocs==1.5.3
mkdocs-material==9.4.8
