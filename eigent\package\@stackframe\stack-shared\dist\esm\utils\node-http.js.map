{"version": 3, "sources": ["../../../src/utils/node-http.tsx"], "sourcesContent": ["import { IncomingMessage, ServerResponse } from \"http\";\nimport { getRelativePart } from \"./urls\";\n\nclass ServerResponseWithBodyChunks extends ServerResponse {\n  bodyChunks: Uint8Array[] = [];\n\n  // note: we actually override this, even though it's private in the parent\n  _send(data: string, encoding: BufferEncoding, callback?: (() => void) | null, byteLength?: number) {\n    if (typeof encoding === \"function\") {\n      callback = encoding;\n      encoding = \"utf-8\";\n    }\n    const encodedBuffer = new Uint8Array(Buffer.from(data, encoding));\n    this.bodyChunks.push(encodedBuffer);\n    callback?.();\n  }\n}\n\nexport async function createNodeHttpServerDuplex(options: {\n  method: string,\n  originalUrl?: URL,\n  url: URL,\n  headers: Headers,\n  body: Uint8Array,\n}): Promise<[IncomingMessage, ServerResponseWithBodyChunks]> {\n  // See https://github.com/nodejs/node/blob/main/lib/_http_incoming.js\n  // and https://github.com/nodejs/node/blob/main/lib/_http_common.js (particularly the `parserXyz` functions)\n\n  const incomingMessage = new IncomingMessage({\n    encrypted: options.originalUrl?.protocol === \"https:\",  // trick frameworks into believing this is an HTTPS request\n  } as any);\n  incomingMessage.httpVersionMajor = 1;\n  incomingMessage.httpVersionMinor = 1;\n  incomingMessage.httpVersion = '1.1';\n  incomingMessage.method = options.method;\n  incomingMessage.url = getRelativePart(options.url);\n  (incomingMessage as any).originalUrl = options.originalUrl && getRelativePart(options.originalUrl);  // originalUrl is an extension used by some servers; for example, oidc-provider reads it to construct the paths for the .well-known/openid-configuration\n  const rawHeaders = [...options.headers.entries()].flat();\n  (incomingMessage as any)._addHeaderLines(rawHeaders, rawHeaders.length);\n  incomingMessage.push(Buffer.from(options.body));\n  incomingMessage.complete = true;\n  incomingMessage.push(null);  // to emit end event, see: https://github.com/nodejs/node/blob/4cf6fabce20eb3050c5b543d249e931ea3d3cad5/lib/_http_common.js#L150\n\n  const serverResponse = new ServerResponseWithBodyChunks(incomingMessage);\n\n  return [incomingMessage, serverResponse];\n}\n"], "mappings": ";AAAA,SAAS,iBAAiB,sBAAsB;AAChD,SAAS,uBAAuB;AAEhC,IAAM,+BAAN,cAA2C,eAAe;AAAA,EAA1D;AAAA;AACE,sBAA2B,CAAC;AAAA;AAAA;AAAA,EAG5B,MAAM,MAAc,UAA0B,UAAgC,YAAqB;AACjG,QAAI,OAAO,aAAa,YAAY;AAClC,iBAAW;AACX,iBAAW;AAAA,IACb;AACA,UAAM,gBAAgB,IAAI,WAAW,OAAO,KAAK,MAAM,QAAQ,CAAC;AAChE,SAAK,WAAW,KAAK,aAAa;AAClC,eAAW;AAAA,EACb;AACF;AAEA,eAAsB,2BAA2B,SAMY;AAI3D,QAAM,kBAAkB,IAAI,gBAAgB;AAAA,IAC1C,WAAW,QAAQ,aAAa,aAAa;AAAA;AAAA,EAC/C,CAAQ;AACR,kBAAgB,mBAAmB;AACnC,kBAAgB,mBAAmB;AACnC,kBAAgB,cAAc;AAC9B,kBAAgB,SAAS,QAAQ;AACjC,kBAAgB,MAAM,gBAAgB,QAAQ,GAAG;AACjD,EAAC,gBAAwB,cAAc,QAAQ,eAAe,gBAAgB,QAAQ,WAAW;AACjG,QAAM,aAAa,CAAC,GAAG,QAAQ,QAAQ,QAAQ,CAAC,EAAE,KAAK;AACvD,EAAC,gBAAwB,gBAAgB,YAAY,WAAW,MAAM;AACtE,kBAAgB,KAAK,OAAO,KAAK,QAAQ,IAAI,CAAC;AAC9C,kBAAgB,WAAW;AAC3B,kBAAgB,KAAK,IAAI;AAEzB,QAAM,iBAAiB,IAAI,6BAA6B,eAAe;AAEvE,SAAO,CAAC,iBAAiB,cAAc;AACzC;", "names": []}