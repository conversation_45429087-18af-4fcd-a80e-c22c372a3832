import csv
import json
from datetime import datetime, timedelta
import sys
import requests
from dateutil.relativedelta import relativedelta

'''
# 判断当前日期是否晚于2025年1月20号，如果是的话，程序直接结束运行
current_date = datetime.now()
end_date = datetime(2025, 1, 20)
if current_date > end_date:
    sys.exit("当前日期晚于2025年1月20号，程序结束。")
'''

# 格式：20230601，用于fdp的开始和结束时间，date1为上上月，date2为上月
current_date = datetime.now()
previous_month_first_day = (current_date.replace(day=1) - timedelta(days=1)).replace(day=1)
previous_month_last_day = current_date.replace(day=1) - timedelta(days=1)
previous_previous_month_first_day = (previous_month_first_day.replace(day=1) - timedelta(days=1)).replace(day=1)
previous_previous_month_last_day = previous_month_first_day - timedelta(days=1)

date1_begin = previous_previous_month_first_day.strftime("%Y%m%d")
date1_end = previous_previous_month_last_day.strftime("%Y%m%d")
date2_begin = previous_month_first_day.strftime("%Y%m%d")
date2_end = previous_month_last_day.strftime("%Y%m%d")

# 用于fdo、bi的开始结束时间，begin1为上上月，begin2为上月；格式依次为:时间戳，2023-06，2023-06-01T00:00:00+08:00
begin1 = int(previous_previous_month_first_day.replace(hour=0, minute=0, second=0).timestamp())
end1 = int(previous_previous_month_last_day.replace(hour=23, minute=59, second=59).timestamp())
begin2 = int(previous_month_first_day.replace(hour=0, minute=0, second=0).timestamp())
end2 = int(previous_month_last_day.replace(hour=23, minute=59, second=59).timestamp())

begMonth = str(previous_previous_month_first_day.year) + '-' + str(previous_previous_month_first_day.month)
endMonth = str(previous_month_first_day.year) + '-' + str(previous_month_first_day.month)
if len(begMonth) < 7:
    begMonth = begMonth[:5] + '0' + begMonth[5:]
if len(endMonth) < 7:
    endMonth = endMonth[:5] + '0' + endMonth[5:]

begin3 = begMonth + '-01' + 'T00:00:00+08:00'
begin4 = endMonth + '-01' + 'T00:00:00+08:00'

# 用于fdo的次三月续费开始时间和结束时间，格式为时间戳
four_months_ago = current_date - relativedelta(months=4)
four_months_ago_first_day = four_months_ago.replace(day=1)
four_months_ago_last_day = four_months_ago_first_day + relativedelta(day=31)

time1 = int(four_months_ago_first_day.replace(hour=0, minute=0, second=0).timestamp())
time2 = int(four_months_ago_last_day.replace(hour=23, minute=59, second=59).timestamp())

# 处理登录
session = requests.session()
url = 'https://account.faisco.biz/login/commit.action?backUrl=https%3A%2F%2Ffdo.faisco.biz'
headers = {
    'Referer': 'https://account.faisco.biz/page/login.jsp?backUrl=https://fdo.faisco.biz',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.88 Safari/537.36',
}
data = {
    'bUrl': 'https%3A%2F%2Ffdo.faisco.biz',
    'account': 'byron',
    'password': '1c172daad5c7d70ffa472e4a3716c845'
}
res = session.post(url, headers=headers, data=data)

url = 'https://bi.faisco.biz/api/session'
json_data = {
    "password": "3292128Kzx",
    "username": "<EMAIL>",
    "remember": True
}
headers = {
    'Referer': 'https://bi.faisco.biz/auth/login?redirect=%2F',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.88 Safari/537.36',
}
res = session.post(url, headers=headers, json=json_data)

url = 'https://fdo.faisco.biz/'
res = session.get(url, headers=headers)

# 销售成效表
url = 'https://bi.faisco.biz/api/card/7514/query'
headers = {
    'Referer': 'https://bi.faisco.biz/dashboard/271?%25E6%2597%25A5%25E6%259C%259F%25E7%25AD%259B%25E9%2580%2589=past2months',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.88 Safari/537.36',
}
json_data = {
    "parameters": [{
        "type": "date/all-options",
        "value": "past2months",
        "target": ["dimension", ["template-tag", "data_month"]]
    }],
    "dashboard_id": 271
}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
name_list = tree['data']['results_metadata']['columns']  # 获取每个字段名称
num_list1 = tree['data']['rows'][0]  # 获取上上月数据
num_list2 = tree['data']['rows'][1]  # 获取上月数据
a = []
d1 = {
    '开通量': '',
    '次日销售覆盖量': '',
    '次日有效联系量': '',
    '7日内销售覆盖量': '',
    '7日内有效联系量': '',
    '30日内销售覆盖量': '',
    '30日内有效联系量': '',
    '互动资源下发量': '',
    '互动资源下发量-A类': '',
    '互动资源下发量-B类': '',
    '互动资源下发量-C类': '',
    '互动资源下发量-其他': '',
    '资源下发7日内成单量': '',
    '资源下发7日内成单金额': '',
    '资源下发7日内成单率': '',
    '资源下发7日内成单Arpu': '',
    '资源下发7日内下发Arpu': '',
    '资源下发15日内成单量': '',
    '资源下发15日内成单金额': '',
    '资源下发15日内成单率': '',
    '资源下发15日内成单Arpu': '',
    '资源下发15日内下发Arpu': '',
    '资源下发30日内成单量': '',
    '资源下发30日内成单金额': '',
    '资源下发30日内成单率': '',
    '资源下发30日内成单Arpu': '',
    '资源下发30日内下发Arpu': '',
    '互动总付费量': '',
    '无销售成单-付费量': '',
    '电销销售成单-付费量': '',
    '面销销售成单-付费量': '',
    '有销售成单-付费量': '',
    '互动总付费金额': '',
    '无销售成单-付费金额': '',
    '电销销售成单-付费金额': '',
    '面销销售成单-付费金额': '',
    '互动总付费Arpu': '',
    '无销售成单-付费Arpu': '',
    '电销销售成单-付费Arpu': '',
    '面销销售成单-付费Arpu': '',
    '互动新购版本付费量': '',
    '无销售成单-新购版本付费量': '',
    '电销销售成单-新购版本付费量': '',
    '面销销售成单-新购版本付费量': '',
    '有销售成单-新购版本付费量': '',
    '互动新购版本付费金额': '',
    '无销售成单-新购版本付费金额': '',
    '电销销售成单-新购版本付费金额': '',
    '面销销售成单-新购版本付费金额': '',
    '电销销售成单-新购版本付费Arpu': '',
    '面销销售成单-新购版本付费Arpu': '',
}
d2 = {
    '开通量': '',
    '次日销售覆盖量': '',
    '次日有效联系量': '',
    '7日内销售覆盖量': '',
    '7日内有效联系量': '',
    '30日内销售覆盖量': '',
    '30日内有效联系量': '',
    '互动资源下发量': '',
    '互动资源下发量-A类': '',
    '互动资源下发量-B类': '',
    '互动资源下发量-C类': '',
    '互动资源下发量-其他': '',
    '资源下发7日内成单量': '',
    '资源下发7日内成单金额': '',
    '资源下发7日内成单率': '',
    '资源下发7日内成单Arpu': '',
    '资源下发7日内下发Arpu': '',
    '资源下发15日内成单量': '',
    '资源下发15日内成单金额': '',
    '资源下发15日内成单率': '',
    '资源下发15日内成单Arpu': '',
    '资源下发15日内下发Arpu': '',
    '资源下发30日内成单量': '',
    '资源下发30日内成单金额': '',
    '资源下发30日内成单率': '',
    '资源下发30日内成单Arpu': '',
    '资源下发30日内下发Arpu': '',
    '互动总付费量': '',
    '无销售成单-付费量': '',
    '电销销售成单-付费量': '',
    '面销销售成单-付费量': '',
    '有销售成单-付费量': '',
    '互动总付费金额': '',
    '无销售成单-付费金额': '',
    '电销销售成单-付费金额': '',
    '面销销售成单-付费金额': '',
    '互动总付费Arpu': '',
    '无销售成单-付费Arpu': '',
    '电销销售成单-付费Arpu': '',
    '面销销售成单-付费Arpu': '',
    '互动新购版本付费量': '',
    '无销售成单-新购版本付费量': '',
    '电销销售成单-新购版本付费量': '',
    '面销销售成单-新购版本付费量': '',
    '有销售成单-新购版本付费量': '',
    '互动新购版本付费金额': '',
    '无销售成单-新购版本付费金额': '',
    '电销销售成单-新购版本付费金额': '',
    '面销销售成单-新购版本付费金额': '',
    '电销销售成单-新购版本付费Arpu': '',
    '面销销售成单-新购版本付费Arpu': '',
}
for name in name_list:
    if name['display_name'] == '互动付费量':
        a.append('互动总付费量')
    elif name['display_name'] == '互动付费金额':
        a.append('互动总付费金额')
    else:
        a.append(name['name'])
for i in range(len(a)):
    if a[i] in d1:
        d1[a[i]] = num_list1[i]
        d2[a[i]] = num_list2[i]
title = list(d1.keys())  # 将每个字段名称按字典顺序放到列表里
num1 = list(d1.values())  # 将每个字段对应的数据放到列表里（上上月）
num2 = list(d2.values())  # 将每个字段对应的数据放到列表里（上月）

file_name = '互动月报' + num_list2[0] + '.csv'
with open(file_name, "w", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    writer.writerow(["大项", "小项", num_list1[0], num_list2[0]])
    for i in range(0, len(title)):
        writer.writerow(["销售成效", title[i], num1[i], num2[i]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 互动财务表 - 互动付费来源
url = 'https://bi.faisco.biz/api/card/1639/query'
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
item_list = tree['data']['rows']  # 获取每个维度数据，包括上上月和上月
i = -1
a = [{
    '总付费额': 0,
    '注册互动-广告大组': 0,
    '注册互动-电商平台': 0,
    '注册互动-活动引流': 0,
    '注册互动-旧SEO及官网SEO': 0,
    '注册互动-内容运营': 0,
    '注册互动-全自来': 0,
    '注册互动-全销售': 0,
    '注册互动-商务合作': 0,
    '注册互动-其他': 0,
    '注册凡科网': 0,
    '注册微传单': 0,
    '注册建站': 0,
    '注册轻站': 0,
    '注册快图': 0,
    '注册商城': 0,
    '其他': 0,
}, {
    '总付费额': 0,
    '注册互动-广告大组': 0,
    '注册互动-电商平台': 0,
    '注册互动-活动引流': 0,
    '注册互动-旧SEO及官网SEO': 0,
    '注册互动-内容运营': 0,
    '注册互动-全自来': 0,
    '注册互动-全销售': 0,
    '注册互动-商务合作': 0,
    '注册互动-其他': 0,
    '注册凡科网': 0,
    '注册微传单': 0,
    '注册建站': 0,
    '注册轻站': 0,
    '注册快图': 0,
    '注册商城': 0,
    '其他': 0,
}]
month_list = []
for item in item_list:
    month = item[0]
    name = item[1]
    num = item[5]
    if not month in month_list:
        month_list.append(month)
        i += 1
    if name == '其他':
        name = '注册互动-其他'
    if name == '总计':
        name = '总付费额'
    if name in a[i]:
        a[i][name] = num
sum = 0
for k in a[0].values():
    sum += k
a[0]['其他'] = a[0]['总付费额'] * 2 - sum
sum = 0
for k in a[1].values():
    sum += k
a[1]['其他'] = a[1]['总付费额'] * 2 - sum
title = list(a[0].keys())

with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(0, len(title)):
        writer.writerow(["互动财务表-互动付费来源", title[i], a[0][title[i]], a[1][title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 互动财务表 - 互动付费来源 - 仅互动版本
url = 'https://bi.faisco.biz/api/card/10829/query'
json_data = {
    "ignore_cache": True,
    "parameters": [{
        "type": "date/month-year",
        "value": "past2months",
        "target": ["dimension", ["template-tag", "order_item_pay_time"]]
    }]
}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
item_list = tree['data']['rows']  # 获取每个维度数据，包括上上月和上月
a = [{
    '总付费额': 0,
    '注册互动-广告大组': 0,
    '注册互动-电商平台': 0,
    '注册互动-活动引流': 0,
    '注册互动-旧SEO及官网SEO': 0,
    '注册互动-内容运营': 0,
    '注册互动-全自来': 0,
    '注册互动-全销售': 0,
    '注册互动-商务合作': 0,
    '注册互动-其他': 0,
    '注册凡科网': 0,
    '注册微传单': 0,
    '注册建站': 0,
    '注册轻站': 0,
    '注册快图': 0,
    '注册商城': 0,
    '其他': 0,
}, {
    '总付费额': 0,
    '注册互动-广告大组': 0,
    '注册互动-电商平台': 0,
    '注册互动-活动引流': 0,
    '注册互动-旧SEO及官网SEO': 0,
    '注册互动-内容运营': 0,
    '注册互动-全自来': 0,
    '注册互动-全销售': 0,
    '注册互动-商务合作': 0,
    '注册互动-其他': 0,
    '注册凡科网': 0,
    '注册微传单': 0,
    '注册建站': 0,
    '注册轻站': 0,
    '注册快图': 0,
    '注册商城': 0,
    '其他': 0,
}]
name_list = ['铂金版', '钻石版', '星钻版', '集团版']
type_list = ['注册公众号助手', '注册客户通', '注册门店通', '注册教育']  # 需要整合到其他注册产品里的
month_list = {}
i = 0
for item in item_list:
    month = item[0]
    name = item[1]
    type = item[2]
    num = int(item[4])
    if not month in month_list.keys():
        month_list[month] = i
        i += 1
    if type == '其他':
        type = '注册互动-其他'
    if type in type_list:
        type = '其他'
    if name in name_list:
        a[month_list[month]][type] += num
sum = 0
for k in a[0].values():
    sum += k
a[0]['总付费额'] = sum
sum = 0
for k in a[1].values():
    sum += k
a[1]['总付费额'] = sum
title = list(a[0].keys())

with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(0, len(title)):
        writer.writerow(["互动财务表-互动付费来源（仅互动版本）", title[i], a[0][title[i]], a[1][title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 互动财务表 - 付费互动项目、企业新购续费互动、互动首购/重购
url = 'https://bi.faisco.biz/api/card/1969/query'
json_data = {
    "parameters": [{
        "type": "date/all-options",
        "value": "past2months",
        "target": ["dimension", ["template-tag", "order_item_pay_time"]]
    }],
    "dashboard_id": 271
}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
item_list = tree['data']['rows']  # 获取每个维度数据，包括上上月和上月
a = [{
    '总付费额': 0,
    '白银版': 0,
    '铂金版': 0,
    '钻石版': 0,
    '星钻版': 0,
    '集团版': 0,
    '门店版': 0,
    '版本收入小计': 0,
    '免广告版': 0,
    '区域分部': 0,
    '去广告券': 0,
    '红包充值手续费': 0,
    'API接口': 0,
    '互动平台类接口': 0,
    '合作方版本收入': 0,
    '互动代码定制': 0,
    '广告收入': 0,
    '增值收入小计': 0,
    '流量': 0,
    '话费': 0,
    '立减金': 0,
    '会员卡': 0,
    '京东卡': 0,
    '滴滴打车券': 0,
    '美团代金券': 0,
    '礼品其他': 0,
    '礼品小计': 0,
    '其他': 0,
}, {
    '总付费额': 0,
    '白银版': 0,
    '铂金版': 0,
    '钻石版': 0,
    '星钻版': 0,
    '集团版': 0,
    '门店版': 0,
    '版本收入小计': 0,
    '免广告版': 0,
    '区域分部': 0,
    '去广告券': 0,
    '红包充值手续费': 0,
    'API接口': 0,
    '互动平台类接口': 0,
    '合作方版本收入': 0,
    '互动代码定制': 0,
    '广告收入': 0,
    '增值收入小计': 0,
    '流量': 0,
    '话费': 0,
    '立减金': 0,
    '会员卡': 0,
    '京东卡': 0,
    '滴滴打车券': 0,
    '美团代金券': 0,
    '礼品其他': 0,
    '礼品小计': 0,
    '其他': 0,
}]
month_list = {}
i = 0
b = ['新购数量', '新购金额', '新购ARPU', '续费数量', '续费金额', '续费ARPU']
b1 = ['首购额', '首购量', '首购ARPU', '重购额', '重购量', '重购ARPU']
c = [[0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]]  # 用來存放新购续费的数据
c1 = [[0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]]  # 用来存放首购重购的数据
for item in item_list:
    month = item[0]
    name = item[1]
    num = int(item[3])
    if not month in month_list.keys():
        month_list[month] = i
        i += 1
    if name == '其他付费产品':
        name = '其他'
    if name == '全部':
        name = '总付费额'
        c[month_list[month]] = [int(item[4]), int(item[5]), int(item[6]), int(item[7]), int(item[8]), int(item[9])]
        c1[month_list[month]] = [int(item[11]), int(item[10]), int(item[12]), int(item[14]), int(item[13]),
                                 int(item[15])]
    if name in a[month_list[month]]:
        a[month_list[month]][name] = num

for i in range(2):
    a[i]['版本收入小计'] = a[i]['白银版'] + a[i]['铂金版'] + a[i]['钻石版'] + a[i]['星钻版'] + a[i]['集团版'] + a[i][
        '门店版']
    a[i]['增值收入小计'] = a[i]['免广告版'] + a[i]['区域分部'] + a[i]['去广告券'] + a[i]['红包充值手续费'] + a[i][
        'API接口'] + a[i]['互动平台类接口'] + a[i]['合作方版本收入'] + a[i]['互动代码定制'] + a[i]['广告收入']
    a[i]['礼品小计'] = a[i]['流量'] + a[i]['话费'] + a[i]['立减金'] + a[i]['会员卡'] + a[i]['京东卡'] + a[i][
        '滴滴打车券'] + a[i]['美团代金券'] + a[i]['礼品其他']

title = list(a[0].keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(title)):
        writer.writerow(["互动财务表-付费互动项目", title[i], a[0][title[i]], a[1][title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])
    for i in range(6):
        writer.writerow(["互动财务表-企业新购续费互动", b[i], c[0][i], c[1][i]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])
    for i in range(6):
        writer.writerow(["互动财务表-互动首购/重购", b1[i], c1[0][i], c1[1][i]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 互动财务表 - 互动注册用户的付费
url = 'https://fdo.faisco.biz/basicdata/payStat/get'
json_data = {
    "order_by": "pay_total_count",
    "group_field": "order_item_product_biz",
    "time": "month",
    "condition_list": [{
        "key": "is_valid",
        "op": "eq",
        "val": [1],
        "keyName": "有效订单（是/否）",
        "opName": "等于"
    }, {
        "key": "reg_biz",
        "op": "eq",
        "val": [1],
        "keyName": "注册产品",
        "opName": "等于"
    }],
    "matcher": "and",
    "begTime": begin1,
    "endTime": end2,
    "page": "/basicdata/payStat",
    "columns": {
        "pay_total_count": True,
        "pay_total_original_price": True,
        "pay_total_coupon_price": True,
        "pay_total_price": True,
        "pay_total_price_arpu": True
    },
    "columns_name": {
        "group_field": "付费业务",
        "time": "日期",
        "pay_total_count": "付费量",
        "pay_total_original_price": "订单额",
        "pay_total_coupon_price": "优惠额",
        "pay_total_price": "付费额",
        "pay_total_price_arpu": "付费ARPU"
    },
    "dimension_list": ["reg_ta", "reg_ta_group", "reg_ta_group_label", "reg_know_from_ta", "reg_biz",
                       "open_product_first", "reg_keyword", "reg_from_spider_keyword", "reg_company_goal",
                       "reg_company_size", "reg_trade", "reg_corp_trade_name", "reg_entity", "reg_area", "reg_city",
                       "reg_province", "reg_source_pro", "reg_custom_pro", "reg_tw", "reg_user_title", "reg_bind_type",
                       "reg_client_type", "reg_browser_type", "ab_testing_type", "aid", "reg_time", "reg_time_interval",
                       "reg_open_time", "is_internal_acct", "first_sale_name", "first_sale_group",
                       "first_sale_department", "first_sale_battle_group", "order_pay_method", "order_item_product_biz",
                       "order_item_product", "is_refund", "order_item_new_buy", "order_item_first_purchase_acct",
                       "order_item_pay", "extra_pay_within_7days_after_reg",
                       "extra_pay_within_7days_to_30days_after_reg", "extra_pay_within_30days_to_90days_after_reg",
                       "extra_pay_90days_after_reg", "sale_type_id", "sale_biz_id", "sale_department_name",
                       "sale_battle_group_name", "sale_group_name", "sale_name", "active_time", "active_type_name",
                       "active_ta", "active_ta_group", "active_ta_group_label", "active_ta_biz_name", "sale_receive_ta",
                       "sale_receive_ta_group", "is_valid", "standard_trade_name", "order_item_package_name",
                       "active_area", "active_province", "active_city", "active_time_interval",
                       "order_item_product_category_name", "order_item_first_purchase_category",
                       "order_item_first_purchase_biz", "reg_publish_plan", "order_item_first_purchase_entity",
                       "current_entity_name", "current_trade_name", "order_item_top_purchase_biz"],
    "array_type_list": ["reg_ta_group_label", "active_ta_group_label"]
}
headers_fdo = {
    'Referer': 'https://fdo.faisco.biz/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.88 Safari/537.36',
}
res = session.post(url, headers=headers_fdo, json=json_data)
tree = res.json()
item_list = tree['data']
a = [{
    '总付费额': 0,
    '互动付费额': 0,
    '建站付费额': 0,
    '商城付费额': 0,
    '域名付费额': 0,
    '邮箱付费额': 0,
    '微传单付费额': 0,
    '轻站付费额': 0,
    '增值付费额': 0,
    '团购付费额': 0,
    '快图付费额': 0,
    '短信付费额': 0,
    '其他付费额': 0,
}, {
    '总付费额': 0,
    '互动付费额': 0,
    '建站付费额': 0,
    '商城付费额': 0,
    '域名付费额': 0,
    '邮箱付费额': 0,
    '微传单付费额': 0,
    '轻站付费额': 0,
    '增值付费额': 0,
    '团购付费额': 0,
    '快图付费额': 0,
    '短信付费额': 0,
    '其他付费额': 0,
}]
month_list = {begMonth: 0, endMonth: 1}
namelist = ['建站', '域名', '邮箱', '互动', '微传单', '增值', '', '', '', '短信', '轻站', '公众号助手', '商城',
            '门店通', '', '', '客户通', '教育', '快图', '凡科网', '', '', '', '设计']
for item in item_list:
    name_num = item['group_field']
    month = item['time']
    num = int(item['pay_total_price'])
    if not name_num == '全部':
        name_num = int(name_num)
        if name_num == 999:
            name = namelist[19] + '付费额'
        else:
            name = namelist[name_num - 1] + '付费额'
        if name in a[month_list[month]].keys():
            a[month_list[month]][name] = num
        else:
            a[month_list[month]]['其他付费额'] += num
        a[month_list[month]]['总付费额'] += num
    else:
        continue

title = list(a[0].keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(title)):
        writer.writerow(["互动财务表-互动注册用户付费", title[i], a[0][title[i]], a[1][title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 产品核心数据 - 互动开通来源（新）
url = 'https://bi.faisco.biz/api/card/1639/query'
json_data = {
    "parameters": [{
        "type": "date/all-options",
        "value": "past2months",
        "target": ["dimension", ["template-tag", "data_month"]]
    }],
    "dashboard_id": 271
}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
item_list = tree['data']['rows']  # 获取每个维度数据，包括上上月和上月
a = [{
    '总开通量': 0,
    '注册互动-广告大组': 0,
    '注册互动-电商平台': 0,
    '注册互动-活动引流': 0,
    '注册互动-旧SEO及官网SEO': 0,
    '注册互动-内容运营': 0,
    '注册互动-全自来': 0,
    '注册互动-全销售': 0,
    '注册互动-商务合作': 0,
    '注册互动-其他': 0,
    '注册凡科网': 0,
    '注册微传单': 0,
    '注册建站': 0,
    '注册轻站': 0,
    '注册快图': 0,
    '注册商城': 0,
    '其他': 0,
}, {
    '总开通量': 0,
    '注册互动-广告大组': 0,
    '注册互动-电商平台': 0,
    '注册互动-活动引流': 0,
    '注册互动-旧SEO及官网SEO': 0,
    '注册互动-内容运营': 0,
    '注册互动-全自来': 0,
    '注册互动-全销售': 0,
    '注册互动-商务合作': 0,
    '注册互动-其他': 0,
    '注册凡科网': 0,
    '注册微传单': 0,
    '注册建站': 0,
    '注册轻站': 0,
    '注册快图': 0,
    '注册商城': 0,
    '其他': 0,
}]
month_list = {}
i = 0
for item in item_list:
    month = item[0]
    name = item[1]
    num = item[3]
    if not month in month_list.keys():
        month_list[month] = i
        i += 1
    if name == '注册互动':
        continue
    if name == '其他':
        name = '注册互动-其他'
    if name == '总计':
        name = '总开通量'
    if name in a[month_list[month]]:
        a[month_list[month]][name] = num
    else:
        a[month_list[month]]['其他'] += num

title = list(a[0].keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(title)):
        writer.writerow(["产品核心数据-互动开通来源（新）", title[i], a[0][title[i]], a[1][title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 产品核心数据 - 销售成单占比
url = 'https://bi.faisco.biz/api/card/3607/query'
url1 = 'https://bi.faisco.biz/api/card/3606/query'
month_list = [begMonth, endMonth]
a = ['首购互动版本有效订单量', '首购互动版本有效订单量（有成单销售）', '首购互动版本有效订单金额',
     '首购互动版本有效订单金额（有成单销售）']
b = [[0, 0, 0, 0], [0, 0, 0, 0]]
i = 0
for month in month_list:
    json_data = {
        "parameters": [{
            "type": "date/month-year",
            "value": month,
            "target": ["dimension", ["field", 1124, None]]
        }],
        "dashboard_id": 463
    }
    res = session.post(url, headers=headers, json=json_data)
    tree = res.json()
    item = tree['data']['rows'][0]  # 获取每个维度数据，包括上上月和上月

    res1 = session.post(url1, headers=headers, json=json_data)
    tree1 = res1.json()
    item1 = tree1['data']['rows'][0]  # 获取每个维度数据，包括上上月和上月
    if month == begMonth:
        i = 0
    else:
        i = 1
    b[i][0] = int(item[1])
    b[i][2] = int(item[2])
    b[i][1] = int(item1[1])
    b[i][3] = int(item1[2])

with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(a)):
        writer.writerow(["产品核心数据-销售成单占比", a[i], b[0][i], b[1][i]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 产品核心数据 - 多年占比、各版本多年占比
url = 'https://bi.faisco.biz/api/card/1969/query'
json_data = {
    "parameters": [{
        "type": "date/all-options",
        "value": "past2months",
        "target": ["dimension", ["template-tag", "order_item_pay_time"]]
    }],
    "dashboard_id": 271
}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
item_list = tree['data']['rows']  # 获取每个维度数据，包括上上月和上月
a = ['版本订单量', '单年订单量', '多年订单量', '半年订单量', '季度订单量', '月度订单量', '其他']
a1 = ['铂金版订单量', '铂金版单年订单量', '铂金版多年订单量', '铂金版单年订单量占比', '铂金版多年订单量占比',
      '钻石版订单量', '钻石版单年订单量', '钻石版多年订单量', '钻石版单年订单量占比', '钻石版多年订单量占比',
      '星钻版订单量', '星钻版单年订单量', '星钻版多年订单量', '星钻版单年订单量占比', '星钻版多年订单量占比']
b = [[0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0]]  # 用來存放新购续费的数据
b1 = [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]
month_list = {}
i = 0
for item in item_list:
    month = item[0]
    name = item[1]
    if (not month in month_list.keys()) and (not month == '全部'):
        month_list[month] = i
        i += 1
    if name == "全部":
        b[month_list[month]] = [item[16] + item[17] + item[19] + item[20] + item[21] + item[22], item[16], item[17],
                                item[19], item[20],
                                item[21], item[22]]
    if name == "铂金版":
        b1[month_list[month]][0] = item[16] + item[17] + item[18]
        b1[month_list[month]][1] = item[16]
        b1[month_list[month]][2] = item[17]
        b1[month_list[month]][3] = float(b1[month_list[month]][1] / b1[month_list[month]][0])
        b1[month_list[month]][4] = float(b1[month_list[month]][2] / b1[month_list[month]][0])
    if name == "钻石版":
        b1[month_list[month]][5] = item[16] + item[17] + item[18]
        b1[month_list[month]][6] = item[16]
        b1[month_list[month]][7] = item[17]
        b1[month_list[month]][8] = float(b1[month_list[month]][6] / b1[month_list[month]][5])
        b1[month_list[month]][9] = float(b1[month_list[month]][7] / b1[month_list[month]][5])
    if name == "星钻版":
        b1[month_list[month]][10] = item[16] + item[17] + item[18]
        b1[month_list[month]][11] = item[16]
        b1[month_list[month]][12] = item[17]
        b1[month_list[month]][13] = float(b1[month_list[month]][11] / b1[month_list[month]][10])
        b1[month_list[month]][14] = float(b1[month_list[month]][12] / b1[month_list[month]][10])

with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(a)):
        writer.writerow(["产品核心数据-多年占比", a[i], b[0][i], b[1][i]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])
    for i in range(len(a1)):
        writer.writerow(["产品核心数据-各版本多年占比", a1[i], b1[0][i], b1[1][i]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 产品核心数据 - 注册互动7天内付费、注册互动30天内付费
url = 'https://bi.faisco.biz/api/card/1639/query'
json_data = {
    "parameters": [{
        "type": "date/all-options",
        "value": "past2months",
        "target": ["dimension", ["template-tag", "data_month"]]
    }],
    "dashboard_id": 271
}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
item_list = tree['data']['rows']  # 获取每个维度数据，包括上上月和上月
item_list1 = tree['data']['cols']  # 获取每个维度数据对应的名称
a = [{
    '注册量': 0,
    '注册7天内的总付费量': 0,
    '注册7天内的总付费额': 0,
    '注册7天内的总付费率': 0,
    '注册7天内的总付费arpu': 0,
    '注册7天内的总注册arpu': 0,
    '注册7天内的互动付费量': 0,
    '注册7天内的互动付费额': 0,
    '注册7天内的互动付费率': 0,
    '注册7天内的互动付费arpu': 0,
    '注册7天内的互动注册arpu': 0,
    '注册7天内的互动版本付费量': 0,
    '注册7天内的互动版本付费额': 0,
    '注册7天内的互动版本付费率': 0,
    '注册7天内的互动版本付费arpu': 0,
    '注册7天内的互动版本注册arpu': 0,
    '注册30天内的总付费量': 0,
    '注册30天内的总付费额': 0,
    '注册30天内的总付费率': 0,
    '注册30天内的总付费arpu': 0,
    '注册30天内的总注册arpu': 0,
    '注册30天内的互动付费量': 0,
    '注册30天内的互动付费额': 0,
    '注册30天内的互动付费率': 0,
    '注册30天内的互动付费arpu': 0,
    '注册30天内的互动注册arpu': 0,
    '注册30天内的互动版本付费量': 0,
    '注册30天内的互动版本付费额': 0,
    '注册30天内的互动版本付费率': 0,
    '注册30天内的互动版本付费arpu': 0,
    '注册30天内的互动版本注册arpu': 0,
}, {
    '注册量': 0,
    '注册7天内的总付费量': 0,
    '注册7天内的总付费额': 0,
    '注册7天内的总付费率': 0,
    '注册7天内的总付费arpu': 0,
    '注册7天内的总注册arpu': 0,
    '注册7天内的互动付费量': 0,
    '注册7天内的互动付费额': 0,
    '注册7天内的互动付费率': 0,
    '注册7天内的互动付费arpu': 0,
    '注册7天内的互动注册arpu': 0,
    '注册7天内的互动版本付费量': 0,
    '注册7天内的互动版本付费额': 0,
    '注册7天内的互动版本付费率': 0,
    '注册7天内的互动版本付费arpu': 0,
    '注册7天内的互动版本注册arpu': 0,
    '注册30天内的总付费量': 0,
    '注册30天内的总付费额': 0,
    '注册30天内的总付费率': 0,
    '注册30天内的总付费arpu': 0,
    '注册30天内的总注册arpu': 0,
    '注册30天内的互动付费量': 0,
    '注册30天内的互动付费额': 0,
    '注册30天内的互动付费率': 0,
    '注册30天内的互动付费arpu': 0,
    '注册30天内的互动注册arpu': 0,
    '注册30天内的互动版本付费量': 0,
    '注册30天内的互动版本付费额': 0,
    '注册30天内的互动版本付费率': 0,
    '注册30天内的互动版本付费arpu': 0,
    '注册30天内的互动版本注册arpu': 0,
}]
a1 = [{
    '注册量': 0,
    '注册7天内的总付费量': 0,
    '注册7天内的总付费额': 0,
    '注册7天内的总付费率': 0,
    '注册7天内的总付费arpu': 0,
    '注册7天内的总注册arpu': 0,
    '注册7天内的互动付费量': 0,
    '注册7天内的互动付费额': 0,
    '注册7天内的互动付费率': 0,
    '注册7天内的互动付费arpu': 0,
    '注册7天内的互动注册arpu': 0,
    '注册7天内的互动版本付费量': 0,
    '注册7天内的互动版本付费额': 0,
    '注册7天内的互动版本付费率': 0,
    '注册7天内的互动版本付费arpu': 0,
    '注册7天内的互动版本注册arpu': 0,
    '注册30天内的总付费量': 0,
    '注册30天内的总付费额': 0,
    '注册30天内的总付费率': 0,
    '注册30天内的总付费arpu': 0,
    '注册30天内的总注册arpu': 0,
    '注册30天内的互动付费量': 0,
    '注册30天内的互动付费额': 0,
    '注册30天内的互动付费率': 0,
    '注册30天内的互动付费arpu': 0,
    '注册30天内的互动注册arpu': 0,
    '注册30天内的互动版本付费量': 0,
    '注册30天内的互动版本付费额': 0,
    '注册30天内的互动版本付费率': 0,
    '注册30天内的互动版本付费arpu': 0,
    '注册30天内的互动版本注册arpu': 0,
}, {
    '注册量': 0,
    '注册7天内的总付费量': 0,
    '注册7天内的总付费额': 0,
    '注册7天内的总付费率': 0,
    '注册7天内的总付费arpu': 0,
    '注册7天内的总注册arpu': 0,
    '注册7天内的互动付费量': 0,
    '注册7天内的互动付费额': 0,
    '注册7天内的互动付费率': 0,
    '注册7天内的互动付费arpu': 0,
    '注册7天内的互动注册arpu': 0,
    '注册7天内的互动版本付费量': 0,
    '注册7天内的互动版本付费额': 0,
    '注册7天内的互动版本付费率': 0,
    '注册7天内的互动版本付费arpu': 0,
    '注册7天内的互动版本注册arpu': 0,
    '注册30天内的总付费量': 0,
    '注册30天内的总付费额': 0,
    '注册30天内的总付费率': 0,
    '注册30天内的总付费arpu': 0,
    '注册30天内的总注册arpu': 0,
    '注册30天内的互动付费量': 0,
    '注册30天内的互动付费额': 0,
    '注册30天内的互动付费率': 0,
    '注册30天内的互动付费arpu': 0,
    '注册30天内的互动注册arpu': 0,
    '注册30天内的互动版本付费量': 0,
    '注册30天内的互动版本付费额': 0,
    '注册30天内的互动版本付费率': 0,
    '注册30天内的互动版本付费arpu': 0,
    '注册30天内的互动版本注册arpu': 0,
}]
b = []
month_list = {}
i = 0
for item1 in item_list1:
    b.append(item1['display_name'])
for item in item_list:
    month = item[0]
    name = item[1]
    if (not month in month_list.keys()) and (not month == '全部'):
        month_list[month] = i
        i += 1
    if name == '注册互动':
        for j in range(len(item)):
            if a[month_list[month]].get(b[j], -1) == 0:
                a[month_list[month]][b[j]] = int(item[j])
    if name == '注册互动-活动引流':
        for j in range(len(item)):
            if a1[month_list[month]].get(b[j], -1) == 0:
                a1[month_list[month]][b[j]] = int(item[j])

for i in range(2):
    a[i]['注册7天内的总付费率'] = float(a[i]['注册7天内的总付费量'] / a[i]['注册量'])
    a[i]['注册7天内的总付费arpu'] = int(a[i]['注册7天内的总付费额'] / a[i]['注册7天内的总付费量'])
    a[i]['注册7天内的总注册arpu'] = float(a[i]['注册7天内的总付费额'] / a[i]['注册量'])
    a[i]['注册7天内的互动付费率'] = float(a[i]['注册7天内的互动付费量'] / a[i]['注册量'])
    a[i]['注册7天内的互动付费arpu'] = int(a[i]['注册7天内的互动付费额'] / a[i]['注册7天内的互动付费量'])
    a[i]['注册7天内的互动注册arpu'] = float(a[i]['注册7天内的互动付费额'] / a[i]['注册量'])
    a[i]['注册7天内的互动版本付费率'] = float(a[i]['注册7天内的互动版本付费量'] / a[i]['注册量'])
    a[i]['注册7天内的互动版本付费arpu'] = int(a[i]['注册7天内的互动版本付费额'] / a[i]['注册7天内的互动版本付费量'])
    a[i]['注册7天内的互动版本注册arpu'] = float(a[i]['注册7天内的互动版本付费额'] / a[i]['注册量'])
    a1[i]['注册7天内的总付费率'] = float(a1[i]['注册7天内的总付费量'] / a1[i]['注册量'])
    a1[i]['注册7天内的总付费arpu'] = int(a1[i]['注册7天内的总付费额'] / a1[i]['注册7天内的总付费量'])
    a1[i]['注册7天内的总注册arpu'] = float(a1[i]['注册7天内的总付费额'] / a1[i]['注册量'])
    a1[i]['注册7天内的互动付费率'] = float(a1[i]['注册7天内的互动付费量'] / a1[i]['注册量'])
    a1[i]['注册7天内的互动付费arpu'] = int(a1[i]['注册7天内的互动付费额'] / a1[i]['注册7天内的互动付费量'])
    a1[i]['注册7天内的互动注册arpu'] = float(a1[i]['注册7天内的互动付费额'] / a1[i]['注册量'])
    a1[i]['注册7天内的互动版本付费率'] = float(a1[i]['注册7天内的互动版本付费量'] / a1[i]['注册量'])
    a1[i]['注册7天内的互动版本付费arpu'] = int(a1[i]['注册7天内的互动版本付费额'] / a1[i]['注册7天内的互动版本付费量'])
    a1[i]['注册7天内的互动版本注册arpu'] = float(a1[i]['注册7天内的互动版本付费额'] / a1[i]['注册量'])

    a[i]['注册30天内的总付费率'] = float(a[i]['注册30天内的总付费量'] / a[i]['注册量'])
    a[i]['注册30天内的总付费arpu'] = int(a[i]['注册30天内的总付费额'] / a[i]['注册30天内的总付费量'])
    a[i]['注册30天内的总注册arpu'] = float(a[i]['注册30天内的总付费额'] / a[i]['注册量'])
    a[i]['注册30天内的互动付费率'] = float(a[i]['注册30天内的互动付费量'] / a[i]['注册量'])
    a[i]['注册30天内的互动付费arpu'] = int(a[i]['注册30天内的互动付费额'] / a[i]['注册30天内的互动付费量'])
    a[i]['注册30天内的互动注册arpu'] = float(a[i]['注册30天内的互动付费额'] / a[i]['注册量'])
    a[i]['注册30天内的互动版本付费率'] = float(a[i]['注册30天内的互动版本付费量'] / a[i]['注册量'])
    a[i]['注册30天内的互动版本付费arpu'] = int(a[i]['注册30天内的互动版本付费额'] / a[i]['注册30天内的互动版本付费量'])
    a[i]['注册30天内的互动版本注册arpu'] = float(a[i]['注册30天内的互动版本付费额'] / a[i]['注册量'])
    a1[i]['注册30天内的总付费率'] = float(a1[i]['注册30天内的总付费量'] / a1[i]['注册量'])
    a1[i]['注册30天内的总付费arpu'] = int(a1[i]['注册30天内的总付费额'] / a1[i]['注册30天内的总付费量'])
    a1[i]['注册30天内的总注册arpu'] = float(a1[i]['注册30天内的总付费额'] / a1[i]['注册量'])
    a1[i]['注册30天内的互动付费率'] = float(a1[i]['注册30天内的互动付费量'] / a1[i]['注册量'])
    a1[i]['注册30天内的互动付费arpu'] = int(a1[i]['注册30天内的互动付费额'] / a1[i]['注册30天内的互动付费量'])
    a1[i]['注册30天内的互动注册arpu'] = float(a1[i]['注册30天内的互动付费额'] / a1[i]['注册量'])
    a1[i]['注册30天内的互动版本付费率'] = float(a1[i]['注册30天内的互动版本付费量'] / a1[i]['注册量'])
    a1[i]['注册30天内的互动版本付费arpu'] = int(
        a1[i]['注册30天内的互动版本付费额'] / a1[i]['注册30天内的互动版本付费量'])
    a1[i]['注册30天内的互动版本注册arpu'] = float(a1[i]['注册30天内的互动版本付费额'] / a1[i]['注册量'])

title = list(a[0].keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(title)):
        if ('注册7天' in title[i]) or ('注册量' in title[i]):
            writer.writerow(["产品核心数据-注册互动7天内付费", title[i], a[0][title[i]], a[1][title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

    for i in range(len(title)):
        if ('注册7天' in title[i]) or ('注册量' in title[i]):
            writer.writerow(["产品核心数据-活动引流-注册互动7天内付费", title[i], a1[0][title[i]], a1[1][title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

    for i in range(len(title)):
        if ('注册30天' in title[i]) or ('注册量' in title[i]):
            writer.writerow(["产品核心数据-注册互动30天内付费", title[i], a[0][title[i]], a[1][title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

    for i in range(len(title)):
        if ('注册30天' in title[i]) or ('注册量' in title[i]):
            writer.writerow(["产品核心数据-活动引流-注册互动30天内付费", title[i], a1[0][title[i]], a1[1][title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 产品核心数据 - 开通互动7天内付费
url = 'https://bi.faisco.biz/api/card/1639/query'
json_data = {
    "parameters": [{
        "type": "date/all-options",
        "value": "past2months",
        "target": ["dimension", ["template-tag", "data_month"]]
    }],
    "dashboard_id": 271
}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
item_list = tree['data']['rows']  # 获取每个维度数据，包括上上月和上月
item_list1 = tree['data']['cols']  # 获取每个维度数据对应的名称
a = [{
    '互动开通量': 0,
    '开通7天内的互动付费量': 0,
    '开通7天内的互动付费额': 0,
    '开通7天内的互动付费率': 0,
    '开通7天内的互动付费arpu': 0,
    '开通7天内的互动开通arpu': 0,
    '开通7天内的互动版本付费量': 0,
    '开通7天内的互动版本付费额': 0,
    '开通7天内的互动版本付费率': 0,
    '开通7天内的互动版本付费arpu': 0,
    '开通7天内的互动版本开通arpu': 0,
}, {
    '互动开通量': 0,
    '开通7天内的互动付费量': 0,
    '开通7天内的互动付费额': 0,
    '开通7天内的互动付费率': 0,
    '开通7天内的互动付费arpu': 0,
    '开通7天内的互动开通arpu': 0,
    '开通7天内的互动版本付费量': 0,
    '开通7天内的互动版本付费额': 0,
    '开通7天内的互动版本付费率': 0,
    '开通7天内的互动版本付费arpu': 0,
    '开通7天内的互动版本开通arpu': 0,
}]
b = []
month_list = {}
i = 0
for item1 in item_list1:
    b.append(item1['display_name'])
for item in item_list:
    month = item[0]
    name = item[1]
    if (not month in month_list.keys()) and (not month == '全部'):
        month_list[month] = i
        i += 1
    if name == '总计':
        for j in range(len(item)):
            if a[month_list[month]].get(b[j], -1) == 0:
                a[month_list[month]][b[j]] = int(item[j])
for i in range(2):
    a[i]['开通7天内的互动付费率'] = float(a[i]['开通7天内的互动付费量'] / a[i]['互动开通量'])
    a[i]['开通7天内的互动付费arpu'] = int(a[i]['开通7天内的互动付费额'] / a[i]['开通7天内的互动付费量'])
    a[i]['开通7天内的互动开通arpu'] = float(a[i]['开通7天内的互动付费额'] / a[i]['互动开通量'])
    a[i]['开通7天内的互动版本付费率'] = float(a[i]['开通7天内的互动版本付费量'] / a[i]['互动开通量'])
    a[i]['开通7天内的互动版本付费arpu'] = int(a[i]['开通7天内的互动版本付费额'] / a[i]['开通7天内的互动版本付费量'])
    a[i]['开通7天内的互动版本开通arpu'] = float(a[i]['开通7天内的互动版本付费额'] / a[i]['互动开通量'])

title = list(a[0].keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(title)):
        writer.writerow(["产品核心数据-开通互动7天内付费", title[i], a[0][title[i]], a[1][title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 产品核心数据 - 开通互动各来源-7天内付费率（新）、30天内付费率（新）
url = 'https://bi.faisco.biz/api/card/1639/query'
json_data = {
    "parameters": [{
        "type": "date/all-options",
        "value": "past2months",
        "target": ["dimension", ["template-tag", "data_month"]]
    }],
    "dashboard_id": 271
}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
item_list = tree['data']['rows']  # 获取每个维度数据，包括上上月和上月
item_list1 = tree['data']['cols']  # 获取每个维度数据对应的名称
a = [{
    '注册互动-广告大组': 0,
    '注册互动-电商平台': 0,
    '注册互动-活动引流': 0,
    '注册互动-旧SEO及官网SEO': 0,
    '注册互动-内容运营': 0,
    '注册互动-全自来': 0,
    '注册互动-全销售': 0,
    '注册互动-商务合作': 0,
    '注册凡科网': 0,
    '注册微传单': 0,
    '注册建站': 0,
    '注册轻站': 0,
    '注册快图': 0,
    '注册商城': 0,
}, {
    '注册互动-广告大组': 0,
    '注册互动-电商平台': 0,
    '注册互动-活动引流': 0,
    '注册互动-旧SEO及官网SEO': 0,
    '注册互动-内容运营': 0,
    '注册互动-全自来': 0,
    '注册互动-全销售': 0,
    '注册互动-商务合作': 0,
    '注册凡科网': 0,
    '注册微传单': 0,
    '注册建站': 0,
    '注册轻站': 0,
    '注册快图': 0,
    '注册商城': 0,
}]
a1 = [{
    '注册互动-广告大组': 0,
    '注册互动-电商平台': 0,
    '注册互动-活动引流': 0,
    '注册互动-旧SEO及官网SEO': 0,
    '注册互动-内容运营': 0,
    '注册互动-全自来': 0,
    '注册互动-全销售': 0,
    '注册互动-商务合作': 0,
    '注册凡科网': 0,
    '注册微传单': 0,
    '注册建站': 0,
    '注册轻站': 0,
    '注册快图': 0,
    '注册商城': 0,
}, {
    '注册互动-广告大组': 0,
    '注册互动-电商平台': 0,
    '注册互动-活动引流': 0,
    '注册互动-旧SEO及官网SEO': 0,
    '注册互动-内容运营': 0,
    '注册互动-全自来': 0,
    '注册互动-全销售': 0,
    '注册互动-商务合作': 0,
    '注册凡科网': 0,
    '注册微传单': 0,
    '注册建站': 0,
    '注册轻站': 0,
    '注册快图': 0,
    '注册商城': 0,
}]
for item in item_list:
    month = item[0]
    name = item[1]
    if month == begMonth:
        i = 0
    else:
        i = 1
    if name in a[i]:
        if item[3] == 0:
            a[i][name] = 0
            a1[i][name] = 0
        else:
            a[i][name] = float(item[60] / item[3])
            a1[i][name] = float(item[66] / item[3])

title = list(a[0].keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(title)):
        writer.writerow(["产品核心数据-开通互动各来源-7天内付费率（新）", title[i], a[0][title[i]], a[1][title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

    for i in range(len(title)):
        writer.writerow(["产品核心数据-开通互动各来源-30天内付费率（新）", title[i], a1[0][title[i]], a1[1][title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 产品核心数据 - 留存率、活动引流留存率
url = 'https://bi.faisco.biz/api/card/1639/query'
json_data = {
    "parameters": [{
        "type": "date/all-options",
        "value": "past2months",
        "target": ["dimension", ["template-tag", "data_month"]]
    }],
    "dashboard_id": 271
}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
item_list = tree['data']['rows']  # 获取每个维度数据，包括上上月和上月
item_list1 = tree['data']['cols']  # 获取每个维度数据对应的名称
a = ['开通量', '次日留存量', '首周留存量', '次三周留存量']
b = [[0, 0, 0, 0], [0, 0, 0, 0]]  # 用來存放留存的数据
b1 = [[0, 0, 0, 0], [0, 0, 0, 0]]  # 用來存放活动引流留存的数据
for item in item_list:
    month = item[0]
    name = item[1]
    if month == begMonth:
        i = 0
    else:
        i = 1
    if name == '总计':
        b[i] = [item[3], item[72], item[73], item[74]]
    if name == '注册互动-活动引流':
        b1[i] = [item[3], item[72], item[73], item[74]]

with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(a)):
        writer.writerow(["产品核心数据-开通互动各来源-留存率", a[i], b[0][i], b[1][i]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

    for i in range(len(a)):
        writer.writerow(["产品核心数据-开通互动各来源-活动引流留存率", a[i], b1[0][i], b1[1][i]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 产品核心数据 - 退款
url = 'https://fdo.faisco.biz/hd/payStat/get'
json_data = {
    "order_by": "pay_total_first_purchase_biz_count",
    "group_field": "",
    "time": "month",
    "condition_list": [{
        "key": "order_item_first_purchase_biz",
        "op": "eq",
        "val": [1],
        "keyName": "业务首购/重购",
        "opName": "等于"
    }, {
        "key": "is_refund",
        "op": "eq",
        "val": [1],
        "keyName": "退款状态",
        "opName": "等于"
    }],
    "matcher": "and",
    "begTime": begin1,
    "endTime": end2,
    "page": "/hd/payStat",
    "columns": {
        "pay_total_first_purchase_biz_count": True,
        "pay_total_first_purchase_biz_price": True
    },
    "columns_name": {
        "time": "日期",
        "pay_total_first_purchase_biz_count": "首购付费量",
        "pay_total_first_purchase_biz_price": "首购付费额",
        "group_field": "分组项目"
    },
    "dimension_list": ["reg_ta", "reg_ta_group", "reg_ta_group_label", "reg_ta_biz", "reg_know_from_ta", "reg_biz",
                       "open_product_first", "reg_keyword", "reg_from_spider_keyword", "reg_company_goal",
                       "reg_company_size", "reg_trade", "reg_corp_trade_name", "reg_entity", "reg_area", "reg_city",
                       "reg_province", "reg_source_pro", "reg_custom_pro", "reg_tw", "reg_user_title", "reg_bind_type",
                       "reg_client_type", "reg_browser_type", "ab_testing_type", "aid", "reg_time", "reg_time_interval",
                       "open_time", "is_internal_acct", "first_sale_name", "first_sale_group", "first_sale_department",
                       "first_sale_battle_group", "active_type_name", "active_ta", "active_ta_group",
                       "active_ta_group_label", "active_ta_biz_name", "open_company_goal", "open_source",
                       "order_pay_method", "order_item_product", "is_refund", "order_item_new_buy",
                       "order_item_first_purchase_biz", "order_item_pay", "is_sales_cover",
                       "extra_pay_within_7days_after_reg", "extra_pay_within_7days_to_30days_after_reg",
                       "extra_pay_within_30days_to_90days_after_reg", "extra_pay_90days_after_reg", "sale_type_id",
                       "sale_biz_id", "open_source_biz", "is_valid", "order_item_package_name", "active_area",
                       "active_province", "active_city"],
    "array_type_list": ["reg_ta_group_label", "active_ta_group_label"]
}
res = session.post(url, headers=headers_fdo, json=json_data)
tree = res.json()
item_list = tree['data']
a = [{
    '退款量': 0,
    '退款额': 0,
}, {
    '退款量': 0,
    '退款额': 0,
}]
for item in item_list:
    month = item['time']
    num1 = item['pay_total_first_purchase_biz_count']
    num2 = item['pay_total_first_purchase_biz_price']
    if month == '':
        continue
    if month == begMonth:
        i = 0
    if month == endMonth:
        i = 1
    a[i]['退款量'] = num1
    a[i]['退款额'] = num2

title = list(a[0].keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(title)):
        writer.writerow(["产品核心数据-退款", title[i], a[0][title[i]], a[1][title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 产品核心数据 - 续费统计
url = 'https://fdo.faisco.biz/hd/payStat/get'
json_data = {
    "order_by": "expire_count",
    "group_field": "",
    "time": "month",
    "condition_list": [{
        "key": "order_item_pay",
        "op": "eq",
        "val": [1],
        "keyName": "支付情况",
        "opName": "等于"
    }],
    "matcher": "and",
    "begTime": begin1,
    "endTime": end2,
    "page": "/hd/corpRenewStat",
    "columns": {
        "expire_count": True,
        "renew_month_count": True,
        "renew_month_rate": True,
        "renew_month_price": True,
        "renew_month_arpu": True,
        "renew_1months_ahead_count": True,
        "renew_1months_ahead_rate": True,
        "renew_3months_after_count": True,
        "renew_3months_after_rate": True
    },
    "columns_name": {
        "time": "日期",
        "expire_count": "到期量",
        "renew_month_count": "当月续费量",
        "renew_month_rate": "当月续费率",
        "renew_month_price": "当月续费额",
        "renew_month_arpu": "当月续费ARPU",
        "renew_1months_ahead_count": "提前一个月续费量",
        "renew_1months_ahead_rate": "提前一个月续费率",
        "renew_3months_after_count": "过期三个月内续费量",
        "renew_3months_after_rate": "过期三个月内续费率",
        "group_field": "分组项目"
    },
    "dimension_list": ["reg_ta", "reg_ta_group", "reg_ta_group_label", "reg_ta_biz", "reg_know_from_ta", "reg_biz",
                       "open_product_first", "reg_keyword", "reg_from_spider_keyword", "reg_company_goal",
                       "reg_company_size", "reg_trade", "reg_corp_trade_name", "reg_entity", "reg_area", "reg_city",
                       "reg_province", "reg_source_pro", "reg_custom_pro", "reg_tw", "reg_user_title", "reg_bind_type",
                       "reg_client_type", "reg_browser_type", "ab_testing_type", "aid", "reg_time", "reg_time_interval",
                       "open_time", "is_internal_acct", "first_sale_name", "first_sale_group", "first_sale_department",
                       "first_sale_battle_group", "order_item_product", "open_company_goal", "open_source",
                       "is_sales_cover", "order_pay_method", "is_refund", "order_item_pay", "open_source_biz"],
    "array_type_list": ["reg_ta_group_label"]
}
a = [{
    '版本到期量': 0,
    '当月续费量': 0,
    '当月续费额': 0,
    '当月续费arpu': 0,
    '当月续费率': 0,
    '提前一月续费量': 0,
    '提前一月续费率': 0,
}, {
    '版本到期量': 0,
    '当月续费量': 0,
    '当月续费额': 0,
    '当月续费arpu': 0,
    '当月续费率': 0,
    '提前一月续费量': 0,
    '提前一月续费率': 0,
}]
b = {
    '次三月续费量': 0,
    '次三月续费率': 0,
}
headers_fdo = {
    'Referer': 'https://fdo.faisco.biz/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.88 Safari/537.36',
    #    'Cookie': fdo_cookie,
}
res = session.post(url, headers=headers_fdo, json=json_data)
tree = res.json()
item_list = tree['data']
for item in item_list:
    month = item['time']
    if month == '':
        continue
    if month == begMonth:
        i = 0
    if month == endMonth:
        i = 1
    a[i]['版本到期量'] = item['expire_count']
    a[i]['当月续费量'] = item['renew_month_count']
    a[i]['当月续费额'] = item['renew_month_price']
    a[i]['当月续费arpu'] = item['renew_month_arpu']
    a[i]['当月续费率'] = item['renew_month_rate']
    a[i]['提前一月续费量'] = item['renew_1months_ahead_count']
    a[i]['提前一月续费率'] = item['renew_1months_ahead_rate']

# 次三月续费率需要获取往前3个月的
json_data = {
    "order_by": "expire_count",
    "group_field": "",
    "time": "month",
    "condition_list": [{
        "key": "order_item_pay",
        "op": "eq",
        "val": [1],
        "keyName": "支付情况",
        "opName": "等于"
    }],
    "matcher": "and",
    "begTime": time1,
    "endTime": time2,
    "page": "/hd/corpRenewStat",
    "columns": {
        "expire_count": True,
        "renew_month_count": True,
        "renew_month_rate": True,
        "renew_month_price": True,
        "renew_month_arpu": True,
        "renew_1months_ahead_count": True,
        "renew_1months_ahead_rate": True,
        "renew_3months_after_count": True,
        "renew_3months_after_rate": True
    },
    "columns_name": {
        "time": "日期",
        "expire_count": "到期量",
        "renew_month_count": "当月续费量",
        "renew_month_rate": "当月续费率",
        "renew_month_price": "当月续费额",
        "renew_month_arpu": "当月续费ARPU",
        "renew_1months_ahead_count": "提前一个月续费量",
        "renew_1months_ahead_rate": "提前一个月续费率",
        "renew_3months_after_count": "过期三个月内续费量",
        "renew_3months_after_rate": "过期三个月内续费率",
        "group_field": "分组项目"
    },
    "dimension_list": ["reg_ta", "reg_ta_group", "reg_ta_group_label", "reg_ta_biz", "reg_know_from_ta", "reg_biz",
                       "open_product_first", "reg_keyword", "reg_from_spider_keyword", "reg_company_goal",
                       "reg_company_size", "reg_trade", "reg_corp_trade_name", "reg_entity", "reg_area", "reg_city",
                       "reg_province", "reg_source_pro", "reg_custom_pro", "reg_tw", "reg_user_title",
                       "reg_bind_type",
                       "reg_client_type", "reg_browser_type", "ab_testing_type", "aid", "reg_time",
                       "reg_time_interval",
                       "open_time", "is_internal_acct", "first_sale_name", "first_sale_group",
                       "first_sale_department",
                       "first_sale_battle_group", "order_item_product", "open_company_goal", "open_source",
                       "is_sales_cover", "order_pay_method", "is_refund", "order_item_pay", "open_source_biz"],
    "array_type_list": ["reg_ta_group_label"]
}
res = session.post(url, headers=headers_fdo, json=json_data)
tree = res.json()
item = tree['data'][0]
b['次三月续费量'] = item['renew_3months_after_count']
b['次三月续费率'] = item['renew_3months_after_rate']

title = list(a[0].keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(title)):
        writer.writerow(["产品核心数据-续费统计", title[i], a[0][title[i]], a[1][title[i]]])
    for m, k in b.items():
        writer.writerow(["产品核心数据-续费统计", m, k, '次三月'])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

'''
# 产品核心数据 - 新用户漏斗 - 非活动引流
url = 'https://fdp.faisco.biz/behavior/funnelAnalysis/getFunnelAnalysisTableData'
a = [{
    '开通量': 0,
    '开通7日内点击预览活动': 0,
    '开通7日内进入编辑页面': 0,
    '开通7日内成功创建活动': 0,
    '开通7日内成功发布活动': 0,
}, {
    '开通量': 0,
    '开通7日内点击预览活动': 0,
    '开通7日内进入编辑页面': 0,
    '开通7日内成功创建活动': 0,
    '开通7日内成功发布活动': 0,
}]
json_data = {
    "funnelId": 188,
    "conditions": [{
        "no": 1,
        "eventName": "biz_open",
        "bizTypeId": 1,
        "conn": "and",
        "conditions": [],
        "etpConditions": [{
            "propName": "reg_ta_group",
            "op": 9,
            "dataTypeId": 1,
            "propTypeId": 2,
            "hasDict": True,
            "value": "[\"互动-活动引流(不下发销售)\"]",
            "conn": "and"
        }],
        "userGroupConditions": []
    }],
    "startTime": date1_begin,
    "endTime": date1_end,
    "dataFormatType": 1,
    "bssId": [],
    "groupByConditions": []
}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
item_list = tree['r_data']
for item in item_list:
    name = item['event_cname']
    num = int(item['total'])
    if name == '产品开通':
        a[0]['开通量'] = num
    elif name == '点击创建活动':
        a[0]['开通7日内点击预览活动'] = num
    elif name == '点击预览窗创建按钮':
        a[0]['开通7日内进入编辑页面'] = num
    elif name == '主动保存活动':
        a[0]['开通7日内成功创建活动'] = num
    elif name == '成功发布活动':
        a[0]['开通7日内成功发布活动'] = num
# 上面是处理上上月，下面是处理下月
json_data = {
    "funnelId": 188,
    "conditions": [{
        "no": 1,
        "eventName": "biz_open",
        "bizTypeId": 1,
        "conn": "and",
        "conditions": [],
        "etpConditions": [{
            "propName": "reg_ta_group",
            "op": 9,
            "dataTypeId": 1,
            "propTypeId": 2,
            "hasDict": True,
            "value": "[\"互动-活动引流(不下发销售)\"]",
            "conn": "and"
        }],
        "userGroupConditions": []
    }],
    "startTime": date2_begin,
    "endTime": date2_end,
    "dataFormatType": 1,
    "bssId": [],
    "groupByConditions": []
}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
item_list = tree['r_data']
for item in item_list:
    name = item['event_cname']
    num = int(item['total'])
    if name == '产品开通':
        a[1]['开通量'] = num
    elif name == '点击创建活动':
        a[1]['开通7日内点击预览活动'] = num
    elif name == '点击预览窗创建按钮':
        a[1]['开通7日内进入编辑页面'] = num
    elif name == '主动保存活动':
        a[1]['开通7日内成功创建活动'] = num
    elif name == '成功发布活动':
        a[1]['开通7日内成功发布活动'] = num

title = list(a[0].keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(title)):
        writer.writerow(["产品核心数据-新用户漏斗-非活动引流", title[i], a[0][title[i]], a[1][title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])
'''
    
# 产品核心数据 - 每日活跃帐号量
url = 'https://fdo.faisco.biz/basicdata/dailyDogStat/getData'
a = {
    '每日进入互动平台的帐号量': 0,
    '每日进入互动平台的帐号量（30天前开通）': 0,
}
json_data = {
    "time": "day",
    "condition_list": [{
        "key": "dog_id",
        "op": "eq",
        "val": [1000140],
        "opName": "等于"
    }],
    "matcher": "and",
    "begTime": begin2,
    "endTime": end2,
    "columns": {
        "src_id": True,
        "src_name": True,
        "total_cnt_avg": True,
        "client_cnt_avg": True,
        "obj_cnt_avg": True,
        "acct_cnt_avg": True,
        "total_pay_acct_cnt_avg": True,
        "total_cnt_rate": True,
        "client_cnt_rate": True,
        "obj_cnt_rate": True,
        "acct_cnt_rate": True,
        "total_pay_acct_cnt_rate": True
    },
    "columns_name": {
        "src_id": "src_id",
        "src_name": "名称",
        "total_cnt_avg": "日均次数",
        "client_cnt_avg": "日均用户数",
        "obj_cnt_avg": "日均实例数",
        "acct_cnt_avg": "日均企业数",
        "total_pay_acct_cnt_avg": "日均付费企业数",
        "total_cnt_rate": "总次数占比",
        "client_cnt_rate": "总用户数占比",
        "obj_cnt_rate": "总实例数占比",
        "acct_cnt_rate": "总企业数占比",
        "total_pay_acct_cnt_rate": "总付费企业数占比"
    }
}
res = session.post(url, headers=headers_fdo, json=json_data)
tree = res.json()
item_list = tree['r_data']
item_list = eval(item_list)
for item in item_list['table']:
    if item['group_field'] == -1:
        a['每日进入互动平台的帐号量'] = item['client_cnt_avg']
    elif item['group_field'] == 1:
        a['每日进入互动平台的帐号量（30天前开通）'] = item['client_cnt_avg']

with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for m, k in a.items():
        writer.writerow(["产品核心数据-每日活跃帐号量", m, "", k])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 产品核心数据 - 系统推荐评分 - 暂不评分
url = 'https://fdo.faisco.biz/basicdata/dailyDogStat/getData'
a = {'暂不评分': 0, }
json_data = {
    "time": "day",
    "condition_list": [{
        "key": "dog_id",
        "op": "eq",
        "val": [1000349],
        "opName": "等于"
    }],
    "matcher": "and",
    "begTime": begin2,
    "endTime": end2,
    "columns": {
        "src_id": True,
        "src_name": True,
        "total_cnt_avg": True,
        "client_cnt_avg": True,
        "obj_cnt_avg": True,
        "acct_cnt_avg": True,
        "total_pay_acct_cnt_avg": True,
        "total_cnt_rate": True,
        "client_cnt_rate": True,
        "obj_cnt_rate": True,
        "acct_cnt_rate": True,
        "total_pay_acct_cnt_rate": True
    },
    "columns_name": {
        "src_id": "src_id",
        "src_name": "名称",
        "total_cnt_avg": "日均次数",
        "client_cnt_avg": "日均用户数",
        "obj_cnt_avg": "日均实例数",
        "acct_cnt_avg": "日均企业数",
        "total_pay_acct_cnt_avg": "日均付费企业数",
        "total_cnt_rate": "总次数占比",
        "client_cnt_rate": "总用户数占比",
        "obj_cnt_rate": "总实例数占比",
        "acct_cnt_rate": "总企业数占比",
        "total_pay_acct_cnt_rate": "总付费企业数占比"
    }
}
res = session.post(url, headers=headers_fdo, json=json_data)
tree = res.json()
item_list = tree['r_data']
item_list = eval(item_list)
for item in item_list['table']:
    if item['group_field'] == 12:
        a['暂不评分'] = item['client_cnt_avg'] * len(item_list['chart'])

with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for m, k in a.items():
        writer.writerow(["产品核心数据-系统暂不评分", m, "", k])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 产品核心数据 - 口碑情况
url = 'https://bi.faisco.biz/api/card/3612/query'
json_data = {"parameters": [], "dashboard_id": 464}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
item_list = tree['data']['rows']
a = [{
    '开通量（朋友介绍）': 0,
    '首开互动量': 0,
    '朋友介绍占总开通量比': '',
    '互动首购有效版本订单量（朋友介绍）': 0,
    '互动首购有效版本订单量': 0,
    '互动首购有效版本订单金额（朋友介绍）': 0,
    '互动首购有效版本订单金额': 0,
}, {
    '开通量（朋友介绍）': 0,
    '首开互动量': 0,
    '朋友介绍占总开通量比': '',
    '互动首购有效版本订单量（朋友介绍）': 0,
    '互动首购有效版本订单量': 0,
    '互动首购有效版本订单金额（朋友介绍）': 0,
    '互动首购有效版本订单金额': 0,
}]
for item in item_list:
    if item[0] == begin3:
        a[0]['开通量（朋友介绍）'] = item[1]
    if item[0] == begin4:
        a[1]['开通量（朋友介绍）'] = item[1]

url = 'https://bi.faisco.biz/api/card/3613/query'
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
item_list = tree['data']['rows']
for item in item_list:
    if item[0] == begin3:
        a[0]['互动首购有效版本订单量（朋友介绍）'] = item[1]
        a[0]['互动首购有效版本订单金额（朋友介绍）'] = item[2]
    if item[0] == begin4:
        a[1]['互动首购有效版本订单量（朋友介绍）'] = int(item[1])
        a[1]['互动首购有效版本订单金额（朋友介绍）'] = int(item[2])

url = 'https://bi.faisco.biz/api/card/3615/query'
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
item_list = tree['data']['rows']
for item in item_list:
    if item[0] == begin3:
        a[0]['互动首购有效版本订单量'] = item[1]
        a[0]['互动首购有效版本订单金额'] = int(item[2])
    if item[0] == begin4:
        a[1]['互动首购有效版本订单量'] = item[1]
        a[1]['互动首购有效版本订单金额'] = int(item[2])

url = 'https://bi.faisco.biz/api/card/3616/query'
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
item_list = tree['data']['rows']
for item in item_list:
    if item[0] == begin3:
        a[0]['首开互动量'] = item[1]
    if item[0] == begin4:
        a[1]['首开互动量'] = item[1]

title = list(a[0].keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(title)):
        writer.writerow(["产品核心数据-口碑情况", title[i], a[0][title[i]], a[1][title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 产品核心数据 - 活动浏览量
url = 'https://fdo.faisco.biz/hd/gameViewStat/get'
json_data1 = {
    "order_by": "create_game_cnt",
    "group_field": "",
    "time": "",
    "condition_list": [],
    "matcher": "and",
    "begTime": begin1,
    "endTime": end1,
    "page": "/hd/gameViewStat",
    "columns": {
        "create_game_cnt": True,
        "game_cnt_with_0_uv_in_create_7days": True,
        "game_cnt_with_1_uv_in_create_7days": True,
        "game_cnt_with_2_10_uv_in_create_7days": True,
        "game_cnt_with_10_30_uv_in_create_7days": True,
        "game_cnt_with_30_100_uv_in_create_7days": True,
        "game_cnt_with_100_500_uv_in_create_7days": True,
        "game_cnt_with_500_1000_uv_in_create_7days": True,
        "game_cnt_with_over_1000_uv_in_create_7days": True,
        "game_cnt_with_0_uv_in_create_30days": True,
        "game_cnt_with_1_uv_in_create_30days": True,
        "game_cnt_with_2_10_uv_in_create_30days": True,
        "game_cnt_with_10_30_uv_in_create_30days": True,
        "game_cnt_with_30_100_uv_in_create_30days": True,
        "game_cnt_with_100_500_uv_in_create_30days": True,
        "game_cnt_with_500_1000_uv_in_create_30days": True,
        "game_cnt_with_over_1000_uv_in_create_30days": True
    },
    "columns_name": {
        "create_game_cnt": "创建量",
        "game_cnt_with_0_uv_in_create_7days": "7日[0]",
        "game_cnt_with_1_uv_in_create_7days": "7日[1]",
        "game_cnt_with_2_10_uv_in_create_7days": "7日[2-10]",
        "game_cnt_with_10_30_uv_in_create_7days": "7日[10-30]",
        "game_cnt_with_30_100_uv_in_create_7days": "7日[30-100]",
        "game_cnt_with_100_500_uv_in_create_7days": "7日[100-500]",
        "game_cnt_with_500_1000_uv_in_create_7days": "7日[500-1000]",
        "game_cnt_with_over_1000_uv_in_create_7days": "7日[1000- ]",
        "game_cnt_with_0_uv_in_create_30days": "30日[0]",
        "game_cnt_with_1_uv_in_create_30days": "30日[1]",
        "game_cnt_with_2_10_uv_in_create_30days": "30日[2-10]",
        "game_cnt_with_10_30_uv_in_create_30days": "30日[10-30]",
        "game_cnt_with_30_100_uv_in_create_30days": "30日[30-100]",
        "game_cnt_with_100_500_uv_in_create_30days": "30日[100-500]",
        "game_cnt_with_500_1000_uv_in_create_30days": "30日[500-1000]",
        "game_cnt_with_over_1000_uv_in_create_30days": "30日[1000- ]",
        "group_field": "分组项目",
        "time": "日期"
    },
    "dimension_list": ["reg_ta_id", "reg_ta_group_id", "reg_biz_name", "reg_company_goal", "reg_corp_trade_name",
                       "reg_entity", "reg_keyword", "ab_testing_type"],
    "array_type_list": []
}
json_data2 = {
    "order_by": "create_game_cnt",
    "group_field": "",
    "time": "",
    "condition_list": [],
    "matcher": "and",
    "begTime": begin2,
    "endTime": end2,
    "page": "/hd/gameViewStat",
    "columns": {
        "create_game_cnt": True,
        "game_cnt_with_0_uv_in_create_7days": True,
        "game_cnt_with_1_uv_in_create_7days": True,
        "game_cnt_with_2_10_uv_in_create_7days": True,
        "game_cnt_with_10_30_uv_in_create_7days": True,
        "game_cnt_with_30_100_uv_in_create_7days": True,
        "game_cnt_with_100_500_uv_in_create_7days": True,
        "game_cnt_with_500_1000_uv_in_create_7days": True,
        "game_cnt_with_over_1000_uv_in_create_7days": True,
        "game_cnt_with_0_uv_in_create_30days": True,
        "game_cnt_with_1_uv_in_create_30days": True,
        "game_cnt_with_2_10_uv_in_create_30days": True,
        "game_cnt_with_10_30_uv_in_create_30days": True,
        "game_cnt_with_30_100_uv_in_create_30days": True,
        "game_cnt_with_100_500_uv_in_create_30days": True,
        "game_cnt_with_500_1000_uv_in_create_30days": True,
        "game_cnt_with_over_1000_uv_in_create_30days": True
    },
    "columns_name": {
        "create_game_cnt": "创建量",
        "game_cnt_with_0_uv_in_create_7days": "7日[0]",
        "game_cnt_with_1_uv_in_create_7days": "7日[1]",
        "game_cnt_with_2_10_uv_in_create_7days": "7日[2-10]",
        "game_cnt_with_10_30_uv_in_create_7days": "7日[10-30]",
        "game_cnt_with_30_100_uv_in_create_7days": "7日[30-100]",
        "game_cnt_with_100_500_uv_in_create_7days": "7日[100-500]",
        "game_cnt_with_500_1000_uv_in_create_7days": "7日[500-1000]",
        "game_cnt_with_over_1000_uv_in_create_7days": "7日[1000- ]",
        "game_cnt_with_0_uv_in_create_30days": "30日[0]",
        "game_cnt_with_1_uv_in_create_30days": "30日[1]",
        "game_cnt_with_2_10_uv_in_create_30days": "30日[2-10]",
        "game_cnt_with_10_30_uv_in_create_30days": "30日[10-30]",
        "game_cnt_with_30_100_uv_in_create_30days": "30日[30-100]",
        "game_cnt_with_100_500_uv_in_create_30days": "30日[100-500]",
        "game_cnt_with_500_1000_uv_in_create_30days": "30日[500-1000]",
        "game_cnt_with_over_1000_uv_in_create_30days": "30日[1000- ]",
        "group_field": "分组项目",
        "time": "日期"
    },
    "dimension_list": ["reg_ta_id", "reg_ta_group_id", "reg_biz_name", "reg_company_goal", "reg_corp_trade_name",
                       "reg_entity", "reg_keyword", "ab_testing_type"],
    "array_type_list": []
}
res1 = session.post(url, headers=headers_fdo, json=json_data1)
res2 = session.post(url, headers=headers_fdo, json=json_data2)
tree1 = res1.json()
tree2 = res2.json()
item_list1 = tree1['data']
item_list2 = tree2['data']
a = [{
    '创建量': 0,
    '创建7天内的活动量（浏览人数=0）': 0,
    '创建7天内的活动量（浏览人数=1）': 0,
    '创建7天内的活动量（2<浏览人数<=10）': 0,
    '创建7天内的活动量（10<浏览人数<=30）': 0,
    '创建7天内的活动率（30<浏览人数<=100）': 0,
    '创建7天内的活动量（100<浏览人数<=500）': 0,
    '创建7天内的活动量（500<浏览人数<1000）': 0,
    '创建7天内的活动量（1000<=浏览人数）': 0,
}, {
    '创建量': 0,
    '创建7天内的活动量（浏览人数=0）': 0,
    '创建7天内的活动量（浏览人数=1）': 0,
    '创建7天内的活动量（2<浏览人数<=10）': 0,
    '创建7天内的活动量（10<浏览人数<=30）': 0,
    '创建7天内的活动率（30<浏览人数<=100）': 0,
    '创建7天内的活动量（100<浏览人数<=500）': 0,
    '创建7天内的活动量（500<浏览人数<1000）': 0,
    '创建7天内的活动量（1000<=浏览人数）': 0,
}]
for item in item_list1:
    a[0]['创建量'] = item['create_game_cnt']
    a[0]['创建7天内的活动量（浏览人数=0）'] = item['game_cnt_with_0_uv_in_create_7days']
    a[0]['创建7天内的活动量（浏览人数=1）'] = item['game_cnt_with_1_uv_in_create_7days']
    a[0]['创建7天内的活动量（2<浏览人数<=10）'] = item['game_cnt_with_2_10_uv_in_create_7days']
    a[0]['创建7天内的活动量（10<浏览人数<=30）'] = item['game_cnt_with_10_30_uv_in_create_7days']
    a[0]['创建7天内的活动率（30<浏览人数<=100）'] = item['game_cnt_with_30_100_uv_in_create_7days']
    a[0]['创建7天内的活动量（100<浏览人数<=500）'] = item['game_cnt_with_100_500_uv_in_create_7days']
    a[0]['创建7天内的活动量（500<浏览人数<1000）'] = item['game_cnt_with_500_1000_uv_in_create_7days']
    a[0]['创建7天内的活动量（1000<=浏览人数）'] = item['game_cnt_with_over_1000_uv_in_create_7days']
for item in item_list2:
    a[1]['创建量'] = item['create_game_cnt']
    a[1]['创建7天内的活动量（浏览人数=0）'] = item['game_cnt_with_0_uv_in_create_7days']
    a[1]['创建7天内的活动量（浏览人数=1）'] = item['game_cnt_with_1_uv_in_create_7days']
    a[1]['创建7天内的活动量（2<浏览人数<=10）'] = item['game_cnt_with_2_10_uv_in_create_7days']
    a[1]['创建7天内的活动量（10<浏览人数<=30）'] = item['game_cnt_with_10_30_uv_in_create_7days']
    a[1]['创建7天内的活动率（30<浏览人数<=100）'] = item['game_cnt_with_30_100_uv_in_create_7days']
    a[1]['创建7天内的活动量（100<浏览人数<=500）'] = item['game_cnt_with_100_500_uv_in_create_7days']
    a[1]['创建7天内的活动量（500<浏览人数<1000）'] = item['game_cnt_with_500_1000_uv_in_create_7days']
    a[1]['创建7天内的活动量（1000<=浏览人数）'] = item['game_cnt_with_over_1000_uv_in_create_7days']

title = list(a[0].keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(title)):
        writer.writerow(["产品核心数据-活动浏览量", title[i], a[0][title[i]], a[1][title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 产品核心数据 - 付费企业活动效果
url = 'https://fdo.faisco.biz/hd/gameViewStat/get'
json_data1 = {
    "order_by": "aid_uv_with_create_game",
    "group_field": "",
    "time": "",
    "condition_list": [],
    "matcher": "and",
    "begTime": begin1,
    "endTime": end1,
    "page": "/hd/payGameStat",
    "columns": {
        "aid_uv_with_create_game": True,
        "aid_uv_with_max_0_30": True,
        "aid_uv_with_max_30_100": True,
        "aid_uv_with_max_100_500": True,
        "aid_uv_with_max_500_1000": True,
        "aid_uv_with_max_over_1000": True
    },
    "columns_name": {
        "aid_uv_with_create_game": " 有创建活动企业量",
        "aid_uv_with_max_0_30": "最大浏览在【0-30）的企业量",
        "aid_uv_with_max_30_100": "最大浏览在【30-100）的企业量",
        "aid_uv_with_max_100_500": "最大浏览在【100-500）的企业量",
        "aid_uv_with_max_500_1000": "最大浏览在【500-1000）的企业量",
        "aid_uv_with_max_over_1000": "最大浏览在1000以上的企业量",
        "group_field": "分组项目",
        "time": "日期"
    },
    "dimension_list": ["reg_ta_id", "reg_ta_group_id", "open_product_first", "reg_biz_name", "reg_company_goal",
                       "reg_corp_trade_name", "reg_entity_name", "reg_keyword", "ab_testing_type"],
    "array_type_list": []
}
json_data2 = {
    "order_by": "aid_uv_with_create_game",
    "group_field": "",
    "time": "",
    "condition_list": [],
    "matcher": "and",
    "begTime": begin2,
    "endTime": end2,
    "page": "/hd/payGameStat",
    "columns": {
        "aid_uv_with_create_game": True,
        "aid_uv_with_max_0_30": True,
        "aid_uv_with_max_30_100": True,
        "aid_uv_with_max_100_500": True,
        "aid_uv_with_max_500_1000": True,
        "aid_uv_with_max_over_1000": True
    },
    "columns_name": {
        "aid_uv_with_create_game": " 有创建活动企业量",
        "aid_uv_with_max_0_30": "最大浏览在【0-30）的企业量",
        "aid_uv_with_max_30_100": "最大浏览在【30-100）的企业量",
        "aid_uv_with_max_100_500": "最大浏览在【100-500）的企业量",
        "aid_uv_with_max_500_1000": "最大浏览在【500-1000）的企业量",
        "aid_uv_with_max_over_1000": "最大浏览在1000以上的企业量",
        "group_field": "分组项目",
        "time": "日期"
    },
    "dimension_list": ["reg_ta_id", "reg_ta_group_id", "open_product_first", "reg_biz_name", "reg_company_goal",
                       "reg_corp_trade_name", "reg_entity_name", "reg_keyword", "ab_testing_type"],
    "array_type_list": []
}
res1 = session.post(url, headers=headers_fdo, json=json_data1)
res2 = session.post(url, headers=headers_fdo, json=json_data2)
tree1 = res1.json()
tree2 = res2.json()
item_list1 = tree1['data']
item_list2 = tree2['data']
a = [{
    '付费用户有创建活动的企业量': 0,
    '活动最大浏览在0-30区间的企业量': 0,
    '活动最大浏览在30-100区间的企业量': 0,
    '活动最大浏览在100-500区间的企业量': 0,
    '活动最大浏览在500-1000区间的企业量': 0,
    '活动最大浏览在1000以上的企业量': 0,
}, {
    '付费用户有创建活动的企业量': 0,
    '活动最大浏览在0-30区间的企业量': 0,
    '活动最大浏览在30-100区间的企业量': 0,
    '活动最大浏览在100-500区间的企业量': 0,
    '活动最大浏览在500-1000区间的企业量': 0,
    '活动最大浏览在1000以上的企业量': 0,
}]
for item in item_list1:
    a[0]['付费用户有创建活动的企业量'] = item['aid_uv_with_create_game']
    a[0]['活动最大浏览在0-30区间的企业量'] = item['aid_uv_with_max_0_30']
    a[0]['活动最大浏览在30-100区间的企业量'] = item['aid_uv_with_max_30_100']
    a[0]['活动最大浏览在100-500区间的企业量'] = item['aid_uv_with_max_100_500']
    a[0]['活动最大浏览在500-1000区间的企业量'] = item['aid_uv_with_max_500_1000']
    a[0]['活动最大浏览在1000以上的企业量'] = item['aid_uv_with_max_over_1000']
for item in item_list2:
    a[1]['付费用户有创建活动的企业量'] = item['aid_uv_with_create_game']
    a[1]['活动最大浏览在0-30区间的企业量'] = item['aid_uv_with_max_0_30']
    a[1]['活动最大浏览在30-100区间的企业量'] = item['aid_uv_with_max_30_100']
    a[1]['活动最大浏览在100-500区间的企业量'] = item['aid_uv_with_max_100_500']
    a[1]['活动最大浏览在500-1000区间的企业量'] = item['aid_uv_with_max_500_1000']
    a[1]['活动最大浏览在1000以上的企业量'] = item['aid_uv_with_max_over_1000']

title = list(a[0].keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(title)):
        writer.writerow(["产品核心数据-付费企业活动效果", title[i], a[0][title[i]], a[1][title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 产品核心数据 - 付费留存
url = 'https://fdo.faisco.biz/hd/payRetentionStat/get'
json_data1 = {
    "order_by": "aid_uv_with_pay",
    "group_field": "",
    "time": "",
    "condition_list": [],
    "matcher": "and",
    "begTime": begin1,
    "endTime": end1,
    "page": "/hd/payRetentionStat",
    "columns": {
        "aid_uv_with_pay": True,
        "retention_uv_with_pay": True,
        "retention_rate_with_pay": True,
        "publish_uv_with_pay": True,
        "publish_rate_with_pay": True
    },
    "columns_name": {
        "aid_uv_with_pay": "付费企业量",
        "retention_uv_with_pay": "付费留存量",
        "retention_rate_with_pay": "付费留存率",
        "publish_uv_with_pay": "付费发布量",
        "publish_rate_with_pay": "付费发布率",
        "group_field": "分组项目",
        "time": "日期"
    },
    "dimension_list": ["reg_ta_id", "reg_ta_group_id", "open_product_first", "reg_biz", "reg_company_goal",
                       "reg_corp_trade_name", "reg_entity", "reg_keyword"],
    "array_type_list": []
}
json_data2 = {
    "order_by": "aid_uv_with_pay",
    "group_field": "",
    "time": "",
    "condition_list": [],
    "matcher": "and",
    "begTime": begin2,
    "endTime": end2,
    "page": "/hd/payRetentionStat",
    "columns": {
        "aid_uv_with_pay": True,
        "retention_uv_with_pay": True,
        "retention_rate_with_pay": True,
        "publish_uv_with_pay": True,
        "publish_rate_with_pay": True
    },
    "columns_name": {
        "aid_uv_with_pay": "付费企业量",
        "retention_uv_with_pay": "付费留存量",
        "retention_rate_with_pay": "付费留存率",
        "publish_uv_with_pay": "付费发布量",
        "publish_rate_with_pay": "付费发布率",
        "group_field": "分组项目",
        "time": "日期"
    },
    "dimension_list": ["reg_ta_id", "reg_ta_group_id", "open_product_first", "reg_biz", "reg_company_goal",
                       "reg_corp_trade_name", "reg_entity", "reg_keyword"],
    "array_type_list": []
}
res1 = session.post(url, headers=headers_fdo, json=json_data1)
res2 = session.post(url, headers=headers_fdo, json=json_data2)
tree1 = res1.json()
tree2 = res2.json()
item_list1 = tree1['data']
item_list2 = tree2['data']
a = [{
    '互动付费企业数': 0,
    '互动付费用户有登录行为企业数': 0,
    '互动付费用户有发布行为企业数': 0,
}, {
    '互动付费企业数': 0,
    '互动付费用户有登录行为企业数': 0,
    '互动付费用户有发布行为企业数': 0,
}]
for item in item_list1:
    a[0]['互动付费企业数'] = item['aid_uv_with_pay']
    a[0]['互动付费用户有登录行为企业数'] = item['retention_uv_with_pay']
    a[0]['互动付费用户有发布行为企业数'] = item['publish_uv_with_pay']
for item in item_list2:
    a[1]['互动付费企业数'] = item['aid_uv_with_pay']
    a[1]['互动付费用户有登录行为企业数'] = item['retention_uv_with_pay']
    a[1]['互动付费用户有发布行为企业数'] = item['publish_uv_with_pay']

title = list(a[0].keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(title)):
        writer.writerow(["产品核心数据-付费留存", title[i], a[0][title[i]], a[1][title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 产品核心数据 - 日均玩家数据
url = 'https://fdo.faisco.biz/basicdata/dailyDogStat/getData'
json_data1 = {
    "time": "",
    "condition_list": [{
        "key": "dog_type",
        "op": "eq",
        "val": [0]
    }, {
        "key": "corp_or_noncorp",
        "op": "eq",
        "val": [0]
    }, {
        "key": "biz_type",
        "op": "eq",
        "val": [2]
    }],
    "matcher": "and",
    "begTime": begin1,
    "endTime": end1,
    "columns": {
        "dog_id": True,
        "dog_name": True,
        "total_cnt": True,
        "client_cnt": True,
        "obj_cnt": True,
        "acct_cnt": True,
        "total_pay_acct_cnt": True,
        "total_cnt_avg": True,
        "client_cnt_avg": True,
        "obj_cnt_avg": True,
        "acct_cnt_avg": True,
        "total_pay_acct_cnt_avg": True
    },
    "columns_name": {
        "dog_id": "dog_id",
        "dog_name": "行为",
        "total_cnt": "总次数",
        "client_cnt": "总用户数",
        "obj_cnt": "总实例数",
        "acct_cnt": "总企业数",
        "total_pay_acct_cnt": "总付费企业数",
        "total_cnt_avg": "日均次数",
        "client_cnt_avg": "日均用户数",
        "obj_cnt_avg": "日均实例数",
        "acct_cnt_avg": "日均企业数",
        "total_pay_acct_cnt_avg": "日均付费企业数"
    }
}
json_data2 = {
    "time": "",
    "condition_list": [{
        "key": "dog_type",
        "op": "eq",
        "val": [0]
    }, {
        "key": "corp_or_noncorp",
        "op": "eq",
        "val": [0]
    }, {
        "key": "biz_type",
        "op": "eq",
        "val": [2]
    }],
    "matcher": "and",
    "begTime": begin2,
    "endTime": end2,
    "columns": {
        "dog_id": True,
        "dog_name": True,
        "total_cnt": True,
        "client_cnt": True,
        "obj_cnt": True,
        "acct_cnt": True,
        "total_pay_acct_cnt": True,
        "total_cnt_avg": True,
        "client_cnt_avg": True,
        "obj_cnt_avg": True,
        "acct_cnt_avg": True,
        "total_pay_acct_cnt_avg": True
    },
    "columns_name": {
        "dog_id": "dog_id",
        "dog_name": "行为",
        "total_cnt": "总次数",
        "client_cnt": "总用户数",
        "obj_cnt": "总实例数",
        "acct_cnt": "总企业数",
        "total_pay_acct_cnt": "总付费企业数",
        "total_cnt_avg": "日均次数",
        "client_cnt_avg": "日均用户数",
        "obj_cnt_avg": "日均实例数",
        "acct_cnt_avg": "日均企业数",
        "total_pay_acct_cnt_avg": "日均付费企业数"
    }
}
res1 = session.post(url, headers=headers_fdo, json=json_data1)
res2 = session.post(url, headers=headers_fdo, json=json_data2)
tree1 = res1.json()
tree2 = res2.json()
item_list1 = json.loads(tree1['r_data'])
item_list2 = json.loads(tree2['r_data'])
item_list1 = item_list1['table']
item_list2 = item_list2['table']
a = [{
    '日均访问的玩家量（全部）': 0,
    '日均参与的玩家量（全部）': 0,
    '日均分享的玩家量（全部）': 0,
    '日均中奖的玩家量（全部）': 0,
    '日均访问的玩家量（付费）': 0,
    '日均参与的玩家量（付费）': 0,
    '日均分享的玩家量（付费）': 0,
    '日均中奖的玩家量（付费）': 0,
}, {
    '日均访问的玩家量（全部）': 0,
    '日均参与的玩家量（全部）': 0,
    '日均分享的玩家量（全部）': 0,
    '日均中奖的玩家量（全部）': 0,
    '日均访问的玩家量（付费）': 0,
    '日均参与的玩家量（付费）': 0,
    '日均分享的玩家量（付费）': 0,
    '日均中奖的玩家量（付费）': 0,
}]
for item in item_list1:
    if item['dog_id'] == 1000001:
        a[0]['日均访问的玩家量（全部）'] = item['client_cnt_avg']
    if item['dog_id'] == 1000002:
        a[0]['日均参与的玩家量（全部）'] = item['client_cnt_avg']
    if item['dog_id'] == 1000003:
        a[0]['日均中奖的玩家量（全部）'] = item['client_cnt_avg']
    if item['dog_id'] == 1000004:
        a[0]['日均分享的玩家量（全部）'] = item['client_cnt_avg']
for item in item_list2:
    if item['group_field'] == 1000001:
        a[1]['日均访问的玩家量（全部）'] = item['client_cnt_avg']
    if item['group_field'] == 1000002:
        a[1]['日均参与的玩家量（全部）'] = item['client_cnt_avg']
    if item['group_field'] == 1000003:
        a[1]['日均中奖的玩家量（全部）'] = item['client_cnt_avg']
    if item['group_field'] == 1000004:
        a[1]['日均分享的玩家量（全部）'] = item['client_cnt_avg']

t = [1000178, 1000179, 1000180, 1000181]
num1 = []
num2 = []
for i in t:
    json_data = {
        "time": "",
        "condition_list": [{
            "key": "dog_id",
            "op": "eq",
            "val": [i],
            "opName": "等于"
        }, {
            "key": "src_id",
            "op": "eq",
            "val": [1],
            "opName": "等于"
        }, {
            "key": "dog_type",
            "op": "eq",
            "val": [0]
        }, {
            "key": "corp_or_noncorp",
            "op": "eq",
            "val": [0]
        }, {
            "key": "biz_type",
            "op": "eq",
            "val": [2]
        }],
        "matcher": "and",
        "begTime": begin1,
        "endTime": end1,
        "columns": {
            "total_cnt": True,
            "client_cnt": True,
            "obj_cnt": True,
            "acct_cnt": True,
            "total_pay_acct_cnt": True,
            "total_cnt_avg": True,
            "client_cnt_avg": True,
            "obj_cnt_avg": True,
            "acct_cnt_avg": True,
            "total_pay_acct_cnt_avg": True,
            "total_cnt_rate": True,
            "client_cnt_rate": True,
            "obj_cnt_rate": True,
            "acct_cnt_rate": True,
            "total_pay_acct_cnt_rate": True
        },
        "columns_name": {
            "total_cnt": "总次数",
            "client_cnt": "总用户数",
            "obj_cnt": "总实例数",
            "acct_cnt": "总企业数",
            "total_pay_acct_cnt": "总付费企业数",
            "total_cnt_avg": "日均次数",
            "client_cnt_avg": "日均用户数",
            "obj_cnt_avg": "日均实例数",
            "acct_cnt_avg": "日均企业数",
            "total_pay_acct_cnt_avg": "日均付费企业数",
            "total_cnt_rate": "总次数占比",
            "client_cnt_rate": "总用户数占比",
            "obj_cnt_rate": "总实例数占比",
            "acct_cnt_rate": "总企业数占比",
            "total_pay_acct_cnt_rate": "总付费企业数占比"
        }
    }
    res = session.post(url, headers=headers_fdo, json=json_data)
    tree = res.json()
    item_list = json.loads(tree['r_data'])
    item_list = item_list['chart'][0]
    m = item_list['client_cnt_avg']
    num1.append(m)
for i in t:
    json_data = {
        "time": "",
        "condition_list": [{
            "key": "dog_id",
            "op": "eq",
            "val": [i],
            "opName": "等于"
        }, {
            "key": "src_id",
            "op": "eq",
            "val": [1],
            "opName": "等于"
        }, {
            "key": "dog_type",
            "op": "eq",
            "val": [0]
        }, {
            "key": "corp_or_noncorp",
            "op": "eq",
            "val": [0]
        }, {
            "key": "biz_type",
            "op": "eq",
            "val": [2]
        }],
        "matcher": "and",
        "begTime": begin2,
        "endTime": end2,
        "columns": {
            "total_cnt": True,
            "client_cnt": True,
            "obj_cnt": True,
            "acct_cnt": True,
            "total_pay_acct_cnt": True,
            "total_cnt_avg": True,
            "client_cnt_avg": True,
            "obj_cnt_avg": True,
            "acct_cnt_avg": True,
            "total_pay_acct_cnt_avg": True,
            "total_cnt_rate": True,
            "client_cnt_rate": True,
            "obj_cnt_rate": True,
            "acct_cnt_rate": True,
            "total_pay_acct_cnt_rate": True
        },
        "columns_name": {
            "total_cnt": "总次数",
            "client_cnt": "总用户数",
            "obj_cnt": "总实例数",
            "acct_cnt": "总企业数",
            "total_pay_acct_cnt": "总付费企业数",
            "total_cnt_avg": "日均次数",
            "client_cnt_avg": "日均用户数",
            "obj_cnt_avg": "日均实例数",
            "acct_cnt_avg": "日均企业数",
            "total_pay_acct_cnt_avg": "日均付费企业数",
            "total_cnt_rate": "总次数占比",
            "client_cnt_rate": "总用户数占比",
            "obj_cnt_rate": "总实例数占比",
            "acct_cnt_rate": "总企业数占比",
            "total_pay_acct_cnt_rate": "总付费企业数占比"
        }
    }
    res = session.post(url, headers=headers_fdo, json=json_data)
    tree = res.json()
    item_list = json.loads(tree['r_data'])
    item_list = item_list['chart'][0]
    m = item_list['client_cnt_avg']
    num2.append(m)

a[0]['日均访问的玩家量（付费）'] = a[0]['日均访问的玩家量（全部）'] - num1[0]
a[0]['日均参与的玩家量（付费）'] = a[0]['日均参与的玩家量（全部）'] - num1[1]
a[0]['日均中奖的玩家量（付费）'] = a[0]['日均中奖的玩家量（全部）'] - num1[2]
a[0]['日均分享的玩家量（付费）'] = a[0]['日均分享的玩家量（全部）'] - num1[3]
a[1]['日均访问的玩家量（付费）'] = a[1]['日均访问的玩家量（全部）'] - num2[0]
a[1]['日均参与的玩家量（付费）'] = a[1]['日均参与的玩家量（全部）'] - num2[1]
a[1]['日均中奖的玩家量（付费）'] = a[1]['日均中奖的玩家量（全部）'] - num2[2]
a[1]['日均分享的玩家量（付费）'] = a[1]['日均分享的玩家量（全部）'] - num2[3]

title = list(a[0].keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(title)):
        writer.writerow(["产品核心数据-日均玩家数据", title[i], a[0][title[i]], a[1][title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

print("执行成功.")
