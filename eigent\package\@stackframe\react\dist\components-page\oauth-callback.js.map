{"version": 3, "sources": ["../../src/components-page/oauth-callback.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { captureError } from \"@stackframe/stack-shared/dist/utils/errors\";\nimport { runAsynchronously } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { Spinner, cn } from \"@stackframe/stack-ui\";\nimport { useEffect, useRef, useState } from \"react\";\nimport { useStackApp } from \"..\";\nimport { MaybeFullPage } from \"../components/elements/maybe-full-page\";\nimport { StyledLink } from \"../components/link\";\nimport { useTranslation } from \"../lib/translations\";\n\nexport function OAuthCallback({ fullPage }: { fullPage?: boolean }) {\n  const { t } = useTranslation();\n  const app = useStackApp();\n  const called = useRef(false);\n  const [error, setError] = useState<unknown>(null);\n  const [showRedirectLink, setShowRedirectLink] = useState(false);\n\n  useEffect(() => runAsynchronously(async () => {\n    if (called.current) return;\n    called.current = true;\n    let hasRedirected = false;\n    try {\n      hasRedirected = await app.callOAuthCallback();\n    } catch (e) {\n      captureError(\"<OAuthCallback />\", e);\n      setError(e);\n    }\n    if (!hasRedirected && (!error || process.env.NODE_ENV === 'production')) {\n      await app.redirectToSignIn({ noRedirectBack: true });\n    }\n  }), []);\n\n  useEffect(() => {\n    setTimeout(() => setShowRedirectLink(true), 3000);\n  }, []);\n\n  return (\n    <MaybeFullPage\n      fullPage={fullPage ?? false}\n      containerClassName=\"flex items-center justify-center\"\n    >\n      <div\n        className={cn(\n          \"text-center justify-center items-center stack-scope flex flex-col gap-4 max-w-[380px]\",\n          fullPage ? \"p-4\" : \"p-0\"\n        )}\n      >\n        <div className=\"flex flex-col justify-center items-center gap-4\">\n          <Spinner size={20} />\n        </div>\n        {showRedirectLink ? <p>{t('If you are not redirected automatically, ')}<StyledLink className=\"whitespace-nowrap\" href={app.urls.home}>{t(\"click here\")}</StyledLink></p> : null}\n        {error ? <div>\n          <p>{t(\"Something went wrong while processing the OAuth callback:\")}</p>\n          <pre>{JSON.stringify(error, null, 2)}</pre>\n          <p>{t(\"This is most likely an error in Stack. Please report it.\")}</p>\n        </div> : null}\n      </div>\n    </MaybeFullPage>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,oBAA6B;AAC7B,sBAAkC;AAClC,sBAA4B;AAC5B,mBAA4C;AAC5C,eAA4B;AAC5B,6BAA8B;AAC9B,kBAA2B;AAC3B,0BAA+B;AAwCrB;AAtCH,SAAS,cAAc,EAAE,SAAS,GAA2B;AAClE,QAAM,EAAE,EAAE,QAAI,oCAAe;AAC7B,QAAM,UAAM,sBAAY;AACxB,QAAM,aAAS,qBAAO,KAAK;AAC3B,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAAkB,IAAI;AAChD,QAAM,CAAC,kBAAkB,mBAAmB,QAAI,uBAAS,KAAK;AAE9D,8BAAU,UAAM,mCAAkB,YAAY;AAC5C,QAAI,OAAO,QAAS;AACpB,WAAO,UAAU;AACjB,QAAI,gBAAgB;AACpB,QAAI;AACF,sBAAgB,MAAM,IAAI,kBAAkB;AAAA,IAC9C,SAAS,GAAG;AACV,sCAAa,qBAAqB,CAAC;AACnC,eAAS,CAAC;AAAA,IACZ;AACA,QAAI,CAAC,kBAAkB,CAAC,SAAS,QAAQ,IAAI,aAAa,eAAe;AACvE,YAAM,IAAI,iBAAiB,EAAE,gBAAgB,KAAK,CAAC;AAAA,IACrD;AAAA,EACF,CAAC,GAAG,CAAC,CAAC;AAEN,8BAAU,MAAM;AACd,eAAW,MAAM,oBAAoB,IAAI,GAAG,GAAI;AAAA,EAClD,GAAG,CAAC,CAAC;AAEL,SACE;AAAA,IAAC;AAAA;AAAA,MACC,UAAU,YAAY;AAAA,MACtB,oBAAmB;AAAA,MAEnB;AAAA,QAAC;AAAA;AAAA,UACC,eAAW;AAAA,YACT;AAAA,YACA,WAAW,QAAQ;AAAA,UACrB;AAAA,UAEA;AAAA,wDAAC,SAAI,WAAU,mDACb,sDAAC,2BAAQ,MAAM,IAAI,GACrB;AAAA,YACC,mBAAmB,6CAAC,OAAG;AAAA,gBAAE,2CAA2C;AAAA,cAAE,4CAAC,0BAAW,WAAU,qBAAoB,MAAM,IAAI,KAAK,MAAO,YAAE,YAAY,GAAE;AAAA,eAAa,IAAO;AAAA,YAC1K,QAAQ,6CAAC,SACR;AAAA,0DAAC,OAAG,YAAE,2DAA2D,GAAE;AAAA,cACnE,4CAAC,SAAK,eAAK,UAAU,OAAO,MAAM,CAAC,GAAE;AAAA,cACrC,4CAAC,OAAG,YAAE,0DAA0D,GAAE;AAAA,eACpE,IAAS;AAAA;AAAA;AAAA,MACX;AAAA;AAAA,EACF;AAEJ;", "names": []}