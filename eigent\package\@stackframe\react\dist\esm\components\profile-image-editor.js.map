{"version": 3, "sources": ["../../../src/components/profile-image-editor.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { fileToBase64 } from '@stackframe/stack-shared/dist/utils/base64';\nimport { runAsynchronouslyWithAlert } from '@stackframe/stack-shared/dist/utils/promises';\nimport { Button, Slider, Typography } from '@stackframe/stack-ui';\nimport imageCompression from 'browser-image-compression';\nimport { Upload } from 'lucide-react';\nimport { ComponentProps, useRef, useState } from 'react';\nimport AvatarEditor from 'react-avatar-editor';\nimport { useTranslation } from '../lib/translations';\nimport { UserAvatar } from './elements/user-avatar';\n\nexport async function checkImageUrl(url: string){\n  try {\n    const res = await fetch(url, { method: 'HEAD' });\n    const buff = await res.blob();\n    return buff.type.startsWith('image/');\n  } catch (e) {\n    return false;\n  }\n}\n\nexport function ProfileImageEditor(props: {\n  user: NonNullable<ComponentProps<typeof UserAvatar>['user']>,\n  onProfileImageUrlChange: (profileImageUrl: string | null) => void | Promise<void>,\n}) {\n  const { t } = useTranslation();\n  const cropRef = useRef<AvatarEditor>(null);\n  const [slideValue, setSlideValue] = useState(1);\n  const [rawUrl, setRawUrl] = useState<string | null>(null);\n  const [error, setError] = useState<string | null>(null);\n\n  function reset() {\n    setSlideValue(1);\n    setRawUrl(null);\n    setError(null);\n  }\n\n  function upload() {\n    const input = document.createElement('input');\n    input.type = 'file';\n    input.onchange = (e) => {\n      const file = (e.target as HTMLInputElement).files?.[0];\n      if (!file) return;\n      runAsynchronouslyWithAlert(async () => {\n        const rawUrl = await fileToBase64(file);\n        if (await checkImageUrl(rawUrl)) {\n          setRawUrl(rawUrl);\n          setError(null);\n        } else {\n          setError(t('Invalid image'));\n        }\n        input.remove();\n      });\n    };\n    input.click();\n  }\n\n  if (!rawUrl) {\n    return <div className='flex flex-col'>\n      <div className='cursor-pointer relative' onClick={upload}>\n        <UserAvatar\n          size={60}\n          user={props.user}\n          border\n        />\n        <div className='absolute top-0 left-0 h-[60px] w-[60px] bg-gray-500/20 backdrop-blur-sm items-center justify-center rounded-full flex opacity-0 hover:opacity-100 transition-opacity'>\n          <div className='bg-background p-2 rounded-full'>\n            <Upload className='h-5 w-5' />\n          </div>\n        </div>\n      </div>\n      {error && <Typography variant='destructive' type='label'>{error}</Typography>}\n    </div>;\n  }\n\n  return (\n    <div className='flex flex-col items-center gap-4'>\n      <AvatarEditor\n        ref={cropRef}\n        image={rawUrl || props.user.profileImageUrl || \"\"}\n        borderRadius={1000}\n        color={[0, 0, 0, 0.72]}\n        scale={slideValue}\n        rotate={0}\n        border={20}\n        className='border'\n      />\n      <Slider\n        min={1}\n        max={5}\n        step={0.1}\n        defaultValue={[slideValue]}\n        value={[slideValue]}\n        onValueChange={(v) => setSlideValue(v[0])}\n      />\n\n      <div className='flex flex-row gap-2'>\n        <Button\n          onClick={async () => {\n            if (cropRef.current && rawUrl) {\n              const croppedUrl = cropRef.current.getImage().toDataURL('image/jpeg');\n              const compressedFile = await imageCompression(\n                await imageCompression.getFilefromDataUrl(croppedUrl, 'profile-image'),\n                {\n                  maxSizeMB: 0.1,\n                  fileType: \"image/jpeg\",\n                }\n              );\n              const compressedUrl = await imageCompression.getDataUrlFromFile(compressedFile);\n              await props.onProfileImageUrlChange(compressedUrl);\n              reset();\n            }\n          }}\n        >\n          {t('Save')}\n        </Button>\n        <Button\n          variant=\"secondary\"\n          onClick={reset}\n        >\n          {t('Cancel')}\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "mappings": ";AAIA,SAAS,oBAAoB;AAC7B,SAAS,kCAAkC;AAC3C,SAAS,QAAQ,QAAQ,kBAAkB;AAC3C,OAAO,sBAAsB;AAC7B,SAAS,cAAc;AACvB,SAAyB,QAAQ,gBAAgB;AACjD,OAAO,kBAAkB;AACzB,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAkDrB,SACE,KADF;AAhDN,eAAsB,cAAc,KAAY;AAC9C,MAAI;AACF,UAAM,MAAM,MAAM,MAAM,KAAK,EAAE,QAAQ,OAAO,CAAC;AAC/C,UAAM,OAAO,MAAM,IAAI,KAAK;AAC5B,WAAO,KAAK,KAAK,WAAW,QAAQ;AAAA,EACtC,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;AAEO,SAAS,mBAAmB,OAGhC;AACD,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,UAAU,OAAqB,IAAI;AACzC,QAAM,CAAC,YAAY,aAAa,IAAI,SAAS,CAAC;AAC9C,QAAM,CAAC,QAAQ,SAAS,IAAI,SAAwB,IAAI;AACxD,QAAM,CAAC,OAAO,QAAQ,IAAI,SAAwB,IAAI;AAEtD,WAAS,QAAQ;AACf,kBAAc,CAAC;AACf,cAAU,IAAI;AACd,aAAS,IAAI;AAAA,EACf;AAEA,WAAS,SAAS;AAChB,UAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,UAAM,OAAO;AACb,UAAM,WAAW,CAAC,MAAM;AACtB,YAAM,OAAQ,EAAE,OAA4B,QAAQ,CAAC;AACrD,UAAI,CAAC,KAAM;AACX,iCAA2B,YAAY;AACrC,cAAMA,UAAS,MAAM,aAAa,IAAI;AACtC,YAAI,MAAM,cAAcA,OAAM,GAAG;AAC/B,oBAAUA,OAAM;AAChB,mBAAS,IAAI;AAAA,QACf,OAAO;AACL,mBAAS,EAAE,eAAe,CAAC;AAAA,QAC7B;AACA,cAAM,OAAO;AAAA,MACf,CAAC;AAAA,IACH;AACA,UAAM,MAAM;AAAA,EACd;AAEA,MAAI,CAAC,QAAQ;AACX,WAAO,qBAAC,SAAI,WAAU,iBACpB;AAAA,2BAAC,SAAI,WAAU,2BAA0B,SAAS,QAChD;AAAA;AAAA,UAAC;AAAA;AAAA,YACC,MAAM;AAAA,YACN,MAAM,MAAM;AAAA,YACZ,QAAM;AAAA;AAAA,QACR;AAAA,QACA,oBAAC,SAAI,WAAU,wKACb,8BAAC,SAAI,WAAU,kCACb,8BAAC,UAAO,WAAU,WAAU,GAC9B,GACF;AAAA,SACF;AAAA,MACC,SAAS,oBAAC,cAAW,SAAQ,eAAc,MAAK,SAAS,iBAAM;AAAA,OAClE;AAAA,EACF;AAEA,SACE,qBAAC,SAAI,WAAU,oCACb;AAAA;AAAA,MAAC;AAAA;AAAA,QACC,KAAK;AAAA,QACL,OAAO,UAAU,MAAM,KAAK,mBAAmB;AAAA,QAC/C,cAAc;AAAA,QACd,OAAO,CAAC,GAAG,GAAG,GAAG,IAAI;AAAA,QACrB,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,WAAU;AAAA;AAAA,IACZ;AAAA,IACA;AAAA,MAAC;AAAA;AAAA,QACC,KAAK;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,QACN,cAAc,CAAC,UAAU;AAAA,QACzB,OAAO,CAAC,UAAU;AAAA,QAClB,eAAe,CAAC,MAAM,cAAc,EAAE,CAAC,CAAC;AAAA;AAAA,IAC1C;AAAA,IAEA,qBAAC,SAAI,WAAU,uBACb;AAAA;AAAA,QAAC;AAAA;AAAA,UACC,SAAS,YAAY;AACnB,gBAAI,QAAQ,WAAW,QAAQ;AAC7B,oBAAM,aAAa,QAAQ,QAAQ,SAAS,EAAE,UAAU,YAAY;AACpE,oBAAM,iBAAiB,MAAM;AAAA,gBAC3B,MAAM,iBAAiB,mBAAmB,YAAY,eAAe;AAAA,gBACrE;AAAA,kBACE,WAAW;AAAA,kBACX,UAAU;AAAA,gBACZ;AAAA,cACF;AACA,oBAAM,gBAAgB,MAAM,iBAAiB,mBAAmB,cAAc;AAC9E,oBAAM,MAAM,wBAAwB,aAAa;AACjD,oBAAM;AAAA,YACR;AAAA,UACF;AAAA,UAEC,YAAE,MAAM;AAAA;AAAA,MACX;AAAA,MACA;AAAA,QAAC;AAAA;AAAA,UACC,SAAQ;AAAA,UACR,SAAS;AAAA,UAER,YAAE,QAAQ;AAAA;AAAA,MACb;AAAA,OACF;AAAA,KACF;AAEJ;", "names": ["rawUrl"]}