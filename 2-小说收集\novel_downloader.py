import requests
from bs4 import BeautifulSoup
import threading
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from urllib.parse import urljoin
import concurrent.futures
import os
import re
import configparser

def load_config():
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.ini')
    default = {
        'save_path': 'novels',
        'max_workers': '8',
        'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36',
        'merge_format': 'txt'
    }
    config['Settings'] = default
    if os.path.exists(config_path):
        config.read(config_path)
    else:
        with open(config_path, 'w') as f:
            config.write(f)
    return config

def get_chapter_list(base_url, headers):
    try:
        # 保证base_url以/结尾，urljoin才能正确拼接
        if not base_url.endswith('/'):
            base_url += '/'
        resp = requests.get(base_url, headers=headers, timeout=10)
        resp.raise_for_status()
        resp.encoding = 'utf-8'
        soup = BeautifulSoup(resp.text, 'html.parser')
        chapter_list = []
        # 兼容不同站点目录结构
        list_tag = soup.select_one('#list') or soup.select_one('.listmain') or soup
        a_tags = list_tag.find_all('a') if list_tag else []
        for a in a_tags:
            href = a.get('href')
            title = a.text.strip()
            if href and href.endswith('.html'):
                full_url = urljoin(base_url, href)
                chapter_list.append((title, full_url))
        if not chapter_list:
            raise Exception(f"未找到章节，a标签数量: {len(a_tags)}，请检查目录页结构")
        return chapter_list
    except Exception as e:
        raise Exception(f"获取章节列表失败: {str(e)}")

def get_chapter_content(url, headers):
    try:
        resp = requests.get(url, headers=headers, timeout=10)
        resp.raise_for_status()
        resp.encoding = 'utf-8'
        soup = BeautifulSoup(resp.text, 'html.parser')
        # 优先查找<div id="chaptercontent" class="Readarea ReadAjax_content">
        content = soup.find('div', id='chaptercontent', class_='Readarea ReadAjax_content')
        if not content:
            content = soup.select_one('#content') or soup.select_one('.content') or soup.find('div', id=True)
        if content:
            # 移除所有<p class="readinline">标签
            for p in content.find_all('p', class_='readinline'):
                p.decompose()
            return content.get_text('\n', strip=True)
        return '（未能获取章节内容，未找到正文div）'
    except Exception as e:
        return f'（获取章节内容失败: {str(e)}）'

def clean_text(text):
    text = re.sub(r'<br\s*/?>', '', text, flags=re.I)  # 去除所有<br />标签
    text = re.sub(r'[\u200b\xa0\r\f\v]', '', text)
    lines = [line.rstrip() for line in text.splitlines()]
    # 去除首尾空行
    while lines and not lines[0]:
        lines.pop(0)
    while lines and not lines[-1]:
        lines.pop()
    result = []
    prev_blank = False
    skip_keywords = [
        '新书开张，感谢各位老爷捧场',
        '请收藏本站',
        '笔趣阁手机版',
        'https://www.7c09b.icu',
        'https://m.7c09b.icu'
    ]
    for line in lines:
        if any(kw in line for kw in skip_keywords):
            continue
        if not line.strip():
            if not prev_blank:
                result.append('')
            prev_blank = True
        else:
            result.append(line)
            prev_blank = False
    return '\n'.join(result)

def save_chapter(idx, title, content, folder):
    safe_title = ''.join(c for c in title if c not in '\\/:*?<>|"') or f'chapter_{idx+1}'
    filename = f"{idx+1:04d}_{safe_title}.txt"
    filepath = os.path.join(folder, filename)
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(f'{title}\n\n')
        f.write(content)
        f.write('\n')
    return filename

def merge_chapters(novel_folder, merged_path, sep='\n\n' + '-'*40 + '\n', title_fmt='【{}】'):
    chapter_files = sorted([
        f for f in os.listdir(novel_folder)
        if f.endswith('.txt')
    ])
    with open(merged_path, 'w', encoding='utf-8') as merged:
        for fname in chapter_files:
            fpath = os.path.join(novel_folder, fname)
            with open(fpath, 'r', encoding='utf-8') as single:
                lines = single.readlines()
                if not lines:
                    continue
                chapter_title = lines[0].strip()
                merged.write(f'\n\n{title_fmt.format(chapter_title)}\n')
                merged.write(''.join(lines[1:]).strip())
                merged.write(sep)

def merge_novel_files(config):
    novels_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), config['Settings']['save_path'])
    folder = filedialog.askdirectory(title='请选择要合并的小说文件夹', initialdir=novels_dir)
    if not folder or not os.path.isdir(folder):
        return
    novel_title = os.path.basename(folder)
    try:
        merged_path = os.path.join(novels_dir, f'{novel_title}.txt')
        merge_chapters(folder, merged_path)
        messagebox.showinfo('完成', f'合并完成，文件已保存为 {merged_path}')
    except Exception as e:
        messagebox.showerror('错误', f'合并章节失败: {e}')

def parse_novel_url(user_input):
    user_input = user_input.strip()
    if user_input.isdigit():
        return f"https://www.7c09b.icu/html/{user_input}/"
    if user_input.startswith('http'):
        return user_input
    m = re.match(r'^(?:html/)?(\d+)/?$', user_input)
    if m:
        return f"https://www.7c09b.icu/html/{m.group(1)}/"
    return user_input

def get_novel_title(novel_url, headers):
    try:
        resp = requests.get(novel_url, headers=headers, timeout=10)
        resp.encoding = 'utf-8'
        soup = BeautifulSoup(resp.text, 'html.parser')
        if soup.title and soup.title.string:
            title = soup.title.string.strip()
            title = re.sub(r'[-_—|｜·•~【】\s]*(笔趣阁)?[\s\S]*$', '', title)
            if title:
                return title
        h1 = soup.find('h1')
        if h1 and h1.text.strip():
            return h1.text.strip()
        return novel_url.rstrip('/').split('/')[-1]
    except Exception:
        return novel_url.rstrip('/').split('/')[-1]

def start_download(url, progress_bar, status_label, start_button, url_entry, config):
    def show_messagebox(kind, title, msg):
        progress_bar.after(0, lambda: messagebox.showerror(title, msg) if kind == 'error' else messagebox.showinfo(title, msg))

    def update_status(msg):
        status_label.after(0, lambda: status_label.config(text=msg))

    def task():
        try:
            if not url:
                show_messagebox('error', '错误', '请输入小说唯一id或网址')
                return
            real_url = parse_novel_url(url)
            update_status("正在获取章节列表...")
            headers = {'User-Agent': config['Settings']['user_agent']}
            chapters = get_chapter_list(real_url, headers)
            total = len(chapters)
            if total == 0:
                show_messagebox('error', '错误', '未找到章节')
                return
            update_status(f"共找到 {total} 个章节，开始下载...")
            progress_bar['maximum'] = total
            progress_bar['value'] = 0
            start_button['state'] = 'disabled'
            url_entry['state'] = 'disabled'
            script_dir = os.path.dirname(os.path.abspath(__file__))
            folder = os.path.join(script_dir, config['Settings']['save_path'])
            os.makedirs(folder, exist_ok=True)
            # 获取小说名
            novel_title = get_novel_title(real_url, headers)
            novel_title = ''.join(c for c in novel_title if c not in '\\/:*?<>|"') or 'novel'
            novel_folder = os.path.join(folder, novel_title)
            os.makedirs(novel_folder, exist_ok=True)
            seen_titles = set()
            max_workers = int(config['Settings']['max_workers'])
            def fetch(idx, title, chapter_url):
                if title in seen_titles:
                    return None
                seen_titles.add(title)
                update_status(f"正在下载: {title}")
                content = get_chapter_content(chapter_url, headers)
                clean_content = clean_text(content)
                save_chapter(idx, title, clean_content, novel_folder)
                return (idx, title, clean_content)
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_idx = {executor.submit(fetch, idx, title, chapter_url): idx for idx, (title, chapter_url) in enumerate(chapters)}
                results = [None] * total
                for i, future in enumerate(concurrent.futures.as_completed(future_to_idx)):
                    result = future.result()
                    if result:
                        idx, title, clean_content = result
                        results[idx] = (title, clean_content)
                    def update_progress(i=i, title=title if result else '...'):
                        progress_bar.config(value=i+1)
                        pct = int(100*(i+1)/total)
                        status_label.config(text=f"下载进度: {pct}% - {title}")
                    progress_bar.after(0, update_progress)
                    progress_bar.after(0, progress_bar.update)
            update_status("下载完成")
            show_messagebox('info', '完成', f'下载完成，章节已保存在“{novel_title}”文件夹中\n（所有小说均保存在本程序同目录下的 novels 文件夹）')
        except Exception as e:
            update_status(f"下载失败: {str(e)}")
            show_messagebox('error', '错误', str(e))
        finally:
            progress_bar.pack(fill=tk.X, pady=10)
            progress_bar.after(0, progress_bar.pack_forget)
            progress_bar.after(0, lambda: start_button.config(state='normal'))
            progress_bar.after(0, lambda: url_entry.config(state='normal'))
            progress_bar.after(0, lambda: status_label.config(text="就绪"))
    
    progress_bar.pack(fill=tk.X, pady=10)
    threading.Thread(target=task, daemon=True).start()

def main():
    config = load_config()
    
    root = tk.Tk()
    root.title('小说下载器')
    width, height = 800, 450  # 16:9
    root.geometry(f'{width}x{height}')
    root.minsize(400, 225)
    root.resizable(True, True)
    
    # 主框架
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # URL输入区域
    url_frame = ttk.Frame(main_frame)
    url_frame.pack(fill=tk.X, pady=10)
    
    ttk.Label(url_frame, text='网址:').pack(side=tk.LEFT, padx=5)
    url_entry = ttk.Entry(url_frame, width=60)
    url_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
    
    # 按钮区域
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=10)
    
    start_button = ttk.Button(button_frame, text='下载', width=20,
        command=lambda: start_download(url_entry.get().strip(), progress_bar, status_label, start_button, url_entry, config))
    start_button.pack(side=tk.LEFT, padx=5)
    
    merge_button = ttk.Button(button_frame, text='文件合并', width=20, command=lambda: merge_novel_files(config))
    merge_button.pack(side=tk.LEFT, padx=5)
    
    # 进度条
    progress_bar = ttk.Progressbar(main_frame, orient='horizontal', mode='determinate', length=600)
    progress_bar.pack(fill=tk.X, pady=10)
    progress_bar.pack_forget()
    
    # 状态标签
    status_label = ttk.Label(main_frame, text="就绪", anchor=tk.W)
    status_label.pack(fill=tk.X, pady=5)
    
    # 配置按钮
    def open_settings():
        # 这里可以实现配置界面
        messagebox.showinfo("设置", "配置文件位于: " + os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.ini'))
    settings_button = ttk.Button(main_frame, text="设置", command=open_settings)
    settings_button.pack(anchor=tk.SE, pady=10)

    # 底部水印
    watermark = ttk.Label(root, text="水印：本软件仅供学习交流，禁止用于商业用途。制作人：兰陵笑笑生，请联系QQ1285374383", foreground="#888", anchor=tk.CENTER, font=("黑体", 9, "bold"))
    watermark.pack(side=tk.BOTTOM, fill=tk.X, pady=2)
    
    root.mainloop()

if __name__ == '__main__':
    main()