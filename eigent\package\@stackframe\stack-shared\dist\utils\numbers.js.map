{"version": 3, "sources": ["../../src/utils/numbers.tsx"], "sourcesContent": ["const magnitudes = [\n  [1_000_000_000_000_000, \"trln\"],\n  [1_000_000_000_000, \"bln\"],\n  [1_000_000_000, \"bn\"],\n  [1_000_000, \"M\"],\n  [1_000, \"k\"],\n] as const;\n\nexport function prettyPrintWithMagnitudes(num: number): string {\n  if (typeof num !== \"number\") throw new Error(\"Expected a number\");\n  if (Number.isNaN(num)) return \"NaN\";\n  if (num < 0) return \"-\" + prettyPrintWithMagnitudes(-num);\n  if (!Number.isFinite(num)) return \"∞\";\n\n  for (const [magnitude, suffix] of magnitudes) {\n    if (num >= magnitude) {\n      return toFixedMax(num / magnitude, 1) + suffix;\n    }\n  }\n  return toFixedMax(num, 1); // Handle numbers less than 1,000 without suffix.\n}\nundefined?.test(\"prettyPrintWithMagnitudes\", ({ expect }) => {\n  // Test different magnitudes\n  expect(prettyPrintWithMagnitudes(1000)).toBe(\"1k\");\n  expect(prettyPrintWithMagnitudes(1500)).toBe(\"1.5k\");\n  expect(prettyPrintWithMagnitudes(1000000)).toBe(\"1M\");\n  expect(prettyPrintWithMagnitudes(1500000)).toBe(\"1.5M\");\n  expect(prettyPrintWithMagnitudes(1000000000)).toBe(\"1bn\");\n  expect(prettyPrintWithMagnitudes(1500000000)).toBe(\"1.5bn\");\n  expect(prettyPrintWithMagnitudes(1000000000000)).toBe(\"1bln\");\n  expect(prettyPrintWithMagnitudes(1500000000000)).toBe(\"1.5bln\");\n  expect(prettyPrintWithMagnitudes(1000000000000000)).toBe(\"1trln\");\n  expect(prettyPrintWithMagnitudes(1500000000000000)).toBe(\"1.5trln\");\n  // Test small numbers\n  expect(prettyPrintWithMagnitudes(100)).toBe(\"100\");\n  expect(prettyPrintWithMagnitudes(0)).toBe(\"0\");\n  expect(prettyPrintWithMagnitudes(0.5)).toBe(\"0.5\");\n  // Test negative numbers\n  expect(prettyPrintWithMagnitudes(-1000)).toBe(\"-1k\");\n  expect(prettyPrintWithMagnitudes(-1500000)).toBe(\"-1.5M\");\n  // Test special cases\n  expect(prettyPrintWithMagnitudes(NaN)).toBe(\"NaN\");\n  expect(prettyPrintWithMagnitudes(Infinity)).toBe(\"∞\");\n  expect(prettyPrintWithMagnitudes(-Infinity)).toBe(\"-∞\");\n});\n\nexport function toFixedMax(num: number, maxDecimals: number): string {\n  return num.toFixed(maxDecimals).replace(/\\.?0+$/, \"\");\n}\nundefined?.test(\"toFixedMax\", ({ expect }) => {\n  expect(toFixedMax(1, 2)).toBe(\"1\");\n  expect(toFixedMax(1.2, 2)).toBe(\"1.2\");\n  expect(toFixedMax(1.23, 2)).toBe(\"1.23\");\n  expect(toFixedMax(1.234, 2)).toBe(\"1.23\");\n  expect(toFixedMax(1.0, 2)).toBe(\"1\");\n  expect(toFixedMax(1.20, 2)).toBe(\"1.2\");\n  expect(toFixedMax(0, 2)).toBe(\"0\");\n});\n\nexport function numberCompare(a: number, b: number): number {\n  return Math.sign(a - b);\n}\nundefined?.test(\"numberCompare\", ({ expect }) => {\n  expect(numberCompare(1, 2)).toBe(-1);\n  expect(numberCompare(2, 1)).toBe(1);\n  expect(numberCompare(1, 1)).toBe(0);\n  expect(numberCompare(0, 0)).toBe(0);\n  expect(numberCompare(-1, -2)).toBe(1);\n  expect(numberCompare(-2, -1)).toBe(-1);\n  expect(numberCompare(-1, 1)).toBe(-1);\n  expect(numberCompare(1, -1)).toBe(1);\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAM,aAAa;AAAA,EACjB,CAAC,MAAuB,MAAM;AAAA,EAC9B,CAAC,MAAmB,KAAK;AAAA,EACzB,CAAC,KAAe,IAAI;AAAA,EACpB,CAAC,KAAW,GAAG;AAAA,EACf,CAAC,KAAO,GAAG;AACb;AAEO,SAAS,0BAA0B,KAAqB;AAC7D,MAAI,OAAO,QAAQ,SAAU,OAAM,IAAI,MAAM,mBAAmB;AAChE,MAAI,OAAO,MAAM,GAAG,EAAG,QAAO;AAC9B,MAAI,MAAM,EAAG,QAAO,MAAM,0BAA0B,CAAC,GAAG;AACxD,MAAI,CAAC,OAAO,SAAS,GAAG,EAAG,QAAO;AAElC,aAAW,CAAC,WAAW,MAAM,KAAK,YAAY;AAC5C,QAAI,OAAO,WAAW;AACpB,aAAO,WAAW,MAAM,WAAW,CAAC,IAAI;AAAA,IAC1C;AAAA,EACF;AACA,SAAO,WAAW,KAAK,CAAC;AAC1B;AA0BO,SAAS,WAAW,KAAa,aAA6B;AACnE,SAAO,IAAI,QAAQ,WAAW,EAAE,QAAQ,UAAU,EAAE;AACtD;AAWO,SAAS,cAAc,GAAW,GAAmB;AAC1D,SAAO,KAAK,KAAK,IAAI,CAAC;AACxB;", "names": []}