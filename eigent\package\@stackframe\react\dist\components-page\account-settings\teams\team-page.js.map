{"version": 3, "sources": ["../../../../src/components-page/account-settings/teams/team-page.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { Team } from \"../../..\";\nimport { PageLayout } from \"../page-layout\";\nimport { LeaveTeamSection } from \"./leave-team-section\";\nimport { TeamApiKeysSection } from \"./team-api-keys-section\";\nimport { TeamDisplayNameSection } from \"./team-display-name-section\";\nimport { TeamMemberInvitationSection } from \"./team-member-invitation-section\";\nimport { TeamMemberListSection } from \"./team-member-list-section\";\nimport { TeamProfileImageSection } from \"./team-profile-image-section\";\nimport { TeamUserProfileSection } from \"./team-profile-user-section\";\n\n\nexport function TeamPage(props: { team: Team }) {\n  return (\n    <PageLayout>\n      <TeamUserProfileSection key={`user-profile-${props.team.id}`} team={props.team} />\n      <TeamProfileImageSection key={`profile-image-${props.team.id}`} team={props.team} />\n      <TeamDisplayNameSection key={`display-name-${props.team.id}`} team={props.team} />\n      <TeamMemberListSection key={`member-list-${props.team.id}`} team={props.team} />\n      <TeamMemberInvitationSection key={`member-invitation-${props.team.id}`} team={props.team} />\n      <TeamApiKeysSection key={`api-keys-${props.team.id}`} team={props.team} />\n      <LeaveTeamSection key={`leave-team-${props.team.id}`} team={props.team} />\n    </PageLayout>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,yBAA2B;AAC3B,gCAAiC;AACjC,mCAAmC;AACnC,uCAAuC;AACvC,4CAA4C;AAC5C,sCAAsC;AACtC,wCAAwC;AACxC,uCAAuC;AAKnC;AAFG,SAAS,SAAS,OAAuB;AAC9C,SACE,6CAAC,iCACC;AAAA,gDAAC,2DAA6D,MAAM,MAAM,QAA7C,gBAAgB,MAAM,KAAK,EAAE,EAAsB;AAAA,IAChF,4CAAC,6DAA+D,MAAM,MAAM,QAA9C,iBAAiB,MAAM,KAAK,EAAE,EAAsB;AAAA,IAClF,4CAAC,2DAA6D,MAAM,MAAM,QAA7C,gBAAgB,MAAM,KAAK,EAAE,EAAsB;AAAA,IAChF,4CAAC,yDAA2D,MAAM,MAAM,QAA5C,eAAe,MAAM,KAAK,EAAE,EAAsB;AAAA,IAC9E,4CAAC,qEAAuE,MAAM,MAAM,QAAlD,qBAAqB,MAAM,KAAK,EAAE,EAAsB;AAAA,IAC1F,4CAAC,mDAAqD,MAAM,MAAM,QAAzC,YAAY,MAAM,KAAK,EAAE,EAAsB;AAAA,IACxE,4CAAC,8CAAqD,MAAM,MAAM,QAA3C,cAAc,MAAM,KAAK,EAAE,EAAsB;AAAA,KAC1E;AAEJ;", "names": []}