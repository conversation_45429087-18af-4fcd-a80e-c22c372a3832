{"version": 3, "sources": ["../../../src/utils/oauth.tsx"], "sourcesContent": ["export const standardProviders = [\"google\", \"github\", \"microsoft\", \"spotify\", \"facebook\", \"discord\", \"gitlab\", \"bitbucket\", \"linkedin\", \"apple\", \"x\"] as const;\n// No more shared providers should be added except for special cases\nexport const sharedProviders = [\"google\", \"github\", \"microsoft\", \"spotify\"] as const;\nexport const allProviders = standardProviders;\n\nexport type ProviderType = typeof allProviders[number];\nexport type StandardProviderType = typeof standardProviders[number];\nexport type SharedProviderType = typeof sharedProviders[number];\n"], "mappings": ";AAAO,IAAM,oBAAoB,CAAC,UAAU,UAAU,aAAa,WAAW,YAAY,WAAW,UAAU,aAAa,YAAY,SAAS,GAAG;AAE7I,IAAM,kBAAkB,CAAC,UAAU,UAAU,aAAa,SAAS;AACnE,IAAM,eAAe;", "names": []}