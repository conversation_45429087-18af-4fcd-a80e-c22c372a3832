/** @type {import('tailwindcss').Config} */
module.exports = {
	darkMode: ["class"],
	content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
	theme: {
		extend: {
			colors: {
				red: {
					50: "var(--colors-red-50)",
					100: "var(--colors-red-100)",
					200: "var(--colors-red-200)",
					300: "var(--colors-red-300)",
					400: "var(--colors-red-400)",
					500: "var(--colors-red-500)",
					700: "var(--colors-red-700)",
					800: "var(--colors-red-800)",
					900: "var(--colors-red-900)",
					950: "var(--colors-red-950)",
					default: "var(--colors-red-default)",
				},
				yellow: {
					50: "var(--colors-yellow-50)",
					100: "var(--colors-yellow-100)",
					200: "var(--colors-yellow-200)",
					300: "var(--colors-yellow-300)",
					400: "var(--colors-yellow-400)",
					500: "var(--colors-yellow-500)",
					700: "var(--colors-yellow-700)",
					800: "var(--colors-yellow-800)",
					900: "var(--colors-yellow-900)",
					950: "var(--colors-yellow-950)",
					default: "var(--colors-yellow-default)",
				},
				green: {
					50: "var(--colors-green-50)",
					100: "var(--colors-green-100)",
					200: "var(--colors-green-200)",
					300: "var(--colors-green-300)",
					400: "var(--colors-green-400)",
					500: "var(--colors-green-500)",
					700: "var(--colors-green-700)",
					800: "var(--colors-green-800)",
					900: "var(--colors-green-900)",
					950: "var(--colors-green-950)",
					default: "var(--colors-green-default)",
				},
				indigo: {
					50: "var(--colors-indigo-50)",
					100: "var(--colors-indigo-100)",
					200: "var(--colors-indigo-200)",
					300: "var(--colors-indigo-300)",
					400: "var(--colors-indigo-400)",
					500: "var(--colors-indigo-500)",
					700: "var(--colors-indigo-700)",
					800: "var(--colors-indigo-800)",
					900: "var(--colors-indigo-900)",
					950: "var(--colors-indigo-950)",
					default: "var(--colors-indigo-default)",
				},
				blue: {
					50: "var(--colors-blue-50)",
					100: "var(--colors-blue-100)",
					200: "var(--colors-blue-200)",
					300: "var(--colors-blue-300)",
					400: "var(--colors-blue-400)",
					500: "var(--colors-blue-500)",
					700: "var(--colors-blue-700)",
					800: "var(--colors-blue-800)",
					900: "var(--colors-blue-900)",
					950: "var(--colors-blue-950)",
					default: "var(--colors-blue-default)",
				},
				amber: {
					50: "var(--colors-amber-50)",
					100: "var(--colors-amber-100)",
					200: "var(--colors-amber-200)",
					300: "var(--colors-amber-300)",
					400: "var(--colors-amber-400)",
					500: "var(--colors-amber-500)",
					700: "var(--colors-amber-700)",
					800: "var(--colors-amber-800)",
					900: "var(--colors-amber-900)",
					950: "var(--colors-amber-950)",
					default: "var(--colors-amber-default)",
				},
				emerald: {
					50: "var(--colors-emerald-50)",
					100: "var(--colors-emerald-100)",
					200: "var(--colors-emerald-200)",
					300: "var(--colors-emerald-300)",
					400: "var(--colors-emerald-400)",
					500: "var(--colors-emerald-500)",
					700: "var(--colors-emerald-700)",
					800: "var(--colors-emerald-800)",
					900: "var(--colors-emerald-900)",
					950: "var(--colors-emerald-950)",
					default: "var(--colors-emerald-default)",
				},
				purple: {
					50: "var(--colors-purple-50)",
					100: "var(--colors-purple-100)",
					200: "var(--colors-purple-200)",
					300: "var(--colors-purple-300)",
					400: "var(--colors-purple-400)",
					500: "var(--colors-purple-500)",
					700: "var(--colors-purple-700)",
					800: "var(--colors-purple-800)",
					900: "var(--colors-purple-900)",
					default: "var(--colors-purple-default)",
				},
				orange: {
					50: "var(--colors-orange-50)",
					100: "var(--colors-orange-100)",
					200: "var(--colors-orange-200)",
					300: "var(--colors-orange-300)",
					400: "var(--colors-orange-400)",
					500: "var(--colors-orange-500)",
					700: "var(--colors-orange-700)",
					800: "var(--colors-orange-800)",
					900: "var(--colors-orange-900)",
					950: "var(--colors-orange-950)",
					default: "var(--colors-orange-default)",
				},
				sky: {
					50: "var(--colors-sky-50)",
					100: "var(--colors-sky-100)",
					200: "var(--colors-sky-200)",
					300: "var(--colors-sky-300)",
					400: "var(--colors-sky-400)",
					500: "var(--colors-sky-500)",
					700: "var(--colors-sky-700)",
					800: "var(--colors-sky-800)",
					900: "var(--colors-sky-900)",
					950: "var(--colors-sky-950)",
					default: "var(--colors-sky-default)",
				},
				fuchsia: {
					50: "var(--colors-fuchsia-50)",
					100: "var(--colors-fuchsia-100)",
					200: "var(--colors-fuchsia-200)",
					300: "var(--colors-fuchsia-300)",
					400: "var(--colors-fuchsia-400)",
					500: "var(--colors-fuchsia-500)",
					700: "var(--colors-fuchsia-700)",
					800: "var(--colors-fuchsia-800)",
					900: "var(--colors-fuchsia-900)",
					950: "var(--colors-fuchsia-950)",
					default: "var(--colors-fuchsia-default)",
				},
				black: {
					"0%": "var(--colors-black-0)",
					"10%": "var(--colors-black-10)",
					"30%": "var(--colors-black-30)",
					"50%": "var(--colors-black-50)",
					"80%": "var(--colors-black-80)",
					"100%": "var(--colors-black-100)",
				},
				primary: {
					1: "var(--colors-primary-1)",
					2: "var(--colors-primary-2)",
					3: "var(--colors-primary-3)",
					4: "var(--colors-primary-4)",
					5: "var(--colors-primary-5)",
					6: "var(--colors-primary-6)",
					7: "var(--colors-primary-7)",
					8: "var(--colors-primary-8)",
					10: "var(--colors-primary-10)",
					11: "var(--colors-primary-11)",
					default: "var(--colors-primary-default)",
				},
				"off-white": {
					"0%": "var(--colors-off-white-0)",
					"10%": "var(--colors-off-white-10)",
					"30%": "var(--colors-off-white-30)",
					"50%": "var(--colors-off-white-50)",
					"80%": "var(--colors-off-white-80)",
					"100%": "var(--colors-off-white-100)",
				},
				white: {
					0: "var(--colors-white-0)",
					"80%": "var(--colors-white-80)",
					"50%": "var(--colors-white-50)",
					"30%": "var(--colors-white-30)",
					"10%": "var(--colors-white-10)",
					"100%": "var(--colors-white-100)",
				},
				"off-black": {
					"0%": "var(--colors-off-black-0)",
					"10%": "var(--colors-off-black-10)",
					"30%": "var(--colors-off-black-30)",
					"50%": "var(--colors-off-black-50)",
					"80%": "var(--colors-off-black-80)",
					"100%": "var(--colors-off-black-100)",
				},
				gradient: "var(--colors-gradient)",
				log: {
					default: "var(--log-default, #F5F5F5)",
				},

				input: {
					"bg-default": "var(--input-bg-default)",
					"border-default": "var(--input-border-default)",
					"border-hover": "var(--input-border-hover)",
					"border-focus": "var(--input-border-focus)",
					"text-default": "var(--input-text-default)",
					"text-focus": "var(--input-text-focus)",
					"label-default": "var(--input-label-default)",
					"border-success": "var(--input-border-success)",
					"border-cuation": "var(--input-border-cuation)",
					"border-warning": "var(--input-border-warning)",
				},
				popup: {
					surface: "var(--popup-surface)",
					bg: "var(--popup-bg)",
					border: "var(--popup-border)",
				},
				menutabs: {
					"fill-default": "var(--menutabs-fill-default)",
					"fill-hover": "var(--menutabs-fill-hover)",
					"fill-active": "var(--menutabs-fill-active)",
					"fill-disabled": "var(--menutabs-fill-disabled)",
					"border-disabled": "var(--menutabs-border-disabled)",
					"border-active": "var(--menutabs-border-active)",
					"border-hover": "var(--menutabs-border-hover)",
					"border-default": "var(--menutabs-border-default)",
					"text-active": "var(--menutabs-text-active)",
					"text-disabled": "var(--menutabs-text-disabled)",
					"text-hover": "var(--menutabs-text-hover)",
					"text-default": "var(--menutabs-text-default)",
					"icon-hover": "var(--menutabs-icon-hover)",
					"icon-default": "var(--menutabs-icon-default)",
					"icon-disabled": "var(--menutabs-icon-disabled)",
					"icon-active": "var(--menutabs-icon-active)",
					"bg-default": "var(--menutabs-bg-default)",
				},
				progress: {
					"fill-default": "var(--progress-fill-default)",
					bg: "var(--progress-bg)",
					"fill-complete": "var(--progress-fill-complete)",
					"fill-past": "var(--progress-fill-past)",
					"fill-new": "var(--progress-fill-new)",
				},
				button: {
					primary: {
						"fill-default": "var(--button-primary-fill-default)",
						"fill-hover": "var(--button-primary-fill-hover)",
						"fill-active": "var(--button-primary-fill-active)",
						"fill-disabled": "var(--button-primary-fill-disabled)",
						"icon-hover": "var(--button-primary-icon-hover)",
						"icon-default": "var(--button-primary-icon-default)",
						"text-disabled": "var(--button-primary-text-disabled)",
						"text-active": "var(--button-primary-text-active)",
						"text-hover": "var(--button-primary-text-hover)",
						"text-default": "var(--button-primary-text-default)",
						"icon-disabled": "var(--button-primary-icon-disabled)",
						"icon-active": "var(--button-primary-icon-active)",
					},
					secondary: {
						"fill-disabled": "var(--button-secondary-fill-disabled)",
						"fill-active": "var(--button-secondary-fill-active)",
						"fill-hover": "var(--button-secondary-fill-hover)",
						"fill-default": "var(--button-secondary-fill-default)",
						"text-default": "var(--button-secondary-text-default)",
						"text-hover": "var(--button-secondary-text-hover)",
						"text-active": "var(--button-secondary-text-active)",
						"text-disabled": "var(--button-secondary-text-disabled)",
						"icon-disabled": "var(--button-secondary-icon-disabled)",
						"icon-active": "var(--button-secondary-icon-active)",
						"icon-hover": "var(--button-secondary-icon-hover)",
						"icon-default": "var(--button-secondary-icon-default)",
					},
					transparent: {
						"fill-disabled": "var(--button-transparent-fill-disabled)",
						"fill-active": "var(--button-transparent-fill-active)",
						"fill-hover": "var(--button-transparent-fill-hover)",
						"fill-default": "var(--button-transparent-fill-default)",
						"icon-default": "var(--button-transparent-icon-default)",
						"text-disabled": "var(--button-transparent-text-disabled)",
						"text-default": "var(--button-transparent-text-default)",
						"text-active": "var(--button-transparent-text-active)",
						"icon-hover": "var(--button-transparent-icon-hover)",
						"text-hover": "var(--button-transparent-text-hover)",
						"icon-disabled": "var(--button-transparent-icon-disabled)",
						"icon-active": "var(--button-transparent-icon-active)",
					},
					tertiery: {
						"fill-hover": "var(--button-tertiery-fill-hover)",
						"fill-default": "var(--button-tertiery-fill-default)",
						"fill-disabled": "var(--button-tertiery-fill-disabled)",
						"fill-active": "var(--button-tertiery-fill-active)",
						"icon-hover": "var(--button-tertiery-icon-hover)",
						"icon-default": "var(--button-tertiery-icon-default)",
						"text-disabled": "var(--button-tertiery-text-disabled)",
						"text-active": "var(--button-tertiery-text-active)",
						"text-hover": "var(--button-tertiery-text-hover)",
						"text-default": "var(--button-tertiery-text-default)",
						"icon-disabled": "var(--button-tertiery-icon-disabled)",
						"icon-active": "var(--button-tertiery-icon-active)",
						"icon-hover 2": "var(--button-tertiery-icon-hover-2)",
						"icon-default 2": "var(--button-tertiery-icon-default-2)",
						"text-disabled 2": "var(--button-tertiery-text-disabled-2)",
						"text-active 2": "var(--button-tertiery-text-active-2)",
						"text-hover 2": "var(--button-tertiery-text-hover-2)",
						"text-default 2": "var(--button-tertiery-text-default-2)",
						"icon-disabled 2": "var(--button-tertiery-icon-disabled-2)",
						"icon-active 2": "var(--button-tertiery-icon-active-2)",
					},
					"fill-success": "var(--button-fill-success)",
					"fill-cuation": "var(--button-fill-cuation)",
					"fill-warning": "var(--button-fill-warning)",
					"fill-success-foreground": "var(--button-fill-success-foreground)",
					"fill-cuation-foreground": "var(--button-fill-cuation-foreground)",
					"fill-warning-foreground": "var(--button-fill-warning-foreground)",
					"fill-information": "var(--button-fill-information)",
					"fill-information-foreground":
						"var(--button-fill-information-foreground)",
				},
				badge: {
					"running-surface": "var(--badge-running-surface)",
					"running-surface-foreground":
						"var(--badge-running-surface-foreground)",
					"paused-surface-foreground": "var(--badge-paused-surface-foreground)",
					"paused-surface": "var(--badge-paused-surface)",
					"error-surface-foreground": "var(--badge-error-surface-foreground)",
					"error-surface": "var(--badge-error-surface)",
					"complete-surface-foreground":
						"var(--badge-complete-surface-foreground)",
					"complete-surface": "var(--badge-complete-surface)",
					"splitting-surface-foreground":
						"var(--badge-splitting-surface-foreground)",
					"splitting-surface": "var(--badge-splitting-surface)",
				},
				switch: {
					"off-fill-track-fill": "var(--switch-off-fill-track-fill)",
					"off-fill-track-border": "var(--switch-off-fill-track-border)",
					"off-fill-thumb-border": "var(--switch-off-fill-thumb-border)",
					"off-fill-thumb-fill": "var(--switch-off-fill-thumb-fill)",
					"on-fill-thumb-border": "var(--switch-on-fill-thumb-border)",
					"on-fill-thumb-fill": "var(--switch-on-fill-thumb-fill)",
					"on-fill-track-border": "var(--switch-on-fill-track-border)",
					"on-fill-track-fill": "var(--switch-on-fill-track-fill)",
					"disabled-fill-thumb-border":
						"var(--switch-disabled-fill-thumb-border)",
					"disabled-fill-track-border":
						"var(--switch-disabled-fill-track-border)",
					"disabled-fill-thumb-fill": "var(--switch-disabled-fill-thumb-fill)",
					"disabled-fill-track-fill": "var(--switch-disabled-fill-track-fill)",
				},
				pill: {
					bg: "var(--pill-bg)",
					surface: "var(--pill-surface)",
					border: "var(--pill-border)",
				},
				menubutton: {
					"fill-default": "var(--menubutton-fill-default)",
					"fill-hover": "var(--menubutton-fill-hover)",
					"fill-active": "var(--menubutton-fill-active)",
					"border-active": "var(--menubutton-border-active)",
					"border-default": "var(--menubutton-border-default)",
					"border-hover": "var(--menubutton-border-hover)",
					disabled: "var(--menubutton-disabled)",
				},
				dropdown: {
					bg: "var(--dropdown-bg)",
					border: "var(--dropdown-border)",
					"item-bg-default": "var(--dropdown-item-bg-default)",
					"item-bg-hover": "var(--dropdown-item-bg-hover)",
					"item-bg-active": "var(--dropdown-item-bg-active)",
				},
				search: {
					bg: "var(--search-bg)",
					"border-hover": "var(--search-border-hover)",
					"border-default": "var(--search-border-default)",
					default: "var(--search-default)",
				},
				tag: {
					surface: "var(--tag-surface)",
					"fill-browser": "var(--tag-fill-browser)",
					"fill-developer": "var(--tag-fill-developer)",
					"fill-document": "var(--tag-fill-document)",
					"fill-multimodal": "var(--tag-fill-multimodal)",
					"fill-socialmedia": "var(--tag-fill-socialmedia)",
					"fill-info": "var(--tag-fill-info)",
					"text-info": "var(--tag-text-info)",
					"surface-hover": "var(--tag-surface-hover)",
					"fill-success": "var(--tag-fill-success)",
					"text-success": "var(--tag-text-success)",
				},
				message: {
					"fill-default": "var(--message-fill-default)",
					"fill-hover": "var(--message-fill-hover)",
					"fill-active": "var(--message-fill-active)",
					"border-default": "var(--message-border-default)",
					"border-focus": "var(--message-border-focus)",
				},
				task: {
					surface: "var(--task-surface)",
					"border-default": "var(--task-border-default)",
					"border-focus": "var(--task-border-focus)",
					"fill-default": "var(--task-fill-default)",
					"fill-hover": "var(--task-fill-hover)",
					"fill-success": "var(--task-fill-success)",
					"fill-warning": "var(--task-fill-warning)",
					"fill-error": "var(--task-fill-error)",
					"border-focus-success": "var(--task-border-focus-success)",
					"border-focus-warning": "var(--task-border-focus-warning)",
					"border-focus-error": "var(--task-border-focus-error)",
				},
				worker: {
					"surface-primary": "var(--worker-surface-primary)",
					"border-default": "var(--worker-border-default)",
					"border-focus": "var(--worker-border-focus)",
					"surface-secondary": "var(--worker-surface-secondary)",
				},
				mask: {
					default: "var(--mask-default)",
					dark: "var(--mask-dark)",
				},
				code: {
					bg: "var(--code-bg)",
					foreground: "var(--code-foreground)",
				},
				"text-heading": "var(--text-heading)",
				"text-body": "var(--text-body)",
				"text-label": "var(--text-label)",
				"text-action": "var(--text-action)",
				"text-action-hover": "var(--text-action-hover)",
				"text-disabled": "var(--text-disabled)",
				"text-information": "var(--text-information)",
				"text-success": "var(--text-success)",
				"text-warning": "var(--text-warning)",
				"text-cuation": "var(--text-cuation)",
				"text-on-action": "var(--text-on-action)",
				"text-on-disabled": "var(--text-on-disabled)",
				"text-document": "var(--text-document)",
				"text-socialmedia": "var(--text-socialmedia)",
				"text-browser": "var(--text-browser)",
				"text-developer": "var(--text-developer)",
				"text-multimodal": "var(--text-multimodal)",
				"text-on-hover": "var(--text-on-hover)",

				"surface-primary": "var(--surface-primary)",
				"surface-secondary": "var(--surface-secondary)",
				"surface-success": "var(--surface-success)",
				"surface-information": "var(--surface-information)",
				"surface-warning": "var(--surface-warning)",
				"surface-cuation": "var(--surface-cuation)",
				"surface-action": "var(--surface-action)",
				"surface-action-hover": "var(--surface-action-hover)",
				"surface-disabled": "var(--surface-disabled)",
				"surface-tirtery": "var(--surface-tirtery)",
				"surface-card": "var(--surface-card)",
				"surface-card-hover": "var(--surface-card-hover)",
				"surface-card-focus": "var(--surface-card-focus)",
				"surface-card-default": "var(--surface-card-default)",

				"border-primary": "var(--border-primary)",
				"border-secondary": "var(--border-secondary)",
				"border-information": "var(--border-information)",
				"border-success": "var(--border-success)",
				"border-warning": "var(--border-warning)",
				"border-cuation": "var(--border-cuation)",
				"border-focus": "var(--border-focus)",
				"border-action": "var(--border-action)",
				"border-action-hover": "var(--border-action-hover)",
				"border-disabled": "var(--border-disabled)",
				"border-developer": "var(--border-developer)",
				"border-browser": "var(--border-browser)",
				"border-socialmedia": "var(--border-socialmedia)",
				"border-multimodal": "var(--border-multimodal)",
				"border-document": "var(--border-document)",
				"border-transparent": "var(--border-transparent)",

				"icon-primary": "var(--icon-primary)",
				"icon-action": "var(--icon-action)",
				"icon-disabled": "var(--icon-disabled)",
				"icon-information": "var(--icon-information)",
				"icon-success": "var(--icon-success)",
				"icon-warning": "var(--icon-warning)",
				"icon-cuation": "var(--icon-cuation)",
				"icon-action-hover": "var(--icon-action-hover)",
				"icon-multimodal": "var(--icon-multimodal)",
				"icon-socialmedia": "var(--icon-socialmedia)",
				"icon-document": "var(--icon-document)",
				"icon-browser": "var(--icon-browser)",
				"icon-developer": "var(--icon-developer)",
				"icon-on-disabled": "var(--icon-on-disabled)",
				"icon-on-hover": "var(--icon-on-hover)",
				"icon-on-action": "var(--icon-on-action)",
				"icon-secondary": "var(--icon-secondary)",

				"fill-default": "var(--fill-default)",
				"fill-fill-primary": "var(--fill-fill-primary)",
				"fill-fill-primary-hover": "var(--fill-fill-primary-hover)",
				"fill-fill-primary-active": "var(--fill-fill-primary-active)",
				"fill-fill-primary-disabled": "var(--fill-fill-primary-disabled)",
				"fill-fill-tertiary": "var(--fill-fill-tertiary)",
				"fill-fill-transparent": "var(--fill-fill-transparent)",
				"fill-fill-transparent-hover": "var(--fill-fill-transparent-hover)",
				"fill-fill-tertiary-hover": "var(--fill-fill-tertiary-hover)",
				"fill-fill-tertiary-active": "var(--fill-fill-tertiary-active)",
				"fill-fill-tertiary-disabled": "var(--fill-fill-tertiary-disabled)",
				"fill-fill-transparent-active": "var(--fill-fill-transparent-active)",
				"fill-fill-transparent-disabled":
					"var(--fill-fill-transparent-disabled)",
				"fill-fill-secondary-disabled": "var(--fill-fill-secondary-disabled)",
				"fill-fill-secondary-active": "var(--fill-fill-secondary-active)",
				"fill-fill-secondary-hover": "var(--fill-fill-secondary-hover)",
				"fill-fill-secondary": "var(--fill-fill-secondary)",
				"fill-fill-success": "var(--fill-fill-success)",
				"fill-fill-success-hover": "var(--fill-fill-success-hover)",
				"fill-fill-success-active": "var(--fill-fill-success-active)",
				"fill-fill-success-disable": "var(--fill-fill-success-disable)",
				"fill-fill-warning": "var(--fill-fill-warning)",
				"fill-fill-cuation": "var(--fill-fill-cuation)",
				"fill-socialmedia": "var(--fill-socialmedia)",
				"fill-document": "var(--fill-document)",
				"fill-browser": "var(--fill-browser)",
				"fill-multimodal": "var(--fill-multimodal)",
				"fill-developer": "var(--fill-developer)",
				"fill-scrollbar-dark": "var(--fill-scrollbar-dark)",
				"fill-scrollbar-light": "var(--fill-scrollbar-light)",
				"fill-skeloten-default": "var(--fill-skeloten-default)",
				"fill-fill-information": "var(--fill-fill-information)",

				"bg-page": "var(--bg-page)",
				"bg-primary": "var(--bg-primary)",
				"bg-secondary": "var(--bg-secondary)",
				"bg-tertiary": "var(--bg-tertiary)",
				"bg-dark": "var(--bg-dark)",
				"bg-dark-primary": "var(--bg-dark-primary)",
				"bg-dark-secondary": "var(--bg-dark-secondary)",
				"bg-dark-tertiary": "var(--bg-dark-tertiary)",
				"bg-dark-default": "var(--bg-dark-default)",
				"bg-page-default": "var(--bg-page-default)",
			},
			boxShadow: {
				"history-item": "0px 3px 4px -1px rgba(0, 0, 0, 0.10)",
			},
			spacing: {
				xs: "var(--spacing-xs, 4px)",
				sm: "var(--spacing-sm, 8px)",
				md: "var(--spacing-md, 16px)",
				lg: "var(--spacing-lg, 32px)",
				xl: "var(--spacing-xl, 64px)",
				"multi-value": "var(--spacing-multi-value, 8 64)",
			},
			borderRadius: {
				sm: "var(--borderRadius-sm, 4px)",
				lg: "var(--borderRadius-lg, 8px)",
				xl: "var(--borderRadius-xl, 16px)",
				"multi-value": "var(--borderRadius-multi-value, 4 8)",
			},
			fontFamily: {
				sans: ["Inter", "sans-serif"],
				mono: ["SFMono-Regular", "Menlo", "monospace"],
				inter: ["Inter"],
				menlo: ["Menlo"],
			},
			fontSize: {
				xs: "var(--fontSize-xs, 10px)",
				sm: "var(--fontSize-sm, 13px)",
				base: "var(--fontSize-base, 15px)",
				md: "var(--fontSize-md, 16px)",
				lg: "var(--fontSize-lg, 18px)",
				xl: "var(--fontSize-xl, 20px)",
				"2xl": "var(--fontSize-2xl, 24px)",
				"3xl": "var(--fontSize-3xl, 28px)",
				"4xl": "var(--fontSize-4xl, 36px)",
				"5xl": "var(--fontSize-5xl, 44px)",
			},
			lineHeight: {
				0: "var(--lineHeight-0, 58)",
				1: "var(--lineHeight-1, 58)",
				2: "var(--lineHeight-2, 46)",
				3: "var(--lineHeight-3, 36)",
				4: "var(--lineHeight-4, 32)",
				5: "var(--lineHeight-5, 30)",
				6: "var(--lineHeight-6, 30)",
				7: "var(--lineHeight-7, 30)",
				8: "var(--lineHeight-8, 30)",
				9: "var(--lineHeight-9, 22)",
				10: "var(--lineHeight-10, 22)",
				11: "var(--lineHeight-11, 22)",
				12: "var(--lineHeight-12, 22)",
				13: "var(--lineHeight-13, 20)",
				14: "var(--lineHeight-14, 20)",
				15: "var(--lineHeight-15, 20)",
				16: "var(--lineHeight-16, 20)",
				17: "var(--lineHeight-17, 16)",
				18: "var(--lineHeight-18, 16)",
				19: "var(--lineHeight-19, 16)",
				20: "var(--lineHeight-20, 16)",
				21: "var(--lineHeight-21, 16)",
				22: "var(--lineHeight-22, 16)",
				23: "var(--lineHeight-23, 20)",
				24: "var(--lineHeight-24, 20)",
				25: "var(--lineHeight-25, 22)",
				26: "var(--lineHeight-26, 22)",
				27: "var(--lineHeight-27, 24)",
				28: "var(--lineHeight-28, 24)",
				29: "var(--lineHeight-29, 16)",
				30: "var(--lineHeight-30, 16)",
				tight: "var(--lineHeight-tight, 16px)",
				normal: "var(--lineHeight-normal, 20px)",
				relaxed: "var(--lineHeight-relaxed, 22px)",
				loose: "var(--lineHeight-loose, 24px)",
				xl: "var(--lineHeight-xl, 28px)",
				"2xl": "var(--lineHeight-2xl, 30px)",
				"3xl": "var(--lineHeight-3xl, 32px)",
				"4xl": "var(--lineHeight-4xl, 36px)",
				"5xl": "var(--lineHeight-5xl, 46px)",
				"6xl": "var(--lineHeight-6xl, 58px)",
			},
			fontWeight: {
				regular: "var(--fontWeight-regular, 400)",
				medium: "var(--fontWeight-medium, 500)",
				semibold: "var(--fontWeight-semibold, 600)",
				bold: "var(--fontWeight-bold, 700)",
				"inter-0": "var(--fontWeight-inter-0, 700)",
				"inter-1": "var(--fontWeight-inter-1, 400)",
				"inter-2": "var(--fontWeight-inter-2, 500)",
				"menlo-3": "var(--fontWeight-menlo-3, 400)",
			},
			animation: {
				"star-movement-bottom":
					"star-movement-bottom linear infinite alternate",
				"star-movement-top": "star-movement-top linear infinite alternate",
			},
			keyframes: {
				"star-movement-bottom": {
					"0%": { transform: "translate(0%, 0%)", opacity: "1" },
					"100%": { transform: "translate(-100%, 0%)", opacity: "0" },
				},
				"star-movement-top": {
					"0%": { transform: "translate(0%, 0%)", opacity: "1" },
					"100%": { transform: "translate(100%, 0%)", opacity: "0" },
				},
			},
		},
	},
	corePlugins: {
		preflight: false,
	},
	plugins: [require("tailwindcss-animate")],
};
