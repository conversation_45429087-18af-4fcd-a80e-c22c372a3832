{"version": 3, "sources": ["../../src/providers/stack-provider-client.tsx"], "sourcesContent": ["\"use client\";\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { CurrentUserCrud } from \"@stackframe/stack-shared/dist/interface/crud/current-user\";\nimport { globalVar } from \"@stackframe/stack-shared/dist/utils/globals\";\nimport React, { useEffect } from \"react\";\nimport { useStackApp } from \"..\";\nimport { StackClientApp, StackClientAppJson, stackAppInternalsSymbol } from \"../lib/stack-app\";\n\nexport const StackContext = React.createContext<null | {\n  app: StackClientApp<true>,\n}>(null);\n\nexport function StackProviderClient(props: {\n  app: StackClientAppJson<true, string> | StackClientApp<true>,\n  serialized: boolean,\n  children?: React.ReactNode,\n}) {\n  const app = props.serialized\n    ? StackClientApp[stackAppInternalsSymbol].fromClientJson(props.app as StackClientAppJson<true, string>)\n    : props.app as StackClientApp<true>;\n\n  globalVar.__STACK_AUTH__ = { app };\n\n  return (\n    <StackContext.Provider value={{ app }}>\n      {props.children}\n    </StackContext.Provider>\n  );\n}\n\nexport function UserSetter(props: { userJsonPromise: Promise<CurrentUserCrud['Client']['Read'] | null> }) {\n  const app = useStackApp();\n  useEffect(() => {\n    const promise = (async () => await props.userJsonPromise)();  // there is a Next.js bug where Promises passed by server components return `undefined` as their `then` value, so wrap it in a normal promise\n    app[stackAppInternalsSymbol].setCurrentUser(promise);\n  }, []);\n  return null;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,qBAA0B;AAC1B,mBAAiC;AACjC,eAA4B;AAC5B,uBAA4E;AAkBxE;AAhBG,IAAM,eAAe,aAAAA,QAAM,cAE/B,IAAI;AAEA,SAAS,oBAAoB,OAIjC;AACD,QAAM,MAAM,MAAM,aACd,gCAAe,wCAAuB,EAAE,eAAe,MAAM,GAAuC,IACpG,MAAM;AAEV,2BAAU,iBAAiB,EAAE,IAAI;AAEjC,SACE,4CAAC,aAAa,UAAb,EAAsB,OAAO,EAAE,IAAI,GACjC,gBAAM,UACT;AAEJ;AAEO,SAAS,WAAW,OAA+E;AACxG,QAAM,UAAM,sBAAY;AACxB,8BAAU,MAAM;AACd,UAAM,WAAW,YAAY,MAAM,MAAM,iBAAiB;AAC1D,QAAI,wCAAuB,EAAE,eAAe,OAAO;AAAA,EACrD,GAAG,CAAC,CAAC;AACL,SAAO;AACT;", "names": ["React"]}