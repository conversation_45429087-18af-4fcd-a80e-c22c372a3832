{"version": 3, "sources": ["../../../src/components/message-cards/known-error-message-card.tsx"], "sourcesContent": ["\"use client\";\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { KnownError } from \"@stackframe/stack-shared\";\nimport { Typography } from \"@stackframe/stack-ui\";\nimport { useStackApp } from \"../..\";\nimport { MessageCard } from \"./message-card\";\n\nexport function KnownErrorMessageCard({\n  error,\n  fullPage=false,\n}: {\n  error: KnownError,\n  fullPage?: boolean,\n}) {\n  const stackApp = useStackApp();\n\n  return (\n    <MessageCard\n      title={\"An error occurred\"}\n      fullPage={fullPage}\n      primaryButtonText={\"Go Home\"}\n      primaryAction={() => stackApp.redirectToHome()}\n    >\n      {<Typography>Error Code: {error.errorCode}</Typography>}\n      {<Typography>Error Message: {error.message}</Typography>}\n    </MessageCard>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,sBAA2B;AAC3B,eAA4B;AAC5B,0BAA4B;AAkBrB;AAhBA,SAAS,sBAAsB;AAAA,EACpC;AAAA,EACA,WAAS;AACX,GAGG;AACD,QAAM,eAAW,sBAAY;AAE7B,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO;AAAA,MACP;AAAA,MACA,mBAAmB;AAAA,MACnB,eAAe,MAAM,SAAS,eAAe;AAAA,MAE5C;AAAA,qDAAC,8BAAW;AAAA;AAAA,UAAa,MAAM;AAAA,WAAU;AAAA,QACzC,6CAAC,8BAAW;AAAA;AAAA,UAAgB,MAAM;AAAA,WAAQ;AAAA;AAAA;AAAA,EAC7C;AAEJ;", "names": []}