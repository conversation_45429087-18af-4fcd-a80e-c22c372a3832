/* Modern Minimalist Theme */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.8;
  color: #333;
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background-color: #fff;
}

h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #111;
  margin-bottom: 1.5rem;
  line-height: 1.3;
  border-bottom: 3px solid #f0f0f0;
  padding-bottom: 0.5rem;
}

h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #222;
  margin-top: 2.5rem;
  margin-bottom: 1rem;
}

h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #333;
  margin-top: 2rem;
  margin-bottom: 0.75rem;
}

p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

a {
  color: #0066cc;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s;
}

a:hover {
  border-bottom-color: #0066cc;
}

blockquote {
  border-left: 4px solid #eee;
  padding: 0 1rem;
  margin: 1.5rem 0;
  color: #555;
  font-style: italic;
}

code {
  background: #f5f5f5;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 0.9rem;
}

ul, ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
}

li {
  margin-bottom: 0.5rem;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 2rem auto;
  border-radius: 5px;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
}

th, td {
  padding: 0.75rem;
  border: 1px solid #eee;
  text-align: left;
}

th {
  background-color: #f9f9f9;
  font-weight: 600;
}

@media (max-width: 768px) {
  body {
    padding: 1rem;
  }
  
  h1 {
    font-size: 2rem;
  }
  
  h2 {
    font-size: 1.5rem;
  }
  
  h3 {
    font-size: 1.25rem;
  }
}