{"version": 3, "sources": ["../../src/utils/results.tsx"], "sourcesContent": ["import { wait } from \"./promises\";\nimport { deindent, nicify } from \"./strings\";\n\nexport type Result<T, E = unknown> =\n  | {\n    status: \"ok\",\n    data: T,\n  }\n  | {\n    status: \"error\",\n    error: E,\n  };\n\nexport type AsyncResult<T, E = unknown, P = void> =\n  | Result<T, E>\n  | (\n    & {\n      status: \"pending\",\n    }\n    & {\n      progress: P,\n    }\n  );\n\n\nexport const Result = {\n  fromThrowing,\n  fromThrowingAsync,\n  fromPromise: promiseToResult,\n  ok<T>(data: T): Result<T, never> & { status: \"ok\" } {\n    return {\n      status: \"ok\",\n      data,\n    };\n  },\n  error<E>(error: E): Result<never, E> & { status: \"error\" } {\n    return {\n      status: \"error\",\n      error,\n    };\n  },\n  map: mapResult,\n  or: <T, E, U>(result: Result<T, E>, fallback: U): T | U => {\n    return result.status === \"ok\" ? result.data : fallback;\n  },\n  orThrow: <T, E>(result: Result<T, E>): T => {\n    if (result.status === \"error\") {\n      throw result.error;\n    }\n    return result.data;\n  },\n  orThrowAsync: async <T, E>(result: Promise<Result<T, E>>): Promise<T> => {\n    return Result.orThrow(await result);\n  },\n  retry,\n};\nundefined?.test(\"Result.ok and Result.error\", ({ expect }) => {\n  // Test Result.ok\n  const okResult = Result.ok(42);\n  expect(okResult.status).toBe(\"ok\");\n  expect(okResult.data).toBe(42);\n\n  // Test Result.error\n  const error = new Error(\"Test error\");\n  const errorResult = Result.error(error);\n  expect(errorResult.status).toBe(\"error\");\n  expect(errorResult.error).toBe(error);\n});\n\nundefined?.test(\"Result.or\", ({ expect }) => {\n  // Test with ok result\n  const okResult: Result<number, string> = { status: \"ok\", data: 42 };\n  expect(Result.or(okResult, 0)).toBe(42);\n\n  // Test with error result\n  const errorResult: Result<number, string> = { status: \"error\", error: \"error message\" };\n  expect(Result.or(errorResult, 0)).toBe(0);\n});\n\nundefined?.test(\"Result.orThrow\", ({ expect }) => {\n  // Test with ok result\n  const okResult: Result<number, Error> = { status: \"ok\", data: 42 };\n  expect(Result.orThrow(okResult)).toBe(42);\n\n  // Test with error result\n  const error = new Error(\"Test error\");\n  const errorResult: Result<number, Error> = { status: \"error\", error };\n  expect(() => Result.orThrow(errorResult)).toThrow(error);\n});\n\nundefined?.test(\"Result.orThrowAsync\", async ({ expect }) => {\n  // Test with ok result\n  const okPromise = Promise.resolve({ status: \"ok\", data: 42 } as Result<number, Error>);\n  expect(await Result.orThrowAsync(okPromise)).toBe(42);\n\n  // Test with error result\n  const error = new Error(\"Test error\");\n  const errorPromise = Promise.resolve({ status: \"error\", error } as Result<number, Error>);\n  await expect(Result.orThrowAsync(errorPromise)).rejects.toThrow(error);\n});\n\nexport const AsyncResult = {\n  fromThrowing,\n  fromPromise: promiseToResult,\n  ok: Result.ok,\n  error: Result.error,\n  pending,\n  map: mapResult,\n  or: <T, E, P, U>(result: AsyncResult<T, E, P>, fallback: U): T | U => {\n    if (result.status === \"pending\") {\n      return fallback;\n    }\n    return Result.or(result, fallback);\n  },\n  orThrow: <T, E, P>(result: AsyncResult<T, E, P>): T => {\n    if (result.status === \"pending\") {\n      throw new Error(\"Result still pending\");\n    }\n    return Result.orThrow(result);\n  },\n  retry,\n};\nundefined?.test(\"AsyncResult.or\", ({ expect }) => {\n  // Test with ok result\n  const okResult: AsyncResult<number, string> = { status: \"ok\", data: 42 };\n  expect(AsyncResult.or(okResult, 0)).toBe(42);\n\n  // Test with error result\n  const errorResult: AsyncResult<number, string> = { status: \"error\", error: \"error message\" };\n  expect(AsyncResult.or(errorResult, 0)).toBe(0);\n\n  // Test with pending result\n  const pendingResult: AsyncResult<number, string> = { status: \"pending\", progress: undefined };\n  expect(AsyncResult.or(pendingResult, 0)).toBe(0);\n});\n\nundefined?.test(\"AsyncResult.orThrow\", ({ expect }) => {\n  // Test with ok result\n  const okResult: AsyncResult<number, Error> = { status: \"ok\", data: 42 };\n  expect(AsyncResult.orThrow(okResult)).toBe(42);\n\n  // Test with error result\n  const error = new Error(\"Test error\");\n  const errorResult: AsyncResult<number, Error> = { status: \"error\", error };\n  expect(() => AsyncResult.orThrow(errorResult)).toThrow(error);\n\n  // Test with pending result\n  const pendingResult: AsyncResult<number, Error> = { status: \"pending\", progress: undefined };\n  expect(() => AsyncResult.orThrow(pendingResult)).toThrow(\"Result still pending\");\n});\n\nfunction pending(): AsyncResult<never, never, void> & { status: \"pending\" };\nfunction pending<P>(progress: P): AsyncResult<never, never, P> & { status: \"pending\" };\nfunction pending<P>(progress?: P): AsyncResult<never, never, P> & { status: \"pending\" } {\n  return {\n    status: \"pending\",\n    progress: progress!,\n  };\n}\nundefined?.test(\"pending\", ({ expect }) => {\n  // Test without progress\n  const pendingResult = pending();\n  expect(pendingResult.status).toBe(\"pending\");\n  expect(pendingResult.progress).toBe(undefined);\n\n  // Test with progress\n  const progressValue = { loaded: 50, total: 100 };\n  const pendingWithProgress = pending(progressValue);\n  expect(pendingWithProgress.status).toBe(\"pending\");\n  expect(pendingWithProgress.progress).toBe(progressValue);\n});\n\nasync function promiseToResult<T>(promise: Promise<T>): Promise<Result<T>> {\n  try {\n    const value = await promise;\n    return Result.ok(value);\n  } catch (error) {\n    return Result.error(error);\n  }\n}\nundefined?.test(\"promiseToResult\", async ({ expect }) => {\n  // Test with resolved promise\n  const resolvedPromise = Promise.resolve(42);\n  const resolvedResult = await promiseToResult(resolvedPromise);\n  expect(resolvedResult.status).toBe(\"ok\");\n  if (resolvedResult.status === \"ok\") {\n    expect(resolvedResult.data).toBe(42);\n  }\n\n  // Test with rejected promise\n  const error = new Error(\"Test error\");\n  const rejectedPromise = Promise.reject(error);\n  const rejectedResult = await promiseToResult(rejectedPromise);\n  expect(rejectedResult.status).toBe(\"error\");\n  if (rejectedResult.status === \"error\") {\n    expect(rejectedResult.error).toBe(error);\n  }\n});\n\nfunction fromThrowing<T>(fn: () => T): Result<T, unknown> {\n  try {\n    return Result.ok(fn());\n  } catch (error) {\n    return Result.error(error);\n  }\n}\nundefined?.test(\"fromThrowing\", ({ expect }) => {\n  // Test with function that succeeds\n  const successFn = () => 42;\n  const successResult = fromThrowing(successFn);\n  expect(successResult.status).toBe(\"ok\");\n  if (successResult.status === \"ok\") {\n    expect(successResult.data).toBe(42);\n  }\n\n  // Test with function that throws\n  const error = new Error(\"Test error\");\n  const errorFn = () => {\n    throw error;\n  };\n  const errorResult = fromThrowing(errorFn);\n  expect(errorResult.status).toBe(\"error\");\n  if (errorResult.status === \"error\") {\n    expect(errorResult.error).toBe(error);\n  }\n});\n\nasync function fromThrowingAsync<T>(fn: () => Promise<T>): Promise<Result<T, unknown>> {\n  try {\n    return Result.ok(await fn());\n  } catch (error) {\n    return Result.error(error);\n  }\n}\nundefined?.test(\"fromThrowingAsync\", async ({ expect }) => {\n  // Test with async function that succeeds\n  const successFn = async () => 42;\n  const successResult = await fromThrowingAsync(successFn);\n  expect(successResult.status).toBe(\"ok\");\n  if (successResult.status === \"ok\") {\n    expect(successResult.data).toBe(42);\n  }\n\n  // Test with async function that throws\n  const error = new Error(\"Test error\");\n  const errorFn = async () => {\n    throw error;\n  };\n  const errorResult = await fromThrowingAsync(errorFn);\n  expect(errorResult.status).toBe(\"error\");\n  if (errorResult.status === \"error\") {\n    expect(errorResult.error).toBe(error);\n  }\n});\n\nfunction mapResult<T, U, E = unknown, P = unknown>(result: Result<T, E>, fn: (data: T) => U): Result<U, E>;\nfunction mapResult<T, U, E = unknown, P = unknown>(result: AsyncResult<T, E, P>, fn: (data: T) => U): AsyncResult<U, E, P>;\nfunction mapResult<T, U, E = unknown, P = unknown>(result: AsyncResult<T, E, P>, fn: (data: T) => U): AsyncResult<U, E, P> {\n  if (result.status === \"error\") return {\n    status: \"error\",\n    error: result.error,\n  };\n  if (result.status === \"pending\") return {\n    status: \"pending\",\n    ...\"progress\" in result ? { progress: result.progress } : {},\n  } as any;\n\n  return Result.ok(fn(result.data));\n}\nundefined?.test(\"mapResult\", ({ expect }) => {\n  // Test with ok result\n  const okResult: Result<number, string> = { status: \"ok\", data: 42 };\n  const mappedOk = mapResult(okResult, (n: number) => n * 2);\n  expect(mappedOk.status).toBe(\"ok\");\n  if (mappedOk.status === \"ok\") {\n    expect(mappedOk.data).toBe(84);\n  }\n\n  // Test with error result\n  const errorResult: Result<number, string> = { status: \"error\", error: \"error message\" };\n  const mappedError = mapResult(errorResult, (n: number) => n * 2);\n  expect(mappedError.status).toBe(\"error\");\n  if (mappedError.status === \"error\") {\n    expect(mappedError.error).toBe(\"error message\");\n  }\n\n  // Test with pending result (no progress)\n  const pendingResult: AsyncResult<number, string, void> = { status: \"pending\", progress: undefined };\n  const mappedPending = mapResult(pendingResult, (n: number) => n * 2);\n  expect(mappedPending.status).toBe(\"pending\");\n\n  // Test with pending result (with progress)\n  const progressValue = { loaded: 50, total: 100 };\n  const pendingWithProgress: AsyncResult<number, string, typeof progressValue> = {\n    status: \"pending\",\n    progress: progressValue\n  };\n  const mappedPendingWithProgress = mapResult(pendingWithProgress, (n: number) => n * 2);\n  expect(mappedPendingWithProgress.status).toBe(\"pending\");\n  if (mappedPendingWithProgress.status === \"pending\") {\n    expect(mappedPendingWithProgress.progress).toBe(progressValue);\n  }\n});\n\n\nclass RetryError extends AggregateError {\n  constructor(public readonly errors: unknown[]) {\n    const strings = errors.map(e => nicify(e));\n    const isAllSame = strings.length > 1 && strings.every(s => s === strings[0]);\n    super(\n      errors,\n      deindent`\n      Error after ${errors.length} attempts.\n      \n      ${isAllSame ? deindent`\n        Attempts 1-${errors.length}:\n          ${strings[0]}\n      ` : strings.map((s, i) => deindent`\n          Attempt ${i + 1}:\n            ${s}\n        `).join(\"\\n\\n\")}\n      `,\n      { cause: errors[errors.length - 1] }\n    );\n    this.name = \"RetryError\";\n  }\n\n  get attempts() {\n    return this.errors.length;\n  }\n}\nRetryError.prototype.name = \"RetryError\";\n\nundefined?.test(\"RetryError\", ({ expect }) => {\n  // Test with single error\n  const singleError = new Error(\"Single error\");\n  const retryErrorSingle = new RetryError([singleError]);\n  expect(retryErrorSingle.name).toBe(\"RetryError\");\n  expect(retryErrorSingle.errors).toEqual([singleError]);\n  expect(retryErrorSingle.attempts).toBe(1);\n  expect(retryErrorSingle.cause).toBe(singleError);\n  expect(retryErrorSingle.message).toContain(\"Error after 1 attempts\");\n\n  // Test with multiple different errors\n  const error1 = new Error(\"Error 1\");\n  const error2 = new Error(\"Error 2\");\n  const retryErrorMultiple = new RetryError([error1, error2]);\n  expect(retryErrorMultiple.name).toBe(\"RetryError\");\n  expect(retryErrorMultiple.errors).toEqual([error1, error2]);\n  expect(retryErrorMultiple.attempts).toBe(2);\n  expect(retryErrorMultiple.cause).toBe(error2);\n  expect(retryErrorMultiple.message).toContain(\"Error after 2 attempts\");\n  expect(retryErrorMultiple.message).toContain(\"Attempt 1\");\n  expect(retryErrorMultiple.message).toContain(\"Attempt 2\");\n\n  // Test with multiple identical errors\n  const sameError = new Error(\"Same error\");\n  const retryErrorSame = new RetryError([sameError, sameError]);\n  expect(retryErrorSame.name).toBe(\"RetryError\");\n  expect(retryErrorSame.errors).toEqual([sameError, sameError]);\n  expect(retryErrorSame.attempts).toBe(2);\n  expect(retryErrorSame.cause).toBe(sameError);\n  expect(retryErrorSame.message).toContain(\"Error after 2 attempts\");\n  expect(retryErrorSame.message).toContain(\"Attempts 1-2\");\n});\n\nasync function retry<T>(\n  fn: (attemptIndex: number) => Result<T> | Promise<Result<T>>,\n  totalAttempts: number,\n  { exponentialDelayBase = 1000 } = {},\n): Promise<Result<T, RetryError> & { attempts: number }> {\n  const errors: unknown[] = [];\n  for (let i = 0; i < totalAttempts; i++) {\n    const res = await fn(i);\n    if (res.status === \"ok\") {\n      return Object.assign(Result.ok(res.data), { attempts: i + 1 });\n    } else {\n      errors.push(res.error);\n      if (i < totalAttempts - 1) {\n        await wait((Math.random() + 0.5) * exponentialDelayBase * (2 ** i));\n      }\n    }\n  }\n  return Object.assign(Result.error(new RetryError(errors)), { attempts: totalAttempts });\n}\nundefined?.test(\"retry\", async ({ expect }) => {\n  // Test successful on first attempt\n  const successFn = async () => Result.ok(\"success\");\n  const successResult = await retry(successFn, 3, { exponentialDelayBase: 0 });\n    expect(successResult).toEqual({ status: \"ok\", data: \"success\", attempts: 1 });\n\n    // Test successful after failures\n    let attemptCount = 0;\n    const eventualSuccessFn = async () => {\n      return ++attemptCount < 2 ? Result.error(new Error(`Attempt ${attemptCount} failed`))\n        : Result.ok(\"eventual success\");\n    };\n    const eventualSuccessResult = await retry(eventualSuccessFn, 3, { exponentialDelayBase: 0 });\n    expect(eventualSuccessResult).toEqual({ status: \"ok\", data: \"eventual success\", attempts: 2 });\n\n    // Test all attempts fail\n    const errors = [new Error(\"Error 1\"), new Error(\"Error 2\"), new Error(\"Error 3\")];\n    const allFailFn = async (attempt: number) => {\n      return Result.error(errors[attempt]);\n    };\n    const allFailResult = await retry(allFailFn, 3, { exponentialDelayBase: 0 });\n    expect(allFailResult).toEqual({ status: \"error\", error: expect.any(RetryError), attempts: 3 });\n    const retryError = (allFailResult as any).error as RetryError;\n    expect(retryError.errors).toEqual(errors);\n    expect(retryError.attempts).toBe(3);\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAAqB;AACrB,qBAAiC;AAwB1B,IAAM,SAAS;AAAA,EACpB;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb,GAAM,MAA8C;AAClD,WAAO;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAS,OAAkD;AACzD,WAAO;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA,KAAK;AAAA,EACL,IAAI,CAAU,QAAsB,aAAuB;AACzD,WAAO,OAAO,WAAW,OAAO,OAAO,OAAO;AAAA,EAChD;AAAA,EACA,SAAS,CAAO,WAA4B;AAC1C,QAAI,OAAO,WAAW,SAAS;AAC7B,YAAM,OAAO;AAAA,IACf;AACA,WAAO,OAAO;AAAA,EAChB;AAAA,EACA,cAAc,OAAa,WAA8C;AACvE,WAAO,OAAO,QAAQ,MAAM,MAAM;AAAA,EACpC;AAAA,EACA;AACF;AA8CO,IAAM,cAAc;AAAA,EACzB;AAAA,EACA,aAAa;AAAA,EACb,IAAI,OAAO;AAAA,EACX,OAAO,OAAO;AAAA,EACd;AAAA,EACA,KAAK;AAAA,EACL,IAAI,CAAa,QAA8B,aAAuB;AACpE,QAAI,OAAO,WAAW,WAAW;AAC/B,aAAO;AAAA,IACT;AACA,WAAO,OAAO,GAAG,QAAQ,QAAQ;AAAA,EACnC;AAAA,EACA,SAAS,CAAU,WAAoC;AACrD,QAAI,OAAO,WAAW,WAAW;AAC/B,YAAM,IAAI,MAAM,sBAAsB;AAAA,IACxC;AACA,WAAO,OAAO,QAAQ,MAAM;AAAA,EAC9B;AAAA,EACA;AACF;AAgCA,SAAS,QAAW,UAAoE;AACtF,SAAO;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF;AACF;AAcA,eAAe,gBAAmB,SAAyC;AACzE,MAAI;AACF,UAAM,QAAQ,MAAM;AACpB,WAAO,OAAO,GAAG,KAAK;AAAA,EACxB,SAAS,OAAO;AACd,WAAO,OAAO,MAAM,KAAK;AAAA,EAC3B;AACF;AAoBA,SAAS,aAAgB,IAAiC;AACxD,MAAI;AACF,WAAO,OAAO,GAAG,GAAG,CAAC;AAAA,EACvB,SAAS,OAAO;AACd,WAAO,OAAO,MAAM,KAAK;AAAA,EAC3B;AACF;AAsBA,eAAe,kBAAqB,IAAmD;AACrF,MAAI;AACF,WAAO,OAAO,GAAG,MAAM,GAAG,CAAC;AAAA,EAC7B,SAAS,OAAO;AACd,WAAO,OAAO,MAAM,KAAK;AAAA,EAC3B;AACF;AAwBA,SAAS,UAA0C,QAA8B,IAA0C;AACzH,MAAI,OAAO,WAAW,QAAS,QAAO;AAAA,IACpC,QAAQ;AAAA,IACR,OAAO,OAAO;AAAA,EAChB;AACA,MAAI,OAAO,WAAW,UAAW,QAAO;AAAA,IACtC,QAAQ;AAAA,IACR,GAAG,cAAc,SAAS,EAAE,UAAU,OAAO,SAAS,IAAI,CAAC;AAAA,EAC7D;AAEA,SAAO,OAAO,GAAG,GAAG,OAAO,IAAI,CAAC;AAClC;AAqCA,IAAM,aAAN,cAAyB,eAAe;AAAA,EACtC,YAA4B,QAAmB;AAC7C,UAAM,UAAU,OAAO,IAAI,WAAK,uBAAO,CAAC,CAAC;AACzC,UAAM,YAAY,QAAQ,SAAS,KAAK,QAAQ,MAAM,OAAK,MAAM,QAAQ,CAAC,CAAC;AAC3E;AAAA,MACE;AAAA,MACA;AAAA,oBACc,OAAO,MAAM;AAAA;AAAA,QAEzB,YAAY;AAAA,qBACC,OAAO,MAAM;AAAA,YACtB,QAAQ,CAAC,CAAC;AAAA,UACZ,QAAQ,IAAI,CAAC,GAAG,MAAM;AAAA,oBACZ,IAAI,CAAC;AAAA,cACX,CAAC;AAAA,SACN,EAAE,KAAK,MAAM,CAAC;AAAA;AAAA,MAEjB,EAAE,OAAO,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,IACrC;AAjB0B;AAkB1B,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,OAAO;AAAA,EACrB;AACF;AACA,WAAW,UAAU,OAAO;AAmC5B,eAAe,MACb,IACA,eACA,EAAE,uBAAuB,IAAK,IAAI,CAAC,GACoB;AACvD,QAAM,SAAoB,CAAC;AAC3B,WAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,UAAM,MAAM,MAAM,GAAG,CAAC;AACtB,QAAI,IAAI,WAAW,MAAM;AACvB,aAAO,OAAO,OAAO,OAAO,GAAG,IAAI,IAAI,GAAG,EAAE,UAAU,IAAI,EAAE,CAAC;AAAA,IAC/D,OAAO;AACL,aAAO,KAAK,IAAI,KAAK;AACrB,UAAI,IAAI,gBAAgB,GAAG;AACzB,kBAAM,uBAAM,KAAK,OAAO,IAAI,OAAO,uBAAwB,KAAK,CAAE;AAAA,MACpE;AAAA,IACF;AAAA,EACF;AACA,SAAO,OAAO,OAAO,OAAO,MAAM,IAAI,WAAW,MAAM,CAAC,GAAG,EAAE,UAAU,cAAc,CAAC;AACxF;", "names": []}