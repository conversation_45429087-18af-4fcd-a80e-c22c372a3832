{"version": 3, "sources": ["../../../../../src/lib/stack-app/users/index.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { KnownErrors } from \"@stackframe/stack-shared\";\nimport { CurrentUserCrud } from \"@stackframe/stack-shared/dist/interface/crud/current-user\";\nimport { UsersCrud } from \"@stackframe/stack-shared/dist/interface/crud/users\";\nimport { InternalSession } from \"@stackframe/stack-shared/dist/sessions\";\nimport { encodeBase64 } from \"@stackframe/stack-shared/dist/utils/bytes\";\nimport { GeoInfo } from \"@stackframe/stack-shared/dist/utils/geo\";\nimport { ReadonlyJson } from \"@stackframe/stack-shared/dist/utils/json\";\nimport { ProviderType } from \"@stackframe/stack-shared/dist/utils/oauth\";\nimport { Result } from \"@stackframe/stack-shared/dist/utils/results\";\nimport { ApiKeyCreationOptions, UserApiKey, UserApiKeyFirstView } from \"../api-keys\";\nimport { AsyncStoreProperty } from \"../common\";\nimport { OAuthConnection } from \"../connected-accounts\";\nimport { ContactChannel, ContactChannelCreateOptions, ServerContactChannel, ServerContactChannelCreateOptions } from \"../contact-channels\";\nimport { AdminTeamPermission, TeamPermission } from \"../permissions\";\nimport { AdminOwnedProject, AdminProjectUpdateOptions } from \"../projects\";\nimport { EditableTeamMemberProfile, ServerTeam, ServerTeamCreateOptions, Team, TeamCreateOptions } from \"../teams\";\n\n\nexport type Session = {\n  getTokens(): Promise<{ accessToken: string | null, refreshToken: string | null }>,\n};\n\n/**\n * Contains everything related to the current user session.\n */\nexport type Auth = {\n  readonly _internalSession: InternalSession,\n  readonly currentSession: Session,\n  signOut(options?: { redirectUrl?: URL | string }): Promise<void>,\n\n  /**\n   * Returns headers for sending authenticated HTTP requests to external servers. Most commonly used in cross-origin\n   * requests. Similar to `getAuthJson`, but specifically for HTTP requests.\n   *\n   * If you are using `tokenStore: \"cookie\"`, you don't need this for same-origin requests. However, most\n   * browsers now disable third-party cookies by default, so we must pass authentication tokens by header instead\n   * if the client and server are on different origins.\n   *\n   * This function returns a header object that can be used with `fetch` or other HTTP request libraries to send\n   * authenticated requests.\n   *\n   * On the server, you can then pass in the `Request` object to the `tokenStore` option\n   * of your Stack app. Please note that CORS does not allow most headers by default, so you\n   * must include `x-stack-auth` in the [`Access-Control-Allow-Headers` header](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Allow-Headers)\n   * of the CORS preflight response.\n   *\n   * If you are not using HTTP (and hence cannot set headers), you will need to use the `getAuthJson()` function\n   * instead.\n   *\n   * Example:\n   *\n   * ```ts\n   * // client\n   * const res = await fetch(\"https://api.example.com\", {\n   *   headers: {\n   *     ...await stackApp.getAuthHeaders()\n   *     // you can also add your own headers here\n   *   },\n   * });\n   *\n   * // server\n   * function handleRequest(req: Request) {\n   *   const user = await stackServerApp.getUser({ tokenStore: req });\n   *   return new Response(\"Welcome, \" + user.displayName);\n   * }\n   * ```\n   */\n  getAuthHeaders(): Promise<{ \"x-stack-auth\": string }>,\n\n  /**\n   * Creates a JSON-serializable object containing the information to authenticate a user on an external server.\n   * Similar to `getAuthHeaders`, but returns an object that can be sent over any protocol instead of just\n   * HTTP headers.\n   *\n   * While `getAuthHeaders` is the recommended way to send authentication tokens over HTTP, your app may use\n   * a different protocol, for example WebSockets or gRPC. This function returns a token object that can be JSON-serialized and sent to the server in any way you like.\n   *\n   * On the server, you can pass in this token object into the `tokenStore` option to fetch user details.\n   *\n   * Example:\n   *\n   * ```ts\n   * // client\n   * const res = await rpcCall(rpcEndpoint, {\n   *   data: {\n   *     auth: await stackApp.getAuthJson(),\n   *   },\n   * });\n   *\n   * // server\n   * function handleRequest(data) {\n   *   const user = await stackServerApp.getUser({ tokenStore: data.auth });\n   *   return new Response(\"Welcome, \" + user.displayName);\n   * }\n   * ```\n   */\n  getAuthJson(): Promise<{ accessToken: string | null, refreshToken: string | null }>,\n  registerPasskey(options?: { hostname?: string }): Promise<Result<undefined, KnownErrors[\"PasskeyRegistrationFailed\"] | KnownErrors[\"PasskeyWebAuthnError\"]>>,\n};\n\n/**\n * ```\n * +----------+-------------+-------------------+\n * |    \\     |   !Server   |      Server       |\n * +----------+-------------+-------------------+\n * | !Session | User        | ServerUser        |\n * | Session  | CurrentUser | CurrentServerUser |\n * +----------+-------------+-------------------+\n * ```\n *\n * The fields on each of these types are available iff:\n * BaseUser: true\n * Auth: Session\n * ServerBaseUser: Server\n * UserExtra: Session OR Server\n *\n * The types are defined as follows (in the typescript manner):\n * User = BaseUser\n * CurrentUser = BaseUser & Auth & UserExtra\n * ServerUser = BaseUser & ServerBaseUser & UserExtra\n * CurrentServerUser = BaseUser & ServerBaseUser & Auth & UserExtra\n **/\n\nexport type BaseUser = {\n  readonly id: string,\n\n  readonly displayName: string | null,\n\n  /**\n   * The user's email address.\n   *\n   * Note: This might NOT be unique across multiple users, so always use `id` for unique identification.\n   */\n  readonly primaryEmail: string | null,\n  readonly primaryEmailVerified: boolean,\n  readonly profileImageUrl: string | null,\n\n  readonly signedUpAt: Date,\n\n  readonly clientMetadata: any,\n  readonly clientReadOnlyMetadata: any,\n\n  /**\n   * Whether the user has a password set.\n   */\n  readonly hasPassword: boolean,\n  readonly otpAuthEnabled: boolean,\n  readonly passkeyAuthEnabled: boolean,\n\n  readonly isMultiFactorRequired: boolean,\n  readonly isAnonymous: boolean,\n  toClientJson(): CurrentUserCrud[\"Client\"][\"Read\"],\n\n  /**\n   * @deprecated, use contact channel's usedForAuth instead\n   */\n  readonly emailAuthEnabled: boolean,\n  /**\n   * @deprecated\n   */\n  readonly oauthProviders: readonly { id: string }[],\n}\n\nexport type UserExtra = {\n  setDisplayName(displayName: string): Promise<void>,\n  /** @deprecated Use contact channel's sendVerificationEmail instead */\n  sendVerificationEmail(): Promise<KnownErrors[\"EmailAlreadyVerified\"] | void>,\n  setClientMetadata(metadata: any): Promise<void>,\n  updatePassword(options: { oldPassword: string, newPassword: string}): Promise<KnownErrors[\"PasswordConfirmationMismatch\"] | KnownErrors[\"PasswordRequirementsNotMet\"] | void>,\n  setPassword(options: { password: string }): Promise<KnownErrors[\"PasswordRequirementsNotMet\"] | void>,\n\n  /**\n   * A shorthand method to update multiple fields of the user at once.\n   */\n  update(update: UserUpdateOptions): Promise<void>,\n\n  useContactChannels(): ContactChannel[], // THIS_LINE_PLATFORM react-like\n  listContactChannels(): Promise<ContactChannel[]>,\n  createContactChannel(data: ContactChannelCreateOptions): Promise<ContactChannel>,\n\n  delete(): Promise<void>,\n\n  getConnectedAccount(id: ProviderType, options: { or: 'redirect', scopes?: string[] }): Promise<OAuthConnection>,\n  getConnectedAccount(id: ProviderType, options?: { or?: 'redirect' | 'throw' | 'return-null', scopes?: string[] }): Promise<OAuthConnection | null>,\n\n  useConnectedAccount(id: ProviderType, options: { or: 'redirect', scopes?: string[] }): OAuthConnection,\n  useConnectedAccount(id: ProviderType, options?: { or?: 'redirect' | 'throw' | 'return-null', scopes?: string[] }): OAuthConnection | null,\n\n  hasPermission(scope: Team, permissionId: string): Promise<boolean>,\n  hasPermission(permissionId: string): Promise<boolean>,\n\n  getPermission(scope: Team, permissionId: string): Promise<TeamPermission | null>,\n  getPermission(permissionId: string): Promise<TeamPermission | null>,\n\n  listPermissions(scope: Team, options?: { recursive?: boolean }): Promise<TeamPermission[]>,\n  listPermissions(options?: { recursive?: boolean }): Promise<TeamPermission[]>,\n\n  usePermissions(scope: Team, options?: { recursive?: boolean }): TeamPermission[],\n  usePermissions(options?: { recursive?: boolean }): TeamPermission[],\n\n  usePermission(scope: Team, permissionId: string): TeamPermission | null,\n  usePermission(permissionId: string): TeamPermission | null,\n\n  readonly selectedTeam: Team | null,\n  setSelectedTeam(team: Team | null): Promise<void>,\n  createTeam(data: TeamCreateOptions): Promise<Team>,\n  leaveTeam(team: Team): Promise<void>,\n\n  getActiveSessions(): Promise<ActiveSession[]>,\n  revokeSession(sessionId: string): Promise<void>,\n  getTeamProfile(team: Team): Promise<EditableTeamMemberProfile>,\n  useTeamProfile(team: Team): EditableTeamMemberProfile, // THIS_LINE_PLATFORM react-like\n\n  createApiKey(options: ApiKeyCreationOptions<\"user\">): Promise<UserApiKeyFirstView>,\n}\n& AsyncStoreProperty<\"apiKeys\", [], UserApiKey[], true>\n& AsyncStoreProperty<\"team\", [id: string], Team | null, false>\n& AsyncStoreProperty<\"teams\", [], Team[], true>\n& AsyncStoreProperty<\"permission\", [scope: Team, permissionId: string, options?: { recursive?: boolean }], TeamPermission | null, false>\n& AsyncStoreProperty<\"permissions\", [scope: Team, options?: { recursive?: boolean }], TeamPermission[], true>;\n\nexport type InternalUserExtra =\n  & {\n    createProject(newProject: AdminProjectUpdateOptions & { displayName: string }): Promise<AdminOwnedProject>,\n  }\n  & AsyncStoreProperty<\"ownedProjects\", [], AdminOwnedProject[], true>\n\nexport type User = BaseUser;\n\nexport type CurrentUser = BaseUser & Auth & UserExtra;\n\nexport type CurrentInternalUser = CurrentUser & InternalUserExtra;\n\nexport type ProjectCurrentUser<ProjectId> = ProjectId extends \"internal\" ? CurrentInternalUser : CurrentUser;\n\n\nexport type ActiveSession = {\n  id: string,\n  userId: string,\n  createdAt: Date,\n  isImpersonation: boolean,\n  lastUsedAt: Date | undefined,\n  isCurrentSession: boolean,\n  geoInfo?: GeoInfo,\n};\n\nexport type UserUpdateOptions = {\n  displayName?: string,\n  clientMetadata?: ReadonlyJson,\n  selectedTeamId?: string | null,\n  totpMultiFactorSecret?: Uint8Array | null,\n  profileImageUrl?: string | null,\n  otpAuthEnabled?: boolean,\n  passkeyAuthEnabled?:boolean,\n}\nexport function userUpdateOptionsToCrud(options: UserUpdateOptions): CurrentUserCrud[\"Client\"][\"Update\"] {\n  return {\n    display_name: options.displayName,\n    client_metadata: options.clientMetadata,\n    selected_team_id: options.selectedTeamId,\n    totp_secret_base64: options.totpMultiFactorSecret != null ? encodeBase64(options.totpMultiFactorSecret) : options.totpMultiFactorSecret,\n    profile_image_url: options.profileImageUrl,\n    otp_auth_enabled: options.otpAuthEnabled,\n    passkey_auth_enabled: options.passkeyAuthEnabled,\n  };\n}\n\n\nexport type ServerBaseUser = {\n  setPrimaryEmail(email: string | null, options?: { verified?: boolean | undefined }): Promise<void>,\n\n  readonly lastActiveAt: Date,\n\n  readonly serverMetadata: any,\n  setServerMetadata(metadata: any): Promise<void>,\n  setClientReadOnlyMetadata(metadata: any): Promise<void>,\n\n  createTeam(data: Omit<ServerTeamCreateOptions, \"creatorUserId\">): Promise<ServerTeam>,\n\n  useContactChannels(): ServerContactChannel[], // THIS_LINE_PLATFORM react-like\n  listContactChannels(): Promise<ServerContactChannel[]>,\n  createContactChannel(data: ServerContactChannelCreateOptions): Promise<ServerContactChannel>,\n\n  update(user: ServerUserUpdateOptions): Promise<void>,\n\n  grantPermission(scope: Team, permissionId: string): Promise<void>,\n  grantPermission(permissionId: string): Promise<void>,\n\n  revokePermission(scope: Team, permissionId: string): Promise<void>,\n  revokePermission(permissionId: string): Promise<void>,\n\n  getPermission(scope: Team, permissionId: string): Promise<TeamPermission | null>,\n  getPermission(permissionId: string): Promise<TeamPermission | null>,\n\n  hasPermission(scope: Team, permissionId: string): Promise<boolean>,\n  hasPermission(permissionId: string): Promise<boolean>,\n\n  listPermissions(scope: Team, options?: { recursive?: boolean }): Promise<TeamPermission[]>,\n  listPermissions(options?: { recursive?: boolean }): Promise<TeamPermission[]>,\n\n  usePermissions(scope: Team, options?: { recursive?: boolean }): TeamPermission[],\n  usePermissions(options?: { recursive?: boolean }): TeamPermission[],\n\n  usePermission(scope: Team, permissionId: string): TeamPermission | null,\n  usePermission(permissionId: string): TeamPermission | null,\n\n  /**\n   * Creates a new session object with a refresh token for this user. Can be used to impersonate them.\n   */\n  createSession(options?: { expiresInMillis?: number, isImpersonation?: boolean }): Promise<Session>,\n}\n& AsyncStoreProperty<\"team\", [id: string], ServerTeam | null, false>\n& AsyncStoreProperty<\"teams\", [], ServerTeam[], true>\n& AsyncStoreProperty<\"permission\", [scope: Team, permissionId: string, options?: { direct?: boolean }], AdminTeamPermission | null, false>\n& AsyncStoreProperty<\"permissions\", [scope: Team, options?: { direct?: boolean }], AdminTeamPermission[], true>;\n\n/**\n * A user including sensitive fields that should only be used on the server, never sent to the client\n * (such as sensitive information and serverMetadata).\n */\nexport type ServerUser = ServerBaseUser & BaseUser & UserExtra;\n\nexport type CurrentServerUser = Auth & ServerUser;\n\nexport type CurrentInternalServerUser = CurrentServerUser & InternalUserExtra;\n\nexport type ProjectCurrentServerUser<ProjectId> = ProjectId extends \"internal\" ? CurrentInternalServerUser : CurrentServerUser;\n\n\nexport type ServerUserUpdateOptions = {\n  primaryEmail?: string | null,\n  primaryEmailVerified?: boolean,\n  primaryEmailAuthEnabled?: boolean,\n  clientReadOnlyMetadata?: ReadonlyJson,\n  serverMetadata?: ReadonlyJson,\n  password?: string,\n} & UserUpdateOptions;\nexport function serverUserUpdateOptionsToCrud(options: ServerUserUpdateOptions): CurrentUserCrud[\"Server\"][\"Update\"] {\n  return {\n    display_name: options.displayName,\n    primary_email: options.primaryEmail,\n    client_metadata: options.clientMetadata,\n    client_read_only_metadata: options.clientReadOnlyMetadata,\n    server_metadata: options.serverMetadata,\n    selected_team_id: options.selectedTeamId,\n    primary_email_auth_enabled: options.primaryEmailAuthEnabled,\n    primary_email_verified: options.primaryEmailVerified,\n    password: options.password,\n    profile_image_url: options.profileImageUrl,\n    totp_secret_base64: options.totpMultiFactorSecret != null ? encodeBase64(options.totpMultiFactorSecret) : options.totpMultiFactorSecret,\n  };\n}\n\n\nexport type ServerUserCreateOptions = {\n  primaryEmail?: string | null,\n  primaryEmailAuthEnabled?: boolean,\n  password?: string,\n  otpAuthEnabled?: boolean,\n  displayName?: string,\n  primaryEmailVerified?: boolean,\n  clientMetadata?: any,\n  clientReadOnlyMetadata?: any,\n  serverMetadata?: any,\n}\nexport function serverUserCreateOptionsToCrud(options: ServerUserCreateOptions): UsersCrud[\"Server\"][\"Create\"] {\n  return {\n    primary_email: options.primaryEmail,\n    password: options.password,\n    otp_auth_enabled: options.otpAuthEnabled,\n    primary_email_auth_enabled: options.primaryEmailAuthEnabled,\n    display_name: options.displayName,\n    primary_email_verified: options.primaryEmailVerified,\n    client_metadata: options.clientMetadata,\n    client_read_only_metadata: options.clientReadOnlyMetadata,\n    server_metadata: options.serverMetadata,\n  };\n}\n"], "mappings": ";AAQA,SAAS,oBAAoB;AA2PtB,SAAS,wBAAwB,SAAiE;AACvG,SAAO;AAAA,IACL,cAAc,QAAQ;AAAA,IACtB,iBAAiB,QAAQ;AAAA,IACzB,kBAAkB,QAAQ;AAAA,IAC1B,oBAAoB,QAAQ,yBAAyB,OAAO,aAAa,QAAQ,qBAAqB,IAAI,QAAQ;AAAA,IAClH,mBAAmB,QAAQ;AAAA,IAC3B,kBAAkB,QAAQ;AAAA,IAC1B,sBAAsB,QAAQ;AAAA,EAChC;AACF;AAwEO,SAAS,8BAA8B,SAAuE;AACnH,SAAO;AAAA,IACL,cAAc,QAAQ;AAAA,IACtB,eAAe,QAAQ;AAAA,IACvB,iBAAiB,QAAQ;AAAA,IACzB,2BAA2B,QAAQ;AAAA,IACnC,iBAAiB,QAAQ;AAAA,IACzB,kBAAkB,QAAQ;AAAA,IAC1B,4BAA4B,QAAQ;AAAA,IACpC,wBAAwB,QAAQ;AAAA,IAChC,UAAU,QAAQ;AAAA,IAClB,mBAAmB,QAAQ;AAAA,IAC3B,oBAAoB,QAAQ,yBAAyB,OAAO,aAAa,QAAQ,qBAAqB,IAAI,QAAQ;AAAA,EACpH;AACF;AAcO,SAAS,8BAA8B,SAAiE;AAC7G,SAAO;AAAA,IACL,eAAe,QAAQ;AAAA,IACvB,UAAU,QAAQ;AAAA,IAClB,kBAAkB,QAAQ;AAAA,IAC1B,4BAA4B,QAAQ;AAAA,IACpC,cAAc,QAAQ;AAAA,IACtB,wBAAwB,QAAQ;AAAA,IAChC,iBAAiB,QAAQ;AAAA,IACzB,2BAA2B,QAAQ;AAAA,IACnC,iBAAiB,QAAQ;AAAA,EAC3B;AACF;", "names": []}