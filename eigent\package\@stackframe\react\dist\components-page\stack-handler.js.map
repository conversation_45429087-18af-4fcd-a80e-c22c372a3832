{"version": 3, "sources": ["../../src/components-page/stack-handler.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { StackAssertionError } from \"@stackframe/stack-shared/dist/utils/errors\";\nimport { FilterUndefined, filterUndefined, pick } from \"@stackframe/stack-shared/dist/utils/objects\";\nimport { getRelativePart } from \"@stackframe/stack-shared/dist/utils/urls\";\nimport { useMemo } from 'react';\nimport { SignIn, SignUp, StackServerApp } from \"..\";\nimport { IframePreventer } from \"../components/iframe-preventer\";\nimport { MessageCard } from \"../components/message-cards/message-card\";\nimport { HandlerUrls, StackClientApp } from \"../lib/stack-app\";\nimport { AccountSettings } from \"./account-settings\";\nimport { CliAuthConfirmation } from \"./cli-auth-confirm\";\nimport { EmailVerification } from \"./email-verification\";\nimport { ErrorPage } from \"./error-page\";\nimport { ForgotPassword } from \"./forgot-password\";\nimport { MagicLinkCallback } from \"./magic-link-callback\";\nimport { OAuthCallback } from \"./oauth-callback\";\nimport { PasswordReset } from \"./password-reset\";\nimport { SignOut } from \"./sign-out\";\nimport { TeamInvitation } from \"./team-invitation\";\n\ntype Components = {\n  SignIn: typeof SignIn,\n  SignUp: typeof SignUp,\n  EmailVerification: typeof EmailVerification,\n  PasswordReset: typeof PasswordReset,\n  ForgotPassword: typeof ForgotPassword,\n  SignOut: typeof SignOut,\n  OAuthCallback: typeof OAuthCallback,\n  MagicLinkCallback: typeof MagicLinkCallback,\n  TeamInvitation: typeof TeamInvitation,\n  ErrorPage: typeof ErrorPage,\n  AccountSettings: typeof AccountSettings,\n  CliAuthConfirmation: typeof CliAuthConfirmation,\n};\n\ntype RouteProps = {\n  params: Promise<{ stack?: string[] }> | { stack?: string[] },\n  searchParams: Promise<Record<string, string>> | Record<string, string>,\n};\n\nconst next15DeprecationWarning = \"DEPRECATION WARNING: Next.js 15 disallows spreading the props argument of <StackHandler /> like `{...props}`, so you must now explicitly pass them in the `routeProps` argument: `routeProps={props}`. You can fix this by updating the code in the file `app/handler/[...stack]/route.tsx`.\";\n\nconst availablePaths = {\n  signIn: 'sign-in',\n  signUp: 'sign-up',\n  emailVerification: 'email-verification',\n  passwordReset: 'password-reset',\n  forgotPassword: 'forgot-password',\n  signOut: 'sign-out',\n  oauthCallback: 'oauth-callback',\n  magicLinkCallback: 'magic-link-callback',\n  teamInvitation: 'team-invitation',\n  accountSettings: 'account-settings',\n  cliAuthConfirm: 'cli-auth-confirm',\n  error: 'error',\n} as const;\n\nconst pathAliases = {\n  // also includes the uppercase and non-dashed versions\n  ...Object.fromEntries(Object.entries(availablePaths).map(([key, value]) => [value, value])),\n  \"log-in\": availablePaths.signIn,\n  \"register\": availablePaths.signUp,\n} as const;\n\ntype BaseHandlerProps = {\n  fullPage: boolean,\n  componentProps?: {\n    [K in keyof Components]?: Parameters<Components[K]>[0];\n  },\n};\n\nfunction renderComponent(props: {\n  path: string,\n  searchParams: Record<string, string>,\n  fullPage: boolean,\n  componentProps?: BaseHandlerProps['componentProps'],\n  redirectIfNotHandler?: (name: keyof HandlerUrls) => void,\n  onNotFound: () => any,\n  app: StackClientApp<any> | StackServerApp<any>,\n}) {\n  const { path, searchParams, fullPage, componentProps, redirectIfNotHandler, onNotFound, app } = props;\n\n  switch (path) {\n    case availablePaths.signIn: {\n      redirectIfNotHandler?.('signIn');\n      return <SignIn\n        fullPage={fullPage}\n        automaticRedirect\n        {...filterUndefinedINU(componentProps?.SignIn)}\n      />;\n    }\n    case availablePaths.signUp: {\n      redirectIfNotHandler?.('signUp');\n      return <SignUp\n        fullPage={fullPage}\n        automaticRedirect\n        {...filterUndefinedINU(componentProps?.SignUp)}\n      />;\n    }\n    case availablePaths.emailVerification: {\n      redirectIfNotHandler?.('emailVerification');\n      return <EmailVerification\n        searchParams={searchParams}\n        fullPage={fullPage}\n        {...filterUndefinedINU(componentProps?.EmailVerification)}\n      />;\n    }\n    case availablePaths.passwordReset: {\n      redirectIfNotHandler?.('passwordReset');\n      return <PasswordReset\n        searchParams={searchParams}\n        fullPage={fullPage}\n        {...filterUndefinedINU(componentProps?.PasswordReset)}\n      />;\n    }\n    case availablePaths.forgotPassword: {\n      redirectIfNotHandler?.('forgotPassword');\n      return <ForgotPassword\n        fullPage={fullPage}\n        {...filterUndefinedINU(componentProps?.ForgotPassword)}\n      />;\n    }\n    case availablePaths.signOut: {\n      redirectIfNotHandler?.('signOut');\n      return <SignOut\n        fullPage={fullPage}\n        {...filterUndefinedINU(componentProps?.SignOut)}\n      />;\n    }\n    case availablePaths.oauthCallback: {\n      redirectIfNotHandler?.('oauthCallback');\n      return <OAuthCallback\n        fullPage={fullPage}\n        {...filterUndefinedINU(componentProps?.OAuthCallback)}\n      />;\n    }\n    case availablePaths.magicLinkCallback: {\n      redirectIfNotHandler?.('magicLinkCallback');\n      return <MagicLinkCallback\n        searchParams={searchParams}\n        fullPage={fullPage}\n        {...filterUndefinedINU(componentProps?.MagicLinkCallback)}\n      />;\n    }\n    case availablePaths.teamInvitation: {\n      redirectIfNotHandler?.('teamInvitation');\n      return <TeamInvitation\n        searchParams={searchParams}\n        fullPage={fullPage}\n        {...filterUndefinedINU(componentProps?.TeamInvitation)}\n      />;\n    }\n    case availablePaths.accountSettings: {\n      return <AccountSettings\n        fullPage={fullPage}\n        {...filterUndefinedINU(componentProps?.AccountSettings)}\n      />;\n    }\n    case availablePaths.error: {\n      return <ErrorPage\n        searchParams={searchParams}\n        fullPage={fullPage}\n        {...filterUndefinedINU(componentProps?.ErrorPage)}\n      />;\n    }\n    case availablePaths.cliAuthConfirm: {\n      return <CliAuthConfirmation\n        fullPage={fullPage}\n        {...filterUndefinedINU(componentProps?.CliAuthConfirmation)}\n      />;\n    }\n    default: {\n      if (Object.values(availablePaths).includes(path as any)) {\n        throw new StackAssertionError(`Path alias ${path} not included in switch statement, but in availablePaths?`, { availablePaths });\n      }\n      for (const [key, value] of Object.entries(pathAliases)) {\n        if (path === key.toLowerCase().replaceAll('-', '')) {\n          const redirectUrl = `${app.urls.handler}/${value}?${new URLSearchParams(searchParams).toString()}`;\n          return { redirect: redirectUrl };\n        }\n      }\n      return onNotFound();\n    }\n  }\n}\n\n\nfunction ReactStackHandler<HasTokenStore extends boolean>(props: BaseHandlerProps & {\n  app: StackClientApp<HasTokenStore>,\n  location: string, // Path like \"/abc/def\"\n}) {\n  const { path, searchParams } = useMemo(() => {\n    // Get search string from window.location since it's not included in currentLocation\n    const search = window.location.search;\n    const handlerPath = new URL(props.app.urls.handler, window.location.origin).pathname;\n\n    // Remove the handler base path to get the relative path\n    const relativePath = props.location.startsWith(handlerPath)\n      ? props.location.slice(handlerPath.length).replace(/^\\/+/, '')\n      : props.location.replace(/^\\/+/, '');\n\n    return {\n      path: relativePath,\n      searchParams: Object.fromEntries(new URLSearchParams(search).entries())\n    };\n  }, [props.location, props.app.urls.handler]);\n\n  const redirectIfNotHandler = (name: keyof HandlerUrls) => {\n    const url = props.app.urls[name];\n    const handlerUrl = props.app.urls.handler;\n\n    if (url !== handlerUrl && url.startsWith(handlerUrl + \"/\")) {\n      return;\n    }\n\n    const urlObj = new URL(url, window.location.origin);\n    for (const [key, value] of Object.entries(searchParams)) {\n      urlObj.searchParams.set(key, value);\n    }\n\n    window.location.href = getRelativePart(urlObj);\n  };\n\n  const result = renderComponent({\n    path,\n    searchParams,\n    fullPage: props.fullPage,\n    componentProps: props.componentProps,\n    redirectIfNotHandler,\n    onNotFound: () => (\n      <MessageCard\n        title=\"Page does not exist\"\n        fullPage={props.fullPage}\n        primaryButtonText=\"Go to Home\"\n        primaryAction={() => props.app.redirectToHome()}\n      >\n        The page you are looking for could not be found. Please check the URL and try again.\n      </MessageCard>\n    ),\n    app: props.app,\n  });\n\n  if (result && 'redirect' in result) {\n    window.location.href = result.redirect;\n    return null;\n  }\n\n  return (\n    <IframePreventer>\n      {result}\n    </IframePreventer>\n  );\n}\n\n\nexport default ReactStackHandler;\n\n// filter undefined values in object. if object itself is undefined, return undefined\nfunction filterUndefinedINU<T extends {}>(value: T | undefined): FilterUndefined<T> | undefined {\n  return value === undefined ? value : filterUndefined(value);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,oBAAoC;AACpC,qBAAuD;AACvD,kBAAgC;AAChC,mBAAwB;AACxB,eAA+C;AAC/C,8BAAgC;AAChC,0BAA4B;AAE5B,8BAAgC;AAChC,8BAAoC;AACpC,gCAAkC;AAClC,wBAA0B;AAC1B,6BAA+B;AAC/B,iCAAkC;AAClC,4BAA8B;AAC9B,4BAA8B;AAC9B,sBAAwB;AACxB,6BAA+B;AAmElB;AA3Cb,IAAM,iBAAiB;AAAA,EACrB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,gBAAgB;AAAA,EAChB,OAAO;AACT;AAEA,IAAM,cAAc;AAAA;AAAA,EAElB,GAAG,OAAO,YAAY,OAAO,QAAQ,cAAc,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,OAAO,KAAK,CAAC,CAAC;AAAA,EAC1F,UAAU,eAAe;AAAA,EACzB,YAAY,eAAe;AAC7B;AASA,SAAS,gBAAgB,OAQtB;AACD,QAAM,EAAE,MAAM,cAAc,UAAU,gBAAgB,sBAAsB,YAAY,IAAI,IAAI;AAEhG,UAAQ,MAAM;AAAA,IACZ,KAAK,eAAe,QAAQ;AAC1B,6BAAuB,QAAQ;AAC/B,aAAO;AAAA,QAAC;AAAA;AAAA,UACN;AAAA,UACA,mBAAiB;AAAA,UAChB,GAAG,mBAAmB,gBAAgB,MAAM;AAAA;AAAA,MAC/C;AAAA,IACF;AAAA,IACA,KAAK,eAAe,QAAQ;AAC1B,6BAAuB,QAAQ;AAC/B,aAAO;AAAA,QAAC;AAAA;AAAA,UACN;AAAA,UACA,mBAAiB;AAAA,UAChB,GAAG,mBAAmB,gBAAgB,MAAM;AAAA;AAAA,MAC/C;AAAA,IACF;AAAA,IACA,KAAK,eAAe,mBAAmB;AACrC,6BAAuB,mBAAmB;AAC1C,aAAO;AAAA,QAAC;AAAA;AAAA,UACN;AAAA,UACA;AAAA,UACC,GAAG,mBAAmB,gBAAgB,iBAAiB;AAAA;AAAA,MAC1D;AAAA,IACF;AAAA,IACA,KAAK,eAAe,eAAe;AACjC,6BAAuB,eAAe;AACtC,aAAO;AAAA,QAAC;AAAA;AAAA,UACN;AAAA,UACA;AAAA,UACC,GAAG,mBAAmB,gBAAgB,aAAa;AAAA;AAAA,MACtD;AAAA,IACF;AAAA,IACA,KAAK,eAAe,gBAAgB;AAClC,6BAAuB,gBAAgB;AACvC,aAAO;AAAA,QAAC;AAAA;AAAA,UACN;AAAA,UACC,GAAG,mBAAmB,gBAAgB,cAAc;AAAA;AAAA,MACvD;AAAA,IACF;AAAA,IACA,KAAK,eAAe,SAAS;AAC3B,6BAAuB,SAAS;AAChC,aAAO;AAAA,QAAC;AAAA;AAAA,UACN;AAAA,UACC,GAAG,mBAAmB,gBAAgB,OAAO;AAAA;AAAA,MAChD;AAAA,IACF;AAAA,IACA,KAAK,eAAe,eAAe;AACjC,6BAAuB,eAAe;AACtC,aAAO;AAAA,QAAC;AAAA;AAAA,UACN;AAAA,UACC,GAAG,mBAAmB,gBAAgB,aAAa;AAAA;AAAA,MACtD;AAAA,IACF;AAAA,IACA,KAAK,eAAe,mBAAmB;AACrC,6BAAuB,mBAAmB;AAC1C,aAAO;AAAA,QAAC;AAAA;AAAA,UACN;AAAA,UACA;AAAA,UACC,GAAG,mBAAmB,gBAAgB,iBAAiB;AAAA;AAAA,MAC1D;AAAA,IACF;AAAA,IACA,KAAK,eAAe,gBAAgB;AAClC,6BAAuB,gBAAgB;AACvC,aAAO;AAAA,QAAC;AAAA;AAAA,UACN;AAAA,UACA;AAAA,UACC,GAAG,mBAAmB,gBAAgB,cAAc;AAAA;AAAA,MACvD;AAAA,IACF;AAAA,IACA,KAAK,eAAe,iBAAiB;AACnC,aAAO;AAAA,QAAC;AAAA;AAAA,UACN;AAAA,UACC,GAAG,mBAAmB,gBAAgB,eAAe;AAAA;AAAA,MACxD;AAAA,IACF;AAAA,IACA,KAAK,eAAe,OAAO;AACzB,aAAO;AAAA,QAAC;AAAA;AAAA,UACN;AAAA,UACA;AAAA,UACC,GAAG,mBAAmB,gBAAgB,SAAS;AAAA;AAAA,MAClD;AAAA,IACF;AAAA,IACA,KAAK,eAAe,gBAAgB;AAClC,aAAO;AAAA,QAAC;AAAA;AAAA,UACN;AAAA,UACC,GAAG,mBAAmB,gBAAgB,mBAAmB;AAAA;AAAA,MAC5D;AAAA,IACF;AAAA,IACA,SAAS;AACP,UAAI,OAAO,OAAO,cAAc,EAAE,SAAS,IAAW,GAAG;AACvD,cAAM,IAAI,kCAAoB,cAAc,IAAI,6DAA6D,EAAE,eAAe,CAAC;AAAA,MACjI;AACA,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,WAAW,GAAG;AACtD,YAAI,SAAS,IAAI,YAAY,EAAE,WAAW,KAAK,EAAE,GAAG;AAClD,gBAAM,cAAc,GAAG,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,gBAAgB,YAAY,EAAE,SAAS,CAAC;AAChG,iBAAO,EAAE,UAAU,YAAY;AAAA,QACjC;AAAA,MACF;AACA,aAAO,WAAW;AAAA,IACpB;AAAA,EACF;AACF;AAGA,SAAS,kBAAiD,OAGvD;AACD,QAAM,EAAE,MAAM,aAAa,QAAI,sBAAQ,MAAM;AAE3C,UAAM,SAAS,OAAO,SAAS;AAC/B,UAAM,cAAc,IAAI,IAAI,MAAM,IAAI,KAAK,SAAS,OAAO,SAAS,MAAM,EAAE;AAG5E,UAAM,eAAe,MAAM,SAAS,WAAW,WAAW,IACtD,MAAM,SAAS,MAAM,YAAY,MAAM,EAAE,QAAQ,QAAQ,EAAE,IAC3D,MAAM,SAAS,QAAQ,QAAQ,EAAE;AAErC,WAAO;AAAA,MACL,MAAM;AAAA,MACN,cAAc,OAAO,YAAY,IAAI,gBAAgB,MAAM,EAAE,QAAQ,CAAC;AAAA,IACxE;AAAA,EACF,GAAG,CAAC,MAAM,UAAU,MAAM,IAAI,KAAK,OAAO,CAAC;AAE3C,QAAM,uBAAuB,CAAC,SAA4B;AACxD,UAAM,MAAM,MAAM,IAAI,KAAK,IAAI;AAC/B,UAAM,aAAa,MAAM,IAAI,KAAK;AAElC,QAAI,QAAQ,cAAc,IAAI,WAAW,aAAa,GAAG,GAAG;AAC1D;AAAA,IACF;AAEA,UAAM,SAAS,IAAI,IAAI,KAAK,OAAO,SAAS,MAAM;AAClD,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,YAAY,GAAG;AACvD,aAAO,aAAa,IAAI,KAAK,KAAK;AAAA,IACpC;AAEA,WAAO,SAAS,WAAO,6BAAgB,MAAM;AAAA,EAC/C;AAEA,QAAM,SAAS,gBAAgB;AAAA,IAC7B;AAAA,IACA;AAAA,IACA,UAAU,MAAM;AAAA,IAChB,gBAAgB,MAAM;AAAA,IACtB;AAAA,IACA,YAAY,MACV;AAAA,MAAC;AAAA;AAAA,QACC,OAAM;AAAA,QACN,UAAU,MAAM;AAAA,QAChB,mBAAkB;AAAA,QAClB,eAAe,MAAM,MAAM,IAAI,eAAe;AAAA,QAC/C;AAAA;AAAA,IAED;AAAA,IAEF,KAAK,MAAM;AAAA,EACb,CAAC;AAED,MAAI,UAAU,cAAc,QAAQ;AAClC,WAAO,SAAS,OAAO,OAAO;AAC9B,WAAO;AAAA,EACT;AAEA,SACE,4CAAC,2CACE,kBACH;AAEJ;AAGA,IAAO,wBAAQ;AAGf,SAAS,mBAAiC,OAAsD;AAC9F,SAAO,UAAU,SAAY,YAAQ,gCAAgB,KAAK;AAC5D;", "names": []}