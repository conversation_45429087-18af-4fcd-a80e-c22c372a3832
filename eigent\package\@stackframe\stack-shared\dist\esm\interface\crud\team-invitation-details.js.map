{"version": 3, "sources": ["../../../../src/interface/crud/team-invitation-details.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport * as schemaFields from \"../../schema-fields\";\nimport { yupObject } from \"../../schema-fields\";\n\n\nexport const teamInvitationDetailsClientReadSchema = yupObject({\n  team_id: schemaFields.teamIdSchema.defined(),\n  team_display_name: schemaFields.teamDisplayNameSchema.defined(),\n}).defined();\n\nexport const teamInvitationDetailsCrud = createCrud({\n  clientReadSchema: teamInvitationDetailsClientReadSchema,\n  docs: {\n    clientRead: {\n      summary: \"Get the team details with invitation code\",\n      description: \"\",\n      tags: [\"Teams\"],\n    },\n  },\n});\n\nexport type TeamInvitationDetailsCrud = CrudTypeOf<typeof teamInvitationDetailsCrud>;\n"], "mappings": ";AAAA,SAAqB,kBAAkB;AACvC,YAAY,kBAAkB;AAC9B,SAAS,iBAAiB;AAGnB,IAAM,wCAAwC,UAAU;AAAA,EAC7D,SAAsB,0BAAa,QAAQ;AAAA,EAC3C,mBAAgC,mCAAsB,QAAQ;AAChE,CAAC,EAAE,QAAQ;AAEJ,IAAM,4BAA4B,WAAW;AAAA,EAClD,kBAAkB;AAAA,EAClB,MAAM;AAAA,IACJ,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,EACF;AACF,CAAC;", "names": []}