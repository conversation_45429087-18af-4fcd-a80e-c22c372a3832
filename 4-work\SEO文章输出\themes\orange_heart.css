/* 橙心主题 - 英文优化版
 * 主色调: rgb(239, 112, 96)
 */
#nice {
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  color: #333;
  line-height: 1.6;
  word-break: normal;
  word-wrap: break-word;
}

/* 段落样式 */
#nice p {
  margin-top: 10px;
  margin-bottom: 10px;
  line-height: 1.8;
  word-spacing: 1px;
  letter-spacing: 0.5px;
  text-align: left;
  color: #3e3e3e;
  font-size: 16px;
  text-indent: 0; /* 移除首行缩进，适合英文 */
}

/* 一级标题 */
#nice h1 {
  font-size: 2em;
  color: rgb(239, 112, 96);
  padding-bottom: 10px;
  margin: 30px 0 20px 0;
  text-align: center;
  font-weight: bold;
}

/* 二级标题 */
#nice h2 {
  border-bottom: 2px solid rgb(239, 112, 96);
  font-size: 1.5em;
  margin: 30px 0 20px 0;
  padding-bottom: 5px;
}

/* 二级标题内容 */
#nice h2 .content {
  display: inline-block;
  font-weight: bold;
  background: rgb(239, 112, 96);
  color: #ffffff;
  padding: 3px 10px 1px;
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
  margin-right: 3px;
}

/* 二级标题修饰 */
#nice h2:after {
  display: inline-block;
  content: " ";
  vertical-align: bottom;
  border-bottom: 36px solid #efebe9;
  border-right: 20px solid transparent;
}

/* 三级标题 */
#nice h3 {
  font-size: 1.3em;
  margin: 25px 0 15px 0;
  color: rgb(239, 112, 96);
  font-weight: bold;
}

/* 无序列表 */
#nice ul {
  padding-left: 20px;
  margin: 10px 0;
  list-style-type: disc;
}

/* 有序列表 */
#nice ol {
  padding-left: 20px;
  margin: 10px 0;
}

/* 列表内容 */
#nice li section {
  margin: 5px 0;
}

/* 引用 */
#nice blockquote {
  border-left: 4px solid rgb(239, 112, 96);
  background: #fff9f9;
  padding: 10px 15px;
  margin: 15px 0;
  color: #555;
}

/* 引用文字 */
#nice blockquote p {
  margin: 5px 0;
  font-style: italic;
}

/* 链接 */
#nice a {
  color: rgb(239, 112, 96);
  border-bottom: 1px solid rgb(239, 112, 96);
  text-decoration: none;
  transition: color 0.3s;
}

#nice a:hover {
  color: #ff7b6a;
}

/* 加粗 */
#nice strong {
  font-weight: bold;
  color: #222;
}

/* 斜体 */
#nice em {
  font-style: italic;
}

/* 加粗斜体 */
#nice em strong {
  font-weight: bold;
  font-style: italic;
}

/* 删除线 */
#nice del {
  text-decoration: line-through;
  color: #999;
}

/* 分隔线 */
#nice hr {
  border: none;
  border-top: 1px solid rgb(239, 112, 96);
  margin: 20px 0;
}

/* 图片 */
#nice img {
  max-width: 100%;
  margin: 20px auto;
  display: block;
  border-radius: 5px;
}

/* 图片描述文字 */
#nice figcaption {
  text-align: center;
  color: #666;
  font-size: 14px;
  margin-top: 5px;
}

/* 行内代码 */
#nice p code, #nice li code {
  color: rgb(239, 112, 96);
  background-color: #f9f2f2;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 14px;
}

/* 代码块 */
#nice pre {
  background-color: #f8f8f8;
  border-radius: 5px;
  padding: 15px;
  margin: 15px 0;
  overflow-x: auto;
}

#nice pre code {
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

/* 表格 */
#nice table {
  border-collapse: collapse;
  margin: 15px 0;
  width: 100%;
}

#nice table tr th,
#nice table tr td {
  border: 1px solid #ddd;
  padding: 8px 12px;
  text-align: left;
}

#nice table tr th {
  background-color: rgb(239, 112, 96);
  color: white;
}

#nice table tr:nth-child(even) {
  background-color: #f9f9f9;
}

/* 脚注文字 */
#nice .footnote-word {
  color: #ff3502;
}

/* 脚注上标 */
#nice .footnote-ref {
  color: rgb(239, 112, 96);
  font-size: 0.8em;
  vertical-align: super;
}

/* 参考资料分隔线 */
#nice .footnotes-sep {
  border-top: 1px solid #ddd;
  margin: 30px 0 15px 0;
}

#nice .footnotes-sep:before {
  content: "References";
  font-weight: bold;
  color: rgb(239, 112, 96);
  font-size: 1.2em;
  display: block;
  margin-bottom: 10px;
}

/* 参考资料编号 */
#nice .footnote-num {
  color: rgb(239, 112, 96);
  font-weight: bold;
}

/* 参考资料文字 */
#nice .footnote-item p {
  color: #555;
  font-size: 15px;
  line-height: 1.6;
}

/* 参考资料斜体 */
#nice .footnote-item p em {
  font-style: italic;
  color: #666;
}

/* 块级公式 */
#nice .block-equation svg {
  display: block;
  margin: 15px auto;
}

/* 行内公式 */
#nice .inline-equation svg {
  vertical-align: middle;
  display: inline-block;
} 