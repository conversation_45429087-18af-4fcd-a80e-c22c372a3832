{"version": 3, "sources": ["../../src/index.ts"], "sourcesContent": ["export {\n  StackAdminInterface\n} from \"./interface/adminInterface\";\nexport {\n  StackClientInterface\n} from \"./interface/clientInterface\";\nexport {\n  StackServerInterface\n} from \"./interface/serverInterface\";\nexport {\n  KnownError,\n  KnownErrors\n} from \"./known-errors\";\n\n"], "mappings": ";AAAA;AAAA,EACE;AAAA,OACK;AACP;AAAA,EACE;AAAA,OACK;AACP;AAAA,EACE;AAAA,OACK;AACP;AAAA,EACE;AAAA,EACA;AAAA,OACK;", "names": []}