# 🐾 模拟炒股游戏 Docker Compose 配置
# Stock Trading Game Docker Compose Configuration

version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: stock_game_postgres
    environment:
      POSTGRES_DB: stock_game
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - stock_game_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存和消息队列
  redis:
    image: redis:7-alpine
    container_name: stock_game_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - stock_game_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 后端 API 服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: stock_game_backend
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=********************************************/stock_game
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - backend_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - stock_game_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery 异步任务处理器
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: stock_game_celery_worker
    command: celery -A app.core.celery worker --loglevel=info
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=********************************************/stock_game
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    volumes:
      - ./backend:/app
      - celery_logs:/app/logs
    depends_on:
      - postgres
      - redis
      - backend
    networks:
      - stock_game_network
    restart: unless-stopped

  # Celery Beat 定时任务调度器
  celery_beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: stock_game_celery_beat
    command: celery -A app.core.celery beat --loglevel=info
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=********************************************/stock_game
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    volumes:
      - ./backend:/app
      - celery_logs:/app/logs
    depends_on:
      - postgres
      - redis
      - backend
    networks:
      - stock_game_network
    restart: unless-stopped

  # Flower Celery 监控
  flower:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: stock_game_flower
    command: celery -A app.core.celery flower --port=5555
    environment:
      - ENVIRONMENT=production
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    ports:
      - "5555:5555"
    depends_on:
      - redis
      - celery_worker
    networks:
      - stock_game_network
    restart: unless-stopped

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: stock_game_frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - stock_game_network
    restart: unless-stopped

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: stock_game_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./docker/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    networks:
      - stock_game_network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local
  celery_logs:
    driver: local

networks:
  stock_game_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
