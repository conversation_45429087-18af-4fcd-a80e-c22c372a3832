{"version": 3, "sources": ["../src/known-errors.tsx"], "sourcesContent": ["import { StackAssertionError, StatusError, throwErr } from \"./utils/errors\";\nimport { identityArgs } from \"./utils/functions\";\nimport { Json } from \"./utils/json\";\nimport { deindent } from \"./utils/strings\";\n\nexport type KnownErrorJson = {\n  code: string,\n  message: string,\n  details?: Json,\n};\n\nexport type AbstractKnownErrorConstructor<Args extends any[]> =\n  & (abstract new (...args: Args) => KnownError)\n  & {\n    constructorArgsFromJson: (json: KnownErrorJson) => Args,\n  };\n\nexport type KnownErrorConstructor<SuperInstance extends KnownError, Args extends any[]> = {\n  new (...args: Args): SuperInstance & { constructorArgs: Args },\n  errorCode: string,\n  constructorArgsFromJson: (json: KnownErrorJson) => Args,\n  isInstance: (error: unknown) => error is SuperInstance & { constructorArgs: Args },\n};\n\nexport abstract class KnownError extends StatusError {\n  private readonly __stackKnownErrorBrand = \"stack-known-error-brand-sentinel\" as const;\n  public name = \"KnownError\";\n\n  constructor(\n    public readonly statusCode: number,\n    public readonly humanReadableMessage: string,\n    public readonly details?: Json,\n  ) {\n    super(\n      statusCode,\n      humanReadableMessage\n    );\n  }\n\n  public static isKnownError(error: unknown): error is KnownError {\n    // like instanceof, but also works for errors thrown in other realms or by different versions of the same package\n    return typeof error === \"object\" && error !== null && \"__stackKnownErrorBrand\" in error && error.__stackKnownErrorBrand === \"stack-known-error-brand-sentinel\";\n  }\n\n  public override getBody(): Uint8Array {\n    return new TextEncoder().encode(JSON.stringify(this.toDescriptiveJson(), undefined, 2));\n  }\n\n  public override getHeaders(): Record<string, string[]> {\n    return {\n      \"Content-Type\": [\"application/json; charset=utf-8\"],\n      \"X-Stack-Known-Error\": [this.errorCode],\n    };\n  }\n\n  public override toDescriptiveJson(): Json {\n    return {\n      code: this.errorCode,\n      ...this.details ? { details: this.details } : {},\n      error: this.humanReadableMessage,\n    };\n  }\n\n  get errorCode(): string {\n    return (this.constructor as any).errorCode ?? throwErr(`Can't find error code for this KnownError. Is its constructor a KnownErrorConstructor? ${this}`);\n  }\n\n  public static constructorArgsFromJson(json: KnownErrorJson): ConstructorParameters<typeof KnownError> {\n    return [\n      400,\n      json.message,\n      json,\n    ];\n  }\n\n  public static fromJson(json: KnownErrorJson): KnownError {\n    for (const [_, KnownErrorType] of Object.entries(KnownErrors)) {\n      if (json.code === KnownErrorType.prototype.errorCode) {\n        const constructorArgs = KnownErrorType.constructorArgsFromJson(json);\n        return new KnownErrorType(\n          // @ts-expect-error\n          ...constructorArgs,\n        );\n      }\n    }\n\n    throw new Error(`Unknown KnownError code. You may need to update your version of Stack to see more detailed information. ${json.code}: ${json.message}`);\n  }\n}\n\nconst knownErrorConstructorErrorCodeSentinel = Symbol(\"knownErrorConstructorErrorCodeSentinel\");\n/**\n * Exists solely so that known errors are nominative types (ie. two KnownErrors with the same interface are not the same type)\n */\ntype KnownErrorBrand<ErrorCode extends string> = {\n  /**\n   * Does not exist at runtime\n   *\n   * Must be an object because it may be true for multiple error codes (it's true for all parents)\n   */\n  [knownErrorConstructorErrorCodeSentinel]: {\n    [K in ErrorCode]: true\n  },\n};\n\nfunction createKnownErrorConstructor<ErrorCode extends string, Super extends AbstractKnownErrorConstructor<any>, Args extends any[]>(\n  SuperClass: Super,\n  errorCode: ErrorCode,\n  create: ((...args: Args) => Readonly<ConstructorParameters<Super>>),\n  constructorArgsFromJson: ((jsonDetails: any) => Args),\n): KnownErrorConstructor<InstanceType<Super> & KnownErrorBrand<ErrorCode>, Args> & { errorCode: ErrorCode };\nfunction createKnownErrorConstructor<ErrorCode extends string, Super extends AbstractKnownErrorConstructor<any>>(\n  SuperClass: Super,\n  errorCode: ErrorCode,\n  create: \"inherit\",\n  constructorArgsFromJson: \"inherit\",\n): KnownErrorConstructor<InstanceType<Super> & KnownErrorBrand<ErrorCode>, ConstructorParameters<Super>> & { errorCode: ErrorCode };\nfunction createKnownErrorConstructor<ErrorCode extends string, Super extends AbstractKnownErrorConstructor<any>, Args extends any[]>(\n  SuperClass: Super,\n  errorCode: ErrorCode,\n  create: \"inherit\" | ((...args: Args) => Readonly<ConstructorParameters<Super>>),\n  constructorArgsFromJson: \"inherit\" | ((jsonDetails: any) => Args),\n): KnownErrorConstructor<InstanceType<Super> & KnownErrorBrand<ErrorCode>, Args> & { errorCode: ErrorCode } {\n  const createFn = create === \"inherit\" ? identityArgs<Args> as never : create;\n  const constructorArgsFromJsonFn = constructorArgsFromJson === \"inherit\" ? SuperClass.constructorArgsFromJson as never : constructorArgsFromJson;\n\n  // @ts-expect-error this is not a mixin, but TS detects it as one\n  class KnownErrorImpl extends SuperClass {\n    public static readonly errorCode = errorCode;\n    public name = `KnownError<${errorCode}>`;\n    public readonly constructorArgs: Args;\n\n    constructor(...args: Args) {\n      // @ts-expect-error DEVIN, STOP REMOVING THIS LINE YOU DUMBASS, YES TYPESCRIPT DOES THROW AN ERROR IF YOU DON'T HAVE IT, NO THIS IS NOT \"UNUSED\", AND YES YOU ARE BREAKING OUR CI PIPELINE EVERY TIME YOU DO IT\n      super(...createFn(...args));\n      this.constructorArgs = args;\n    }\n\n    static constructorArgsFromJson(json: KnownErrorJson): Args {\n      return constructorArgsFromJsonFn(json.details);\n    }\n\n    static isInstance(error: unknown): error is InstanceType<Super> & { constructorArgs: Args } {\n      if (!KnownError.isKnownError(error)) return false;\n      let current: unknown = error;\n      while (true) {\n        current = Object.getPrototypeOf(current);\n        if (!current) break;\n        if (\"errorCode\" in current.constructor && current.constructor.errorCode === errorCode) return true;\n      }\n      return false;\n    }\n  };\n\n  // @ts-expect-error\n  return KnownErrorImpl;\n}\nundefined?.test(\"KnownError.isInstance\", ({ expect }) => {\n  expect(KnownErrors.InvalidProjectAuthentication.isInstance(undefined)).toBe(false);\n  expect(KnownErrors.InvalidProjectAuthentication.isInstance(new Error())).toBe(false);\n\n  const error = new KnownErrors.ProjectKeyWithoutAccessType();\n  expect(KnownErrors.ProjectKeyWithoutAccessType.isInstance(error)).toBe(true);\n  expect(KnownErrors.InvalidProjectAuthentication.isInstance(error)).toBe(true);\n  expect(KnownErrors.InvalidAccessType.isInstance(error)).toBe(false);\n});\n\nconst UnsupportedError = createKnownErrorConstructor(\n  KnownError,\n  \"UNSUPPORTED_ERROR\",\n  (originalErrorCode: string) => [\n    500,\n    `An error occurred that is not currently supported (possibly because it was added in a version of Stack that is newer than this client). The original unsupported error code was: ${originalErrorCode}`,\n    {\n      originalErrorCode,\n    },\n  ] as const,\n  (json) => [\n    (json as any)?.originalErrorCode ?? throwErr(\"originalErrorCode not found in UnsupportedError details\"),\n  ] as const,\n);\n\nconst BodyParsingError = createKnownErrorConstructor(\n  KnownError,\n  \"BODY_PARSING_ERROR\",\n  (message: string) => [\n    400,\n    message,\n  ] as const,\n  (json) => [json.message] as const,\n);\n\nconst SchemaError = createKnownErrorConstructor(\n  KnownError,\n  \"SCHEMA_ERROR\",\n  (message: string) => [\n    400,\n    message || throwErr(\"SchemaError requires a message\"),\n    {\n      message,\n    },\n  ] as const,\n  (json: any) => [json.message] as const,\n);\n\nconst AllOverloadsFailed = createKnownErrorConstructor(\n  KnownError,\n  \"ALL_OVERLOADS_FAILED\",\n  (overloadErrors: Json[]) => [\n    400,\n    deindent`\n      This endpoint has multiple overloads, but they all failed to process the request.\n\n        ${overloadErrors.map((e, i) => deindent`\n          Overload ${i + 1}: ${JSON.stringify(e, undefined, 2)}\n        `).join(\"\\n\\n\")}\n    `,\n    {\n      overload_errors: overloadErrors,\n    },\n  ] as const,\n  (json) => [\n    (json as any)?.overload_errors ?? throwErr(\"overload_errors not found in AllOverloadsFailed details\"),\n  ] as const,\n);\n\nconst ProjectAuthenticationError = createKnownErrorConstructor(\n  KnownError,\n  \"PROJECT_AUTHENTICATION_ERROR\",\n  \"inherit\",\n  \"inherit\",\n);\n\nconst InvalidProjectAuthentication = createKnownErrorConstructor(\n  ProjectAuthenticationError,\n  \"INVALID_PROJECT_AUTHENTICATION\",\n  \"inherit\",\n  \"inherit\",\n);\n\nconst ProjectKeyWithoutAccessType = createKnownErrorConstructor(\n  InvalidProjectAuthentication,\n  \"PROJECT_KEY_WITHOUT_ACCESS_TYPE\",\n  () => [\n    400,\n    \"Either an API key or an admin access token was provided, but the x-stack-access-type header is missing. Set it to 'client', 'server', or 'admin' as appropriate.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst InvalidAccessType = createKnownErrorConstructor(\n  InvalidProjectAuthentication,\n  \"INVALID_ACCESS_TYPE\",\n  (accessType: string) => [\n    400,\n    `The x-stack-access-type header must be 'client', 'server', or 'admin', but was '${accessType}'.`,\n  ] as const,\n  (json) => [\n    (json as any)?.accessType ?? throwErr(\"accessType not found in InvalidAccessType details\"),\n  ] as const,\n);\n\nconst AccessTypeWithoutProjectId = createKnownErrorConstructor(\n  InvalidProjectAuthentication,\n  \"ACCESS_TYPE_WITHOUT_PROJECT_ID\",\n  (accessType: \"client\" | \"server\" | \"admin\") => [\n    400,\n    deindent`\n      The x-stack-access-type header was '${accessType}', but the x-stack-project-id header was not provided.\n      \n      For more information, see the docs on REST API authentication: https://docs.stack-auth.com/rest-api/overview#authentication\n    `,\n    {\n      request_type: accessType,\n    },\n  ] as const,\n  (json: any) => [json.request_type] as const,\n);\n\nconst AccessTypeRequired = createKnownErrorConstructor(\n  InvalidProjectAuthentication,\n  \"ACCESS_TYPE_REQUIRED\",\n  () => [\n    400,\n    deindent`\n      You must specify an access level for this Stack project. Make sure project API keys are provided (eg. x-stack-publishable-client-key) and you set the x-stack-access-type header to 'client', 'server', or 'admin'.\n      \n      For more information, see the docs on REST API authentication: https://docs.stack-auth.com/rest-api/overview#authentication\n    `,\n  ] as const,\n  () => [] as const,\n);\n\nconst InsufficientAccessType = createKnownErrorConstructor(\n  InvalidProjectAuthentication,\n  \"INSUFFICIENT_ACCESS_TYPE\",\n  (actualAccessType: \"client\" | \"server\" | \"admin\", allowedAccessTypes: (\"client\" | \"server\" | \"admin\")[]) => [\n    401,\n    `The x-stack-access-type header must be ${allowedAccessTypes.map(s => `'${s}'`).join(\" or \")}, but was '${actualAccessType}'.`,\n    {\n      actual_access_type: actualAccessType,\n      allowed_access_types: allowedAccessTypes,\n    },\n  ] as const,\n  (json: any) => [\n    json.actual_access_type,\n    json.allowed_access_types,\n  ] as const,\n);\n\nconst InvalidPublishableClientKey = createKnownErrorConstructor(\n  InvalidProjectAuthentication,\n  \"INVALID_PUBLISHABLE_CLIENT_KEY\",\n  (projectId: string) => [\n    401,\n    `The publishable key is not valid for the project ${JSON.stringify(projectId)}. Does the project and/or the key exist?`,\n    {\n      project_id: projectId,\n    },\n  ] as const,\n  (json: any) => [json.project_id] as const,\n);\n\nconst InvalidSecretServerKey = createKnownErrorConstructor(\n  InvalidProjectAuthentication,\n  \"INVALID_SECRET_SERVER_KEY\",\n  (projectId: string) => [\n    401,\n    `The secret server key is not valid for the project ${JSON.stringify(projectId)}. Does the project and/or the key exist?`,\n    {\n      project_id: projectId,\n    },\n  ] as const,\n  (json: any) => [json.project_id] as const,\n);\n\nconst InvalidSuperSecretAdminKey = createKnownErrorConstructor(\n  InvalidProjectAuthentication,\n  \"INVALID_SUPER_SECRET_ADMIN_KEY\",\n  (projectId: string) => [\n    401,\n    `The super secret admin key is not valid for the project ${JSON.stringify(projectId)}. Does the project and/or the key exist?`,\n    {\n      project_id: projectId,\n    },\n  ] as const,\n  (json: any) => [json.project_id] as const,\n);\n\nconst InvalidAdminAccessToken = createKnownErrorConstructor(\n  InvalidProjectAuthentication,\n  \"INVALID_ADMIN_ACCESS_TOKEN\",\n  \"inherit\",\n  \"inherit\",\n);\n\nconst UnparsableAdminAccessToken = createKnownErrorConstructor(\n  InvalidAdminAccessToken,\n  \"UNPARSABLE_ADMIN_ACCESS_TOKEN\",\n  () => [\n    401,\n    \"Admin access token is not parsable.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst AdminAccessTokenExpired = createKnownErrorConstructor(\n  InvalidAdminAccessToken,\n  \"ADMIN_ACCESS_TOKEN_EXPIRED\",\n  (expiredAt: Date | undefined) => [\n    401,\n    `Admin access token has expired. Please refresh it and try again.${expiredAt ? ` (The access token expired at ${expiredAt.toISOString()}.)`: \"\"}`,\n    { expired_at_millis: expiredAt?.getTime() ?? null },\n  ] as const,\n  (json: any) => [json.expired_at_millis ?? undefined] as const,\n);\n\nconst InvalidProjectForAdminAccessToken = createKnownErrorConstructor(\n  InvalidAdminAccessToken,\n  \"INVALID_PROJECT_FOR_ADMIN_ACCESS_TOKEN\",\n  () => [\n    401,\n    \"Admin access tokens must be created on the internal project.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst AdminAccessTokenIsNotAdmin = createKnownErrorConstructor(\n  InvalidAdminAccessToken,\n  \"ADMIN_ACCESS_TOKEN_IS_NOT_ADMIN\",\n  () => [\n    401,\n    \"Admin access token does not have the required permissions to access this project.\",\n  ] as const,\n  () => [] as const,\n);\n\n/**\n * @deprecated Use InsufficientAccessType instead\n */\nconst ProjectAuthenticationRequired = createKnownErrorConstructor(\n  ProjectAuthenticationError,\n  \"PROJECT_AUTHENTICATION_REQUIRED\",\n  \"inherit\",\n  \"inherit\",\n);\n\n\n/**\n * @deprecated Use InsufficientAccessType instead\n */\nconst ClientAuthenticationRequired = createKnownErrorConstructor(\n  ProjectAuthenticationRequired,\n  \"CLIENT_AUTHENTICATION_REQUIRED\",\n  () => [\n    401,\n    \"The publishable client key must be provided.\",\n  ] as const,\n  () => [] as const,\n);\n\n/**\n * @deprecated Use InsufficientAccessType instead\n */\nconst ServerAuthenticationRequired = createKnownErrorConstructor(\n  ProjectAuthenticationRequired,\n  \"SERVER_AUTHENTICATION_REQUIRED\",\n  () => [\n    401,\n    \"The secret server key must be provided.\",\n  ] as const,\n  () => [] as const,\n);\n\n/**\n * @deprecated Use InsufficientAccessType instead\n */\nconst ClientOrServerAuthenticationRequired = createKnownErrorConstructor(\n  ProjectAuthenticationRequired,\n  \"CLIENT_OR_SERVER_AUTHENTICATION_REQUIRED\",\n  () => [\n    401,\n    \"Either the publishable client key or the secret server key must be provided.\",\n  ] as const,\n  () => [] as const,\n);\n\n/**\n * @deprecated Use InsufficientAccessType instead\n */\nconst ClientOrAdminAuthenticationRequired = createKnownErrorConstructor(\n  ProjectAuthenticationRequired,\n  \"CLIENT_OR_ADMIN_AUTHENTICATION_REQUIRED\",\n  () => [\n    401,\n    \"Either the publishable client key or the super secret admin key must be provided.\",\n  ] as const,\n  () => [] as const,\n);\n\n/**\n * @deprecated Use InsufficientAccessType instead\n */\nconst ClientOrServerOrAdminAuthenticationRequired = createKnownErrorConstructor(\n  ProjectAuthenticationRequired,\n  \"CLIENT_OR_SERVER_OR_ADMIN_AUTHENTICATION_REQUIRED\",\n  () => [\n    401,\n    \"Either the publishable client key, the secret server key, or the super secret admin key must be provided.\",\n  ] as const,\n  () => [] as const,\n);\n\n/**\n * @deprecated Use InsufficientAccessType instead\n */\nconst AdminAuthenticationRequired = createKnownErrorConstructor(\n  ProjectAuthenticationRequired,\n  \"ADMIN_AUTHENTICATION_REQUIRED\",\n  () => [\n    401,\n    \"The super secret admin key must be provided.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst ExpectedInternalProject = createKnownErrorConstructor(\n  ProjectAuthenticationError,\n  \"EXPECTED_INTERNAL_PROJECT\",\n  () => [\n    401,\n    \"The project ID is expected to be internal.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst SessionAuthenticationError = createKnownErrorConstructor(\n  KnownError,\n  \"SESSION_AUTHENTICATION_ERROR\",\n  \"inherit\",\n  \"inherit\",\n);\n\nconst InvalidSessionAuthentication = createKnownErrorConstructor(\n  SessionAuthenticationError,\n  \"INVALID_SESSION_AUTHENTICATION\",\n  \"inherit\",\n  \"inherit\",\n);\n\nconst InvalidAccessToken = createKnownErrorConstructor(\n  InvalidSessionAuthentication,\n  \"INVALID_ACCESS_TOKEN\",\n  \"inherit\",\n  \"inherit\",\n);\n\nconst UnparsableAccessToken = createKnownErrorConstructor(\n  InvalidAccessToken,\n  \"UNPARSABLE_ACCESS_TOKEN\",\n  () => [\n    401,\n    \"Access token is not parsable.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst AccessTokenExpired = createKnownErrorConstructor(\n  InvalidAccessToken,\n  \"ACCESS_TOKEN_EXPIRED\",\n  (expiredAt: Date | undefined) => [\n    401,\n    `Access token has expired. Please refresh it and try again.${expiredAt ? ` (The access token expired at ${expiredAt.toISOString()}.)`: \"\"}`,\n    { expired_at_millis: expiredAt?.getTime() ?? null },\n  ] as const,\n  (json: any) => [json.expired_at_millis ? new Date(json.expired_at_millis) : undefined] as const,\n);\n\nconst InvalidProjectForAccessToken = createKnownErrorConstructor(\n  InvalidAccessToken,\n  \"INVALID_PROJECT_FOR_ACCESS_TOKEN\",\n  (expectedProjectId: string, actualProjectId: string) => [\n    401,\n    `Access token not valid for this project. Expected project ID ${JSON.stringify(expectedProjectId)}, but the token is for project ID ${JSON.stringify(actualProjectId)}.`,\n    {\n      expected_project_id: expectedProjectId,\n      actual_project_id: actualProjectId,\n    },\n  ] as const,\n  (json: any) => [json.expected_project_id, json.actual_project_id] as const,\n);\n\n\nconst RefreshTokenError = createKnownErrorConstructor(\n  KnownError,\n  \"REFRESH_TOKEN_ERROR\",\n  \"inherit\",\n  \"inherit\",\n);\n\nconst RefreshTokenNotFoundOrExpired = createKnownErrorConstructor(\n  RefreshTokenError,\n  \"REFRESH_TOKEN_NOT_FOUND_OR_EXPIRED\",\n  () => [\n    401,\n    \"Refresh token not found for this project, or the session has expired/been revoked.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst CannotDeleteCurrentSession = createKnownErrorConstructor(\n  RefreshTokenError,\n  \"CANNOT_DELETE_CURRENT_SESSION\",\n  () => [\n    400,\n    \"Cannot delete the current session.\",\n  ] as const,\n  () => [] as const,\n);\n\n\nconst ProviderRejected = createKnownErrorConstructor(\n  RefreshTokenError,\n  \"PROVIDER_REJECTED\",\n  () => [\n    401,\n    \"The provider refused to refresh their token. This usually means that the provider used to authenticate the user no longer regards this session as valid, and the user must re-authenticate.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst UserWithEmailAlreadyExists = createKnownErrorConstructor(\n  KnownError,\n  \"USER_EMAIL_ALREADY_EXISTS\",\n  (email: string) => [\n    409,\n    `A user with email ${JSON.stringify(email)} already exists.`,\n    {\n      email,\n    },\n  ] as const,\n  (json: any) => [json.email] as const,\n);\n\nconst EmailNotVerified = createKnownErrorConstructor(\n  KnownError,\n  \"EMAIL_NOT_VERIFIED\",\n  () => [\n    400,\n    \"The email is not verified.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst CannotGetOwnUserWithoutUser = createKnownErrorConstructor(\n  KnownError,\n  \"CANNOT_GET_OWN_USER_WITHOUT_USER\",\n  () => [\n    400,\n    \"You have specified 'me' as a userId, but did not provide authentication for a user.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst UserIdDoesNotExist = createKnownErrorConstructor(\n  KnownError,\n  \"USER_ID_DOES_NOT_EXIST\",\n  (userId: string) => [\n    400,\n    `The given user with the ID ${userId} does not exist.`,\n    {\n      user_id: userId,\n    },\n  ] as const,\n  (json: any) => [json.user_id] as const,\n);\n\nconst UserNotFound = createKnownErrorConstructor(\n  KnownError,\n  \"USER_NOT_FOUND\",\n  () => [\n    404,\n    \"User not found.\",\n  ] as const,\n  () => [] as const,\n);\n\n\nconst ProjectNotFound = createKnownErrorConstructor(\n  KnownError,\n  \"PROJECT_NOT_FOUND\",\n  (projectId: string) => {\n    if (typeof projectId !== \"string\") throw new StackAssertionError(\"projectId of KnownErrors.ProjectNotFound must be a string\");\n    return [\n      404,\n      `Project ${projectId} not found or is not accessible with the current user.`,\n      {\n        project_id: projectId,\n      },\n    ] as const;\n  },\n  (json: any) => [json.project_id] as const,\n);\n\nconst BranchDoesNotExist = createKnownErrorConstructor(\n  KnownError,\n  \"BRANCH_DOES_NOT_EXIST\",\n  (branchId: string) => [\n    400,\n    `The branch with ID ${branchId} does not exist.`,\n    {\n      branch_id: branchId,\n    },\n  ] as const,\n  (json: any) => [json.branch_id] as const,\n);\n\n\nconst SignUpNotEnabled = createKnownErrorConstructor(\n  KnownError,\n  \"SIGN_UP_NOT_ENABLED\",\n  () => [\n    400,\n    \"Creation of new accounts is not enabled for this project. Please ask the project owner to enable it.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst PasswordAuthenticationNotEnabled = createKnownErrorConstructor(\n  KnownError,\n  \"PASSWORD_AUTHENTICATION_NOT_ENABLED\",\n  () => [\n    400,\n    \"Password authentication is not enabled for this project.\",\n  ] as const,\n  () => [] as const,\n);\n\n\nconst PasskeyAuthenticationNotEnabled = createKnownErrorConstructor(\n  KnownError,\n  \"PASSKEY_AUTHENTICATION_NOT_ENABLED\",\n  () => [\n    400,\n    \"Passkey authentication is not enabled for this project.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst AnonymousAccountsNotEnabled = createKnownErrorConstructor(\n  KnownError,\n  \"ANONYMOUS_ACCOUNTS_NOT_ENABLED\",\n  () => [\n    400,\n    \"Anonymous accounts are not enabled for this project.\",\n  ] as const,\n  () => [] as const,\n);\n\n\nconst EmailPasswordMismatch = createKnownErrorConstructor(\n  KnownError,\n  \"EMAIL_PASSWORD_MISMATCH\",\n  () => [\n    400,\n    \"Wrong e-mail or password.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst RedirectUrlNotWhitelisted = createKnownErrorConstructor(\n  KnownError,\n  \"REDIRECT_URL_NOT_WHITELISTED\",\n  () => [\n    400,\n    \"Redirect URL not whitelisted. Did you forget to add this domain to the trusted domains list on the Stack Auth dashboard?\",\n  ] as const,\n  () => [] as const,\n);\n\nconst PasswordRequirementsNotMet = createKnownErrorConstructor(\n  KnownError,\n  \"PASSWORD_REQUIREMENTS_NOT_MET\",\n  \"inherit\",\n  \"inherit\",\n);\n\nconst PasswordTooShort = createKnownErrorConstructor(\n  PasswordRequirementsNotMet,\n  \"PASSWORD_TOO_SHORT\",\n  (minLength: number) => [\n    400,\n    `Password too short. Minimum length is ${minLength}.`,\n    {\n      min_length: minLength,\n    },\n  ] as const,\n  (json) => [\n    (json as any)?.min_length ?? throwErr(\"min_length not found in PasswordTooShort details\"),\n  ] as const,\n);\n\nconst PasswordTooLong = createKnownErrorConstructor(\n  PasswordRequirementsNotMet,\n  \"PASSWORD_TOO_LONG\",\n  (maxLength: number) => [\n    400,\n    `Password too long. Maximum length is ${maxLength}.`,\n    {\n      maxLength,\n    },\n  ] as const,\n  (json) => [\n    (json as any)?.maxLength ?? throwErr(\"maxLength not found in PasswordTooLong details\"),\n  ] as const,\n);\n\nconst UserDoesNotHavePassword = createKnownErrorConstructor(\n  KnownError,\n  \"USER_DOES_NOT_HAVE_PASSWORD\",\n  () => [\n    400,\n    \"This user does not have password authentication enabled.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst VerificationCodeError = createKnownErrorConstructor(\n  KnownError,\n  \"VERIFICATION_ERROR\",\n  \"inherit\",\n  \"inherit\",\n);\n\nconst VerificationCodeNotFound = createKnownErrorConstructor(\n  VerificationCodeError,\n  \"VERIFICATION_CODE_NOT_FOUND\",\n  () => [\n    404,\n    \"The verification code does not exist for this project.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst VerificationCodeExpired = createKnownErrorConstructor(\n  VerificationCodeError,\n  \"VERIFICATION_CODE_EXPIRED\",\n  () => [\n    400,\n    \"The verification code has expired.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst VerificationCodeAlreadyUsed = createKnownErrorConstructor(\n  VerificationCodeError,\n  \"VERIFICATION_CODE_ALREADY_USED\",\n  () => [\n    409,\n    \"The verification link has already been used.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst VerificationCodeMaxAttemptsReached = createKnownErrorConstructor(\n  VerificationCodeError,\n  \"VERIFICATION_CODE_MAX_ATTEMPTS_REACHED\",\n  () => [\n    400,\n    \"The verification code nonce has reached the maximum number of attempts. This code is not valid anymore.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst PasswordConfirmationMismatch = createKnownErrorConstructor(\n  KnownError,\n  \"PASSWORD_CONFIRMATION_MISMATCH\",\n  () => [\n    400,\n    \"Passwords do not match.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst EmailAlreadyVerified = createKnownErrorConstructor(\n  KnownError,\n  \"EMAIL_ALREADY_VERIFIED\",\n  () => [\n    409,\n    \"The e-mail is already verified.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst EmailNotAssociatedWithUser = createKnownErrorConstructor(\n  KnownError,\n  \"EMAIL_NOT_ASSOCIATED_WITH_USER\",\n  () => [\n    400,\n    \"The e-mail is not associated with a user that could log in with that e-mail.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst EmailIsNotPrimaryEmail = createKnownErrorConstructor(\n  KnownError,\n  \"EMAIL_IS_NOT_PRIMARY_EMAIL\",\n  (email: string, primaryEmail: string | null) => [\n    400,\n    `The given e-mail (${email}) must equal the user's primary e-mail (${primaryEmail}).`,\n    {\n      email,\n      primary_email: primaryEmail,\n    },\n  ] as const,\n  (json: any) => [json.email, json.primary_email] as const,\n);\n\n\nconst PasskeyRegistrationFailed = createKnownErrorConstructor(\n  KnownError,\n  \"PASSKEY_REGISTRATION_FAILED\",\n  (message: string) => [\n    400,\n    message,\n  ] as const,\n  (json: any) => [json.message] as const,\n);\n\n\nconst PasskeyWebAuthnError = createKnownErrorConstructor(\n  KnownError,\n  \"PASSKEY_WEBAUTHN_ERROR\",\n  (message: string, code: string) => [\n    400,\n    message,\n    {\n      message,\n      code,\n    },\n  ] as const,\n  (json: any) => [json.message, json.code] as const,\n);\n\nconst PasskeyAuthenticationFailed = createKnownErrorConstructor(\n  KnownError,\n  \"PASSKEY_AUTHENTICATION_FAILED\",\n  (message: string) => [\n    400,\n    message,\n  ] as const,\n  (json: any) => [json.message] as const,\n);\n\n\nconst PermissionNotFound = createKnownErrorConstructor(\n  KnownError,\n  \"PERMISSION_NOT_FOUND\",\n  (permissionId: string) => [\n    404,\n    `Permission \"${permissionId}\" not found. Make sure you created it on the dashboard.`,\n    {\n      permission_id: permissionId,\n    },\n  ] as const,\n  (json: any) => [json.permission_id] as const,\n);\n\nconst PermissionScopeMismatch = createKnownErrorConstructor(\n  KnownError,\n  \"WRONG_PERMISSION_SCOPE\",\n  (permissionId: string, expectedScope: \"team\" | \"project\", actualScope: \"team\" | \"project\" | null) => [\n    404,\n    `Permission ${JSON.stringify(permissionId)} not found. (It was found for a different scope ${JSON.stringify(actualScope)}, but scope ${JSON.stringify(expectedScope)} was expected.)`,\n    {\n      permission_id: permissionId,\n      expected_scope: expectedScope,\n      actual_scope: actualScope,\n    },\n  ] as const,\n  (json: any) => [json.permission_id, json.expected_scope, json.actual_scope] as const,\n);\n\nconst ContainedPermissionNotFound = createKnownErrorConstructor(\n  KnownError,\n  \"CONTAINED_PERMISSION_NOT_FOUND\",\n  (permissionId: string) => [\n    400,\n    `Contained permission with ID \"${permissionId}\" not found. Make sure you created it on the dashboard.`,\n    {\n      permission_id: permissionId,\n    },\n  ] as const,\n  (json: any) => [json.permission_id] as const,\n);\n\nconst TeamNotFound = createKnownErrorConstructor(\n  KnownError,\n  \"TEAM_NOT_FOUND\",\n  (teamId: string) => [\n    404,\n    `Team ${teamId} not found.`,\n    {\n      team_id: teamId,\n    },\n  ] as const,\n  (json: any) => [json.team_id] as const,\n);\n\nconst TeamAlreadyExists = createKnownErrorConstructor(\n  KnownError,\n  \"TEAM_ALREADY_EXISTS\",\n  (teamId: string) => [\n    409,\n    `Team ${teamId} already exists.`,\n    {\n      team_id: teamId,\n    },\n  ] as const,\n  (json: any) => [json.team_id] as const,\n);\n\nconst TeamMembershipNotFound = createKnownErrorConstructor(\n  KnownError,\n  \"TEAM_MEMBERSHIP_NOT_FOUND\",\n  (teamId: string, userId: string) => [\n    404,\n    `User ${userId} is not found in team ${teamId}.`,\n    {\n      team_id: teamId,\n      user_id: userId,\n    },\n  ] as const,\n  (json: any) => [json.team_id, json.user_id] as const,\n);\n\n\nconst EmailTemplateAlreadyExists = createKnownErrorConstructor(\n  KnownError,\n  \"EMAIL_TEMPLATE_ALREADY_EXISTS\",\n  () => [\n    409,\n    \"Email template already exists.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst OAuthConnectionNotConnectedToUser = createKnownErrorConstructor(\n  KnownError,\n  \"OAUTH_CONNECTION_NOT_CONNECTED_TO_USER\",\n  () => [\n    400,\n    \"The OAuth connection is not connected to any user.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst OAuthConnectionAlreadyConnectedToAnotherUser = createKnownErrorConstructor(\n  KnownError,\n  \"OAUTH_CONNECTION_ALREADY_CONNECTED_TO_ANOTHER_USER\",\n  () => [\n    409,\n    \"The OAuth connection is already connected to another user.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst OAuthConnectionDoesNotHaveRequiredScope = createKnownErrorConstructor(\n  KnownError,\n  \"OAUTH_CONNECTION_DOES_NOT_HAVE_REQUIRED_SCOPE\",\n  () => [\n    400,\n    \"The OAuth connection does not have the required scope.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst OAuthExtraScopeNotAvailableWithSharedOAuthKeys = createKnownErrorConstructor(\n  KnownError,\n  \"OAUTH_EXTRA_SCOPE_NOT_AVAILABLE_WITH_SHARED_OAUTH_KEYS\",\n  () => [\n    400,\n    \"Extra scopes are not available with shared OAuth keys. Please add your own OAuth keys on the Stack dashboard to use extra scopes.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst OAuthAccessTokenNotAvailableWithSharedOAuthKeys = createKnownErrorConstructor(\n  KnownError,\n  \"OAUTH_ACCESS_TOKEN_NOT_AVAILABLE_WITH_SHARED_OAUTH_KEYS\",\n  () => [\n    400,\n    \"Access tokens are not available with shared OAuth keys. Please add your own OAuth keys on the Stack dashboard to use access tokens.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst InvalidOAuthClientIdOrSecret = createKnownErrorConstructor(\n  KnownError,\n  \"INVALID_OAUTH_CLIENT_ID_OR_SECRET\",\n  (clientId?: string) => [\n    400,\n    \"The OAuth client ID or secret is invalid. The client ID must be equal to the project ID (potentially with a hash and a branch ID), and the client secret must be a publishable client key.\",\n    {\n      client_id: clientId ?? null,\n    },\n  ] as const,\n  (json: any) => [json.client_id ?? undefined] as const,\n);\n\nconst InvalidScope = createKnownErrorConstructor(\n  KnownError,\n  \"INVALID_SCOPE\",\n  (scope: string) => [\n    400,\n    `The scope \"${scope}\" is not a valid OAuth scope for Stack.`,\n  ] as const,\n  (json: any) => [json.scope] as const,\n);\n\nconst UserAlreadyConnectedToAnotherOAuthConnection = createKnownErrorConstructor(\n  KnownError,\n  \"USER_ALREADY_CONNECTED_TO_ANOTHER_OAUTH_CONNECTION\",\n  () => [\n    409,\n    \"The user is already connected to another OAuth account. Did you maybe selected the wrong account?\",\n  ] as const,\n  () => [] as const,\n);\n\nconst OuterOAuthTimeout = createKnownErrorConstructor(\n  KnownError,\n  \"OUTER_OAUTH_TIMEOUT\",\n  () => [\n    408,\n    \"The OAuth flow has timed out. Please sign in again.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst OAuthProviderNotFoundOrNotEnabled = createKnownErrorConstructor(\n  KnownError,\n  \"OAUTH_PROVIDER_NOT_FOUND_OR_NOT_ENABLED\",\n  () => [\n    400,\n    \"The OAuth provider is not found or not enabled.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst MultiFactorAuthenticationRequired = createKnownErrorConstructor(\n  KnownError,\n  \"MULTI_FACTOR_AUTHENTICATION_REQUIRED\",\n  (attemptCode: string) => [\n    400,\n    `Multi-factor authentication is required for this user.`,\n    {\n      attempt_code: attemptCode,\n    },\n  ] as const,\n  (json) => [json.attempt_code] as const,\n);\n\nconst InvalidTotpCode = createKnownErrorConstructor(\n  KnownError,\n  \"INVALID_TOTP_CODE\",\n  () => [\n    400,\n    \"The TOTP code is invalid. Please try again.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst UserAuthenticationRequired = createKnownErrorConstructor(\n  KnownError,\n  \"USER_AUTHENTICATION_REQUIRED\",\n  () => [\n    401,\n    \"User authentication required for this endpoint.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst TeamMembershipAlreadyExists = createKnownErrorConstructor(\n  KnownError,\n  \"TEAM_MEMBERSHIP_ALREADY_EXISTS\",\n  () => [\n    409,\n    \"Team membership already exists.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst ProjectPermissionRequired = createKnownErrorConstructor(\n  KnownError,\n  \"PROJECT_PERMISSION_REQUIRED\",\n  (userId, permissionId) => [\n    401,\n    `User ${userId} does not have permission ${permissionId}.`,\n    {\n      user_id: userId,\n      permission_id: permissionId,\n    },\n  ] as const,\n  (json) => [json.user_id, json.permission_id] as const,\n);\n\nconst TeamPermissionRequired = createKnownErrorConstructor(\n  KnownError,\n  \"TEAM_PERMISSION_REQUIRED\",\n  (teamId, userId, permissionId) => [\n    401,\n    `User ${userId} does not have permission ${permissionId} in team ${teamId}.`,\n    {\n      team_id: teamId,\n      user_id: userId,\n      permission_id: permissionId,\n    },\n  ] as const,\n  (json) => [json.team_id, json.user_id, json.permission_id] as const,\n);\n\nconst TeamPermissionNotFound = createKnownErrorConstructor(\n  KnownError,\n  \"TEAM_PERMISSION_NOT_FOUND\",\n  (teamId, userId, permissionId) => [\n    401,\n    `User ${userId} does not have permission ${permissionId} in team ${teamId}.`,\n    {\n      team_id: teamId,\n      user_id: userId,\n      permission_id: permissionId,\n    },\n  ] as const,\n  (json) => [json.team_id, json.user_id, json.permission_id] as const,\n);\n\nconst InvalidSharedOAuthProviderId = createKnownErrorConstructor(\n  KnownError,\n  \"INVALID_SHARED_OAUTH_PROVIDER_ID\",\n  (providerId) => [\n    400,\n    `The shared OAuth provider with ID ${providerId} is not valid.`,\n    {\n      provider_id: providerId,\n    },\n  ] as const,\n  (json) => [json.provider_id] as const,\n);\n\nconst InvalidStandardOAuthProviderId = createKnownErrorConstructor(\n  KnownError,\n  \"INVALID_STANDARD_OAUTH_PROVIDER_ID\",\n  (providerId) => [\n    400,\n    `The standard OAuth provider with ID ${providerId} is not valid.`,\n    {\n      provider_id: providerId,\n    },\n  ] as const,\n  (json) => [json.provider_id] as const,\n);\n\nconst InvalidAuthorizationCode = createKnownErrorConstructor(\n  KnownError,\n  \"INVALID_AUTHORIZATION_CODE\",\n  () => [\n    400,\n    \"The given authorization code is invalid.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst OAuthProviderAccessDenied = createKnownErrorConstructor(\n  KnownError,\n  \"OAUTH_PROVIDER_ACCESS_DENIED\",\n  () => [\n    400,\n    \"The OAuth provider denied access to the user.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst ContactChannelAlreadyUsedForAuthBySomeoneElse = createKnownErrorConstructor(\n  KnownError,\n  \"CONTACT_CHANNEL_ALREADY_USED_FOR_AUTH_BY_SOMEONE_ELSE\",\n  (type: \"email\", contactChannelValue?: string) => [\n    409,\n    contactChannelValue ?\n    `The ${type} (${contactChannelValue}) is already used for authentication by another account.` :\n    `This ${type} is already used for authentication by another account.`,\n    { type, contact_channel_value: contactChannelValue ?? null },\n  ] as const,\n  (json) => [json.type, json.contact_channel_value] as const,\n);\n\nconst InvalidPollingCodeError = createKnownErrorConstructor(\n  KnownError,\n  \"INVALID_POLLING_CODE\",\n  (details?: Json) => [\n    400,\n    \"The polling code is invalid or does not exist.\",\n    details,\n  ] as const,\n  (json: any) => [json] as const,\n);\n\nconst CliAuthError = createKnownErrorConstructor(\n  KnownError,\n  \"CLI_AUTH_ERROR\",\n  (message: string) => [\n    400,\n    message,\n  ] as const,\n  (json: any) => [json.message] as const,\n);\n\nconst CliAuthExpiredError = createKnownErrorConstructor(\n  KnownError,\n  \"CLI_AUTH_EXPIRED_ERROR\",\n  (message: string = \"CLI authentication request expired. Please try again.\") => [\n    400,\n    message,\n  ] as const,\n  (json: any) => [json.message] as const,\n);\n\nconst CliAuthUsedError = createKnownErrorConstructor(\n  KnownError,\n  \"CLI_AUTH_USED_ERROR\",\n  (message: string = \"This authentication token has already been used.\") => [\n    400,\n    message,\n  ] as const,\n  (json: any) => [json.message] as const,\n);\n\n\nconst ApiKeyNotValid = createKnownErrorConstructor(\n  KnownError,\n  \"API_KEY_NOT_VALID\",\n  \"inherit\",\n  \"inherit\",\n);\n\nconst ApiKeyExpired = createKnownErrorConstructor(\n  ApiKeyNotValid,\n  \"API_KEY_EXPIRED\",\n  () => [\n    401,\n    \"API key has expired.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst ApiKeyRevoked = createKnownErrorConstructor(\n  ApiKeyNotValid,\n  \"API_KEY_REVOKED\",\n  () => [\n    401,\n    \"API key has been revoked.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst WrongApiKeyType = createKnownErrorConstructor(\n  ApiKeyNotValid,\n  \"WRONG_API_KEY_TYPE\",\n  (expectedType: string, actualType: string) => [\n    400,\n    `This endpoint is for ${expectedType} API keys, but a ${actualType} API key was provided.`,\n    { expected_type: expectedType, actual_type: actualType },\n  ] as const,\n  (json) => [json.expected_type, json.actual_type] as const,\n);\n\nconst ApiKeyNotFound = createKnownErrorConstructor(\n  ApiKeyNotValid,\n  \"API_KEY_NOT_FOUND\",\n  () => [\n    404,\n    \"API key not found.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst PublicApiKeyCannotBeRevoked = createKnownErrorConstructor(\n  ApiKeyNotValid,\n  \"PUBLIC_API_KEY_CANNOT_BE_REVOKED\",\n  () => [\n    400,\n    \"Public API keys cannot be revoked by the secretscanner endpoint.\",\n  ] as const,\n  () => [] as const,\n);\n\nconst PermissionIdAlreadyExists = createKnownErrorConstructor(\n  KnownError,\n  \"PERMISSION_ID_ALREADY_EXISTS\",\n  (permissionId: string) => [\n    400,\n    `Permission with ID \"${permissionId}\" already exists. Choose a different ID.`,\n    {\n      permission_id: permissionId,\n    },\n  ] as const,\n  (json: any) => [json.permission_id] as const,\n);\n\nexport type KnownErrors = {\n  [K in keyof typeof KnownErrors]: InstanceType<typeof KnownErrors[K]>;\n};\n\nexport const KnownErrors = {\n  CannotDeleteCurrentSession,\n  UnsupportedError,\n  BodyParsingError,\n  SchemaError,\n  AllOverloadsFailed,\n  ProjectAuthenticationError,\n  PermissionIdAlreadyExists,\n  CliAuthError,\n  CliAuthExpiredError,\n  CliAuthUsedError,\n  InvalidProjectAuthentication,\n  ProjectKeyWithoutAccessType,\n  InvalidAccessType,\n  AccessTypeWithoutProjectId,\n  AccessTypeRequired,\n  CannotGetOwnUserWithoutUser,\n  InsufficientAccessType,\n  InvalidPublishableClientKey,\n  InvalidSecretServerKey,\n  InvalidSuperSecretAdminKey,\n  InvalidAdminAccessToken,\n  UnparsableAdminAccessToken,\n  AdminAccessTokenExpired,\n  InvalidProjectForAdminAccessToken,\n  AdminAccessTokenIsNotAdmin,\n  ProjectAuthenticationRequired,\n  ClientAuthenticationRequired,\n  ServerAuthenticationRequired,\n  ClientOrServerAuthenticationRequired,\n  ClientOrAdminAuthenticationRequired,\n  ClientOrServerOrAdminAuthenticationRequired,\n  AdminAuthenticationRequired,\n  ExpectedInternalProject,\n  SessionAuthenticationError,\n  InvalidSessionAuthentication,\n  InvalidAccessToken,\n  UnparsableAccessToken,\n  AccessTokenExpired,\n  InvalidProjectForAccessToken,\n  RefreshTokenError,\n  ProviderRejected,\n  RefreshTokenNotFoundOrExpired,\n  UserWithEmailAlreadyExists,\n  EmailNotVerified,\n  UserIdDoesNotExist,\n  UserNotFound,\n  ApiKeyNotFound,\n  PublicApiKeyCannotBeRevoked,\n  ProjectNotFound,\n  BranchDoesNotExist,\n  SignUpNotEnabled,\n  PasswordAuthenticationNotEnabled,\n  PasskeyAuthenticationNotEnabled,\n  AnonymousAccountsNotEnabled,\n  EmailPasswordMismatch,\n  RedirectUrlNotWhitelisted,\n  PasswordRequirementsNotMet,\n  PasswordTooShort,\n  PasswordTooLong,\n  UserDoesNotHavePassword,\n  VerificationCodeError,\n  VerificationCodeNotFound,\n  VerificationCodeExpired,\n  VerificationCodeAlreadyUsed,\n  VerificationCodeMaxAttemptsReached,\n  PasswordConfirmationMismatch,\n  EmailAlreadyVerified,\n  EmailNotAssociatedWithUser,\n  EmailIsNotPrimaryEmail,\n  PasskeyRegistrationFailed,\n  PasskeyWebAuthnError,\n  PasskeyAuthenticationFailed,\n  PermissionNotFound,\n  PermissionScopeMismatch,\n  ContainedPermissionNotFound,\n  TeamNotFound,\n  TeamMembershipNotFound,\n  EmailTemplateAlreadyExists,\n  OAuthConnectionNotConnectedToUser,\n  OAuthConnectionAlreadyConnectedToAnotherUser,\n  OAuthConnectionDoesNotHaveRequiredScope,\n  OAuthExtraScopeNotAvailableWithSharedOAuthKeys,\n  OAuthAccessTokenNotAvailableWithSharedOAuthKeys,\n  InvalidOAuthClientIdOrSecret,\n  InvalidScope,\n  UserAlreadyConnectedToAnotherOAuthConnection,\n  OuterOAuthTimeout,\n  OAuthProviderNotFoundOrNotEnabled,\n  MultiFactorAuthenticationRequired,\n  InvalidTotpCode,\n  UserAuthenticationRequired,\n  TeamMembershipAlreadyExists,\n  ProjectPermissionRequired,\n  TeamPermissionRequired,\n  InvalidSharedOAuthProviderId,\n  InvalidStandardOAuthProviderId,\n  InvalidAuthorizationCode,\n  TeamPermissionNotFound,\n  OAuthProviderAccessDenied,\n  ContactChannelAlreadyUsedForAuthBySomeoneElse,\n  InvalidPollingCodeError,\n  ApiKeyNotValid,\n  ApiKeyExpired,\n  ApiKeyRevoked,\n  WrongApiKeyType,\n} satisfies Record<string, KnownErrorConstructor<any, any>>;\n\n\n// ensure that all known error codes are unique\nconst knownErrorCodes = new Set<string>();\nfor (const [_, KnownError] of Object.entries(KnownErrors)) {\n  if (knownErrorCodes.has(KnownError.errorCode)) {\n    throw new Error(`Duplicate known error code: ${KnownError.errorCode}`);\n  }\n  knownErrorCodes.add(KnownError.errorCode);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2D;AAC3D,uBAA6B;AAE7B,qBAAyB;AAqBlB,IAAe,aAAf,cAAkC,0BAAY;AAAA,EAInD,YACkB,YACA,sBACA,SAChB;AACA;AAAA,MACE;AAAA,MACA;AAAA,IACF;AAPgB;AACA;AACA;AANlB,SAAiB,yBAAyB;AAC1C,SAAO,OAAO;AAAA,EAWd;AAAA,EAEA,OAAc,aAAa,OAAqC;AAE9D,WAAO,OAAO,UAAU,YAAY,UAAU,QAAQ,4BAA4B,SAAS,MAAM,2BAA2B;AAAA,EAC9H;AAAA,EAEgB,UAAsB;AACpC,WAAO,IAAI,YAAY,EAAE,OAAO,KAAK,UAAU,KAAK,kBAAkB,GAAG,QAAW,CAAC,CAAC;AAAA,EACxF;AAAA,EAEgB,aAAuC;AACrD,WAAO;AAAA,MACL,gBAAgB,CAAC,iCAAiC;AAAA,MAClD,uBAAuB,CAAC,KAAK,SAAS;AAAA,IACxC;AAAA,EACF;AAAA,EAEgB,oBAA0B;AACxC,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,GAAG,KAAK,UAAU,EAAE,SAAS,KAAK,QAAQ,IAAI,CAAC;AAAA,MAC/C,OAAO,KAAK;AAAA,IACd;AAAA,EACF;AAAA,EAEA,IAAI,YAAoB;AACtB,WAAQ,KAAK,YAAoB,iBAAa,wBAAS,0FAA0F,IAAI,EAAE;AAAA,EACzJ;AAAA,EAEA,OAAc,wBAAwB,MAAgE;AACpG,WAAO;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA,EAEA,OAAc,SAAS,MAAkC;AACvD,eAAW,CAAC,GAAG,cAAc,KAAK,OAAO,QAAQ,WAAW,GAAG;AAC7D,UAAI,KAAK,SAAS,eAAe,UAAU,WAAW;AACpD,cAAM,kBAAkB,eAAe,wBAAwB,IAAI;AACnE,eAAO,IAAI;AAAA,UAET,GAAG;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAEA,UAAM,IAAI,MAAM,2GAA2G,KAAK,IAAI,KAAK,KAAK,OAAO,EAAE;AAAA,EACzJ;AACF;AAEA,IAAM,yCAAyC,OAAO,wCAAwC;AA2B9F,SAAS,4BACP,YACA,WACA,QACA,yBAC0G;AAC1G,QAAM,WAAW,WAAW,YAAY,gCAA8B;AACtE,QAAM,4BAA4B,4BAA4B,YAAY,WAAW,0BAAmC;AAAA,EAGxH,MAAM,uBAAuB,WAAW;AAAA,IAKtC,eAAe,MAAY;AAEzB,YAAM,GAAG,SAAS,GAAG,IAAI,CAAC;AAL5B,WAAO,OAAO,cAAc,SAAS;AAMnC,WAAK,kBAAkB;AAAA,IACzB;AAAA,IAEA,OAAO,wBAAwB,MAA4B;AACzD,aAAO,0BAA0B,KAAK,OAAO;AAAA,IAC/C;AAAA,IAEA,OAAO,WAAW,OAA0E;AAC1F,UAAI,CAAC,WAAW,aAAa,KAAK,EAAG,QAAO;AAC5C,UAAI,UAAmB;AACvB,aAAO,MAAM;AACX,kBAAU,OAAO,eAAe,OAAO;AACvC,YAAI,CAAC,QAAS;AACd,YAAI,eAAe,QAAQ,eAAe,QAAQ,YAAY,cAAc,UAAW,QAAO;AAAA,MAChG;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAxBE,EADI,eACmB,YAAY;AAwBpC;AAGD,SAAO;AACT;AAWA,IAAM,mBAAmB;AAAA,EACvB;AAAA,EACA;AAAA,EACA,CAAC,sBAA8B;AAAA,IAC7B;AAAA,IACA,oLAAoL,iBAAiB;AAAA,IACrM;AAAA,MACE;AAAA,IACF;AAAA,EACF;AAAA,EACA,CAAC,SAAS;AAAA,IACP,MAAc,yBAAqB,wBAAS,yDAAyD;AAAA,EACxG;AACF;AAEA,IAAM,mBAAmB;AAAA,EACvB;AAAA,EACA;AAAA,EACA,CAAC,YAAoB;AAAA,IACnB;AAAA,IACA;AAAA,EACF;AAAA,EACA,CAAC,SAAS,CAAC,KAAK,OAAO;AACzB;AAEA,IAAM,cAAc;AAAA,EAClB;AAAA,EACA;AAAA,EACA,CAAC,YAAoB;AAAA,IACnB;AAAA,IACA,eAAW,wBAAS,gCAAgC;AAAA,IACpD;AAAA,MACE;AAAA,IACF;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,OAAO;AAC9B;AAEA,IAAM,qBAAqB;AAAA,EACzB;AAAA,EACA;AAAA,EACA,CAAC,mBAA2B;AAAA,IAC1B;AAAA,IACA;AAAA;AAAA;AAAA,UAGM,eAAe,IAAI,CAAC,GAAG,MAAM;AAAA,qBAClB,IAAI,CAAC,KAAK,KAAK,UAAU,GAAG,QAAW,CAAC,CAAC;AAAA,SACrD,EAAE,KAAK,MAAM,CAAC;AAAA;AAAA,IAEnB;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AAAA,EACA,CAAC,SAAS;AAAA,IACP,MAAc,uBAAmB,wBAAS,yDAAyD;AAAA,EACtG;AACF;AAEA,IAAM,6BAA6B;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,+BAA+B;AAAA,EACnC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,8BAA8B;AAAA,EAClC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,oBAAoB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,CAAC,eAAuB;AAAA,IACtB;AAAA,IACA,mFAAmF,UAAU;AAAA,EAC/F;AAAA,EACA,CAAC,SAAS;AAAA,IACP,MAAc,kBAAc,wBAAS,mDAAmD;AAAA,EAC3F;AACF;AAEA,IAAM,6BAA6B;AAAA,EACjC;AAAA,EACA;AAAA,EACA,CAAC,eAA8C;AAAA,IAC7C;AAAA,IACA;AAAA,4CACwC,UAAU;AAAA;AAAA;AAAA;AAAA,IAIlD;AAAA,MACE,cAAc;AAAA,IAChB;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,YAAY;AACnC;AAEA,IAAM,qBAAqB;AAAA,EACzB;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,yBAAyB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA,CAAC,kBAAiD,uBAA0D;AAAA,IAC1G;AAAA,IACA,0CAA0C,mBAAmB,IAAI,OAAK,IAAI,CAAC,GAAG,EAAE,KAAK,MAAM,CAAC,cAAc,gBAAgB;AAAA,IAC1H;AAAA,MACE,oBAAoB;AAAA,MACpB,sBAAsB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,CAAC,SAAc;AAAA,IACb,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AACF;AAEA,IAAM,8BAA8B;AAAA,EAClC;AAAA,EACA;AAAA,EACA,CAAC,cAAsB;AAAA,IACrB;AAAA,IACA,oDAAoD,KAAK,UAAU,SAAS,CAAC;AAAA,IAC7E;AAAA,MACE,YAAY;AAAA,IACd;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,UAAU;AACjC;AAEA,IAAM,yBAAyB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA,CAAC,cAAsB;AAAA,IACrB;AAAA,IACA,sDAAsD,KAAK,UAAU,SAAS,CAAC;AAAA,IAC/E;AAAA,MACE,YAAY;AAAA,IACd;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,UAAU;AACjC;AAEA,IAAM,6BAA6B;AAAA,EACjC;AAAA,EACA;AAAA,EACA,CAAC,cAAsB;AAAA,IACrB;AAAA,IACA,2DAA2D,KAAK,UAAU,SAAS,CAAC;AAAA,IACpF;AAAA,MACE,YAAY;AAAA,IACd;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,UAAU;AACjC;AAEA,IAAM,0BAA0B;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,6BAA6B;AAAA,EACjC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,0BAA0B;AAAA,EAC9B;AAAA,EACA;AAAA,EACA,CAAC,cAAgC;AAAA,IAC/B;AAAA,IACA,mEAAmE,YAAY,iCAAiC,UAAU,YAAY,CAAC,OAAM,EAAE;AAAA,IAC/I,EAAE,mBAAmB,WAAW,QAAQ,KAAK,KAAK;AAAA,EACpD;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,qBAAqB,MAAS;AACrD;AAEA,IAAM,oCAAoC;AAAA,EACxC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,6BAA6B;AAAA,EACjC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAKA,IAAM,gCAAgC;AAAA,EACpC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAMA,IAAM,+BAA+B;AAAA,EACnC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAKA,IAAM,+BAA+B;AAAA,EACnC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAKA,IAAM,uCAAuC;AAAA,EAC3C;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAKA,IAAM,sCAAsC;AAAA,EAC1C;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAKA,IAAM,8CAA8C;AAAA,EAClD;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAKA,IAAM,8BAA8B;AAAA,EAClC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,0BAA0B;AAAA,EAC9B;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,6BAA6B;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,+BAA+B;AAAA,EACnC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,qBAAqB;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,wBAAwB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,qBAAqB;AAAA,EACzB;AAAA,EACA;AAAA,EACA,CAAC,cAAgC;AAAA,IAC/B;AAAA,IACA,6DAA6D,YAAY,iCAAiC,UAAU,YAAY,CAAC,OAAM,EAAE;AAAA,IACzI,EAAE,mBAAmB,WAAW,QAAQ,KAAK,KAAK;AAAA,EACpD;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,oBAAoB,IAAI,KAAK,KAAK,iBAAiB,IAAI,MAAS;AACvF;AAEA,IAAM,+BAA+B;AAAA,EACnC;AAAA,EACA;AAAA,EACA,CAAC,mBAA2B,oBAA4B;AAAA,IACtD;AAAA,IACA,gEAAgE,KAAK,UAAU,iBAAiB,CAAC,qCAAqC,KAAK,UAAU,eAAe,CAAC;AAAA,IACrK;AAAA,MACE,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,IACrB;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,qBAAqB,KAAK,iBAAiB;AAClE;AAGA,IAAM,oBAAoB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,gCAAgC;AAAA,EACpC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,6BAA6B;AAAA,EACjC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAGA,IAAM,mBAAmB;AAAA,EACvB;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,6BAA6B;AAAA,EACjC;AAAA,EACA;AAAA,EACA,CAAC,UAAkB;AAAA,IACjB;AAAA,IACA,qBAAqB,KAAK,UAAU,KAAK,CAAC;AAAA,IAC1C;AAAA,MACE;AAAA,IACF;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,KAAK;AAC5B;AAEA,IAAM,mBAAmB;AAAA,EACvB;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,8BAA8B;AAAA,EAClC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,qBAAqB;AAAA,EACzB;AAAA,EACA;AAAA,EACA,CAAC,WAAmB;AAAA,IAClB;AAAA,IACA,8BAA8B,MAAM;AAAA,IACpC;AAAA,MACE,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,OAAO;AAC9B;AAEA,IAAM,eAAe;AAAA,EACnB;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAGA,IAAM,kBAAkB;AAAA,EACtB;AAAA,EACA;AAAA,EACA,CAAC,cAAsB;AACrB,QAAI,OAAO,cAAc,SAAU,OAAM,IAAI,kCAAoB,2DAA2D;AAC5H,WAAO;AAAA,MACL;AAAA,MACA,WAAW,SAAS;AAAA,MACpB;AAAA,QACE,YAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,UAAU;AACjC;AAEA,IAAM,qBAAqB;AAAA,EACzB;AAAA,EACA;AAAA,EACA,CAAC,aAAqB;AAAA,IACpB;AAAA,IACA,sBAAsB,QAAQ;AAAA,IAC9B;AAAA,MACE,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,SAAS;AAChC;AAGA,IAAM,mBAAmB;AAAA,EACvB;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,mCAAmC;AAAA,EACvC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAGA,IAAM,kCAAkC;AAAA,EACtC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,8BAA8B;AAAA,EAClC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAGA,IAAM,wBAAwB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,4BAA4B;AAAA,EAChC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,6BAA6B;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,mBAAmB;AAAA,EACvB;AAAA,EACA;AAAA,EACA,CAAC,cAAsB;AAAA,IACrB;AAAA,IACA,yCAAyC,SAAS;AAAA,IAClD;AAAA,MACE,YAAY;AAAA,IACd;AAAA,EACF;AAAA,EACA,CAAC,SAAS;AAAA,IACP,MAAc,kBAAc,wBAAS,kDAAkD;AAAA,EAC1F;AACF;AAEA,IAAM,kBAAkB;AAAA,EACtB;AAAA,EACA;AAAA,EACA,CAAC,cAAsB;AAAA,IACrB;AAAA,IACA,wCAAwC,SAAS;AAAA,IACjD;AAAA,MACE;AAAA,IACF;AAAA,EACF;AAAA,EACA,CAAC,SAAS;AAAA,IACP,MAAc,iBAAa,wBAAS,gDAAgD;AAAA,EACvF;AACF;AAEA,IAAM,0BAA0B;AAAA,EAC9B;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,wBAAwB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,2BAA2B;AAAA,EAC/B;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,0BAA0B;AAAA,EAC9B;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,8BAA8B;AAAA,EAClC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,qCAAqC;AAAA,EACzC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,+BAA+B;AAAA,EACnC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,uBAAuB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,6BAA6B;AAAA,EACjC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,yBAAyB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA,CAAC,OAAe,iBAAgC;AAAA,IAC9C;AAAA,IACA,qBAAqB,KAAK,2CAA2C,YAAY;AAAA,IACjF;AAAA,MACE;AAAA,MACA,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,OAAO,KAAK,aAAa;AAChD;AAGA,IAAM,4BAA4B;AAAA,EAChC;AAAA,EACA;AAAA,EACA,CAAC,YAAoB;AAAA,IACnB;AAAA,IACA;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,OAAO;AAC9B;AAGA,IAAM,uBAAuB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,CAAC,SAAiB,SAAiB;AAAA,IACjC;AAAA,IACA;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,SAAS,KAAK,IAAI;AACzC;AAEA,IAAM,8BAA8B;AAAA,EAClC;AAAA,EACA;AAAA,EACA,CAAC,YAAoB;AAAA,IACnB;AAAA,IACA;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,OAAO;AAC9B;AAGA,IAAM,qBAAqB;AAAA,EACzB;AAAA,EACA;AAAA,EACA,CAAC,iBAAyB;AAAA,IACxB;AAAA,IACA,eAAe,YAAY;AAAA,IAC3B;AAAA,MACE,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,aAAa;AACpC;AAEA,IAAM,0BAA0B;AAAA,EAC9B;AAAA,EACA;AAAA,EACA,CAAC,cAAsB,eAAmC,gBAA2C;AAAA,IACnG;AAAA,IACA,cAAc,KAAK,UAAU,YAAY,CAAC,mDAAmD,KAAK,UAAU,WAAW,CAAC,eAAe,KAAK,UAAU,aAAa,CAAC;AAAA,IACpK;AAAA,MACE,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,eAAe,KAAK,gBAAgB,KAAK,YAAY;AAC5E;AAEA,IAAM,8BAA8B;AAAA,EAClC;AAAA,EACA;AAAA,EACA,CAAC,iBAAyB;AAAA,IACxB;AAAA,IACA,iCAAiC,YAAY;AAAA,IAC7C;AAAA,MACE,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,aAAa;AACpC;AAEA,IAAM,eAAe;AAAA,EACnB;AAAA,EACA;AAAA,EACA,CAAC,WAAmB;AAAA,IAClB;AAAA,IACA,QAAQ,MAAM;AAAA,IACd;AAAA,MACE,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,OAAO;AAC9B;AAEA,IAAM,oBAAoB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,CAAC,WAAmB;AAAA,IAClB;AAAA,IACA,QAAQ,MAAM;AAAA,IACd;AAAA,MACE,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,OAAO;AAC9B;AAEA,IAAM,yBAAyB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA,CAAC,QAAgB,WAAmB;AAAA,IAClC;AAAA,IACA,QAAQ,MAAM,yBAAyB,MAAM;AAAA,IAC7C;AAAA,MACE,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,SAAS,KAAK,OAAO;AAC5C;AAGA,IAAM,6BAA6B;AAAA,EACjC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,oCAAoC;AAAA,EACxC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,+CAA+C;AAAA,EACnD;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,0CAA0C;AAAA,EAC9C;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,iDAAiD;AAAA,EACrD;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,kDAAkD;AAAA,EACtD;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,+BAA+B;AAAA,EACnC;AAAA,EACA;AAAA,EACA,CAAC,aAAsB;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,MACE,WAAW,YAAY;AAAA,IACzB;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,aAAa,MAAS;AAC7C;AAEA,IAAM,eAAe;AAAA,EACnB;AAAA,EACA;AAAA,EACA,CAAC,UAAkB;AAAA,IACjB;AAAA,IACA,cAAc,KAAK;AAAA,EACrB;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,KAAK;AAC5B;AAEA,IAAM,+CAA+C;AAAA,EACnD;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,oBAAoB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,oCAAoC;AAAA,EACxC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,oCAAoC;AAAA,EACxC;AAAA,EACA;AAAA,EACA,CAAC,gBAAwB;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,MACE,cAAc;AAAA,IAChB;AAAA,EACF;AAAA,EACA,CAAC,SAAS,CAAC,KAAK,YAAY;AAC9B;AAEA,IAAM,kBAAkB;AAAA,EACtB;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,6BAA6B;AAAA,EACjC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,8BAA8B;AAAA,EAClC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,4BAA4B;AAAA,EAChC;AAAA,EACA;AAAA,EACA,CAAC,QAAQ,iBAAiB;AAAA,IACxB;AAAA,IACA,QAAQ,MAAM,6BAA6B,YAAY;AAAA,IACvD;AAAA,MACE,SAAS;AAAA,MACT,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,CAAC,SAAS,CAAC,KAAK,SAAS,KAAK,aAAa;AAC7C;AAEA,IAAM,yBAAyB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA,CAAC,QAAQ,QAAQ,iBAAiB;AAAA,IAChC;AAAA,IACA,QAAQ,MAAM,6BAA6B,YAAY,YAAY,MAAM;AAAA,IACzE;AAAA,MACE,SAAS;AAAA,MACT,SAAS;AAAA,MACT,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,CAAC,SAAS,CAAC,KAAK,SAAS,KAAK,SAAS,KAAK,aAAa;AAC3D;AAEA,IAAM,yBAAyB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA,CAAC,QAAQ,QAAQ,iBAAiB;AAAA,IAChC;AAAA,IACA,QAAQ,MAAM,6BAA6B,YAAY,YAAY,MAAM;AAAA,IACzE;AAAA,MACE,SAAS;AAAA,MACT,SAAS;AAAA,MACT,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,CAAC,SAAS,CAAC,KAAK,SAAS,KAAK,SAAS,KAAK,aAAa;AAC3D;AAEA,IAAM,+BAA+B;AAAA,EACnC;AAAA,EACA;AAAA,EACA,CAAC,eAAe;AAAA,IACd;AAAA,IACA,qCAAqC,UAAU;AAAA,IAC/C;AAAA,MACE,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,CAAC,SAAS,CAAC,KAAK,WAAW;AAC7B;AAEA,IAAM,iCAAiC;AAAA,EACrC;AAAA,EACA;AAAA,EACA,CAAC,eAAe;AAAA,IACd;AAAA,IACA,uCAAuC,UAAU;AAAA,IACjD;AAAA,MACE,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,CAAC,SAAS,CAAC,KAAK,WAAW;AAC7B;AAEA,IAAM,2BAA2B;AAAA,EAC/B;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,4BAA4B;AAAA,EAChC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,gDAAgD;AAAA,EACpD;AAAA,EACA;AAAA,EACA,CAAC,MAAe,wBAAiC;AAAA,IAC/C;AAAA,IACA,sBACA,OAAO,IAAI,KAAK,mBAAmB,6DACnC,QAAQ,IAAI;AAAA,IACZ,EAAE,MAAM,uBAAuB,uBAAuB,KAAK;AAAA,EAC7D;AAAA,EACA,CAAC,SAAS,CAAC,KAAK,MAAM,KAAK,qBAAqB;AAClD;AAEA,IAAM,0BAA0B;AAAA,EAC9B;AAAA,EACA;AAAA,EACA,CAAC,YAAmB;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,IAAI;AACtB;AAEA,IAAM,eAAe;AAAA,EACnB;AAAA,EACA;AAAA,EACA,CAAC,YAAoB;AAAA,IACnB;AAAA,IACA;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,OAAO;AAC9B;AAEA,IAAM,sBAAsB;AAAA,EAC1B;AAAA,EACA;AAAA,EACA,CAAC,UAAkB,4DAA4D;AAAA,IAC7E;AAAA,IACA;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,OAAO;AAC9B;AAEA,IAAM,mBAAmB;AAAA,EACvB;AAAA,EACA;AAAA,EACA,CAAC,UAAkB,uDAAuD;AAAA,IACxE;AAAA,IACA;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,OAAO;AAC9B;AAGA,IAAM,iBAAiB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,gBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,gBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,kBAAkB;AAAA,EACtB;AAAA,EACA;AAAA,EACA,CAAC,cAAsB,eAAuB;AAAA,IAC5C;AAAA,IACA,wBAAwB,YAAY,oBAAoB,UAAU;AAAA,IAClE,EAAE,eAAe,cAAc,aAAa,WAAW;AAAA,EACzD;AAAA,EACA,CAAC,SAAS,CAAC,KAAK,eAAe,KAAK,WAAW;AACjD;AAEA,IAAM,iBAAiB;AAAA,EACrB;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,8BAA8B;AAAA,EAClC;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC;AACT;AAEA,IAAM,4BAA4B;AAAA,EAChC;AAAA,EACA;AAAA,EACA,CAAC,iBAAyB;AAAA,IACxB;AAAA,IACA,uBAAuB,YAAY;AAAA,IACnC;AAAA,MACE,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,CAAC,SAAc,CAAC,KAAK,aAAa;AACpC;AAMO,IAAM,cAAc;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAIA,IAAM,kBAAkB,oBAAI,IAAY;AACxC,WAAW,CAAC,GAAGA,WAAU,KAAK,OAAO,QAAQ,WAAW,GAAG;AACzD,MAAI,gBAAgB,IAAIA,YAAW,SAAS,GAAG;AAC7C,UAAM,IAAI,MAAM,+BAA+BA,YAAW,SAAS,EAAE;AAAA,EACvE;AACA,kBAAgB,IAAIA,YAAW,SAAS;AAC1C;", "names": ["KnownError"]}