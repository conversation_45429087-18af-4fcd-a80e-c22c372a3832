<execution>
  <constraint>
    ## HTML标准的客观限制
    - **W3C标准约束**：必须遵循HTML5和CSS3的W3C标准
    - **浏览器兼容性**：考虑主流浏览器的支持差异
    - **性能限制**：文件大小和加载速度的平衡
    - **可访问性要求**：符合WCAG 2.1 AA级标准
  </constraint>

  <rule>
    ## HTML编码强制规则
    
    ### 文档结构规范
    ```html
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>页面标题</title>
        <meta name="description" content="页面描述">
    </head>
    <body>
        <!-- 页面内容 -->
    </body>
    </html>
    ```
    
    ### 语义化标签使用
    - **导航**：使用 `<nav>` 标签包装导航链接
    - **主要内容**：使用 `<main>` 标签包装主要内容
    - **文章内容**：使用 `<article>` 标签包装独立内容
    - **侧边栏**：使用 `<aside>` 标签包装辅助内容
    - **页脚**：使用 `<footer>` 标签包装页脚信息
    
    ### 可访问性规范
    ```html
    <!-- 图片必须有alt属性 -->
    <img src="image.jpg" alt="图片描述">
    
    <!-- 表单标签关联 -->
    <label for="username">用户名：</label>
    <input type="text" id="username" name="username">
    
    <!-- 链接有意义的文本 -->
    <a href="/about">关于我们</a>
    ```
  </rule>

  <guideline>
    ## HTML最佳实践指导
    
    ### 代码组织
    - **缩进一致**：使用2个空格进行缩进
    - **注释清晰**：为复杂结构添加注释说明
    - **命名规范**：使用有意义的class和id名称
    - **结构清晰**：保持HTML结构的逻辑性和可读性
    
    ### CSS组织
    ```css
    /* 重置样式 */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    /* 基础样式 */
    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
    }
    
    /* 组件样式 */
    .header {
        background: #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    ```
    
    ### 响应式设计
    ```css
    /* 移动优先 */
    .container {
        width: 100%;
        padding: 0 16px;
    }
    
    /* 平板适配 */
    @media (min-width: 768px) {
        .container {
            max-width: 750px;
            margin: 0 auto;
        }
    }
    
    /* 桌面适配 */
    @media (min-width: 1200px) {
        .container {
            max-width: 1170px;
        }
    }
    ```
  </guideline>

  <process>
    ## HTML开发流程
    
    ### 文件结构规划
    ```
    project/
    ├── index.html          # 主页面
    ├── css/
    │   ├── reset.css       # 重置样式
    │   ├── base.css        # 基础样式
    │   └── components.css  # 组件样式
    ├── js/
    │   └── main.js         # 主要脚本
    ├── images/
    │   └── ...             # 图片资源
    └── fonts/
        └── ...             # 字体文件
    ```
    
    ### 开发检查清单
    - [ ] HTML5文档类型声明
    - [ ] 正确的meta标签设置
    - [ ] 语义化标签使用
    - [ ] 图片alt属性完整
    - [ ] 表单标签正确关联
    - [ ] CSS样式组织清晰
    - [ ] 响应式布局实现
    - [ ] 浏览器兼容性测试
    - [ ] 可访问性检查
    - [ ] 性能优化完成
  </process>

  <criteria>
    ## HTML质量评估标准
    
    ### 代码质量
    - ✅ HTML验证无错误
    - ✅ CSS验证无错误
    - ✅ 语义化标签使用正确
    - ✅ 代码结构清晰可读
    - ✅ 注释完整适当

    ### 性能标准
    - ✅ 页面加载时间 ≤ 3秒
    - ✅ 图片优化压缩
    - ✅ CSS/JS文件精简
    - ✅ 减少HTTP请求数量

    ### 兼容性标准
    - ✅ Chrome/Firefox/Safari/Edge兼容
    - ✅ 移动端Safari/Chrome兼容
    - ✅ 响应式布局正确
    - ✅ 可访问性标准合规
  </criteria>
</execution>
