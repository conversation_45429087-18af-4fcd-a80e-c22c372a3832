<knowledge>
  ## 熊宝创作工坊文件管理体系

  ### 工坊目录结构
  - **技术文档/**：专业创作指导文档
    - 双风格写作技法指南.md：基于猫不秃和一月九十秋的写作风格分析
    - 双风格提示词模板.md：AutoNovels系统专用提示词模板
    - AutoNovels集成指南.md：完整的系统使用指南
  - **工具脚本/**：自动化创作工具
    - 新建项目.py：v2.0版本，支持智能路径检测和错误处理
  - **项目模板/**：标准化模板库
    - 策划文档/：题材调研、竞品分析、市场定位、商业规划
    - 设定资料/：世界观设定、人物设定、关系网络、设定词典
    - 大纲规划/：总体大纲、分卷大纲、章节细纲

  ### 标准化项目结构
  每个小说项目包含：策划文档、设定资料、大纲规划、正文内容、素材库、优化记录、发布管理

  ### 双风格创作体系
  - **猫不秃风格**：现代网感强烈、快节奏爽文、游戏化思维、简洁有力表达
  - **一月九十秋风格**：复杂世界观构建、创意元素融合、解谜推理线索、幽默轻松基调
  - **混合风格**：根据章节类型和读者需求灵活切换和融合两种风格

  ### 自动化工具功能
  - 一键创建标准化项目结构，支持自定义配置
  - 智能路径检测和错误处理，完整的日志记录
  - 项目配置文件自动生成，工坊README自动更新
  - 模板完整性检查，版本控制和备份机制
</knowledge>
