{"version": 3, "sources": ["../../src/components/oauth-button.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { BrandIcons, Button } from '@stackframe/stack-ui';\nimport Color from 'color';\nimport { useEffect, useId, useState } from 'react';\nimport { useStackApp } from '..';\nimport { useTranslation } from '../lib/translations';\n\nconst iconSize = 22;\n\nconst changeColor = (c: Color, value: number) => {\n  if (c.isLight()) {\n    value = -value;\n  }\n  return c.hsl(c.hue(), c.saturationl(), c.lightness() + value).toString();\n};\n\nexport function OAuthButton({\n  provider,\n  type,\n  isMock = false,\n}: {\n  provider: string,\n  type: 'sign-in' | 'sign-up',\n  isMock?: boolean,\n}) {\n  const { t } = useTranslation();\n  const stackApp = useStackApp();\n  const styleId = useId().replaceAll(':', '-');\n\n  const [lastUsed, setLastUsed] = useState<string | null>(null);\n  useEffect(() => {\n    setLastUsed(localStorage.getItem('_STACK_AUTH.lastUsed'));\n  }, []);\n\n  let style : {\n    backgroundColor?: string,\n    textColor?: string,\n    name: string,\n    icon: JSX.Element | null,\n    border?: string,\n  };\n  switch (provider) {\n    case 'google': {\n      style = {\n        backgroundColor: '#fff',\n        textColor: '#000',\n        name: 'Google',\n        border: '1px solid #ddd',\n        icon: <BrandIcons.Google iconSize={iconSize} />,\n      };\n      break;\n    }\n    case 'github': {\n      style = {\n        backgroundColor: '#111',\n        textColor: '#fff',\n        border: '1px solid #333',\n        name: 'GitHub',\n        icon: <BrandIcons.GitHub iconSize={iconSize} />,\n      };\n      break;\n    }\n    case 'facebook': {\n      style = {\n        backgroundColor: '#1877F2',\n        textColor: '#fff',\n        name: 'Facebook',\n        icon: <BrandIcons.Facebook iconSize={iconSize} />,\n      };\n      break;\n    }\n    case 'microsoft': {\n      style = {\n        backgroundColor: '#2f2f2f',\n        textColor: '#fff',\n        name: 'Microsoft',\n        icon: <BrandIcons.Microsoft iconSize={iconSize} />,\n      };\n      break;\n    }\n    case 'spotify': {\n      style = {\n        backgroundColor: '#1DB954',\n        textColor: '#fff',\n        name: 'Spotify',\n        icon: <BrandIcons.Spotify iconSize={iconSize} />,\n      };\n      break;\n    }\n    case 'discord': {\n      style = {\n        backgroundColor: '#5865F2',\n        textColor: '#fff',\n        name: 'Discord',\n        icon: <BrandIcons.Discord iconSize={iconSize} />,\n      };\n      break;\n    }\n    case 'gitlab': {\n      style = {\n        backgroundColor: \"#111\",\n        textColor: \"#fff\",\n        border: \"1px solid #333\",\n        name: \"Gitlab\",\n        icon: <BrandIcons.Gitlab iconSize={iconSize} />,\n      };\n      break;\n    }\n    case 'apple': {\n      style = {\n        backgroundColor: \"#000\",\n        textColor: \"#fff\",\n        border: \"1px solid #333\",\n        name: \"Apple\",\n        icon: <BrandIcons.Apple iconSize={iconSize} />,\n      };\n      break;\n    }\n    case \"bitbucket\": {\n      style = {\n        backgroundColor: \"#fff\",\n        textColor: \"#000\",\n        border: \"1px solid #ddd\",\n        name: \"Bitbucket\",\n        icon: <BrandIcons.Bitbucket iconSize={iconSize} />,\n      };\n      break;\n    }\n    case 'linkedin': {\n      style = {\n        backgroundColor: \"#0073b1\",\n        textColor: \"#fff\",\n        name: \"LinkedIn\",\n        icon: <BrandIcons.LinkedIn iconSize={iconSize} />,\n      };\n      break;\n    }\n    case 'x': {\n      style = {\n        backgroundColor: \"#000\",\n        textColor: \"#fff\",\n        name: \"X\",\n        icon: <BrandIcons.X iconSize={iconSize} />,\n      };\n      break;\n    }\n    default: {\n      style = {\n        name: provider,\n        icon: null,\n      };\n    }\n  }\n\n  const styleSheet = `\n    .stack-oauth-button-${styleId} {\n      background-color: ${style.backgroundColor} !important;\n      color: ${style.textColor} !important;\n      border: ${style.border} !important;\n    }\n    .stack-oauth-button-${styleId}:hover {\n      background-color: ${changeColor(Color(style.backgroundColor), 10)} !important;\n    }\n  `;\n\n  return (\n    <>\n      <style>{styleSheet}</style>\n      <Button\n        onClick={async () => {\n          localStorage.setItem('_STACK_AUTH.lastUsed', provider);\n          await stackApp.signInWithOAuth(provider);\n        }}\n        className={`stack-oauth-button-${styleId} stack-scope relative`}\n      >\n        {!isMock && lastUsed === provider && (\n          <span className=\"absolute -top-2 -right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-md\">\n            last\n          </span>\n        )}\n        <div className='flex items-center w-full gap-4'>\n          {style.icon}\n          <span className='flex-1'>\n            {type === 'sign-up' ?\n              t('Sign up with {provider}', { provider: style.name }) :\n              t('Sign in with {provider}', { provider: style.name })\n            }\n          </span>\n        </div>\n      </Button>\n    </>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,sBAAmC;AACnC,mBAAkB;AAClB,mBAA2C;AAC3C,eAA4B;AAC5B,0BAA+B;AA2CjB;AAzCd,IAAM,WAAW;AAEjB,IAAM,cAAc,CAAC,GAAU,UAAkB;AAC/C,MAAI,EAAE,QAAQ,GAAG;AACf,YAAQ,CAAC;AAAA,EACX;AACA,SAAO,EAAE,IAAI,EAAE,IAAI,GAAG,EAAE,YAAY,GAAG,EAAE,UAAU,IAAI,KAAK,EAAE,SAAS;AACzE;AAEO,SAAS,YAAY;AAAA,EAC1B;AAAA,EACA;AAAA,EACA,SAAS;AACX,GAIG;AACD,QAAM,EAAE,EAAE,QAAI,oCAAe;AAC7B,QAAM,eAAW,sBAAY;AAC7B,QAAM,cAAU,oBAAM,EAAE,WAAW,KAAK,GAAG;AAE3C,QAAM,CAAC,UAAU,WAAW,QAAI,uBAAwB,IAAI;AAC5D,8BAAU,MAAM;AACd,gBAAY,aAAa,QAAQ,sBAAsB,CAAC;AAAA,EAC1D,GAAG,CAAC,CAAC;AAEL,MAAI;AAOJ,UAAQ,UAAU;AAAA,IAChB,KAAK,UAAU;AACb,cAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM,4CAAC,2BAAW,QAAX,EAAkB,UAAoB;AAAA,MAC/C;AACA;AAAA,IACF;AAAA,IACA,KAAK,UAAU;AACb,cAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM,4CAAC,2BAAW,QAAX,EAAkB,UAAoB;AAAA,MAC/C;AACA;AAAA,IACF;AAAA,IACA,KAAK,YAAY;AACf,cAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,MAAM;AAAA,QACN,MAAM,4CAAC,2BAAW,UAAX,EAAoB,UAAoB;AAAA,MACjD;AACA;AAAA,IACF;AAAA,IACA,KAAK,aAAa;AAChB,cAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,MAAM;AAAA,QACN,MAAM,4CAAC,2BAAW,WAAX,EAAqB,UAAoB;AAAA,MAClD;AACA;AAAA,IACF;AAAA,IACA,KAAK,WAAW;AACd,cAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,MAAM;AAAA,QACN,MAAM,4CAAC,2BAAW,SAAX,EAAmB,UAAoB;AAAA,MAChD;AACA;AAAA,IACF;AAAA,IACA,KAAK,WAAW;AACd,cAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,MAAM;AAAA,QACN,MAAM,4CAAC,2BAAW,SAAX,EAAmB,UAAoB;AAAA,MAChD;AACA;AAAA,IACF;AAAA,IACA,KAAK,UAAU;AACb,cAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM,4CAAC,2BAAW,QAAX,EAAkB,UAAoB;AAAA,MAC/C;AACA;AAAA,IACF;AAAA,IACA,KAAK,SAAS;AACZ,cAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM,4CAAC,2BAAW,OAAX,EAAiB,UAAoB;AAAA,MAC9C;AACA;AAAA,IACF;AAAA,IACA,KAAK,aAAa;AAChB,cAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM,4CAAC,2BAAW,WAAX,EAAqB,UAAoB;AAAA,MAClD;AACA;AAAA,IACF;AAAA,IACA,KAAK,YAAY;AACf,cAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,MAAM;AAAA,QACN,MAAM,4CAAC,2BAAW,UAAX,EAAoB,UAAoB;AAAA,MACjD;AACA;AAAA,IACF;AAAA,IACA,KAAK,KAAK;AACR,cAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,MAAM;AAAA,QACN,MAAM,4CAAC,2BAAW,GAAX,EAAa,UAAoB;AAAA,MAC1C;AACA;AAAA,IACF;AAAA,IACA,SAAS;AACP,cAAQ;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,QAAM,aAAa;AAAA,0BACK,OAAO;AAAA,0BACP,MAAM,eAAe;AAAA,eAChC,MAAM,SAAS;AAAA,gBACd,MAAM,MAAM;AAAA;AAAA,0BAEF,OAAO;AAAA,0BACP,gBAAY,aAAAA,SAAM,MAAM,eAAe,GAAG,EAAE,CAAC;AAAA;AAAA;AAIrE,SACE,4EACE;AAAA,gDAAC,WAAO,sBAAW;AAAA,IACnB;AAAA,MAAC;AAAA;AAAA,QACC,SAAS,YAAY;AACnB,uBAAa,QAAQ,wBAAwB,QAAQ;AACrD,gBAAM,SAAS,gBAAgB,QAAQ;AAAA,QACzC;AAAA,QACA,WAAW,sBAAsB,OAAO;AAAA,QAEvC;AAAA,WAAC,UAAU,aAAa,YACvB,4CAAC,UAAK,WAAU,gFAA+E,kBAE/F;AAAA,UAEF,6CAAC,SAAI,WAAU,kCACZ;AAAA,kBAAM;AAAA,YACP,4CAAC,UAAK,WAAU,UACb,mBAAS,YACR,EAAE,2BAA2B,EAAE,UAAU,MAAM,KAAK,CAAC,IACrD,EAAE,2BAA2B,EAAE,UAAU,MAAM,KAAK,CAAC,GAEzD;AAAA,aACF;AAAA;AAAA;AAAA,IACF;AAAA,KACF;AAEJ;", "names": ["Color"]}