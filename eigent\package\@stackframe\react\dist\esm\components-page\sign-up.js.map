{"version": 3, "sources": ["../../../src/components-page/sign-up.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { AuthPage } from './auth-page';\n\nexport function SignUp(props: {\n  fullPage?: boolean,\n  automaticRedirect?: boolean,\n  noPasswordRepeat?: boolean,\n  extraInfo?: React.ReactNode,\n  firstTab?: 'magic-link' | 'password',\n}) {\n  return <AuthPage\n    fullPage={!!props.fullPage}\n    type='sign-up'\n    automaticRedirect={!!props.automaticRedirect}\n    noPasswordRepeat={props.noPasswordRepeat}\n    extraInfo={props.extraInfo}\n    firstTab={props.firstTab}\n  />;\n}\n"], "mappings": ";;;AAMA,SAAS,gBAAgB;AAShB;AAPF,SAAS,OAAO,OAMpB;AACD,SAAO;AAAA,IAAC;AAAA;AAAA,MACN,UAAU,CAAC,CAAC,MAAM;AAAA,MAClB,MAAK;AAAA,MACL,mBAAmB,CAAC,CAAC,MAAM;AAAA,MAC3B,kBAAkB,MAAM;AAAA,MACxB,WAAW,MAAM;AAAA,MACjB,UAAU,MAAM;AAAA;AAAA,EAClB;AACF;", "names": []}