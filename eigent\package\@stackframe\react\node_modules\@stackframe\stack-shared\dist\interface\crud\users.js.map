{"version": 3, "sources": ["../../../src/interface/crud/users.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport * as fieldSchema from \"../../schema-fields\";\nimport { WebhookEvent } from \"../webhooks\";\nimport { teamsCrudServerReadSchema } from \"./teams\";\n\nexport const usersCrudServerUpdateSchema = fieldSchema.yupObject({\n  display_name: fieldSchema.userDisplayNameSchema.optional(),\n  profile_image_url: fieldSchema.profileImageUrlSchema.nullable().optional(),\n  client_metadata: fieldSchema.userClientMetadataSchema.optional(),\n  client_read_only_metadata: fieldSchema.userClientReadOnlyMetadataSchema.optional(),\n  server_metadata: fieldSchema.userServerMetadataSchema.optional(),\n  primary_email: fieldSchema.primaryEmailSchema.nullable().optional().nonEmpty(),\n  primary_email_verified: fieldSchema.primaryEmailVerifiedSchema.optional(),\n  primary_email_auth_enabled: fieldSchema.primaryEmailAuthEnabledSchema.optional(),\n  passkey_auth_enabled: fieldSchema.userOtpAuthEnabledSchema.optional(),\n  password: fieldSchema.userPasswordMutationSchema.optional(),\n  password_hash: fieldSchema.userPasswordHashMutationSchema.optional(),\n  otp_auth_enabled: fieldSchema.userOtpAuthEnabledMutationSchema.optional(),\n  totp_secret_base64: fieldSchema.userTotpSecretMutationSchema.optional(),\n  selected_team_id: fieldSchema.selectedTeamIdSchema.nullable().optional(),\n  is_anonymous: fieldSchema.yupBoolean().oneOf([false]).optional(),\n}).defined();\n\nexport const usersCrudServerReadSchema = fieldSchema.yupObject({\n  id: fieldSchema.userIdSchema.defined(),\n  primary_email: fieldSchema.primaryEmailSchema.nullable().defined(),\n  primary_email_verified: fieldSchema.primaryEmailVerifiedSchema.defined(),\n  primary_email_auth_enabled: fieldSchema.primaryEmailAuthEnabledSchema.defined(),\n  display_name: fieldSchema.userDisplayNameSchema.nullable().defined(),\n  selected_team: teamsCrudServerReadSchema.nullable().defined(),\n  selected_team_id: fieldSchema.selectedTeamIdSchema.nullable().defined(),\n  profile_image_url: fieldSchema.profileImageUrlSchema.nullable().defined(),\n  signed_up_at_millis: fieldSchema.signedUpAtMillisSchema.defined(),\n  has_password: fieldSchema.userHasPasswordSchema.defined(),\n  otp_auth_enabled: fieldSchema.userOtpAuthEnabledSchema.defined(),\n  passkey_auth_enabled: fieldSchema.userOtpAuthEnabledSchema.defined(),\n  client_metadata: fieldSchema.userClientMetadataSchema,\n  client_read_only_metadata: fieldSchema.userClientReadOnlyMetadataSchema,\n  server_metadata: fieldSchema.userServerMetadataSchema,\n  last_active_at_millis: fieldSchema.userLastActiveAtMillisSchema.nonNullable().defined(),\n  is_anonymous: fieldSchema.yupBoolean().defined(),\n\n  oauth_providers: fieldSchema.yupArray(fieldSchema.yupObject({\n    id: fieldSchema.yupString().defined(),\n    account_id: fieldSchema.yupString().defined(),\n    email: fieldSchema.yupString().nullable(),\n  }).defined()).defined().meta({ openapiField: { hidden: true } }),\n\n  /**\n   * @deprecated\n   */\n  auth_with_email: fieldSchema.yupBoolean().defined().meta({ openapiField: { hidden: true, description: 'Whether the user can authenticate with their primary e-mail. If set to true, the user can log-in with credentials and/or magic link, if enabled in the project settings.', exampleValue: true } }),\n  /**\n   * @deprecated\n   */\n  requires_totp_mfa: fieldSchema.yupBoolean().defined().meta({ openapiField: { hidden: true, description: 'Whether the user is required to use TOTP MFA to sign in', exampleValue: false } }),\n}).defined();\n\nexport const usersCrudServerCreateSchema = usersCrudServerUpdateSchema.omit(['selected_team_id']).concat(fieldSchema.yupObject({\n  oauth_providers: fieldSchema.yupArray(fieldSchema.yupObject({\n    id: fieldSchema.yupString().defined(),\n    account_id: fieldSchema.yupString().defined(),\n    email: fieldSchema.yupString().nullable().defined().default(null),\n  }).defined()).optional().meta({ openapiField: { hidden: true } }),\n  is_anonymous: fieldSchema.yupBoolean().optional(),\n}).defined());\n\nexport const usersCrudServerDeleteSchema = fieldSchema.yupMixed();\n\nexport const usersCrud = createCrud({\n  serverReadSchema: usersCrudServerReadSchema,\n  serverUpdateSchema: usersCrudServerUpdateSchema,\n  serverCreateSchema: usersCrudServerCreateSchema,\n  serverDeleteSchema: usersCrudServerDeleteSchema,\n  docs: {\n    serverCreate: {\n      tags: [\"Users\"],\n      summary: 'Create user',\n      description: 'Creates a new user. E-mail authentication is always enabled, and no password is set, meaning the only way to authenticate the newly created user is through magic link.',\n    },\n    serverRead: {\n      tags: [\"Users\"],\n      summary: 'Get user',\n      description: 'Gets a user by user ID.',\n    },\n    serverUpdate: {\n      tags: [\"Users\"],\n      summary: 'Update user',\n      description: 'Updates a user. Only the values provided will be updated.',\n    },\n    serverDelete: {\n      tags: [\"Users\"],\n      summary: 'Delete user',\n      description: 'Deletes a user. Use this with caution.',\n    },\n    serverList: {\n      tags: [\"Users\"],\n      summary: 'List users',\n      description: 'Lists all the users in the project.',\n    },\n  },\n});\nexport type UsersCrud = CrudTypeOf<typeof usersCrud>;\n\nexport const userCreatedWebhookEvent = {\n  type: \"user.created\",\n  schema: usersCrud.server.readSchema,\n  metadata: {\n    summary: \"User Created\",\n    description: \"This event is triggered when a user is created.\",\n    tags: [\"Users\"],\n  },\n} satisfies WebhookEvent<typeof usersCrud.server.readSchema>;\n\nexport const userUpdatedWebhookEvent = {\n  type: \"user.updated\",\n  schema: usersCrud.server.readSchema,\n  metadata: {\n    summary: \"User Updated\",\n    description: \"This event is triggered when a user is updated.\",\n    tags: [\"Users\"],\n  },\n} satisfies WebhookEvent<typeof usersCrud.server.readSchema>;\n\nconst webhookUserDeletedSchema = fieldSchema.yupObject({\n  id: fieldSchema.userIdSchema.defined(),\n  teams: fieldSchema.yupArray(fieldSchema.yupObject({\n    id: fieldSchema.yupString().defined(),\n  })).defined(),\n}).defined();\n\nexport const userDeletedWebhookEvent = {\n  type: \"user.deleted\",\n  schema: webhookUserDeletedSchema,\n  metadata: {\n    summary: \"User Deleted\",\n    description: \"This event is triggered when a user is deleted.\",\n    tags: [\"Users\"],\n  },\n} satisfies WebhookEvent<typeof webhookUserDeletedSchema>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAuC;AACvC,kBAA6B;AAE7B,mBAA0C;AAEnC,IAAM,8BAA0C,sBAAU;AAAA,EAC/D,cAA0B,kCAAsB,SAAS;AAAA,EACzD,mBAA+B,kCAAsB,SAAS,EAAE,SAAS;AAAA,EACzE,iBAA6B,qCAAyB,SAAS;AAAA,EAC/D,2BAAuC,6CAAiC,SAAS;AAAA,EACjF,iBAA6B,qCAAyB,SAAS;AAAA,EAC/D,eAA2B,+BAAmB,SAAS,EAAE,SAAS,EAAE,SAAS;AAAA,EAC7E,wBAAoC,uCAA2B,SAAS;AAAA,EACxE,4BAAwC,0CAA8B,SAAS;AAAA,EAC/E,sBAAkC,qCAAyB,SAAS;AAAA,EACpE,UAAsB,uCAA2B,SAAS;AAAA,EAC1D,eAA2B,2CAA+B,SAAS;AAAA,EACnE,kBAA8B,6CAAiC,SAAS;AAAA,EACxE,oBAAgC,yCAA6B,SAAS;AAAA,EACtE,kBAA8B,iCAAqB,SAAS,EAAE,SAAS;AAAA,EACvE,cAA0B,uBAAW,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,SAAS;AACjE,CAAC,EAAE,QAAQ;AAEJ,IAAM,4BAAwC,sBAAU;AAAA,EAC7D,IAAgB,yBAAa,QAAQ;AAAA,EACrC,eAA2B,+BAAmB,SAAS,EAAE,QAAQ;AAAA,EACjE,wBAAoC,uCAA2B,QAAQ;AAAA,EACvE,4BAAwC,0CAA8B,QAAQ;AAAA,EAC9E,cAA0B,kCAAsB,SAAS,EAAE,QAAQ;AAAA,EACnE,eAAe,uCAA0B,SAAS,EAAE,QAAQ;AAAA,EAC5D,kBAA8B,iCAAqB,SAAS,EAAE,QAAQ;AAAA,EACtE,mBAA+B,kCAAsB,SAAS,EAAE,QAAQ;AAAA,EACxE,qBAAiC,mCAAuB,QAAQ;AAAA,EAChE,cAA0B,kCAAsB,QAAQ;AAAA,EACxD,kBAA8B,qCAAyB,QAAQ;AAAA,EAC/D,sBAAkC,qCAAyB,QAAQ;AAAA,EACnE,iBAA6B;AAAA,EAC7B,2BAAuC;AAAA,EACvC,iBAA6B;AAAA,EAC7B,uBAAmC,yCAA6B,YAAY,EAAE,QAAQ;AAAA,EACtF,cAA0B,uBAAW,EAAE,QAAQ;AAAA,EAE/C,iBAA6B,qBAAqB,sBAAU;AAAA,IAC1D,IAAgB,sBAAU,EAAE,QAAQ;AAAA,IACpC,YAAwB,sBAAU,EAAE,QAAQ;AAAA,IAC5C,OAAmB,sBAAU,EAAE,SAAS;AAAA,EAC1C,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,KAAK,EAAE,CAAC;AAAA;AAAA;AAAA;AAAA,EAK/D,iBAA6B,uBAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,MAAM,aAAa,4KAA4K,cAAc,KAAK,EAAE,CAAC;AAAA;AAAA;AAAA;AAAA,EAIxS,mBAA+B,uBAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,MAAM,aAAa,2DAA2D,cAAc,MAAM,EAAE,CAAC;AAC5L,CAAC,EAAE,QAAQ;AAEJ,IAAM,8BAA8B,4BAA4B,KAAK,CAAC,kBAAkB,CAAC,EAAE,OAAmB,sBAAU;AAAA,EAC7H,iBAA6B,qBAAqB,sBAAU;AAAA,IAC1D,IAAgB,sBAAU,EAAE,QAAQ;AAAA,IACpC,YAAwB,sBAAU,EAAE,QAAQ;AAAA,IAC5C,OAAmB,sBAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,IAAI;AAAA,EAClE,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,KAAK,EAAE,CAAC;AAAA,EAChE,cAA0B,uBAAW,EAAE,SAAS;AAClD,CAAC,EAAE,QAAQ,CAAC;AAEL,IAAM,8BAA0C,qBAAS;AAEzD,IAAM,gBAAY,wBAAW;AAAA,EAClC,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,MAAM;AAAA,IACJ,cAAc;AAAA,MACZ,MAAM,CAAC,OAAO;AAAA,MACd,SAAS;AAAA,MACT,aAAa;AAAA,IACf;AAAA,IACA,YAAY;AAAA,MACV,MAAM,CAAC,OAAO;AAAA,MACd,SAAS;AAAA,MACT,aAAa;AAAA,IACf;AAAA,IACA,cAAc;AAAA,MACZ,MAAM,CAAC,OAAO;AAAA,MACd,SAAS;AAAA,MACT,aAAa;AAAA,IACf;AAAA,IACA,cAAc;AAAA,MACZ,MAAM,CAAC,OAAO;AAAA,MACd,SAAS;AAAA,MACT,aAAa;AAAA,IACf;AAAA,IACA,YAAY;AAAA,MACV,MAAM,CAAC,OAAO;AAAA,MACd,SAAS;AAAA,MACT,aAAa;AAAA,IACf;AAAA,EACF;AACF,CAAC;AAGM,IAAM,0BAA0B;AAAA,EACrC,MAAM;AAAA,EACN,QAAQ,UAAU,OAAO;AAAA,EACzB,UAAU;AAAA,IACR,SAAS;AAAA,IACT,aAAa;AAAA,IACb,MAAM,CAAC,OAAO;AAAA,EAChB;AACF;AAEO,IAAM,0BAA0B;AAAA,EACrC,MAAM;AAAA,EACN,QAAQ,UAAU,OAAO;AAAA,EACzB,UAAU;AAAA,IACR,SAAS;AAAA,IACT,aAAa;AAAA,IACb,MAAM,CAAC,OAAO;AAAA,EAChB;AACF;AAEA,IAAM,2BAAuC,sBAAU;AAAA,EACrD,IAAgB,yBAAa,QAAQ;AAAA,EACrC,OAAmB,qBAAqB,sBAAU;AAAA,IAChD,IAAgB,sBAAU,EAAE,QAAQ;AAAA,EACtC,CAAC,CAAC,EAAE,QAAQ;AACd,CAAC,EAAE,QAAQ;AAEJ,IAAM,0BAA0B;AAAA,EACrC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,UAAU;AAAA,IACR,SAAS;AAAA,IACT,aAAa;AAAA,IACb,MAAM,CAAC,OAAO;AAAA,EAChB;AACF;", "names": []}