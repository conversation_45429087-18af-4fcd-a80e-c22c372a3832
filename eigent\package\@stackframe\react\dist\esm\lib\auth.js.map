{"version": 3, "sources": ["../../../src/lib/auth.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { KnownError, StackClientInterface } from \"@stackframe/stack-shared\";\nimport { InternalSession } from \"@stackframe/stack-shared/dist/sessions\";\nimport { StackAssertionError, throwErr } from \"@stackframe/stack-shared/dist/utils/errors\";\nimport { neverResolve } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { Result } from \"@stackframe/stack-shared/dist/utils/results\";\nimport { deindent } from \"@stackframe/stack-shared/dist/utils/strings\";\nimport { constructRedirectUrl } from \"../utils/url\";\nimport { consumeVerifierAndStateCookie, saveVerifierAndState } from \"./cookie\";\n\nexport async function signInWithOAuth(\n  iface: StackClientInterface,\n  options: {\n    provider: string,\n    redirectUrl: string,\n    errorRedirectUrl: string,\n    providerScope?: string,\n  }\n) {\n  const { codeChallenge, state } = await saveVerifierAndState();\n  const location = await iface.getOAuthUrl({\n    provider: options.provider,\n    redirectUrl: constructRedirectUrl(options.redirectUrl, \"redirectUrl\"),\n    errorRedirectUrl: constructRedirectUrl(options.errorRedirectUrl, \"errorRedirectUrl\"),\n    codeChallenge,\n    state,\n    type: \"authenticate\",\n    providerScope: options.providerScope,\n  });\n  window.location.assign(location);\n  await neverResolve();\n}\n\nexport async function addNewOAuthProviderOrScope(\n  iface: StackClientInterface,\n  options: {\n    provider: string,\n    redirectUrl: string,\n    errorRedirectUrl: string,\n    providerScope?: string,\n  },\n  session: InternalSession,\n) {\n  const { codeChallenge, state } = await saveVerifierAndState();\n  const location = await iface.getOAuthUrl({\n    provider: options.provider,\n    redirectUrl: constructRedirectUrl(options.redirectUrl, \"redirectUrl\"),\n    errorRedirectUrl: constructRedirectUrl(options.errorRedirectUrl, \"errorRedirectUrl\"),\n    afterCallbackRedirectUrl: constructRedirectUrl(window.location.href, \"afterCallbackRedirectUrl\"),\n    codeChallenge,\n    state,\n    type: \"link\",\n    session,\n    providerScope: options.providerScope,\n  });\n  window.location.assign(location);\n  await neverResolve();\n}\n\n/**\n * Checks if the current URL has the query parameters for an OAuth callback, and if so, removes them.\n *\n * Must be synchronous for the logic in callOAuthCallback to work without race conditions.\n */\nfunction consumeOAuthCallbackQueryParams() {\n  const requiredParams = [\"code\", \"state\"];\n  const originalUrl = new URL(window.location.href);\n  for (const param of requiredParams) {\n    if (!originalUrl.searchParams.has(param)) {\n      console.warn(new Error(`Missing required query parameter on OAuth callback: ${param}. Maybe you opened or reloaded the oauth-callback page from your history?`));\n      return null;\n    }\n  }\n\n  const expectedState = originalUrl.searchParams.get(\"state\") ?? throwErr(\"This should never happen; isn't state required above?\");\n  const cookieResult = consumeVerifierAndStateCookie(expectedState);\n\n  if (!cookieResult) {\n    // If the state can't be found in the cookies, then the callback wasn't meant for us.\n    // Maybe the website uses another OAuth library?\n    console.warn(deindent`\n      Stack found an outer OAuth callback state in the query parameters, but not in cookies.\n      \n      This could have multiple reasons:\n        - The cookie expired, because the OAuth flow took too long.\n        - The user's browser deleted the cookie, either manually or because of a very strict cookie policy.\n        - The cookie was already consumed by this page, and the user already logged in.\n        - You are using another OAuth client library with the same callback URL as Stack.\n        - The user opened the OAuth callback page from their history.\n\n      Either way, it is probably safe to ignore this warning unless you are debugging an OAuth issue.\n    `);\n    return null;\n  }\n\n\n  const newUrl = new URL(originalUrl);\n  for (const param of requiredParams) {\n    newUrl.searchParams.delete(param);\n  }\n\n  // let's get rid of the authorization code in the history as we\n  // don't redirect to `redirectUrl` if there's a validation error\n  // (as the redirectUrl might be malicious!).\n  //\n  // We use history.replaceState instead of location.assign(...) to\n  // prevent an unnecessary reload\n  window.history.replaceState({}, \"\", newUrl.toString());\n\n  return {\n    originalUrl,\n    codeVerifier: cookieResult.codeVerifier,\n    state: expectedState,\n  };\n}\n\nexport async function callOAuthCallback(\n  iface: StackClientInterface,\n  redirectUrl: string,\n) {\n  // note: this part of the function (until the return) needs\n  // to be synchronous, to prevent race conditions when\n  // callOAuthCallback is called multiple times in parallel\n  const consumed = consumeOAuthCallbackQueryParams();\n  if (!consumed) return Result.ok(undefined);\n\n  // the rest can be asynchronous (we now know that we are the\n  // intended recipient of the callback, and the only instance\n  // of callOAuthCallback that's running)\n  try {\n    return Result.ok(await iface.callOAuthCallback({\n      oauthParams: consumed.originalUrl.searchParams,\n      redirectUri: constructRedirectUrl(redirectUrl, \"redirectUri\"),\n      codeVerifier: consumed.codeVerifier,\n      state: consumed.state,\n    }));\n  } catch (e) {\n    if (KnownError.isKnownError(e)) {\n      throw e;\n    }\n    throw new StackAssertionError(\"Error signing in during OAuth callback. Please try again.\", { cause: e });\n  }\n}\n"], "mappings": ";AAIA,SAAS,kBAAwC;AAEjD,SAAS,qBAAqB,gBAAgB;AAC9C,SAAS,oBAAoB;AAC7B,SAAS,cAAc;AACvB,SAAS,gBAAgB;AACzB,SAAS,4BAA4B;AACrC,SAAS,+BAA+B,4BAA4B;AAEpE,eAAsB,gBACpB,OACA,SAMA;AACA,QAAM,EAAE,eAAe,MAAM,IAAI,MAAM,qBAAqB;AAC5D,QAAM,WAAW,MAAM,MAAM,YAAY;AAAA,IACvC,UAAU,QAAQ;AAAA,IAClB,aAAa,qBAAqB,QAAQ,aAAa,aAAa;AAAA,IACpE,kBAAkB,qBAAqB,QAAQ,kBAAkB,kBAAkB;AAAA,IACnF;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,eAAe,QAAQ;AAAA,EACzB,CAAC;AACD,SAAO,SAAS,OAAO,QAAQ;AAC/B,QAAM,aAAa;AACrB;AAEA,eAAsB,2BACpB,OACA,SAMA,SACA;AACA,QAAM,EAAE,eAAe,MAAM,IAAI,MAAM,qBAAqB;AAC5D,QAAM,WAAW,MAAM,MAAM,YAAY;AAAA,IACvC,UAAU,QAAQ;AAAA,IAClB,aAAa,qBAAqB,QAAQ,aAAa,aAAa;AAAA,IACpE,kBAAkB,qBAAqB,QAAQ,kBAAkB,kBAAkB;AAAA,IACnF,0BAA0B,qBAAqB,OAAO,SAAS,MAAM,0BAA0B;AAAA,IAC/F;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA,eAAe,QAAQ;AAAA,EACzB,CAAC;AACD,SAAO,SAAS,OAAO,QAAQ;AAC/B,QAAM,aAAa;AACrB;AAOA,SAAS,kCAAkC;AACzC,QAAM,iBAAiB,CAAC,QAAQ,OAAO;AACvC,QAAM,cAAc,IAAI,IAAI,OAAO,SAAS,IAAI;AAChD,aAAW,SAAS,gBAAgB;AAClC,QAAI,CAAC,YAAY,aAAa,IAAI,KAAK,GAAG;AACxC,cAAQ,KAAK,IAAI,MAAM,uDAAuD,KAAK,2EAA2E,CAAC;AAC/J,aAAO;AAAA,IACT;AAAA,EACF;AAEA,QAAM,gBAAgB,YAAY,aAAa,IAAI,OAAO,KAAK,SAAS,uDAAuD;AAC/H,QAAM,eAAe,8BAA8B,aAAa;AAEhE,MAAI,CAAC,cAAc;AAGjB,YAAQ,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAWZ;AACD,WAAO;AAAA,EACT;AAGA,QAAM,SAAS,IAAI,IAAI,WAAW;AAClC,aAAW,SAAS,gBAAgB;AAClC,WAAO,aAAa,OAAO,KAAK;AAAA,EAClC;AAQA,SAAO,QAAQ,aAAa,CAAC,GAAG,IAAI,OAAO,SAAS,CAAC;AAErD,SAAO;AAAA,IACL;AAAA,IACA,cAAc,aAAa;AAAA,IAC3B,OAAO;AAAA,EACT;AACF;AAEA,eAAsB,kBACpB,OACA,aACA;AAIA,QAAM,WAAW,gCAAgC;AACjD,MAAI,CAAC,SAAU,QAAO,OAAO,GAAG,MAAS;AAKzC,MAAI;AACF,WAAO,OAAO,GAAG,MAAM,MAAM,kBAAkB;AAAA,MAC7C,aAAa,SAAS,YAAY;AAAA,MAClC,aAAa,qBAAqB,aAAa,aAAa;AAAA,MAC5D,cAAc,SAAS;AAAA,MACvB,OAAO,SAAS;AAAA,IAClB,CAAC,CAAC;AAAA,EACJ,SAAS,GAAG;AACV,QAAI,WAAW,aAAa,CAAC,GAAG;AAC9B,YAAM;AAAA,IACR;AACA,UAAM,IAAI,oBAAoB,6DAA6D,EAAE,OAAO,EAAE,CAAC;AAAA,EACzG;AACF;", "names": []}