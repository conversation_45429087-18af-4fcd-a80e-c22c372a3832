{"version": 3, "sources": ["../../../../../src/lib/stack-app/email-templates/index.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { EmailTemplateCrud, EmailTemplateType } from \"@stackframe/stack-shared/dist/interface/crud/email-templates\";\n\n\nexport type AdminEmailTemplate = {\n  type: EmailTemplateType,\n  subject: string,\n  content: any,\n  isDefault: boolean,\n}\n\nexport type AdminEmailTemplateUpdateOptions = {\n  subject?: string,\n  content?: any,\n};\nexport function adminEmailTemplateUpdateOptionsToCrud(options: AdminEmailTemplateUpdateOptions): EmailTemplateCrud['Admin']['Update'] {\n  return {\n    subject: options.subject,\n    content: options.content,\n  };\n}\n"], "mappings": ";AAkBO,SAAS,sCAAsC,SAAgF;AACpI,SAAO;AAAA,IACL,SAAS,QAAQ;AAAA,IACjB,SAAS,QAAQ;AAAA,EACnB;AACF;", "names": []}