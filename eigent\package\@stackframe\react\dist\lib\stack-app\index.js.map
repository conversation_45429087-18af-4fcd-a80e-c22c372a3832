{"version": 3, "sources": ["../../../src/lib/stack-app/index.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nexport {\n  StackAdminApp, StackClientApp,\n  StackServerApp\n} from \"./apps\";\nexport type {\n  StackAdminAppConstructor,\n  StackAdminAppConstructorOptions,\n  StackClientAppConstructor,\n  StackClientAppConstructorOptions,\n  StackClientAppJson,\n  StackServerAppConstructor,\n  StackServerAppConstructorOptions\n} from \"./apps\";\n\nexport type {\n  ProjectConfig\n} from \"./project-configs\";\n\nexport type {\n  InternalApiKey,\n  InternalApiKeyBase,\n  InternalApiKeyBaseCrudRead,\n  InternalApiKeyCreateOptions,\n  InternalApiKeyFirstView\n} from \"./internal-api-keys\";\n\nexport {\n  stackAppInternalsSymbol\n} from \"./common\";\nexport type {\n  GetUserOptions,\n  HandlerUrls,\n  OAuthScopesOnSignIn\n} from \"./common\";\n\nexport type {\n  Connection,\n  OAuthConnection\n} from \"./connected-accounts\";\n\nexport type {\n  ContactChannel,\n  ServerContactChannel\n} from \"./contact-channels\";\n\nexport type {\n  AdminSentEmail\n} from \"./email\";\n\nexport type {\n  AdminTeamPermission,\n  AdminTeamPermissionDefinition,\n  AdminTeamPermissionDefinitionCreateOptions,\n  AdminTeamPermissionDefinitionUpdateOptions,\n  AdminProjectPermission,\n  AdminProjectPermissionDefinition,\n  AdminProjectPermissionDefinitionCreateOptions,\n  AdminProjectPermissionDefinitionUpdateOptions,\n} from \"./permissions\";\n\nexport type {\n  AdminDomainConfig,\n  AdminEmailConfig,\n  AdminOAuthProviderConfig,\n  AdminProjectConfig,\n  AdminProjectConfigUpdateOptions,\n  OAuthProviderConfig\n} from \"./project-configs\";\n\nexport type {\n  AdminOwnedProject,\n  AdminProject,\n  AdminProjectCreateOptions,\n  AdminProjectUpdateOptions,\n  Project\n} from \"./projects\";\n\nexport type {\n  EditableTeamMemberProfile,\n  ServerListUsersOptions,\n  ServerTeam,\n  ServerTeamCreateOptions,\n  ServerTeamMemberProfile,\n  ServerTeamUpdateOptions,\n  ServerTeamUser,\n  Team,\n  TeamCreateOptions,\n  TeamInvitation,\n  TeamMemberProfile,\n  TeamUpdateOptions,\n  TeamUser\n} from \"./teams\";\n\nexport type {\n  Auth,\n  CurrentInternalServerUser,\n  CurrentInternalUser,\n  CurrentServerUser,\n  CurrentUser,\n  ServerUser,\n  Session,\n  User\n} from \"./users\";\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,kBAGO;AAuBP,oBAEO;", "names": []}