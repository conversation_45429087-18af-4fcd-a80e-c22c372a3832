{"version": 3, "sources": ["../../src/index.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nexport * from './lib/stack-app';\n\nexport { default as StackHandler } from \"./components-page/stack-handler\";\nexport { useStackApp, useUser } from \"./lib/hooks\";\nexport { default as StackProvider } from \"./providers/stack-provider\";\nexport { StackTheme } from './providers/theme-provider';\n\nexport { AccountSettings } from \"./components-page/account-settings\";\nexport { AuthPage } from \"./components-page/auth-page\";\nexport { EmailVerification } from \"./components-page/email-verification\";\nexport { ForgotPassword } from \"./components-page/forgot-password\";\nexport { PasswordReset } from \"./components-page/password-reset\";\nexport { SignIn } from \"./components-page/sign-in\";\nexport { SignUp } from \"./components-page/sign-up\";\nexport { CredentialSignIn as CredentialSignIn } from \"./components/credential-sign-in\";\nexport { CredentialSignUp as CredentialSignUp } from \"./components/credential-sign-up\";\nexport { UserAvatar } from \"./components/elements/user-avatar\";\nexport { MagicLinkSignIn as MagicLinkSignIn } from \"./components/magic-link-sign-in\";\nexport { MessageCard } from \"./components/message-cards/message-card\";\nexport { OAuthButton } from \"./components/oauth-button\";\nexport { OAuthButtonGroup } from \"./components/oauth-button-group\";\nexport { SelectedTeamSwitcher } from \"./components/selected-team-switcher\";\nexport { UserButton } from \"./components/user-button\";\nexport { CliAuthConfirmation } from \"./components-page/cli-auth-confirm\";\n"], "mappings": ";AAIA,cAAc;AAEd,SAAoB,WAAXA,gBAA+B;AACxC,SAAS,aAAa,eAAe;AACrC,SAAoB,WAAXA,gBAAgC;AACzC,SAAS,kBAAkB;AAE3B,SAAS,uBAAuB;AAChC,SAAS,gBAAgB;AACzB,SAAS,yBAAyB;AAClC,SAAS,sBAAsB;AAC/B,SAAS,qBAAqB;AAC9B,SAAS,cAAc;AACvB,SAAS,cAAc;AACvB,SAA6B,wBAAwB;AACrD,SAA6B,wBAAwB;AACrD,SAAS,kBAAkB;AAC3B,SAA4B,uBAAuB;AACnD,SAAS,mBAAmB;AAC5B,SAAS,mBAAmB;AAC5B,SAAS,wBAAwB;AACjC,SAAS,4BAA4B;AACrC,SAAS,kBAAkB;AAC3B,SAAS,2BAA2B;", "names": ["default"]}