{"name": "Eigent", "version": "0.0.37", "main": "dist-electron/main/index.js", "description": "Eigent", "author": "eigent", "license": "MIT", "private": true, "debug": {"env": {"VITE_DEV_SERVER_URL": "http://127.0.0.1:7777/"}}, "type": "module", "scripts": {"compile-babel": "cd backend && pybabel compile -d lang", "dev": "npm run compile-babel && vite", "build": "npm run compile-babel && tsc && vite build &&  electron-builder -- --publish always", "build:mac": "npm run compile-babel && tsc && vite build && electron-builder --mac", "build:win": "npm run compile-babel && tsc && vite build && electron-builder --win", "build:all": "npm run compile-babel && tsc && vite build && electron-builder --mac --win", "preview": "vite preview", "pretest": "vite build --mode=test", "test": "vitest run"}, "dependencies": {"@electron/notarize": "^2.5.0", "@fontsource/inter": "^5.2.5", "@microsoft/fetch-event-source": "^2.0.1", "@monaco-editor/loader": "^1.5.0", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@stackframe/react": "file:package/@stackframe/react", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "@xyflow/react": "^12.6.4", "adm-zip": "^0.5.16", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "csv-parser": "^3.2.0", "electron-log": "^5.4.0", "electron-updater": "^6.3.9", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.17.0", "gsap": "^3.13.0", "lodash-es": "^4.17.21", "lottie-web": "^5.13.0", "lucide-react": "^0.509.0", "mammoth": "^1.9.1", "monaco-editor": "^0.52.2", "next-themes": "^0.4.6", "papaparse": "^5.5.3", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "tar": "^7.4.3", "tree-kill": "^1.2.2", "tw-animate-css": "^1.2.9", "unzipper": "^0.12.3", "xml2js": "^0.6.2", "zustand": "^5.0.4"}, "devDependencies": {"@playwright/test": "^1.48.2", "@types/lodash-es": "^4.17.12", "@types/papaparse": "^5.3.16", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/unzipper": "^0.10.11", "@types/xml2js": "^0.4.14", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "electron": "^33.2.0", "electron-builder": "^24.13.3", "postcss": "^8.4.49", "postcss-import": "^16.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwindcss": "^3.4.15", "typescript": "^5.4.2", "vite": "^5.4.11", "vite-plugin-electron": "^0.29.0", "vite-plugin-electron-renderer": "^0.14.6", "vitest": "^2.1.5"}}