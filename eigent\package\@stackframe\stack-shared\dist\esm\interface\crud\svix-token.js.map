{"version": 3, "sources": ["../../../../src/interface/crud/svix-token.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport { yupObject, yupString } from \"../../schema-fields\";\n\nexport const svixTokenAdminReadSchema = yupObject({\n  token: yupString().defined(),\n}).defined();\n\nexport const svixTokenAdminCreateSchema = yupObject({}).defined();\n\nexport const svixTokenCrud = createCrud({\n  adminReadSchema: svixTokenAdminReadSchema,\n  adminCreateSchema: svixTokenAdminCreateSchema,\n  docs: {\n    adminCreate: {\n      hidden: true,\n    },\n  }\n});\nexport type SvixTokenCrud = CrudTypeOf<typeof svixTokenCrud>;\n"], "mappings": ";AAAA,SAAqB,kBAAkB;AACvC,SAAS,WAAW,iBAAiB;AAE9B,IAAM,2BAA2B,UAAU;AAAA,EAChD,OAAO,UAAU,EAAE,QAAQ;AAC7B,CAAC,EAAE,QAAQ;AAEJ,IAAM,6BAA6B,UAAU,CAAC,CAAC,EAAE,QAAQ;AAEzD,IAAM,gBAAgB,WAAW;AAAA,EACtC,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,MAAM;AAAA,IACJ,aAAa;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,EACF;AACF,CAAC;", "names": []}