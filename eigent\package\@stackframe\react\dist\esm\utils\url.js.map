{"version": 3, "sources": ["../../../src/utils/url.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { StackAssertionError } from \"@stackframe/stack-shared/dist/utils/errors\";\n\n\nexport function constructRedirectUrl(redirectUrl: URL | string | undefined, callbackUrlName: string) {\n  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n  if (typeof window === 'undefined' || !window.location) {\n    throw new StackAssertionError(`${callbackUrlName} option is required in a non-browser environment.`, { redirectUrl });\n  }\n\n  const retainedQueryParams = [\"after_auth_return_to\"];\n  const currentUrl = new URL(window.location.href);\n  const url = redirectUrl ? new URL(redirectUrl, window.location.href) : new URL(window.location.href);\n  for (const param of retainedQueryParams) {\n    if (currentUrl.searchParams.has(param)) {\n      url.searchParams.set(param, currentUrl.searchParams.get(param)!);\n    }\n  }\n  url.hash = \"\";\n  return url.toString();\n}\n"], "mappings": ";AAIA,SAAS,2BAA2B;AAG7B,SAAS,qBAAqB,aAAuC,iBAAyB;AAEnG,MAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU;AACrD,UAAM,IAAI,oBAAoB,GAAG,eAAe,qDAAqD,EAAE,YAAY,CAAC;AAAA,EACtH;AAEA,QAAM,sBAAsB,CAAC,sBAAsB;AACnD,QAAM,aAAa,IAAI,IAAI,OAAO,SAAS,IAAI;AAC/C,QAAM,MAAM,cAAc,IAAI,IAAI,aAAa,OAAO,SAAS,IAAI,IAAI,IAAI,IAAI,OAAO,SAAS,IAAI;AACnG,aAAW,SAAS,qBAAqB;AACvC,QAAI,WAAW,aAAa,IAAI,KAAK,GAAG;AACtC,UAAI,aAAa,IAAI,OAAO,WAAW,aAAa,IAAI,KAAK,CAAE;AAAA,IACjE;AAAA,EACF;AACA,MAAI,OAAO;AACX,SAAO,IAAI,SAAS;AACtB;", "names": []}