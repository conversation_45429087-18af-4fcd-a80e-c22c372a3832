{"version": 3, "sources": ["../../src/components-page/team-creation.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { yupResolver } from \"@hookform/resolvers/yup\";\nimport { yupObject, yupString } from \"@stackframe/stack-shared/dist/schema-fields\";\nimport { runAsynchronously } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { Button, Input, Label, Typography } from \"@stackframe/stack-ui\";\nimport { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport * as yup from \"yup\";\nimport { MessageCard, useStackApp, useUser } from \"..\";\nimport { FormWarningText } from \"../components/elements/form-warning\";\nimport { MaybeFullPage } from \"../components/elements/maybe-full-page\";\nimport { useTranslation } from \"../lib/translations\";\n\nexport function TeamCreation(props: { fullPage?: boolean }) {\n  const { t } = useTranslation();\n\n  const schema = yupObject({\n    displayName: yupString().defined().nonEmpty(t('Please enter a team name')),\n  });\n\n  const { register, handleSubmit, formState: { errors } } = useForm({\n    resolver: yupResolver(schema)\n  });\n  const app = useStackApp();\n  const project = app.useProject();\n  const user = useUser({ or: 'redirect' });\n  const [loading, setLoading] = useState(false);\n  const navigate = app.useNavigate();\n\n  if (!project.config.clientTeamCreationEnabled) {\n    return <MessageCard title={t('Team creation is not enabled')} />;\n  }\n\n  const onSubmit = async (data: yup.InferType<typeof schema>) => {\n    setLoading(true);\n\n    try {\n      const team = await user.createTeam({ displayName: data.displayName });\n      navigate(`${app.urls.handler}/team-settings/${team.id}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <MaybeFullPage fullPage={!!props.fullPage}>\n      <div className='stack-scope flex flex-col items-stretch' style={{ maxWidth: '380px', flexBasis: '380px', padding: props.fullPage ? '1rem' : 0 }}>\n        <div className=\"text-center mb-6\">\n          <Typography type='h2'>\n            {t('Create a Team')}\n          </Typography>\n        </div>\n        <form\n          className=\"flex flex-col items-stretch stack-scope\"\n          onSubmit={e => runAsynchronously(handleSubmit(onSubmit)(e))}\n          noValidate\n        >\n          <Label htmlFor=\"display-name\" className=\"mb-1\">{t('Display name')}</Label>\n          <Input\n            id=\"display-name\"\n            {...register('displayName')}\n          />\n          <FormWarningText text={errors.displayName?.message?.toString()} />\n\n          <Button type=\"submit\" className=\"mt-6\" loading={loading}>\n            {t('Create')}\n          </Button>\n        </form>\n      </div>\n    </MaybeFullPage>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,iBAA4B;AAC5B,2BAAqC;AACrC,sBAAkC;AAClC,sBAAiD;AACjD,mBAAyB;AACzB,6BAAwB;AAExB,eAAkD;AAClD,0BAAgC;AAChC,6BAA8B;AAC9B,0BAA+B;AAmBpB;AAjBJ,SAAS,aAAa,OAA+B;AAC1D,QAAM,EAAE,EAAE,QAAI,oCAAe;AAE7B,QAAM,aAAS,gCAAU;AAAA,IACvB,iBAAa,gCAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,0BAA0B,CAAC;AAAA,EAC3E,CAAC;AAED,QAAM,EAAE,UAAU,cAAc,WAAW,EAAE,OAAO,EAAE,QAAI,gCAAQ;AAAA,IAChE,cAAU,wBAAY,MAAM;AAAA,EAC9B,CAAC;AACD,QAAM,UAAM,sBAAY;AACxB,QAAM,UAAU,IAAI,WAAW;AAC/B,QAAM,WAAO,kBAAQ,EAAE,IAAI,WAAW,CAAC;AACvC,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAS,KAAK;AAC5C,QAAM,WAAW,IAAI,YAAY;AAEjC,MAAI,CAAC,QAAQ,OAAO,2BAA2B;AAC7C,WAAO,4CAAC,wBAAY,OAAO,EAAE,8BAA8B,GAAG;AAAA,EAChE;AAEA,QAAM,WAAW,OAAO,SAAuC;AAC7D,eAAW,IAAI;AAEf,QAAI;AACF,YAAM,OAAO,MAAM,KAAK,WAAW,EAAE,aAAa,KAAK,YAAY,CAAC;AACpE,eAAS,GAAG,IAAI,KAAK,OAAO,kBAAkB,KAAK,EAAE,EAAE;AAAA,IACzD,UAAE;AACA,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AAEA,SACE,4CAAC,wCAAc,UAAU,CAAC,CAAC,MAAM,UAC/B,uDAAC,SAAI,WAAU,2CAA0C,OAAO,EAAE,UAAU,SAAS,WAAW,SAAS,SAAS,MAAM,WAAW,SAAS,EAAE,GAC5I;AAAA,gDAAC,SAAI,WAAU,oBACb,sDAAC,8BAAW,MAAK,MACd,YAAE,eAAe,GACpB,GACF;AAAA,IACA;AAAA,MAAC;AAAA;AAAA,QACC,WAAU;AAAA,QACV,UAAU,WAAK,mCAAkB,aAAa,QAAQ,EAAE,CAAC,CAAC;AAAA,QAC1D,YAAU;AAAA,QAEV;AAAA,sDAAC,yBAAM,SAAQ,gBAAe,WAAU,QAAQ,YAAE,cAAc,GAAE;AAAA,UAClE;AAAA,YAAC;AAAA;AAAA,cACC,IAAG;AAAA,cACF,GAAG,SAAS,aAAa;AAAA;AAAA,UAC5B;AAAA,UACA,4CAAC,uCAAgB,MAAM,OAAO,aAAa,SAAS,SAAS,GAAG;AAAA,UAEhE,4CAAC,0BAAO,MAAK,UAAS,WAAU,QAAO,SACpC,YAAE,QAAQ,GACb;AAAA;AAAA;AAAA,IACF;AAAA,KACF,GACF;AAEJ;", "names": []}