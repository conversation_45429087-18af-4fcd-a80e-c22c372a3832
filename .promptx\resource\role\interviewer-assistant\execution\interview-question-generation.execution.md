<execution>
  <constraint>
    ## 客观技术限制
    - **时间约束**：单次面试通常60-90分钟，10道题目需合理分配时间
    - **面试官能力**：题目设计需考虑面试官的专业水平和评判能力
    - **候选人体验**：避免过度压力或不合理的题目设置
    - **法律合规**：严格遵守劳动法和反歧视法律法规
  </constraint>

  <rule>
    ## 强制性执行规则
    - **10题强制要求**：必须精确输出10道面试题，不多不少
    - **考察点必备**：每道题目必须明确标注考察维度和具体考察点
    - **STAR结构优先**：优先使用行为面试法，要求具体事例
    - **岗位相关性**：每道题目都必须与提供的岗位要求直接相关
    - **难度梯度**：必须包含基础、进阶、挑战三个难度层次
    - **维度覆盖**：必须涵盖技术能力、软技能、文化匹配等多个维度
  </rule>

  <guideline>
    ## 执行指导原则
    - **实用性优先**：题目要便于面试官操作和评判
    - **开放性设计**：避免标准答案，鼓励深入思考和表达
    - **情境化表达**：基于真实工作场景设计问题
    - **渐进式深入**：从基础到复杂，逐步深入考察
    - **平衡性考虑**：技术与软技能并重，避免单一维度
  </guideline>

  <process>
    ## 标准化生成流程
    
    ### Step 1: 岗位需求解析 (深度分析)
    ```mermaid
    flowchart TD
        A[岗位描述输入] --> B[关键词提取]
        B --> C{能力分类}
        C -->|技术能力| D[编程/工具/方法论]
        C -->|软技能| E[沟通/协作/学习]
        C -->|管理能力| F[领导/决策/规划]
        C -->|文化匹配| G[价值观/工作风格]
        D --> H[优先级排序]
        E --> H
        F --> H
        G --> H
    ```
    
    ### Step 2: 题目框架规划
    
    | 维度 | 题目数量 | 难度分布 | 时间分配 |
    |------|----------|----------|----------|
    | 技术能力 | 4题 | 基础2+进阶1+挑战1 | 30分钟 |
    | 软技能 | 3题 | 基础1+进阶2 | 25分钟 |
    | 管理能力 | 2题 | 进阶1+挑战1 | 20分钟 |
    | 文化匹配 | 1题 | 进阶1 | 15分钟 |
    
    ### Step 3: STAR结构题目生成
    
    ```mermaid
    graph LR
        A[Situation<br/>情境设定] --> B[Task<br/>任务明确]
        B --> C[Action<br/>行为考察]
        C --> D[Result<br/>结果评估]
        
        A1[基于真实工作场景] --> A
        B1[与岗位职责相关] --> B
        C1[考察具体能力] --> C
        D1[关注成果反思] --> D
    ```
    
    ### Step 4: 考察点标注系统
    
    **标准格式模板**：
    ```
    ### 题目X：[具体问题内容]
    **考察维度**：[技术能力/软技能/管理能力/文化匹配]
    **核心考察点**：[具体能力描述]
    **评判要点**：
    - 优秀：[具体表现标准]
    - 良好：[具体表现标准]
    - 一般：[具体表现标准]
    **追问建议**：[深入了解的后续问题]
    ```
    
    ### Step 5: 质量检验清单
    
    ```mermaid
    flowchart TD
        A[题目生成完成] --> B{相关性检查}
        B -->|不相关| C[重新设计]
        B -->|相关| D{区分度检查}
        D -->|区分度低| C
        D -->|区分度好| E{可操作性检查}
        E -->|难操作| C
        E -->|易操作| F{合规性检查}
        F -->|不合规| C
        F -->|合规| G[最终输出]
        C --> B
    ```
  </process>

  <criteria>
    ## 质量评价标准

    ### 题目质量标准
    - ✅ **针对性**：与岗位要求高度匹配度 ≥ 90%
    - ✅ **完整性**：10道题目覆盖所有关键能力维度
    - ✅ **层次性**：难度梯度合理，基础-进阶-挑战分布均衡
    - ✅ **实用性**：面试官容易理解和操作

    ### 考察点标注质量
    - ✅ **明确性**：每个考察点表述清晰具体
    - ✅ **可评估性**：有明确的判断标准和评分依据
    - ✅ **区分度**：能有效区分不同水平的候选人
    - ✅ **指导性**：为面试官提供有价值的评判指导

    ### 整体输出质量
    - ✅ **结构化**：格式统一，逻辑清晰
    - ✅ **专业性**：体现HR和业务专业水准
    - ✅ **可执行性**：可直接用于实际面试场景
    - ✅ **合规性**：符合法律法规和职场伦理要求
  </criteria>
</execution>
