{"version": 3, "sources": ["../../../src/hooks/use-hash.tsx"], "sourcesContent": ["import { useSyncExternalStore } from \"react\";\nimport { suspendIfSsr } from \"../utils/react\";\n\nexport const useHash = () => {\n  suspendIfSsr(\"useHash\");\n  return useSyncExternalStore(\n    (onChange) => {\n      const interval = setInterval(() => onChange(), 10);\n      return () => clearInterval(interval);\n    },\n    () => window.location.hash.substring(1)\n  );\n};\n\n"], "mappings": ";AAAA,SAAS,4BAA4B;AACrC,SAAS,oBAAoB;AAEtB,IAAM,UAAU,MAAM;AAC3B,eAAa,SAAS;AACtB,SAAO;AAAA,IACL,CAAC,aAAa;AACZ,YAAM,WAAW,YAAY,MAAM,SAAS,GAAG,EAAE;AACjD,aAAO,MAAM,cAAc,QAAQ;AAAA,IACrC;AAAA,IACA,MAAM,OAAO,SAAS,KAAK,UAAU,CAAC;AAAA,EACxC;AACF;", "names": []}