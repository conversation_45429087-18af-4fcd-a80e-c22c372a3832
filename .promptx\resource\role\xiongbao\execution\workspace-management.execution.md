<execution>
  <constraint>
    ## 基础文件管理约束
    - **专属工作目录**：所有创作文件存储在`E:\python\熊宝创作工坊/`目录下
    - **项目文件夹结构**：每部小说都有独立文件夹，以小说名称命名
    - **长篇内容文件化**：大纲、章节、正文等长篇内容直接保存为文件
    - **文件路径规范**：按照标准路径格式保存文件
  </constraint>

  <rule>
    ## 基础文件操作规则
    - **文件创建**：使用`save-file`工具创建长篇内容文件
    - **标准路径格式**：`E:\python\熊宝创作工坊\[小说名称]\[子目录]\[文件名].md`
    - **文件命名规范**：按照既定格式命名，如`第01章.md`、`总体大纲.md`等
  </rule>
  
  <guideline>
    ## 基础文件管理指导

    ### 项目目录结构
    ```
    E:\python\熊宝创作工坊\[小说名称]\
    ├── 大纲规划\
    │   ├── 总体大纲.md
    │   ├── 章节目录.md
    │   └── 章节细纲.md
    ├── 正文内容\
    │   ├── 第一卷\
    │   │   ├── 第01章.md
    │   │   └── 第02章.md
    │   └── 第二卷\
    └── 设定资料\
        ├── 世界观设定.md
        ├── 人物设定.md
        └── 关系网络.md
    ```

    ### 基础文件创建

    #### 大纲文件
    - 路径：`E:\python\熊宝创作工坊\[小说名称]\大纲规划\总体大纲.md`
    - 使用save-file工具创建

    #### 章节目录文件
    - 路径：`E:\python\熊宝创作工坊\[小说名称]\大纲规划\章节目录.md`
    - 使用save-file工具创建

    #### 章节细纲文件
    - 路径：`E:\python\熊宝创作工坊\[小说名称]\大纲规划\章节细纲.md`
    - 使用save-file工具创建

    #### 正文章节文件
    - 路径：`E:\python\熊宝创作工坊\[小说名称]\正文内容\第X卷\第XX章.md`
    - 使用save-file工具创建

    ### 文件命名规范
    - **章节文件**：`第01章.md`、`第02章.md`（两位数字编号）
    - **大纲文件**：`总体大纲.md`、`章节目录.md`、`章节细纲.md`
    - **设定文件**：`世界观设定.md`、`人物设定.md`、`关系网络.md`
  </guideline>

  <process>
    ## 基础创作流程

    ### 创作文件管理流程
    1. **大纲创作**：使用save-file创建总体大纲文件
    2. **章节目录**：使用save-file创建章节目录文件
    3. **章节细纲**：使用save-file创建章节细纲文件
    4. **正文创作**：使用save-file创建章节正文文件

    ### 文件保存原则
    - 长篇内容直接保存为文件，不在对话中完整展示
    - 按照标准路径格式保存所有文件
    - 使用规范的文件命名格式
  </process>

  <criteria>
    ## 基础文件管理标准

    ### 文件组织标准
    - **目录结构清晰**：按照标准结构组织文件
    - **命名规范统一**：文件名称符合既定标准
    - **路径格式正确**：使用标准的文件路径格式

    ### 文件操作标准
    - **工具使用正确**：正确使用save-file工具创建文件
    - **内容保存完整**：长篇内容完整保存到文件中
    - **格式规范统一**：文件格式和编码保持一致
  </criteria>
</execution>
