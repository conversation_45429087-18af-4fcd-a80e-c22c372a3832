/* 现代简约风格 - 突出特点：几何感强、色彩对比明显、留白充足 */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.7;
  color: #333;
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background-color: #fff;
}

h1 {
  font-size: 3rem;
  font-weight: 800;
  color: #000;
  margin: 2rem 0;
  letter-spacing: -0.05em;
  line-height: 1.1;
  position: relative;
  padding-bottom: 1rem;
}

h1::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 6px;
  background-color: #000;
}

h2 {
  font-size: 2.2rem;
  font-weight: 700;
  color: #000;
  margin-top: 3rem;
  margin-bottom: 1.5rem;
  letter-spacing: -0.03em;
  line-height: 1.2;
  position: relative;
}

h2 .content {
  position: relative;
  z-index: 1;
}

h2::before {
  content: "";
  position: absolute;
  left: -10px;
  top: 0.6em;
  width: 30px;
  height: 12px;
  background-color: #ff5252;
  z-index: 0;
}

h3 {
  font-size: 1.6rem;
  font-weight: 600;
  color: #333;
  margin-top: 2rem;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

h3 .content {
  display: inline-block;
  border-bottom: 2px solid #ff5252;
  padding-bottom: 3px;
}

p {
  margin-bottom: 1.5rem;
  font-size: 1.05rem;
  line-height: 1.7;
}

a {
  color: #ff5252;
  text-decoration: none;
  font-weight: 500;
  position: relative;
  transition: all 0.2s;
}

a:hover {
  color: #d32f2f;
}

a::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 1px;
  bottom: -2px;
  left: 0;
  background-color: #ff5252;
  visibility: hidden;
  transform: scaleX(0);
  transition: all 0.3s ease-in-out;
}

a:hover::after {
  visibility: visible;
  transform: scaleX(1);
}

blockquote {
  margin: 2rem 0;
  padding: 1.5rem 2rem;
  background-color: #f8f8f8;
  border-left: 6px solid #ff5252;
  font-style: italic;
  color: #555;
  font-size: 1.1rem;
}

code {
  background: #f0f0f0;
  font-family: 'SF Mono', 'Menlo', monospace;
  font-size: 0.9em;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  color: #d32f2f;
}

ul, ol {
  margin: 1.5rem 0;
  padding-left: 1.5rem;
}

ul {
  list-style-type: none;
}

ul li {
  margin-bottom: 0.8rem;
  position: relative;
  padding-left: 1.5rem;
}

ul li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: #ff5252;
  font-weight: bold;
  font-size: 1.2em;
}

ol li {
  margin-bottom: 0.8rem;
  padding-left: 0.5rem;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 2rem auto;
  border-radius: 5px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  border-radius: 10px;
}

th, td {
  padding: 1rem;
  text-align: left;
}

th {
  background-color: #ff5252;
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-size: 0.9rem;
}

tr:nth-child(even) {
  background-color: #f9f9f9;
}

tr:hover {
  background-color: #f0f0f0;
}

/* 强调元素 */
.highlight-word {
  background-color: #ffeeee;
  padding: 0 3px;
  border-radius: 3px;
  font-weight: 600;
  color: #ff5252;
}