<execution>
  <constraint>
    ## Python编码标准的客观限制
    - **PEP规范约束**：必须严格遵循Python Enhancement Proposals
    - **版本兼容性**：需要考虑不同Python版本的特性差异
    - **第三方库依赖**：依赖库的版本兼容性和安全性
    - **平台差异性**：Windows、Linux、macOS的环境差异
  </constraint>

  <rule>
    ## Python编码强制规则
    
    ### 代码格式规范
    - **缩进**：使用4个空格，禁止使用Tab
    - **行长度**：每行不超过88字符（black默认）
    - **空行**：类定义前后2个空行，函数定义前后1个空行
    - **导入**：每行一个导入，按标准库、第三方库、本地模块分组
    
    ### 命名规范
    ```python
    # 变量和函数：snake_case
    user_name = "example"
    def get_user_info():
        pass
    
    # 类名：PascalCase
    class UserManager:
        pass
    
    # 常量：UPPER_CASE
    MAX_RETRY_COUNT = 3
    
    # 私有属性：前缀下划线
    class Example:
        def __init__(self):
            self._private_var = None
            self.__very_private = None
    ```
    
    ### 类型提示规范
    ```python
    from typing import List, Dict, Optional, Union
    
    def process_data(
        items: List[str], 
        config: Dict[str, Any],
        timeout: Optional[int] = None
    ) -> Union[str, None]:
        """处理数据的函数示例"""
        pass
    ```
  </rule>

  <guideline>
    ## Python最佳实践指导
    
    ### 异常处理
    ```python
    # 好的做法
    try:
        result = risky_operation()
    except SpecificException as e:
        logger.error(f"操作失败: {e}")
        return None
    except Exception as e:
        logger.error(f"未预期的错误: {e}")
        raise
    
    # 避免的做法
    try:
        result = risky_operation()
    except:  # 裸露的except
        pass
    ```
    
    ### 文档字符串规范
    ```python
    def calculate_distance(point1: tuple, point2: tuple) -> float:
        """计算两点之间的欧几里得距离
        
        Args:
            point1: 第一个点的坐标 (x, y)
            point2: 第二个点的坐标 (x, y)
            
        Returns:
            两点之间的距离
            
        Raises:
            ValueError: 当输入不是有效坐标时
            
        Example:
            >>> calculate_distance((0, 0), (3, 4))
            5.0
        """
        pass
    ```
    
    ### 上下文管理器使用
    ```python
    # 文件操作
    with open('file.txt', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 数据库连接
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM users")
    ```
  </guideline>

  <process>
    ## Python代码质量检查流程
    
    ### 静态分析工具链
    ```mermaid
    flowchart TD
        A[代码编写] --> B[black格式化]
        B --> C[isort导入排序]
        C --> D[flake8语法检查]
        D --> E[pylint质量分析]
        E --> F[mypy类型检查]
        F --> G[bandit安全扫描]
        G --> H[pytest测试运行]
        H --> I[coverage覆盖率检查]
    ```
    
    ### 工具配置示例
    ```toml
    # pyproject.toml
    [tool.black]
    line-length = 88
    target-version = ['py38']
    
    [tool.isort]
    profile = "black"
    multi_line_output = 3
    
    [tool.pylint]
    max-line-length = 88
    disable = ["C0114", "C0116"]
    
    [tool.mypy]
    python_version = "3.8"
    warn_return_any = true
    warn_unused_configs = true
    ```
  </process>

  <criteria>
    ## 代码质量评估标准
    
    ### 自动化检查通过标准
    - ✅ black格式化：无格式问题
    - ✅ flake8检查：无语法和风格错误
    - ✅ pylint评分：≥8.0分
    - ✅ mypy检查：无类型错误
    - ✅ bandit扫描：无高危安全问题
    - ✅ 测试覆盖率：≥80%
    
    ### 代码审查检查点
    - ✅ 函数职责单一，长度适中（<50行）
    - ✅ 变量命名清晰，避免缩写
    - ✅ 注释和文档完整准确
    - ✅ 错误处理完善
    - ✅ 性能考虑合理
    - ✅ 安全性措施到位
  </criteria>
</execution>
