{"version": 3, "sources": ["../../../../src/interface/crud/oauth.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport { yupObject, yupString } from \"../../schema-fields\";\n\nexport const connectedAccountAccessTokenReadSchema = yupObject({\n  access_token: yupString().defined(),\n}).defined();\n\nexport const connectedAccountAccessTokenCreateSchema = yupObject({\n  scope: yupString().optional(),\n}).defined();\n\nexport const connectedAccountAccessTokenCrud = createCrud({\n  clientReadSchema: connectedAccountAccessTokenReadSchema,\n  clientCreateSchema: connectedAccountAccessTokenCreateSchema,\n  docs: {\n    clientCreate: {\n      hidden: true,\n    }\n  },\n});\nexport type ConnectedAccountAccessTokenCrud = CrudTypeOf<typeof connectedAccountAccessTokenCrud>;\n"], "mappings": ";AAAA,SAAqB,kBAAkB;AACvC,SAAS,WAAW,iBAAiB;AAE9B,IAAM,wCAAwC,UAAU;AAAA,EAC7D,cAAc,UAAU,EAAE,QAAQ;AACpC,CAAC,EAAE,QAAQ;AAEJ,IAAM,0CAA0C,UAAU;AAAA,EAC/D,OAAO,UAAU,EAAE,SAAS;AAC9B,CAAC,EAAE,QAAQ;AAEJ,IAAM,kCAAkC,WAAW;AAAA,EACxD,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,MAAM;AAAA,IACJ,cAAc;AAAA,MACZ,QAAQ;AAAA,IACV;AAAA,EACF;AACF,CAAC;", "names": []}