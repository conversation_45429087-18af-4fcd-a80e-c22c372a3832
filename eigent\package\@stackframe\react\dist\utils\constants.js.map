{"version": 3, "sources": ["../../src/utils/constants.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nexport const FONT_SIZES = { 'xs': '0.75rem', 'sm': '0.875rem', 'md': '1rem', 'lg': '1.125rem', 'xl': '1.25rem' } as const;\nexport const LINE_HEIGHTS = { 'xs': '1rem', 'sm': '1.25rem', 'md': '1.5rem', 'lg': '1.75rem', 'xl': '2rem' } as const;\nexport const FONT_FAMILY = 'ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"';\nexport const PRIMARY_FONT_COLORS = { 'dark': 'white', 'light': 'black' } as const;\nexport const SECONDARY_FONT_COLORS = { 'dark': '#a8a8a8', 'light': '#737373' } as const;\nexport const SELECTED_BACKGROUND_COLORS = { 'dark': 'rgba(255, 255, 255, 0.1)', 'light': 'rgba(0, 0, 0, 0.04)' } as const;\nexport const LINK_COLORS = { 'dark': '#fff', 'light': '#000' } as const;\nexport const SHADOW = '0 1px 2px 0 rgba(0, 0, 0, 0.05)';\n\nexport const DEFAULT_THEME = {\n  light: {\n    background: 'hsl(0 0% 100%)',\n    foreground: 'hsl(240 10% 3.9%)',\n    card: 'hsl(0 0% 100%)',\n    cardForeground: 'hsl(240 10% 3.9%)',\n    popover: 'hsl(0 0% 100%)',\n    popoverForeground: 'hsl(240 10% 3.9%)',\n    primary: 'hsl(240 5.9% 10%)',\n    primaryForeground: 'hsl(0 0% 98%)',\n    secondary: 'hsl(240 4.8% 95.9%)',\n    secondaryForeground: 'hsl(240 5.9% 10%)',\n    muted: 'hsl(240 4.8% 95.9%)',\n    mutedForeground: 'hsl(240 3.8% 46.1%)',\n    accent: 'hsl(240 4.8% 95.9%)',\n    accentForeground: 'hsl(240 5.9% 10%)',\n    destructive: 'hsl(0 84.2% 60.2%)',\n    destructiveForeground: 'hsl(0 0% 98%)',\n    border: 'hsl(240 5.9% 90%)',\n    input: 'hsl(240 5.9% 90%)',\n    ring: 'hsl(240 10% 3.9%)',\n  },\n  dark: {\n    background: 'hsl(240 10% 3.9%)',\n    foreground: 'hsl(0 0% 98%)',\n    card: 'hsl(240 10% 3.9%)',\n    cardForeground: 'hsl(0 0% 98%)',\n    popover: 'hsl(240 10% 3.9%)',\n    popoverForeground: 'hsl(0 0% 98%)',\n    primary: 'hsl(0 0% 98%)',\n    primaryForeground: 'hsl(240 5.9% 10%)',\n    secondary: 'hsl(240 3.7% 15.9%)',\n    secondaryForeground: 'hsl(0 0% 98%)',\n    muted: 'hsl(240 3.7% 15.9%)',\n    mutedForeground: 'hsl(240 5% 64.9%)',\n    accent: 'hsl(240 3.7% 15.9%)',\n    accentForeground: 'hsl(0 0% 98%)',\n    destructive: 'hsl(0 62.8% 50%)',\n    destructiveForeground: 'hsl(0 0% 98%)',\n    border: 'hsl(240 3.7% 15.9%)',\n    input: 'hsl(240 3.7% 15.9%)',\n    ring: 'hsl(240 4.9% 83.9%)',\n  },\n  radius: '0.5rem',\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIO,IAAM,aAAa,EAAE,MAAM,WAAW,MAAM,YAAY,MAAM,QAAQ,MAAM,YAAY,MAAM,UAAU;AACxG,IAAM,eAAe,EAAE,MAAM,QAAQ,MAAM,WAAW,MAAM,UAAU,MAAM,WAAW,MAAM,OAAO;AACpG,IAAM,cAAc;AACpB,IAAM,sBAAsB,EAAE,QAAQ,SAAS,SAAS,QAAQ;AAChE,IAAM,wBAAwB,EAAE,QAAQ,WAAW,SAAS,UAAU;AACtE,IAAM,6BAA6B,EAAE,QAAQ,4BAA4B,SAAS,sBAAsB;AACxG,IAAM,cAAc,EAAE,QAAQ,QAAQ,SAAS,OAAO;AACtD,IAAM,SAAS;AAEf,IAAM,gBAAgB;AAAA,EAC3B,OAAO;AAAA,IACL,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,mBAAmB;AAAA,IACnB,SAAS;AAAA,IACT,mBAAmB;AAAA,IACnB,WAAW;AAAA,IACX,qBAAqB;AAAA,IACrB,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,uBAAuB;AAAA,IACvB,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,mBAAmB;AAAA,IACnB,SAAS;AAAA,IACT,mBAAmB;AAAA,IACnB,WAAW;AAAA,IACX,qBAAqB;AAAA,IACrB,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,uBAAuB;AAAA,IACvB,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,QAAQ;AACV;", "names": []}