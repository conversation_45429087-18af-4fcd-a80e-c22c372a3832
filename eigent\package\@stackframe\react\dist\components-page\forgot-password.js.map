{"version": 3, "sources": ["../../src/components-page/forgot-password.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { yupResolver } from \"@hookform/resolvers/yup\";\nimport { strictEmailSchema, yupObject } from \"@stackframe/stack-shared/dist/schema-fields\";\nimport { runAsynchronouslyWithAlert } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { Button, Input, Label, Typography, cn } from \"@stackframe/stack-ui\";\nimport { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport * as yup from \"yup\";\nimport { useStackApp, useUser } from \"..\";\nimport { FormWarningText } from \"../components/elements/form-warning\";\nimport { MaybeFullPage } from \"../components/elements/maybe-full-page\";\nimport { StyledLink } from \"../components/link\";\nimport { PredefinedMessageCard } from \"../components/message-cards/predefined-message-card\";\nimport { useTranslation } from \"../lib/translations\";\n\nexport function ForgotPasswordForm({ onSent }: { onSent?: () => void }) {\n  const { t } = useTranslation();\n\n  const schema = yupObject({\n    email: strictEmailSchema(t(\"Please enter a valid email\")).defined().nonEmpty(t(\"Please enter your email\"))\n  });\n\n  const { register, handleSubmit, formState: { errors }, clearErrors } = useForm({\n    resolver: yupResolver(schema)\n  });\n  const stackApp = useStackApp();\n  const [loading, setLoading] = useState(false);\n\n  const onSubmit = async (data: yup.InferType<typeof schema>) => {\n    setLoading(true);\n    try {\n      const { email } = data;\n      await stackApp.sendForgotPasswordEmail(email);\n    onSent?.();\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <form\n      className=\"flex flex-col items-stretch stack-scope\"\n      onSubmit={e => runAsynchronouslyWithAlert(handleSubmit(onSubmit)(e))}\n      noValidate\n    >\n      <Label htmlFor=\"email\" className=\"mb-1\">{t(\"Your Email\")}</Label>\n      <Input\n        id=\"email\"\n        type=\"email\"\n        autoComplete=\"email\"\n        {...register('email')}\n        onChange={() => clearErrors('email')}\n      />\n      <FormWarningText text={errors.email?.message?.toString()} />\n\n      <Button type=\"submit\" className=\"mt-6\" loading={loading}>\n        {t(\"Send Email\")}\n      </Button>\n    </form>\n  );\n}\n\n\nexport function ForgotPassword(props: { fullPage?: boolean }) {\n  const { t } = useTranslation();\n  const stackApp = useStackApp();\n  const user = useUser();\n  const [sent, setSent] = useState(false);\n\n  if (user) {\n    return <PredefinedMessageCard type='signedIn' fullPage={!!props.fullPage} />;\n  }\n\n  if (sent) {\n    return <PredefinedMessageCard type='emailSent' fullPage={!!props.fullPage} />;\n  }\n\n  return (\n    <MaybeFullPage fullPage={!!props.fullPage}>\n      <div className={cn(\n        \"stack-scope max-w-[380px] flex-basis-[380px]\",\n        props.fullPage ? \"p-4\" : \"p-0\"\n      )}>\n        <div className=\"text-center\">\n          <Typography type='h2'>{t(\"Reset Your Password\")}</Typography>\n          <Typography>\n            {t(\"Don't need to reset?\")}{\" \"}\n            <StyledLink href={stackApp.urls['signIn']}>\n              {t(\"Sign in\")}\n            </StyledLink>\n          </Typography>\n        </div>\n        <div className=\"mt-6\">\n          <ForgotPasswordForm onSent={() => setSent(true)} />\n        </div>\n      </div>\n    </MaybeFullPage>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,iBAA4B;AAC5B,2BAA6C;AAC7C,sBAA2C;AAC3C,sBAAqD;AACrD,mBAAyB;AACzB,6BAAwB;AAExB,eAAqC;AACrC,0BAAgC;AAChC,6BAA8B;AAC9B,kBAA2B;AAC3B,qCAAsC;AACtC,0BAA+B;AA2B3B;AAzBG,SAAS,mBAAmB,EAAE,OAAO,GAA4B;AACtE,QAAM,EAAE,EAAE,QAAI,oCAAe;AAE7B,QAAM,aAAS,gCAAU;AAAA,IACvB,WAAO,wCAAkB,EAAE,4BAA4B,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,yBAAyB,CAAC;AAAA,EAC3G,CAAC;AAED,QAAM,EAAE,UAAU,cAAc,WAAW,EAAE,OAAO,GAAG,YAAY,QAAI,gCAAQ;AAAA,IAC7E,cAAU,wBAAY,MAAM;AAAA,EAC9B,CAAC;AACD,QAAM,eAAW,sBAAY;AAC7B,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAS,KAAK;AAE5C,QAAM,WAAW,OAAO,SAAuC;AAC7D,eAAW,IAAI;AACf,QAAI;AACF,YAAM,EAAE,MAAM,IAAI;AAClB,YAAM,SAAS,wBAAwB,KAAK;AAC9C,eAAS;AAAA,IACT,UAAE;AACA,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AAEA,SACE;AAAA,IAAC;AAAA;AAAA,MACC,WAAU;AAAA,MACV,UAAU,WAAK,4CAA2B,aAAa,QAAQ,EAAE,CAAC,CAAC;AAAA,MACnE,YAAU;AAAA,MAEV;AAAA,oDAAC,yBAAM,SAAQ,SAAQ,WAAU,QAAQ,YAAE,YAAY,GAAE;AAAA,QACzD;AAAA,UAAC;AAAA;AAAA,YACC,IAAG;AAAA,YACH,MAAK;AAAA,YACL,cAAa;AAAA,YACZ,GAAG,SAAS,OAAO;AAAA,YACpB,UAAU,MAAM,YAAY,OAAO;AAAA;AAAA,QACrC;AAAA,QACA,4CAAC,uCAAgB,MAAM,OAAO,OAAO,SAAS,SAAS,GAAG;AAAA,QAE1D,4CAAC,0BAAO,MAAK,UAAS,WAAU,QAAO,SACpC,YAAE,YAAY,GACjB;AAAA;AAAA;AAAA,EACF;AAEJ;AAGO,SAAS,eAAe,OAA+B;AAC5D,QAAM,EAAE,EAAE,QAAI,oCAAe;AAC7B,QAAM,eAAW,sBAAY;AAC7B,QAAM,WAAO,kBAAQ;AACrB,QAAM,CAAC,MAAM,OAAO,QAAI,uBAAS,KAAK;AAEtC,MAAI,MAAM;AACR,WAAO,4CAAC,wDAAsB,MAAK,YAAW,UAAU,CAAC,CAAC,MAAM,UAAU;AAAA,EAC5E;AAEA,MAAI,MAAM;AACR,WAAO,4CAAC,wDAAsB,MAAK,aAAY,UAAU,CAAC,CAAC,MAAM,UAAU;AAAA,EAC7E;AAEA,SACE,4CAAC,wCAAc,UAAU,CAAC,CAAC,MAAM,UAC/B,uDAAC,SAAI,eAAW;AAAA,IACd;AAAA,IACA,MAAM,WAAW,QAAQ;AAAA,EAC3B,GACE;AAAA,iDAAC,SAAI,WAAU,eACb;AAAA,kDAAC,8BAAW,MAAK,MAAM,YAAE,qBAAqB,GAAE;AAAA,MAChD,6CAAC,8BACE;AAAA,UAAE,sBAAsB;AAAA,QAAG;AAAA,QAC5B,4CAAC,0BAAW,MAAM,SAAS,KAAK,QAAQ,GACrC,YAAE,SAAS,GACd;AAAA,SACF;AAAA,OACF;AAAA,IACA,4CAAC,SAAI,WAAU,QACb,sDAAC,sBAAmB,QAAQ,MAAM,QAAQ,IAAI,GAAG,GACnD;AAAA,KACF,GACF;AAEJ;", "names": []}