---
title: Welcome
description: Learn about <PERSON>igent and Unlock Your Exceptional Productivity now
icon: wave
---

**Eigent** is the world’s first **Multi-agent Workforce** desktop application, empowering you to build, manage, and deploy a custom AI workforce that can turn your most complex workflows into automated tasks.

Built on CAMEL-AI's acclaimed open-source project (CAMEL with 13k⭐ on GitHub, #1 on GitHub Daily Trending), our system introduces a **Multi-Agent Workforce** that **boosts productivity** through parallel execution, customization, and privacy protection. Previously #1 opensource project on GAIA.

<img 
  src="/docs/images/thumbnail.png" 
  alt="Dynamic Workforce"
  width="100%"
  height="auto"
/>


## Core Features and Capabilities

<CardGroup>
  <Card
    title="Build Your Own Workforce"
    icon="cubes">
    Customize your AI “workers” (agents) according to your industry or personal workflow needs. You have full control to create specialized Worker nodes with domain-specific skills and toolkits, assembling a workforce tailored to you.
  </Card>
  <Card
    title="Dynamic Multi-Agent Collaboration"
    icon="people-group">
    Eigent dynamically breaks down tasks and activates multiple agents to work **in parallel**, automating complex tasks much faster than traditional single-agent sequential workflows. This parallelism accelerates execution and handles flexible, multi-step scenarios with ease.
  </Card>
  <Card
    title="Human-in-the-Loop"
    icon="hand-pointer">
    If a task gets stuck or encounters uncertainty, Eigent will automatically request human input. This safety net ensures critical decisions or errors are handled with human oversight, improving reliability.
  </Card>
  <Card
    title="MCP Tools Integration (MCP)"
    icon="plug">
    Eigent comes with over 200 built-in **Model Context Protocol (MCP)** tools (for web browsing, code execution, etc.), and also lets you **install your own tools**. Equip agents with exactly the right tools for your scenarios – even integrate internal APIs or custom functions – to enhance their capabilities.
  </Card>
  <Card
    title="100% Open Source"
    icon="unlock">
    Eigent is completely open-sourced. You can download, inspect, and modify the code, ensuring transparency and fostering a community-driven ecosystem for multi-agent innovation.
  </Card>
  <Card
    title="Local Model Support"
    icon="server">
    Deploy Eigent locally with your preferred models. Your data stays on **your own device**, addressing privacy and security concerns. You can use personal API keys or local LLMs so that sensitive information never leaves your environment.
  </Card>
</CardGroup>