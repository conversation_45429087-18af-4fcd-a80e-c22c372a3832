name: CI

on:
  pull_request_target:
    branches:
      - main

permissions:
  pull-requests: write

jobs:
  job1:
    name: Check Not Allowed File Changes
    runs-on: ubuntu-latest
    outputs:
      markdown_change: ${{ steps.filter_markdown.outputs.change }}
      markdown_files: ${{ steps.filter_markdown.outputs.change_files }}
    steps:

      - name: Check Not Allowed File Changes
        uses: dorny/paths-filter@v2
        id: filter_not_allowed
        with:
          list-files: json
          filters: |
            change:
              - 'package-lock.json'
              - 'yarn.lock'
              - 'pnpm-lock.yaml'

      # ref: https://github.com/github/docs/blob/main/.github/workflows/triage-unallowed-contributions.yml
      - name: Comment About Changes We Can't Accept
        if: ${{ steps.filter_not_allowed.outputs.change == 'true' }}
        uses: actions/github-script@v6
        with:
          script: |
            let workflowFailMessage = "It looks like you've modified some files that we can't accept as contributions."
            try {
              const badFilesArr = [
                'package-lock.json',
                'yarn.lock',
                'pnpm-lock.yaml',
              ]
              const badFiles = badFilesArr.join('\n- ')
              const reviewMessage = `👋 Hey there spelunker. It looks like you've modified some files that we can't accept as contributions. The complete list of files we can't accept are:\n- ${badFiles}\n\nYou'll need to revert all of the files you changed in that list using [GitHub Desktop](https://docs.github.com/en/free-pro-team@latest/desktop/contributing-and-collaborating-using-github-desktop/managing-commits/reverting-a-commit) or \`git checkout origin/main <file name>\`. Once you get those files reverted, we can continue with the review process. :octocat:\n\nMore discussion:\n- https://github.com/electron-vite/electron-vite-vue/issues/192`
              createdComment = await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.payload.number,
                body: reviewMessage,
              })
              workflowFailMessage = `${workflowFailMessage} Please see ${createdComment.data.html_url} for details.`
            } catch(err) {
              console.log("Error creating comment.", err)
            }
            core.setFailed(workflowFailMessage)

      - name: Check Not Linted Markdown
        if: ${{ always() }}
        uses: dorny/paths-filter@v2
        id: filter_markdown
        with:
          list-files: shell
          filters: |
            change:
              - added|modified: '*.md'


  job2:
    name: Lint Markdown
    runs-on: ubuntu-latest
    needs: job1
    if: ${{ always() && needs.job1.outputs.markdown_change == 'true' }}
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Lint markdown
        run: npx markdownlint-cli ${{ needs.job1.outputs.markdown_files }} --ignore node_modules