{"version": 3, "sources": ["../../../../../../src/lib/stack-app/apps/implementations/common.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { InternalSession } from \"@stackframe/stack-shared/dist/sessions\";\nimport { AsyncCache } from \"@stackframe/stack-shared/dist/utils/caches\";\nimport { isBrowserLike } from \"@stackframe/stack-shared/dist/utils/env\";\nimport { StackAssertionError, concatStacktraces, throwErr } from \"@stackframe/stack-shared/dist/utils/errors\";\nimport { filterUndefined } from \"@stackframe/stack-shared/dist/utils/objects\";\nimport { ReactPromise } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { suspendIfSsr } from \"@stackframe/stack-shared/dist/utils/react\";\nimport { Result } from \"@stackframe/stack-shared/dist/utils/results\";\nimport { Store } from \"@stackframe/stack-shared/dist/utils/stores\";\nimport React, { useCallback } from \"react\"; // THIS_LINE_PLATFORM react-like\nimport { HandlerUrls } from \"../../common\";\n\n// hack to make sure process is defined in non-node environments\nconst process = (globalThis as any).process ?? { env: {} }; // THIS_LINE_PLATFORM js react\n\nexport const clientVersion = \"js @stackframe/react@2.8.12\";\nif (clientVersion.startsWith(\"STACK_COMPILE_TIME\")) {\n  throw new StackAssertionError(\"Client version was not replaced. Something went wrong during build!\");\n}\n\n\nexport const createCache = <D extends any[], T>(fetcher: (dependencies: D) => Promise<T>) => {\n  return new AsyncCache<D, Result<T>>(\n    async (dependencies) => await Result.fromThrowingAsync(async () => await fetcher(dependencies)),\n    {},\n  );\n};\n\nexport const createCacheBySession = <D extends any[], T>(fetcher: (session: InternalSession, extraDependencies: D) => Promise<T> ) => {\n  return new AsyncCache<[InternalSession, ...D], Result<T>>(\n    async ([session, ...extraDependencies]) => await Result.fromThrowingAsync(async () => await fetcher(session, extraDependencies)),\n    {\n      onSubscribe: ([session], refresh) => {\n        const handler = session.onInvalidate(() => refresh());\n        return () => handler.unsubscribe();\n      },\n    },\n  );\n};\n\nexport function getUrls(partial: Partial<HandlerUrls>): HandlerUrls {\n  const handler = partial.handler ?? \"/handler\";\n  const home = partial.home ?? \"/\";\n  const afterSignIn = partial.afterSignIn ?? home;\n  return {\n    handler,\n    signIn: `${handler}/sign-in`,\n    afterSignIn: home,\n    signUp: `${handler}/sign-up`,\n    afterSignUp: afterSignIn,\n    signOut: `${handler}/sign-out`,\n    afterSignOut: home,\n    emailVerification: `${handler}/email-verification`,\n    passwordReset: `${handler}/password-reset`,\n    forgotPassword: `${handler}/forgot-password`,\n    oauthCallback: `${handler}/oauth-callback`,\n    magicLinkCallback: `${handler}/magic-link-callback`,\n    home: home,\n    accountSettings: `${handler}/account-settings`,\n    error: `${handler}/error`,\n    teamInvitation: `${handler}/team-invitation`,\n    ...filterUndefined(partial),\n  };\n}\n\nexport function getDefaultProjectId() {\n  return process.env.NEXT_PUBLIC_STACK_PROJECT_ID || throwErr(new Error(\"Welcome to Stack Auth! It seems that you haven't provided a project ID. Please create a project on the Stack dashboard at https://app.stack-auth.com and put it in the NEXT_PUBLIC_STACK_PROJECT_ID environment variable.\"));\n}\n\nexport function getDefaultPublishableClientKey() {\n  return process.env.NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY || throwErr(new Error(\"Welcome to Stack Auth! It seems that you haven't provided a publishable client key. Please create an API key for your project on the Stack dashboard at https://app.stack-auth.com and copy your publishable client key into the NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY environment variable.\"));\n}\n\nexport function getDefaultSecretServerKey() {\n  return process.env.STACK_SECRET_SERVER_KEY || throwErr(new Error(\"No secret server key provided. Please copy your key from the Stack dashboard and put it in the STACK_SECRET_SERVER_KEY environment variable.\"));\n}\n\nexport function getDefaultSuperSecretAdminKey() {\n  return process.env.STACK_SUPER_SECRET_ADMIN_KEY || throwErr(new Error(\"No super secret admin key provided. Please copy your key from the Stack dashboard and put it in the STACK_SUPER_SECRET_ADMIN_KEY environment variable.\"));\n}\n\nexport function getDefaultExtraRequestHeaders() {\n  return JSON.parse(process.env.NEXT_PUBLIC_STACK_EXTRA_REQUEST_HEADERS || '{}');\n}\n\n/**\n * Returns the base URL for the Stack API.\n *\n * The URL can be specified in several ways, in order of precedence:\n * 1. Directly through userSpecifiedBaseUrl parameter as string or browser/server object\n * 2. Through environment variables:\n *    - Browser: NEXT_PUBLIC_BROWSER_STACK_API_URL\n *    - Server: NEXT_PUBLIC_SERVER_STACK_API_URL\n *    - Fallback: NEXT_PUBLIC_STACK_API_URL or NEXT_PUBLIC_STACK_URL\n * 3. Default base URL if none of the above are specified\n *\n * The function also ensures the URL doesn't end with a trailing slash\n * by removing it if present.\n *\n * @param userSpecifiedBaseUrl - Optional URL override as string or {browser, server} object\n * @returns The configured base URL without trailing slash\n\n */\nexport function getBaseUrl(userSpecifiedBaseUrl: string | { browser: string, server: string } | undefined) {\n  let url;\n  if (userSpecifiedBaseUrl) {\n    if (typeof userSpecifiedBaseUrl === \"string\") {\n      url = userSpecifiedBaseUrl;\n    } else {\n      if (isBrowserLike()) {\n        url = userSpecifiedBaseUrl.browser;\n      } else {\n        url = userSpecifiedBaseUrl.server;\n      }\n    }\n  } else {\n    if (isBrowserLike()) {\n      url = process.env.NEXT_PUBLIC_BROWSER_STACK_API_URL;\n    } else {\n      url = process.env.NEXT_PUBLIC_SERVER_STACK_API_URL;\n    }\n    url = url || process.env.NEXT_PUBLIC_STACK_API_URL || process.env.NEXT_PUBLIC_STACK_URL || defaultBaseUrl;\n  }\n\n  return url.endsWith('/') ? url.slice(0, -1) : url;\n}\nconst defaultBaseUrl = \"https://api.stack-auth.com\";\n\nexport type TokenObject = {\n  accessToken: string | null,\n  refreshToken: string | null,\n};\n\nexport function createEmptyTokenStore() {\n  return new Store<TokenObject>({\n    refreshToken: null,\n    accessToken: null,\n  });\n}\n\n\nconst cachePromiseByHookId = new Map<string, ReactPromise<Result<unknown>>>();\nexport function useAsyncCache<D extends any[], T>(cache: AsyncCache<D, Result<T>>, dependencies: D, caller: string): T {\n  // we explicitly don't want to run this hook in SSR\n  suspendIfSsr(caller);\n\n  const id = React.useId();\n\n  const subscribe = useCallback((cb: () => void) => {\n    const { unsubscribe } = cache.onStateChange(dependencies, () => {\n      cachePromiseByHookId.delete(id);\n      cb();\n    });\n    return unsubscribe;\n  }, [cache, ...dependencies]);\n  const getSnapshot = useCallback(() => {\n    // React checks whether a promise passed to `use` is still the same as the previous one by comparing the reference.\n    // If we didn't cache here, this wouldn't work because the promise would be recreated every time the value changes.\n    if (!cachePromiseByHookId.has(id)) {\n      cachePromiseByHookId.set(id, cache.getOrWait(dependencies, \"read-write\"));\n    }\n    return cachePromiseByHookId.get(id) as ReactPromise<Result<T>>;\n  }, [cache, ...dependencies]);\n\n  // note: we must use React.useSyncExternalStore instead of importing the function directly, as it will otherwise\n  // throw an error on Next.js (\"can't import useSyncExternalStore from the server\")\n  const promise = React.useSyncExternalStore(\n    subscribe,\n    getSnapshot,\n    () => throwErr(new Error(\"getServerSnapshot should never be called in useAsyncCache because we restrict to CSR earlier\"))\n  );\n\n  const result = React.use(promise);\n  if (result.status === \"error\") {\n    const error = result.error;\n    if (error instanceof Error && !(error as any).__stackHasConcatenatedStacktraces) {\n      concatStacktraces(error, new Error());\n      (error as any).__stackHasConcatenatedStacktraces = true;\n    }\n    throw error;\n  }\n  return result.data;\n}\n"], "mappings": ";AAKA,SAAS,kBAAkB;AAC3B,SAAS,qBAAqB;AAC9B,SAAS,qBAAqB,mBAAmB,gBAAgB;AACjE,SAAS,uBAAuB;AAEhC,SAAS,oBAAoB;AAC7B,SAAS,cAAc;AACvB,SAAS,aAAa;AACtB,OAAO,SAAS,mBAAmB;AAInC,IAAM,UAAW,WAAmB,WAAW,EAAE,KAAK,CAAC,EAAE;AAElD,IAAM,gBAAgB;AAC7B,IAAI,cAAc,WAAW,oBAAoB,GAAG;AAClD,QAAM,IAAI,oBAAoB,qEAAqE;AACrG;AAGO,IAAM,cAAc,CAAqB,YAA6C;AAC3F,SAAO,IAAI;AAAA,IACT,OAAO,iBAAiB,MAAM,OAAO,kBAAkB,YAAY,MAAM,QAAQ,YAAY,CAAC;AAAA,IAC9F,CAAC;AAAA,EACH;AACF;AAEO,IAAM,uBAAuB,CAAqB,YAA6E;AACpI,SAAO,IAAI;AAAA,IACT,OAAO,CAAC,SAAY,oBAAiB,MAAM,MAAM,OAAO,kBAAkB,YAAY,MAAM,QAAQ,SAAS,iBAAiB,CAAC;AAAA,IAC/H;AAAA,MACE,aAAa,CAAC,CAAC,OAAO,GAAG,YAAY;AACnC,cAAM,UAAU,QAAQ,aAAa,MAAM,QAAQ,CAAC;AACpD,eAAO,MAAM,QAAQ,YAAY;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AACF;AAEO,SAAS,QAAQ,SAA4C;AAClE,QAAM,UAAU,QAAQ,WAAW;AACnC,QAAM,OAAO,QAAQ,QAAQ;AAC7B,QAAM,cAAc,QAAQ,eAAe;AAC3C,SAAO;AAAA,IACL;AAAA,IACA,QAAQ,GAAG,OAAO;AAAA,IAClB,aAAa;AAAA,IACb,QAAQ,GAAG,OAAO;AAAA,IAClB,aAAa;AAAA,IACb,SAAS,GAAG,OAAO;AAAA,IACnB,cAAc;AAAA,IACd,mBAAmB,GAAG,OAAO;AAAA,IAC7B,eAAe,GAAG,OAAO;AAAA,IACzB,gBAAgB,GAAG,OAAO;AAAA,IAC1B,eAAe,GAAG,OAAO;AAAA,IACzB,mBAAmB,GAAG,OAAO;AAAA,IAC7B;AAAA,IACA,iBAAiB,GAAG,OAAO;AAAA,IAC3B,OAAO,GAAG,OAAO;AAAA,IACjB,gBAAgB,GAAG,OAAO;AAAA,IAC1B,GAAG,gBAAgB,OAAO;AAAA,EAC5B;AACF;AAEO,SAAS,sBAAsB;AACpC,SAAO,QAAQ,IAAI,gCAAgC,SAAS,IAAI,MAAM,2NAA2N,CAAC;AACpS;AAEO,SAAS,iCAAiC;AAC/C,SAAO,QAAQ,IAAI,4CAA4C,SAAS,IAAI,MAAM,iSAAiS,CAAC;AACtX;AAEO,SAAS,4BAA4B;AAC1C,SAAO,QAAQ,IAAI,2BAA2B,SAAS,IAAI,MAAM,8IAA8I,CAAC;AAClN;AAEO,SAAS,gCAAgC;AAC9C,SAAO,QAAQ,IAAI,gCAAgC,SAAS,IAAI,MAAM,wJAAwJ,CAAC;AACjO;AAEO,SAAS,gCAAgC;AAC9C,SAAO,KAAK,MAAM,QAAQ,IAAI,2CAA2C,IAAI;AAC/E;AAoBO,SAAS,WAAW,sBAAgF;AACzG,MAAI;AACJ,MAAI,sBAAsB;AACxB,QAAI,OAAO,yBAAyB,UAAU;AAC5C,YAAM;AAAA,IACR,OAAO;AACL,UAAI,cAAc,GAAG;AACnB,cAAM,qBAAqB;AAAA,MAC7B,OAAO;AACL,cAAM,qBAAqB;AAAA,MAC7B;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,cAAc,GAAG;AACnB,YAAM,QAAQ,IAAI;AAAA,IACpB,OAAO;AACL,YAAM,QAAQ,IAAI;AAAA,IACpB;AACA,UAAM,OAAO,QAAQ,IAAI,6BAA6B,QAAQ,IAAI,yBAAyB;AAAA,EAC7F;AAEA,SAAO,IAAI,SAAS,GAAG,IAAI,IAAI,MAAM,GAAG,EAAE,IAAI;AAChD;AACA,IAAM,iBAAiB;AAOhB,SAAS,wBAAwB;AACtC,SAAO,IAAI,MAAmB;AAAA,IAC5B,cAAc;AAAA,IACd,aAAa;AAAA,EACf,CAAC;AACH;AAGA,IAAM,uBAAuB,oBAAI,IAA2C;AACrE,SAAS,cAAkC,OAAiC,cAAiB,QAAmB;AAErH,eAAa,MAAM;AAEnB,QAAM,KAAK,MAAM,MAAM;AAEvB,QAAM,YAAY,YAAY,CAAC,OAAmB;AAChD,UAAM,EAAE,YAAY,IAAI,MAAM,cAAc,cAAc,MAAM;AAC9D,2BAAqB,OAAO,EAAE;AAC9B,SAAG;AAAA,IACL,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,OAAO,GAAG,YAAY,CAAC;AAC3B,QAAM,cAAc,YAAY,MAAM;AAGpC,QAAI,CAAC,qBAAqB,IAAI,EAAE,GAAG;AACjC,2BAAqB,IAAI,IAAI,MAAM,UAAU,cAAc,YAAY,CAAC;AAAA,IAC1E;AACA,WAAO,qBAAqB,IAAI,EAAE;AAAA,EACpC,GAAG,CAAC,OAAO,GAAG,YAAY,CAAC;AAI3B,QAAM,UAAU,MAAM;AAAA,IACpB;AAAA,IACA;AAAA,IACA,MAAM,SAAS,IAAI,MAAM,8FAA8F,CAAC;AAAA,EAC1H;AAEA,QAAM,SAAS,MAAM,IAAI,OAAO;AAChC,MAAI,OAAO,WAAW,SAAS;AAC7B,UAAM,QAAQ,OAAO;AACrB,QAAI,iBAAiB,SAAS,CAAE,MAAc,mCAAmC;AAC/E,wBAAkB,OAAO,IAAI,MAAM,CAAC;AACpC,MAAC,MAAc,oCAAoC;AAAA,IACrD;AACA,UAAM;AAAA,EACR;AACA,SAAO,OAAO;AAChB;", "names": []}