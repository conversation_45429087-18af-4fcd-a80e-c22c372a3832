# Radix Icons

A crisp set of 15×15 icons designed by the [WorkOS](https://workos.com) team.

Visit [icons.radix-ui.com](https://icons.radix-ui.com) to browse the icons.

---

## Documentation

All icons are available as individual React components.

Install Radix Icons from npm:

```bash
npm install @radix-ui/react-icons
```

Import the icons into your React project:

```js
import { FaceIcon, ImageIcon, SunIcon } from '@radix-ui/react-icons';

function MyComponent() {
  return (
    <div>
      <FaceIcon />
      <SunIcon />
      <ImageIcon />
    </div>
  );
}
```

## Contributing

Please follow our [contributing guidelines](./CONTRIBUTING.md).

## Authors

- <PERSON> ([@vladyslavmoroz](https://twitter.com/vladyslavmoroz))
- Colm Tuite ([@colmtuite](https://twitter.com/colmtuite))
- <PERSON> ([@peduarte](https://twitter.com/peduarte))
- <PERSON><PERSON> ([@loklaan](https://twitter.com/loklaan))

## License

Licensed under the MIT License, Copyright © 2022-present WorkOS.

See [LICENSE](./LICENSE.md) for more information.
