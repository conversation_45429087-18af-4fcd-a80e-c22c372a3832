{"version": 3, "sources": ["../../../src/components/selected-team-switcher.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { runAsynchronouslyWithAlert } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport {\n  Button,\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n  Skeleton,\n  Typography\n} from \"@stackframe/stack-ui\";\nimport { PlusCircle, Settings } from \"lucide-react\";\nimport { Suspense, useEffect, useMemo } from \"react\";\nimport { Team, useStackApp, useUser } from \"..\";\nimport { useTranslation } from \"../lib/translations\";\nimport { TeamIcon } from \"./team-icon\";\n\ntype SelectedTeamSwitcherProps = {\n  urlMap?: (team: Team) => string,\n  selectedTeam?: Team,\n  noUpdateSelectedTeam?: boolean,\n};\n\nexport function SelectedTeamSwitcher(props: SelectedTeamSwitcherProps) {\n  return <Suspense fallback={<Fallback />}>\n    <Inner {...props} />\n  </Suspense>;\n}\n\nfunction Fallback() {\n  return <Skeleton className=\"h-9 w-full max-w-64 stack-scope\" />;\n}\n\nfunction Inner(props: SelectedTeamSwitcherProps) {\n  const { t } = useTranslation();\n  const app = useStackApp();\n  const user = useUser();\n  const project = app.useProject();\n  const navigate = app.useNavigate();\n  const selectedTeam = user?.selectedTeam || props.selectedTeam;\n  const rawTeams = user?.useTeams();\n  const teams = useMemo(() => rawTeams?.sort((a, b) => b.id === selectedTeam?.id ? 1 : -1), [rawTeams, selectedTeam]);\n\n  useEffect(() => {\n    if (!props.noUpdateSelectedTeam && props.selectedTeam) {\n      runAsynchronouslyWithAlert(user?.setSelectedTeam(props.selectedTeam));\n    }\n  }, [props.noUpdateSelectedTeam, props.selectedTeam]);\n\n  return (\n    <Select\n      value={selectedTeam?.id}\n      onValueChange={(value) => {\n        runAsynchronouslyWithAlert(async () => {\n          const team = teams?.find(team => team.id === value);\n          if (!team) {\n            throw new Error('Team not found, this should not happen');\n          }\n\n          if (!props.noUpdateSelectedTeam) {\n            await user?.setSelectedTeam(team);\n          }\n          if (props.urlMap) {\n            navigate(props.urlMap(team));\n          }\n        });\n      }}\n    >\n      <SelectTrigger className=\"stack-scope max-w-64\">\n        <SelectValue placeholder=\"Select team\"/>\n      </SelectTrigger>\n      <SelectContent className=\"stack-scope\">\n        {user?.selectedTeam ? <SelectGroup>\n          <SelectLabel>\n            <div className=\"flex items-center justify-between\">\n              <span>\n                {t('Current team')}\n              </span>\n              <Button variant='ghost' size='icon' className=\"h-6 w-6\" onClick={() => navigate(`${app.urls.accountSettings}#team-${user.selectedTeam?.id}`)}>\n                <Settings className=\"h-4 w-4\"/>\n              </Button>\n            </div>\n          </SelectLabel>\n          <SelectItem value={user.selectedTeam.id}>\n            <div className=\"flex items-center gap-2\">\n              <TeamIcon team={user.selectedTeam} />\n              <Typography className=\"max-w-40 truncate\">{user.selectedTeam.displayName}</Typography>\n            </div>\n          </SelectItem>\n        </SelectGroup> : undefined}\n\n        {teams?.length ?\n          <SelectGroup>\n            <SelectLabel>{t('Other teams')}</SelectLabel>\n            {teams.filter(team => team.id !== user?.selectedTeam?.id)\n              .map(team => (\n                <SelectItem value={team.id} key={team.id}>\n                  <div className=\"flex items-center gap-2\">\n                    <TeamIcon team={team} />\n                    <Typography className=\"max-w-64 truncate\">{team.displayName}</Typography>\n                  </div>\n                </SelectItem>\n              ))}\n          </SelectGroup> :\n          <SelectGroup>\n            <SelectLabel>{t('No teams yet')}</SelectLabel>\n          </SelectGroup>}\n\n        {project.config.clientTeamCreationEnabled && <>\n          <SelectSeparator/>\n          <div>\n            <Button\n              onClick={() => navigate(`${app.urls.accountSettings}#team-creation`)}\n              className=\"w-full\"\n              variant='ghost'\n            >\n              <PlusCircle className=\"mr-2 h-4 w-4\"/> {t('Create a team')}\n            </Button>\n          </div>\n        </>}\n      </SelectContent>\n    </Select>\n  );\n}\n"], "mappings": ";;;AAMA,SAAS,kCAAkC;AAC3C;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,YAAY,gBAAgB;AACrC,SAAS,UAAU,WAAW,eAAe;AAC7C,SAAe,aAAa,eAAe;AAC3C,SAAS,sBAAsB;AAC/B,SAAS,gBAAgB;AASI,SAoFwB,UApFxB,KAkDjB,YAlDiB;AADtB,SAAS,qBAAqB,OAAkC;AACrE,SAAO,oBAAC,YAAS,UAAU,oBAAC,YAAS,GACnC,8BAAC,SAAO,GAAG,OAAO,GACpB;AACF;AAEA,SAAS,WAAW;AAClB,SAAO,oBAAC,YAAS,WAAU,mCAAkC;AAC/D;AAEA,SAAS,MAAM,OAAkC;AAC/C,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,MAAM,YAAY;AACxB,QAAM,OAAO,QAAQ;AACrB,QAAM,UAAU,IAAI,WAAW;AAC/B,QAAM,WAAW,IAAI,YAAY;AACjC,QAAM,eAAe,MAAM,gBAAgB,MAAM;AACjD,QAAM,WAAW,MAAM,SAAS;AAChC,QAAM,QAAQ,QAAQ,MAAM,UAAU,KAAK,CAAC,GAAG,MAAM,EAAE,OAAO,cAAc,KAAK,IAAI,EAAE,GAAG,CAAC,UAAU,YAAY,CAAC;AAElH,YAAU,MAAM;AACd,QAAI,CAAC,MAAM,wBAAwB,MAAM,cAAc;AACrD,iCAA2B,MAAM,gBAAgB,MAAM,YAAY,CAAC;AAAA,IACtE;AAAA,EACF,GAAG,CAAC,MAAM,sBAAsB,MAAM,YAAY,CAAC;AAEnD,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO,cAAc;AAAA,MACrB,eAAe,CAAC,UAAU;AACxB,mCAA2B,YAAY;AACrC,gBAAM,OAAO,OAAO,KAAK,CAAAA,UAAQA,MAAK,OAAO,KAAK;AAClD,cAAI,CAAC,MAAM;AACT,kBAAM,IAAI,MAAM,wCAAwC;AAAA,UAC1D;AAEA,cAAI,CAAC,MAAM,sBAAsB;AAC/B,kBAAM,MAAM,gBAAgB,IAAI;AAAA,UAClC;AACA,cAAI,MAAM,QAAQ;AAChB,qBAAS,MAAM,OAAO,IAAI,CAAC;AAAA,UAC7B;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MAEA;AAAA,4BAAC,iBAAc,WAAU,wBACvB,8BAAC,eAAY,aAAY,eAAa,GACxC;AAAA,QACA,qBAAC,iBAAc,WAAU,eACtB;AAAA,gBAAM,eAAe,qBAAC,eACrB;AAAA,gCAAC,eACC,+BAAC,SAAI,WAAU,qCACb;AAAA,kCAAC,UACE,YAAE,cAAc,GACnB;AAAA,cACA,oBAAC,UAAO,SAAQ,SAAQ,MAAK,QAAO,WAAU,WAAU,SAAS,MAAM,SAAS,GAAG,IAAI,KAAK,eAAe,SAAS,KAAK,cAAc,EAAE,EAAE,GACzI,8BAAC,YAAS,WAAU,WAAS,GAC/B;AAAA,eACF,GACF;AAAA,YACA,oBAAC,cAAW,OAAO,KAAK,aAAa,IACnC,+BAAC,SAAI,WAAU,2BACb;AAAA,kCAAC,YAAS,MAAM,KAAK,cAAc;AAAA,cACnC,oBAAC,cAAW,WAAU,qBAAqB,eAAK,aAAa,aAAY;AAAA,eAC3E,GACF;AAAA,aACF,IAAiB;AAAA,UAEhB,OAAO,SACN,qBAAC,eACC;AAAA,gCAAC,eAAa,YAAE,aAAa,GAAE;AAAA,YAC9B,MAAM,OAAO,UAAQ,KAAK,OAAO,MAAM,cAAc,EAAE,EACrD,IAAI,UACH,oBAAC,cAAW,OAAO,KAAK,IACtB,+BAAC,SAAI,WAAU,2BACb;AAAA,kCAAC,YAAS,MAAY;AAAA,cACtB,oBAAC,cAAW,WAAU,qBAAqB,eAAK,aAAY;AAAA,eAC9D,KAJ+B,KAAK,EAKtC,CACD;AAAA,aACL,IACA,oBAAC,eACC,8BAAC,eAAa,YAAE,cAAc,GAAE,GAClC;AAAA,UAED,QAAQ,OAAO,6BAA6B,iCAC3C;AAAA,gCAAC,mBAAe;AAAA,YAChB,oBAAC,SACC;AAAA,cAAC;AAAA;AAAA,gBACC,SAAS,MAAM,SAAS,GAAG,IAAI,KAAK,eAAe,gBAAgB;AAAA,gBACnE,WAAU;AAAA,gBACV,SAAQ;AAAA,gBAER;AAAA,sCAAC,cAAW,WAAU,gBAAc;AAAA,kBAAE;AAAA,kBAAE,EAAE,eAAe;AAAA;AAAA;AAAA,YAC3D,GACF;AAAA,aACF;AAAA,WACF;AAAA;AAAA;AAAA,EACF;AAEJ;", "names": ["team"]}