{"version": 3, "sources": ["../../../../../src/lib/stack-app/internal-api-keys/index.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { InternalApiKeyCreateCrudRequest } from \"@stackframe/stack-shared/dist/interface/adminInterface\";\nimport { InternalApiKeysCrud } from \"@stackframe/stack-shared/dist/interface/crud/internal-api-keys\";\n\nexport type InternalApiKeyBase = {\n  id: string,\n  description: string,\n  expiresAt: Date,\n  manuallyRevokedAt: Date | null,\n  createdAt: Date,\n  isValid(): boolean,\n  whyInvalid(): \"expired\" | \"manually-revoked\" | null,\n  revoke(): Promise<void>,\n};\n\nexport type InternalApiKeyBaseCrudRead = Pick<InternalApiKeysCrud[\"Admin\"][\"Read\"], \"id\" | \"created_at_millis\" | \"description\" | \"expires_at_millis\" | \"manually_revoked_at_millis\">;\n\nexport type InternalApiKeyFirstView = {\n  publishableClientKey?: string,\n  secretServerKey?: string,\n  superSecretAdminKey?: string,\n} & InternalApiKeyBase;\n\nexport type InternalApiKey = {\n  publishableClientKey: null | {\n    lastFour: string,\n  },\n  secretServerKey: null | {\n    lastFour: string,\n  },\n  superSecretAdminKey: null | {\n    lastFour: string,\n  },\n} & InternalApiKeyBase;\n\nexport type InternalApiKeyCreateOptions = {\n  description: string,\n  expiresAt: Date,\n  hasPublishableClientKey: boolean,\n  hasSecretServerKey: boolean,\n  hasSuperSecretAdminKey: boolean,\n};\nexport function internalApiKeyCreateOptionsToCrud(options: InternalApiKeyCreateOptions): InternalApiKeyCreateCrudRequest {\n  return {\n    description: options.description,\n    expires_at_millis: options.expiresAt.getTime(),\n    has_publishable_client_key: options.hasPublishableClientKey,\n    has_secret_server_key: options.hasSecretServerKey,\n    has_super_secret_admin_key: options.hasSuperSecretAdminKey,\n  };\n}\n"], "mappings": ";AA6CO,SAAS,kCAAkC,SAAuE;AACvH,SAAO;AAAA,IACL,aAAa,QAAQ;AAAA,IACrB,mBAAmB,QAAQ,UAAU,QAAQ;AAAA,IAC7C,4BAA4B,QAAQ;AAAA,IACpC,uBAAuB,QAAQ;AAAA,IAC/B,4BAA4B,QAAQ;AAAA,EACtC;AACF;", "names": []}