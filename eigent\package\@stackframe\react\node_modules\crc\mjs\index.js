import crc1 from './crc1.js';
import crc8 from './crc8.js';
import crc81wire from './crc81wire.js';
import crc8dvbs2 from './crc8dvbs2.js';
import crc16 from './crc16.js';
import crc16ccitt from './crc16ccitt.js';
import crc16modbus from './crc16modbus.js';
import crc16xmodem from './crc16xmodem.js';
import crc16kermit from './crc16kermit.js';
import crc24 from './crc24.js';
import crc32 from './crc32.js';
import crc32mpeg2 from './crc32mpeg2.js';
import crcjam from './crcjam.js';
export { crc1 };
export { crc8 };
export { crc81wire };
export { crc8dvbs2 };
export { crc16 };
export { crc16ccitt };
export { crc16modbus };
export { crc16xmodem };
export { crc16kermit };
export { crc24 };
export { crc32 };
export { crc32mpeg2 };
export { crcjam };
export default {
    crc1,
    crc8,
    crc81wire,
    crc8dvbs2,
    crc16,
    crc16ccitt,
    crc16modbus,
    crc16xmodem,
    crc16kermit,
    crc24,
    crc32,
    crc32mpeg2,
    crcjam,
};
