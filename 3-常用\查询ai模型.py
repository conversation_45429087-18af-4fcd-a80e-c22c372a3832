import http.client,json
from pprint import pprint

conn = http.client.HTTPSConnection("ai98.vip")
payload = ''
headers = {
    'Authorization': 'Bearer sk-1r4ApCAZjjmjZLUJ9691C2B7946e47669254254c89066a46',
}
conn.request("GET", "/v1/models", payload, headers)
res = conn.getresponse()
data = res.read()
tree = json.loads(data.decode("utf-8"))
# pprint(tree['data'])
a = []
for i in tree['data']:
    a.append(i['id'])
a.sort()

try:
    # print("准备写入文件，内容为：", a)  # 打印要写入的内容
    with open('E:/python/常用models.txt', 'w', errors='ignore') as f:
        f.write('\n'.join(a))
    print("文件写入成功")
except Exception as e:
    print("写入文件时发生错误：", e)
    