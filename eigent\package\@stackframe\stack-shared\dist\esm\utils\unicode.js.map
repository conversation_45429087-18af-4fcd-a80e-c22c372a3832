{"version": 3, "sources": ["../../../src/utils/unicode.tsx"], "sourcesContent": ["import { StackAssertionError } from \"./errors\";\n\nexport function getFlagEmoji(twoLetterCountryCode: string) {\n  if (!/^[a-zA-Z][a-zA-Z]$/.test(twoLetterCountryCode)) throw new StackAssertionError(\"Country code must be two alphabetical letters\");\n  const codePoints = twoLetterCountryCode\n    .toUpperCase()\n    .split('')\n    .map(char => 127397 + char.charCodeAt(0));\n  return String.fromCodePoint(...codePoints);\n}\nundefined?.test(\"getFlagEmoji\", ({ expect }) => {\n  // Test with valid country codes\n  expect(getFlagEmoji(\"US\")).toBe(\"🇺🇸\");\n  expect(getFlagEmoji(\"us\")).toBe(\"🇺🇸\");\n  expect(getFlagEmoji(\"GB\")).toBe(\"🇬🇧\");\n  expect(getFlagEmoji(\"JP\")).toBe(\"🇯🇵\");\n\n  // Test with invalid country codes\n  expect(() => getFlagEmoji(\"\")).toThrow(\"Country code must be two alphabetical letters\");\n  expect(() => getFlagEmoji(\"A\")).toThrow(\"Country code must be two alphabetical letters\");\n  expect(() => getFlagEmoji(\"ABC\")).toThrow(\"Country code must be two alphabetical letters\");\n  expect(() => getFlagEmoji(\"12\")).toThrow(\"Country code must be two alphabetical letters\");\n  expect(() => getFlagEmoji(\"A1\")).toThrow(\"Country code must be two alphabetical letters\");\n});\n"], "mappings": ";AAAA,SAAS,2BAA2B;AAE7B,SAAS,aAAa,sBAA8B;AACzD,MAAI,CAAC,qBAAqB,KAAK,oBAAoB,EAAG,OAAM,IAAI,oBAAoB,+CAA+C;AACnI,QAAM,aAAa,qBAChB,YAAY,EACZ,MAAM,EAAE,EACR,IAAI,UAAQ,SAAS,KAAK,WAAW,CAAC,CAAC;AAC1C,SAAO,OAAO,cAAc,GAAG,UAAU;AAC3C;", "names": []}