{"version": 3, "sources": ["../../../../../src/components-page/account-settings/email-and-auth/emails-section.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { yupResolver } from \"@hookform/resolvers/yup\";\nimport { KnownErrors } from \"@stackframe/stack-shared/dist/known-errors\";\nimport { strictEmailSchema, yupObject } from \"@stackframe/stack-shared/dist/schema-fields\";\nimport { runAsynchronously } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { ActionCell, Badge, Button, Input, Table, TableBody, TableCell, TableRow, Typography } from \"@stackframe/stack-ui\";\nimport { useEffect, useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport * as yup from \"yup\";\nimport { FormWarningText } from \"../../../components/elements/form-warning\";\nimport { useUser } from \"../../../lib/hooks\";\nimport { useTranslation } from \"../../../lib/translations\";\n\nexport function EmailsSection() {\n  const { t } = useTranslation();\n  const user = useUser({ or: 'redirect' });\n  const contactChannels = user.useContactChannels();\n  const [addingEmail, setAddingEmail] = useState(contactChannels.length === 0);\n  const [addingEmailLoading, setAddingEmailLoading] = useState(false);\n  const [addedEmail, setAddedEmail] = useState<string | null>(null);\n  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n  const isLastEmail = contactChannels.filter(x => x.usedForAuth && x.type === 'email').length === 1;\n\n  useEffect(() => {\n    if (addedEmail) {\n      runAsynchronously(async () => {\n        const cc = contactChannels.find(x => x.value === addedEmail);\n        if (cc && !cc.isVerified) {\n          await cc.sendVerificationEmail();\n        }\n        setAddedEmail(null);\n      });\n    }\n  }, [contactChannels, addedEmail]);\n\n  const emailSchema = yupObject({\n    email: strictEmailSchema(t('Please enter a valid email address'))\n      .notOneOf(contactChannels.map(x => x.value), t('Email already exists'))\n      .defined()\n      .nonEmpty(t('Email is required')),\n  });\n\n  const { register, handleSubmit, formState: { errors }, reset } = useForm({\n    resolver: yupResolver(emailSchema)\n  });\n\n  const onSubmit = async (data: yup.InferType<typeof emailSchema>) => {\n    setAddingEmailLoading(true);\n    try {\n      await user.createContactChannel({ type: 'email', value: data.email, usedForAuth: false });\n      setAddedEmail(data.email);\n    } finally {\n      setAddingEmailLoading(false);\n    }\n    setAddingEmail(false);\n    reset();\n  };\n\n  return (\n    <div>\n      <div className='flex flex-col md:flex-row justify-between mb-4 gap-4'>\n        <Typography className='font-medium'>{t(\"Emails\")}</Typography>\n        {addingEmail ? (\n          <form\n            onSubmit={(e) => {\n              e.preventDefault();\n              runAsynchronously(handleSubmit(onSubmit));\n            }}\n            className='flex flex-col'\n          >\n            <div className='flex gap-2'>\n              <Input\n                {...register(\"email\")}\n                placeholder={t(\"Enter email\")}\n              />\n              <Button type=\"submit\" loading={addingEmailLoading}>\n                {t(\"Add\")}\n              </Button>\n              <Button\n                variant='secondary'\n                onClick={() => {\n                  setAddingEmail(false);\n                  reset();\n                }}\n              >\n                {t(\"Cancel\")}\n              </Button>\n            </div>\n            {errors.email && <FormWarningText text={errors.email.message} />}\n          </form>\n        ) : (\n          <div className='flex md:justify-end'>\n            <Button variant='secondary' onClick={() => setAddingEmail(true)}>{t(\"Add an email\")}</Button>\n          </div>\n        )}\n      </div>\n\n      {contactChannels.length > 0 ? (\n        <div className='border rounded-md'>\n          <Table>\n            <TableBody>\n              {/*eslint-disable-next-line @typescript-eslint/no-unnecessary-condition*/}\n              {contactChannels.filter(x => x.type === 'email')\n                .sort((a, b) => {\n                  if (a.isPrimary !== b.isPrimary) return a.isPrimary ? -1 : 1;\n                  if (a.isVerified !== b.isVerified) return a.isVerified ? -1 : 1;\n                  return 0;\n                })\n                .map(x => (\n                  <TableRow key={x.id}>\n                    <TableCell>\n                      <div className='flex flex-col md:flex-row gap-2 md:gap-4'>\n                        {x.value}\n                        <div className='flex gap-2'>\n                          {x.isPrimary ? <Badge>{t(\"Primary\")}</Badge> : null}\n                          {!x.isVerified ? <Badge variant='destructive'>{t(\"Unverified\")}</Badge> : null}\n                          {x.usedForAuth ? <Badge variant='outline'>{t(\"Used for sign-in\")}</Badge> : null}\n                        </div>\n                      </div>\n                    </TableCell>\n                    <TableCell className=\"flex justify-end\">\n                      <ActionCell items={[\n                        ...(!x.isVerified ? [{\n                          item: t(\"Send verification email\"),\n                          onClick: async () => { await x.sendVerificationEmail(); },\n                        }] : []),\n                        ...(!x.isPrimary && x.isVerified ? [{\n                          item: t(\"Set as primary\"),\n                          onClick: async () => { await x.update({ isPrimary: true }); },\n                        }] :\n                          !x.isPrimary ? [{\n                            item: t(\"Set as primary\"),\n                            onClick: async () => {},\n                            disabled: true,\n                            disabledTooltip: t(\"Please verify your email first\"),\n                          }] : []),\n                        ...(!x.usedForAuth && x.isVerified ? [{\n                          item: t(\"Use for sign-in\"),\n                          onClick: async () => {\n                            try {\n                              await x.update({ usedForAuth: true });\n                            } catch (e) {\n                              if (KnownErrors.ContactChannelAlreadyUsedForAuthBySomeoneElse.isInstance(e)) {\n                                alert(t(\"This email is already used for sign-in by another user.\"));\n                              }\n                            }\n                          }\n                        }] : []),\n                        ...(x.usedForAuth && !isLastEmail ? [{\n                          item: t(\"Stop using for sign-in\"),\n                          onClick: async () => { await x.update({ usedForAuth: false }); },\n                        }] : x.usedForAuth ? [{\n                          item: t(\"Stop using for sign-in\"),\n                          onClick: async () => {},\n                          disabled: true,\n                          disabledTooltip: t(\"You can not remove your last sign-in email\"),\n                        }] : []),\n                        ...(!isLastEmail || !x.usedForAuth ? [{\n                          item: t(\"Remove\"),\n                          onClick: async () => { await x.delete(); },\n                          danger: true,\n                        }] : [{\n                          item: t(\"Remove\"),\n                          onClick: async () => {},\n                          disabled: true,\n                          disabledTooltip: t(\"You can not remove your last sign-in email\"),\n                        }]),\n                      ]}/>\n                    </TableCell>\n                  </TableRow>\n                ))}\n            </TableBody>\n          </Table>\n        </div>\n      ) : null}\n    </div>\n  );\n}\n"], "mappings": ";AAIA,SAAS,mBAAmB;AAC5B,SAAS,mBAAmB;AAC5B,SAAS,mBAAmB,iBAAiB;AAC7C,SAAS,yBAAyB;AAClC,SAAS,YAAY,OAAO,QAAQ,OAAO,OAAO,WAAW,WAAW,UAAU,kBAAkB;AACpG,SAAS,WAAW,gBAAgB;AACpC,SAAS,eAAe;AAExB,SAAS,uBAAuB;AAChC,SAAS,eAAe;AACxB,SAAS,sBAAsB;AAkDvB,cASI,YATJ;AAhDD,SAAS,gBAAgB;AAC9B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,OAAO,QAAQ,EAAE,IAAI,WAAW,CAAC;AACvC,QAAM,kBAAkB,KAAK,mBAAmB;AAChD,QAAM,CAAC,aAAa,cAAc,IAAI,SAAS,gBAAgB,WAAW,CAAC;AAC3E,QAAM,CAAC,oBAAoB,qBAAqB,IAAI,SAAS,KAAK;AAClE,QAAM,CAAC,YAAY,aAAa,IAAI,SAAwB,IAAI;AAEhE,QAAM,cAAc,gBAAgB,OAAO,OAAK,EAAE,eAAe,EAAE,SAAS,OAAO,EAAE,WAAW;AAEhG,YAAU,MAAM;AACd,QAAI,YAAY;AACd,wBAAkB,YAAY;AAC5B,cAAM,KAAK,gBAAgB,KAAK,OAAK,EAAE,UAAU,UAAU;AAC3D,YAAI,MAAM,CAAC,GAAG,YAAY;AACxB,gBAAM,GAAG,sBAAsB;AAAA,QACjC;AACA,sBAAc,IAAI;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,iBAAiB,UAAU,CAAC;AAEhC,QAAM,cAAc,UAAU;AAAA,IAC5B,OAAO,kBAAkB,EAAE,oCAAoC,CAAC,EAC7D,SAAS,gBAAgB,IAAI,OAAK,EAAE,KAAK,GAAG,EAAE,sBAAsB,CAAC,EACrE,QAAQ,EACR,SAAS,EAAE,mBAAmB,CAAC;AAAA,EACpC,CAAC;AAED,QAAM,EAAE,UAAU,cAAc,WAAW,EAAE,OAAO,GAAG,MAAM,IAAI,QAAQ;AAAA,IACvE,UAAU,YAAY,WAAW;AAAA,EACnC,CAAC;AAED,QAAM,WAAW,OAAO,SAA4C;AAClE,0BAAsB,IAAI;AAC1B,QAAI;AACF,YAAM,KAAK,qBAAqB,EAAE,MAAM,SAAS,OAAO,KAAK,OAAO,aAAa,MAAM,CAAC;AACxF,oBAAc,KAAK,KAAK;AAAA,IAC1B,UAAE;AACA,4BAAsB,KAAK;AAAA,IAC7B;AACA,mBAAe,KAAK;AACpB,UAAM;AAAA,EACR;AAEA,SACE,qBAAC,SACC;AAAA,yBAAC,SAAI,WAAU,wDACb;AAAA,0BAAC,cAAW,WAAU,eAAe,YAAE,QAAQ,GAAE;AAAA,MAChD,cACC;AAAA,QAAC;AAAA;AAAA,UACC,UAAU,CAAC,MAAM;AACf,cAAE,eAAe;AACjB,8BAAkB,aAAa,QAAQ,CAAC;AAAA,UAC1C;AAAA,UACA,WAAU;AAAA,UAEV;AAAA,iCAAC,SAAI,WAAU,cACb;AAAA;AAAA,gBAAC;AAAA;AAAA,kBACE,GAAG,SAAS,OAAO;AAAA,kBACpB,aAAa,EAAE,aAAa;AAAA;AAAA,cAC9B;AAAA,cACA,oBAAC,UAAO,MAAK,UAAS,SAAS,oBAC5B,YAAE,KAAK,GACV;AAAA,cACA;AAAA,gBAAC;AAAA;AAAA,kBACC,SAAQ;AAAA,kBACR,SAAS,MAAM;AACb,mCAAe,KAAK;AACpB,0BAAM;AAAA,kBACR;AAAA,kBAEC,YAAE,QAAQ;AAAA;AAAA,cACb;AAAA,eACF;AAAA,YACC,OAAO,SAAS,oBAAC,mBAAgB,MAAM,OAAO,MAAM,SAAS;AAAA;AAAA;AAAA,MAChE,IAEA,oBAAC,SAAI,WAAU,uBACb,8BAAC,UAAO,SAAQ,aAAY,SAAS,MAAM,eAAe,IAAI,GAAI,YAAE,cAAc,GAAE,GACtF;AAAA,OAEJ;AAAA,IAEC,gBAAgB,SAAS,IACxB,oBAAC,SAAI,WAAU,qBACb,8BAAC,SACC,8BAAC,aAEE,0BAAgB,OAAO,OAAK,EAAE,SAAS,OAAO,EAC5C,KAAK,CAAC,GAAG,MAAM;AACd,UAAI,EAAE,cAAc,EAAE,UAAW,QAAO,EAAE,YAAY,KAAK;AAC3D,UAAI,EAAE,eAAe,EAAE,WAAY,QAAO,EAAE,aAAa,KAAK;AAC9D,aAAO;AAAA,IACT,CAAC,EACA,IAAI,OACH,qBAAC,YACC;AAAA,0BAAC,aACC,+BAAC,SAAI,WAAU,4CACZ;AAAA,UAAE;AAAA,QACH,qBAAC,SAAI,WAAU,cACZ;AAAA,YAAE,YAAY,oBAAC,SAAO,YAAE,SAAS,GAAE,IAAW;AAAA,UAC9C,CAAC,EAAE,aAAa,oBAAC,SAAM,SAAQ,eAAe,YAAE,YAAY,GAAE,IAAW;AAAA,UACzE,EAAE,cAAc,oBAAC,SAAM,SAAQ,WAAW,YAAE,kBAAkB,GAAE,IAAW;AAAA,WAC9E;AAAA,SACF,GACF;AAAA,MACA,oBAAC,aAAU,WAAU,oBACnB,8BAAC,cAAW,OAAO;AAAA,QACjB,GAAI,CAAC,EAAE,aAAa,CAAC;AAAA,UACnB,MAAM,EAAE,yBAAyB;AAAA,UACjC,SAAS,YAAY;AAAE,kBAAM,EAAE,sBAAsB;AAAA,UAAG;AAAA,QAC1D,CAAC,IAAI,CAAC;AAAA,QACN,GAAI,CAAC,EAAE,aAAa,EAAE,aAAa,CAAC;AAAA,UAClC,MAAM,EAAE,gBAAgB;AAAA,UACxB,SAAS,YAAY;AAAE,kBAAM,EAAE,OAAO,EAAE,WAAW,KAAK,CAAC;AAAA,UAAG;AAAA,QAC9D,CAAC,IACC,CAAC,EAAE,YAAY,CAAC;AAAA,UACd,MAAM,EAAE,gBAAgB;AAAA,UACxB,SAAS,YAAY;AAAA,UAAC;AAAA,UACtB,UAAU;AAAA,UACV,iBAAiB,EAAE,gCAAgC;AAAA,QACrD,CAAC,IAAI,CAAC;AAAA,QACR,GAAI,CAAC,EAAE,eAAe,EAAE,aAAa,CAAC;AAAA,UACpC,MAAM,EAAE,iBAAiB;AAAA,UACzB,SAAS,YAAY;AACnB,gBAAI;AACF,oBAAM,EAAE,OAAO,EAAE,aAAa,KAAK,CAAC;AAAA,YACtC,SAAS,GAAG;AACV,kBAAI,YAAY,8CAA8C,WAAW,CAAC,GAAG;AAC3E,sBAAM,EAAE,yDAAyD,CAAC;AAAA,cACpE;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC,IAAI,CAAC;AAAA,QACN,GAAI,EAAE,eAAe,CAAC,cAAc,CAAC;AAAA,UACnC,MAAM,EAAE,wBAAwB;AAAA,UAChC,SAAS,YAAY;AAAE,kBAAM,EAAE,OAAO,EAAE,aAAa,MAAM,CAAC;AAAA,UAAG;AAAA,QACjE,CAAC,IAAI,EAAE,cAAc,CAAC;AAAA,UACpB,MAAM,EAAE,wBAAwB;AAAA,UAChC,SAAS,YAAY;AAAA,UAAC;AAAA,UACtB,UAAU;AAAA,UACV,iBAAiB,EAAE,4CAA4C;AAAA,QACjE,CAAC,IAAI,CAAC;AAAA,QACN,GAAI,CAAC,eAAe,CAAC,EAAE,cAAc,CAAC;AAAA,UACpC,MAAM,EAAE,QAAQ;AAAA,UAChB,SAAS,YAAY;AAAE,kBAAM,EAAE,OAAO;AAAA,UAAG;AAAA,UACzC,QAAQ;AAAA,QACV,CAAC,IAAI,CAAC;AAAA,UACJ,MAAM,EAAE,QAAQ;AAAA,UAChB,SAAS,YAAY;AAAA,UAAC;AAAA,UACtB,UAAU;AAAA,UACV,iBAAiB,EAAE,4CAA4C;AAAA,QACjE,CAAC;AAAA,MACH,GAAE,GACJ;AAAA,SA3Da,EAAE,EA4DjB,CACD,GACL,GACF,GACF,IACE;AAAA,KACN;AAEJ;", "names": []}