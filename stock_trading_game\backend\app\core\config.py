"""
🐾 应用配置管理
Application Configuration Management

统一管理所有应用配置，支持环境变量和配置文件
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, validator
from pydantic_settings import BaseSettings as PydanticBaseSettings


class Settings(PydanticBaseSettings):
    """应用配置类"""
    
    # 基础配置
    APP_NAME: str = "模拟炒股游戏"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 安全配置
    SECRET_KEY: str = "your-super-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    ALGORITHM: str = "HS256"
    
    # 数据库配置
    DATABASE_URL: str = "postgresql://postgres:password@localhost:5432/stock_game"
    DATABASE_POOL_SIZE: int = 10
    DATABASE_MAX_OVERFLOW: int = 20
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_POOL_SIZE: int = 10
    
    # Celery配置
    CELERY_BROKER_URL: str = "redis://localhost:6379/1"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/2"
    
    # CORS配置
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # 静态文件配置
    SERVE_STATIC: bool = True
    STATIC_DIR: str = "static"
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: Optional[str] = None
    
    # 市场模拟配置
    MARKET_OPEN_HOUR: int = 9
    MARKET_OPEN_MINUTE: int = 30
    MARKET_CLOSE_HOUR: int = 15
    MARKET_CLOSE_MINUTE: int = 0
    MARKET_UPDATE_INTERVAL: float = 1.0  # 秒
    
    # 股票配置
    INITIAL_STOCK_COUNT: int = 50
    INITIAL_STOCK_PRICE_MIN: float = 10.0
    INITIAL_STOCK_PRICE_MAX: float = 100.0
    MAX_PRICE_CHANGE_PERCENT: float = 0.1  # 10%涨跌停限制
    
    # 交易配置
    INITIAL_USER_BALANCE: float = 100000.0  # 初始资金10万
    MIN_ORDER_AMOUNT: float = 100.0  # 最小交易金额
    TRANSACTION_FEE_RATE: float = 0.0003  # 交易手续费率0.03%
    
    # 机器人配置
    BOT_COUNT: int = 10
    BOT_INITIAL_BALANCE: float = 50000.0
    BOT_TRADE_INTERVAL_MIN: int = 30  # 秒
    BOT_TRADE_INTERVAL_MAX: int = 300  # 秒
    
    # WebSocket配置
    WS_HEARTBEAT_INTERVAL: int = 30  # 心跳间隔（秒）
    WS_MAX_CONNECTIONS: int = 1000
    
    # 缓存配置
    CACHE_TTL: int = 300  # 缓存过期时间（秒）
    
    # 邮件配置（可选）
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: Optional[int] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    
    # 第三方API配置
    STOCK_DATA_API_KEY: Optional[str] = None
    NEWS_API_KEY: Optional[str] = None
    
    @validator("ALLOWED_HOSTS", pre=True)
    def parse_allowed_hosts(cls, v):
        """解析允许的主机列表"""
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @validator("DATABASE_URL", pre=True)
    def validate_database_url(cls, v):
        """验证数据库URL"""
        if not v:
            raise ValueError("DATABASE_URL is required")
        return v
    
    @validator("SECRET_KEY", pre=True)
    def validate_secret_key(cls, v):
        """验证密钥"""
        if not v or len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


class DevelopmentSettings(Settings):
    """开发环境配置"""
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"
    
    # 开发环境数据库
    DATABASE_URL: str = "postgresql://postgres:password@localhost:5432/stock_game_dev"
    
    # 开发环境Redis
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # 允许所有来源（开发环境）
    ALLOWED_HOSTS: List[str] = ["*"]


class ProductionSettings(Settings):
    """生产环境配置"""
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    
    # 生产环境需要设置更安全的配置
    SERVE_STATIC: bool = False
    
    # 生产环境CORS限制
    ALLOWED_HOSTS: List[str] = ["yourdomain.com", "www.yourdomain.com"]


class TestSettings(Settings):
    """测试环境配置"""
    DEBUG: bool = True
    
    # 测试数据库
    DATABASE_URL: str = "postgresql://postgres:password@localhost:5432/stock_game_test"
    
    # 测试Redis
    REDIS_URL: str = "redis://localhost:6379/15"
    
    # 测试环境快速配置
    MARKET_UPDATE_INTERVAL: float = 0.1
    BOT_TRADE_INTERVAL_MIN: int = 1
    BOT_TRADE_INTERVAL_MAX: int = 5


def get_settings() -> Settings:
    """获取配置实例"""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    elif env == "test":
        return TestSettings()
    else:
        return DevelopmentSettings()


# 全局配置实例
settings = get_settings()


# 导出常用配置
__all__ = [
    "settings",
    "Settings",
    "DevelopmentSettings", 
    "ProductionSettings",
    "TestSettings",
    "get_settings"
]
