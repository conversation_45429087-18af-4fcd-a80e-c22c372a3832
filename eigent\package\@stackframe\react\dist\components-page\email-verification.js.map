{"version": 3, "sources": ["../../src/components-page/email-verification.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { KnownErrors } from \"@stackframe/stack-shared\";\nimport { throwErr } from \"@stackframe/stack-shared/dist/utils/errors\";\nimport React from \"react\";\nimport { useStackApp, useUser } from \"..\";\nimport { MessageCard } from \"../components/message-cards/message-card\";\nimport { useTranslation } from \"../lib/translations\";\n\nexport function EmailVerification(props: {\n  searchParams?: Record<string, string>,\n  fullPage?: boolean,\n}) {\n  const { t } = useTranslation();\n  const stackApp = useStackApp();\n  const user = useUser();\n  const [result, setResult] = React.useState<Awaited<ReturnType<typeof stackApp.verifyEmail>> | null>(null);\n\n  const invalidJsx = (\n    <MessageCard title={t(\"Invalid Verification Link\")} fullPage={!!props.fullPage}>\n      <p>{t(\"Please check if you have the correct link. If you continue to have issues, please contact support.\")}</p>\n    </MessageCard>\n  );\n\n  const expiredJsx = (\n    <MessageCard title={t(\"Expired Verification Link\")} fullPage={!!props.fullPage}>\n      <p>{t(\"Your email verification link has expired. Please request a new verification link from your account settings.\")}</p>\n    </MessageCard>\n  );\n\n  if (!props.searchParams?.code) {\n    return invalidJsx;\n  }\n\n  if (!result) {\n    return <MessageCard\n      title={t(\"Do you want to verify your email?\")}\n      fullPage={!!props.fullPage}\n      primaryButtonText={t(\"Verify\")}\n      primaryAction={async () => {\n        const result = await stackApp.verifyEmail(props.searchParams?.code || throwErr(\"No verification code provided\"));\n        setResult(result);\n      }}\n      secondaryButtonText={t(\"Cancel\")}\n      secondaryAction={async () => {\n        await stackApp.redirectToHome();\n      }}\n    />;\n  } else {\n    if (result.status === 'error') {\n      if (KnownErrors.VerificationCodeNotFound.isInstance(result.error)) {\n        return invalidJsx;\n      } else if (KnownErrors.VerificationCodeExpired.isInstance(result.error)) {\n        return expiredJsx;\n      } else if (KnownErrors.VerificationCodeAlreadyUsed.isInstance(result.error)) {\n        // everything fine, continue\n      } else {\n        throw result.error;\n      }\n    }\n\n    return <MessageCard\n      title={t(\"You email has been verified!\")}\n      fullPage={!!props.fullPage}\n      primaryButtonText={t(\"Go home\")}\n      primaryAction={async () => {\n        await stackApp.redirectToHome();\n      }}\n    />;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,0BAA4B;AAC5B,oBAAyB;AACzB,mBAAkB;AAClB,eAAqC;AACrC,0BAA4B;AAC5B,0BAA+B;AAazB;AAXC,SAAS,kBAAkB,OAG/B;AACD,QAAM,EAAE,EAAE,QAAI,oCAAe;AAC7B,QAAM,eAAW,sBAAY;AAC7B,QAAM,WAAO,kBAAQ;AACrB,QAAM,CAAC,QAAQ,SAAS,IAAI,aAAAA,QAAM,SAAkE,IAAI;AAExG,QAAM,aACJ,4CAAC,mCAAY,OAAO,EAAE,2BAA2B,GAAG,UAAU,CAAC,CAAC,MAAM,UACpE,sDAAC,OAAG,YAAE,oGAAoG,GAAE,GAC9G;AAGF,QAAM,aACJ,4CAAC,mCAAY,OAAO,EAAE,2BAA2B,GAAG,UAAU,CAAC,CAAC,MAAM,UACpE,sDAAC,OAAG,YAAE,8GAA8G,GAAE,GACxH;AAGF,MAAI,CAAC,MAAM,cAAc,MAAM;AAC7B,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,MAAC;AAAA;AAAA,QACN,OAAO,EAAE,mCAAmC;AAAA,QAC5C,UAAU,CAAC,CAAC,MAAM;AAAA,QAClB,mBAAmB,EAAE,QAAQ;AAAA,QAC7B,eAAe,YAAY;AACzB,gBAAMC,UAAS,MAAM,SAAS,YAAY,MAAM,cAAc,YAAQ,wBAAS,+BAA+B,CAAC;AAC/G,oBAAUA,OAAM;AAAA,QAClB;AAAA,QACA,qBAAqB,EAAE,QAAQ;AAAA,QAC/B,iBAAiB,YAAY;AAC3B,gBAAM,SAAS,eAAe;AAAA,QAChC;AAAA;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,OAAO,WAAW,SAAS;AAC7B,UAAI,gCAAY,yBAAyB,WAAW,OAAO,KAAK,GAAG;AACjE,eAAO;AAAA,MACT,WAAW,gCAAY,wBAAwB,WAAW,OAAO,KAAK,GAAG;AACvE,eAAO;AAAA,MACT,WAAW,gCAAY,4BAA4B,WAAW,OAAO,KAAK,GAAG;AAAA,MAE7E,OAAO;AACL,cAAM,OAAO;AAAA,MACf;AAAA,IACF;AAEA,WAAO;AAAA,MAAC;AAAA;AAAA,QACN,OAAO,EAAE,8BAA8B;AAAA,QACvC,UAAU,CAAC,CAAC,MAAM;AAAA,QAClB,mBAAmB,EAAE,SAAS;AAAA,QAC9B,eAAe,YAAY;AACzB,gBAAM,SAAS,eAAe;AAAA,QAChC;AAAA;AAAA,IACF;AAAA,EACF;AACF;", "names": ["React", "result"]}