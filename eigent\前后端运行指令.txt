后端运行代码：
方式一：
# 进入后端目录
cd e:\python\eigent\backend

# 启动后端服务
uvicorn simple_main:app --host 0.0.0.0 --port 5001 --reload

方式二：
# 进入后端目录
cd e:\python\eigent\backend

# 使用 uv 运行
uv run uvicorn simple_main:app --host 0.0.0.0 --port 5001 --reload


前端运行代码：
桌面应用：
# 进入项目根目录
cd e:\python\eigent

# 启动开发模式
npm run dev

# 或者构建生产版本
npm run build
npm run electron:serve

web版本：
# 进入项目根目录
cd e:\python\eigent

# 启动 Vite 开发服务器
npm run dev:web