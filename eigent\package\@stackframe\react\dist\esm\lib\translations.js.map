{"version": 3, "sources": ["../../../src/lib/translations.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport React from \"react\";\nimport { TranslationContext } from \"../providers/translation-provider-client\";\n\nexport function useTranslation() {\n  const translationContext = React.useContext(TranslationContext);\n  if (!translationContext) {\n    throw new Error(\"Translation context not found; did you forget to wrap your app in a <StackProvider />?\");\n  }\n  return {\n    t: (str: string, templateVars?: Record<string, string>) => {\n      const { quetzalKeys, quetzalLocale } = translationContext;\n      let translation = quetzalLocale.get(quetzalKeys.get(str) ?? (undefined as never)) ?? str;\n      for (const [key, value] of Object.entries(templateVars || {})) {\n        translation = translation.replace(`{${key}}`, value);\n      }\n      return translation;\n    },\n  };\n}\n"], "mappings": ";AAIA,OAAO,WAAW;AAClB,SAAS,0BAA0B;AAE5B,SAAS,iBAAiB;AAC/B,QAAM,qBAAqB,MAAM,WAAW,kBAAkB;AAC9D,MAAI,CAAC,oBAAoB;AACvB,UAAM,IAAI,MAAM,wFAAwF;AAAA,EAC1G;AACA,SAAO;AAAA,IACL,GAAG,CAAC,KAAa,iBAA0C;AACzD,YAAM,EAAE,aAAa,cAAc,IAAI;AACvC,UAAI,cAAc,cAAc,IAAI,YAAY,IAAI,GAAG,KAAM,MAAmB,KAAK;AACrF,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,gBAAgB,CAAC,CAAC,GAAG;AAC7D,sBAAc,YAAY,QAAQ,IAAI,GAAG,KAAK,KAAK;AAAA,MACrD;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}