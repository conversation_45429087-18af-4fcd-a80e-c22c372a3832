2025-08-04 10:20:31,459 - INFO - 🚀 开始执行模板自动填充...
2025-08-04 10:20:31,460 - INFO - 🔍 chardet检测结果: UTF-8-SIG (置信度: 1.00)
2025-08-04 10:20:31,460 - INFO - 📋 模板文件编码: UTF-8-SIG
2025-08-04 10:20:31,468 - INFO - 📋 模板表头加载成功: 5个字段
2025-08-04 10:20:31,468 - INFO - 表头: ['主营业务（火锅店、美甲店、瑜伽馆...）', '经营地点', '优势亮点/服务特色', '店铺名称', '脚本数量']
2025-08-04 10:20:31,548 - INFO - 🔍 chardet检测结果: GB2312 (置信度: 0.99)
2025-08-04 10:20:31,549 - INFO - 📊 源数据文件编码: GB2312
2025-08-04 10:20:31,555 - INFO - 📊 源数据加载成功: 38行 x 46列
2025-08-04 10:20:31,556 - INFO - 源数据表头: ['产出数量', '脚本数量', '行业', '主营业务', '主营业务（例如火锅店、女装店、母婴店）', '主营业务（火锅店、美甲店、瑜伽馆...）', '品牌名称', '城市名称', '城市优点', '城市槽点', '居住年限', '本地优势（可选）', '行业专有名词（例如面料、材质、工艺等）', '经营地点', '开业时间', '店铺名称', '店铺特色', '优势亮点/服务特色', '优势特点（例如专业性高，款式多，价格实惠，设备先进，技术专业）', '投资金额', '人设标签（例如90后/富二代/博士毕业/东北人等）', '老板昵称', '老板性别', '职业身份', '经营理念', '新品名称', '服务/产品名称', '产品/服务名称', '产品/服务明细（包含的内容、特点、退款政策等）', '产品特点', '关键信息（例如款式多，价格实惠，设备先进，技术专业）', '产品/服务优势', '售价', '目标人群（上班族/90后/00后/宝妈/奶爸/二次元）', '目标人群', '客户需求（例如有法律纠纷/找门窗/找装修/找团建项目）', '客户顾虑', '关键痛点', '内幕（例如原材料、标价门道、经营秘诀、行业秘密等）', '店铺亮点', '店铺成果（例如天天爆满，回头客多，外地顾客远道而来，好评榜第一）', '呼吁行动（例如评论区留言、点赞收藏、私信咨询、到店体验、到店选购）', '呼吁行动（私信咨询、评论区提问）', '热点话题', '影响（负面影响例如成本上升、口碑破坏；正面影响例如消费者获益、推出全新套餐、优化新设备）', '变化（例如行业趋势、市场趋势、消费者习惯、顾客认知等）']
2025-08-04 10:20:31,556 - INFO - ✅ 表头匹配成功: '主营业务（火锅店、美甲店、瑜伽馆...）'
2025-08-04 10:20:31,556 - INFO - ✅ 表头匹配成功: '经营地点'
2025-08-04 10:20:31,557 - INFO - ✅ 表头匹配成功: '优势亮点/服务特色'
2025-08-04 10:20:31,557 - INFO - ✅ 表头匹配成功: '店铺名称'
2025-08-04 10:20:31,557 - INFO - ✅ 表头匹配成功: '脚本数量'
2025-08-04 10:20:31,557 - INFO - 🎯 匹配结果: 5/5 个表头匹配成功
2025-08-04 10:20:31,564 - INFO - 📤 数据提取成功: 38行 x 5列
2025-08-04 10:20:31,567 - INFO - ✨ 模板填充完成: 38行数据
2025-08-04 10:20:31,570 - INFO - 💾 结果保存成功: E:\template_filled.csv
2025-08-04 10:20:31,570 - INFO - 📊 输出数据: 38行 x 5列
2025-08-04 10:20:31,570 - INFO - 🎉 模板填充完成！
2025-08-04 10:45:18,401 - INFO - 🚀 开始执行模板自动填充...
2025-08-04 10:45:18,401 - INFO - 🔍 chardet检测结果: UTF-8-SIG (置信度: 1.00)
2025-08-04 10:45:18,402 - INFO - 📋 模板文件编码: UTF-8-SIG
2025-08-04 10:45:18,407 - INFO - 📋 模板表头加载成功: 6个字段
2025-08-04 10:45:18,408 - INFO - 表头: ['经营地点', '脚本数量', '店铺名称', '优势特点（例如专业性高，款式多，价格实惠，设备先进，技术专业）', '呼吁行动（例如评论区留言、点赞收藏、私信咨询、到店体验、到店选购）', '主营业务（例如火锅店、女装店、母婴店）']
2025-08-04 10:45:18,483 - INFO - 🔍 chardet检测结果: GB2312 (置信度: 0.99)
2025-08-04 10:45:18,484 - INFO - 📊 源数据文件编码: GB2312
2025-08-04 10:45:18,491 - INFO - 📊 源数据加载成功: 38行 x 46列
2025-08-04 10:45:18,491 - INFO - 源数据表头: ['产出数量', '脚本数量', '行业', '主营业务', '主营业务（例如火锅店、女装店、母婴店）', '主营业务（火锅店、美甲店、瑜伽馆...）', '品牌名称', '城市名称', '城市优点', '城市槽点', '居住年限', '本地优势（可选）', '行业专有名词（例如面料、材质、工艺等）', '经营地点', '开业时间', '店铺名称', '店铺特色', '优势亮点/服务特色', '优势特点（例如专业性高，款式多，价格实惠，设备先进，技术专业）', '投资金额', '人设标签（例如90后/富二代/博士毕业/东北人等）', '老板昵称', '老板性别', '职业身份', '经营理念', '新品名称', '服务/产品名称', '产品/服务名称', '产品/服务明细（包含的内容、特点、退款政策等）', '产品特点', '关键信息（例如款式多，价格实惠，设备先进，技术专业）', '产品/服务优势', '售价', '目标人群（上班族/90后/00后/宝妈/奶爸/二次元）', '目标人群', '客户需求（例如有法律纠纷/找门窗/找装修/找团建项目）', '客户顾虑', '关键痛点', '内幕（例如原材料、标价门道、经营秘诀、行业秘密等）', '店铺亮点', '店铺成果（例如天天爆满，回头客多，外地顾客远道而来，好评榜第一）', '呼吁行动（例如评论区留言、点赞收藏、私信咨询、到店体验、到店选购）', '呼吁行动（私信咨询、评论区提问）', '热点话题', '影响（负面影响例如成本上升、口碑破坏；正面影响例如消费者获益、推出全新套餐、优化新设备）', '变化（例如行业趋势、市场趋势、消费者习惯、顾客认知等）']
2025-08-04 10:45:18,492 - INFO - ✅ 表头匹配成功: '经营地点'
2025-08-04 10:45:18,492 - INFO - ✅ 表头匹配成功: '脚本数量'
2025-08-04 10:45:18,492 - INFO - ✅ 表头匹配成功: '店铺名称'
2025-08-04 10:45:18,492 - INFO - ✅ 表头匹配成功: '优势特点（例如专业性高，款式多，价格实惠，设备先进，技术专业）'
2025-08-04 10:45:18,493 - INFO - ✅ 表头匹配成功: '呼吁行动（例如评论区留言、点赞收藏、私信咨询、到店体验、到店选购）'
2025-08-04 10:45:18,493 - INFO - ✅ 表头匹配成功: '主营业务（例如火锅店、女装店、母婴店）'
2025-08-04 10:45:18,493 - INFO - 🎯 匹配结果: 6/6 个表头匹配成功
2025-08-04 10:45:18,503 - INFO - 📤 数据提取成功: 38行 x 6列
2025-08-04 10:45:18,506 - INFO - ✨ 模板填充完成: 38行数据
2025-08-04 10:45:18,508 - INFO - 💾 结果保存成功: E:\template_filled.csv
2025-08-04 10:45:18,508 - INFO - 📊 输出数据: 38行 x 6列
2025-08-04 10:45:18,509 - INFO - 🎉 模板填充完成！
2025-08-04 11:07:47,999 - INFO - 🚀 开始执行模板自动填充...
2025-08-04 11:07:48,000 - INFO - 🔍 chardet检测结果: UTF-8-SIG (置信度: 1.00)
2025-08-04 11:07:48,000 - INFO - 📋 模板文件编码: UTF-8-SIG
2025-08-04 11:07:48,004 - INFO - 📋 模板表头加载成功: 6个字段
2025-08-04 11:07:48,004 - INFO - 表头: ['主营业务（火锅店、美甲店、瑜伽馆...）', '经营地点', '优势亮点/服务特色', '店铺名称', '脚本数量', '目标客户']
2025-08-04 11:07:48,084 - INFO - 🔍 chardet检测结果: GB2312 (置信度: 0.99)
2025-08-04 11:07:48,084 - INFO - 📊 源数据文件编码: GB2312
2025-08-04 11:07:48,090 - INFO - 📊 源数据加载成功: 38行 x 47列
2025-08-04 11:07:48,090 - INFO - 源数据表头: ['产出数量', '脚本数量', '行业', '主营业务', '主营业务（例如火锅店、女装店、母婴店）', '主营业务（火锅店、美甲店、瑜伽馆...）', '品牌名称', '城市名称', '城市优点', '城市槽点', '居住年限', '本地优势（可选）', '行业专有名词（例如面料、材质、工艺等）', '经营地点', '开业时间', '店铺名称', '店铺特色', '优势亮点/服务特色', '优势特点（例如专业性高，款式多，价格实惠，设备先进，技术专业）', '投资金额', '人设标签（例如90后/富二代/博士毕业/东北人等）', '老板昵称', '老板性别', '职业身份', '经营理念', '新品名称', '服务/产品名称', '产品/服务名称', '产品/服务明细（包含的内容、特点、退款政策等）', '产品特点', '关键信息（例如款式多，价格实惠，设备先进，技术专业）', '产品/服务优势', '售价', '目标人群（上班族/90后/00后/宝妈/奶爸/二次元）', '目标人群', '目标客户', '客户需求（例如有法律纠纷/找门窗/找装修/找团建项目）', '客户顾虑', '关键痛点', '内幕（例如原材料、标价门道、经营秘诀、行业秘密等）', '店铺亮点', '店铺成果（例如天天爆满，回头客多，外地顾客远道而来，好评榜第一）', '呼吁行动（例如评论区留言、点赞收藏、私信咨询、到店体验、到店选购）', '呼吁行动（私信咨询、评论区提问）', '热点话题', '影响（负面影响例如成本上升、口碑破坏；正面影响例如消费者获益、推出全新套餐、优化新设备）', '变化（例如行业趋势、市场趋势、消费者习惯、顾客认知等）']
2025-08-04 11:07:48,092 - INFO - ✅ 表头匹配成功: '主营业务（火锅店、美甲店、瑜伽馆...）'
2025-08-04 11:07:48,092 - INFO - ✅ 表头匹配成功: '经营地点'
2025-08-04 11:07:48,092 - INFO - ✅ 表头匹配成功: '优势亮点/服务特色'
2025-08-04 11:07:48,092 - INFO - ✅ 表头匹配成功: '店铺名称'
2025-08-04 11:07:48,093 - INFO - ✅ 表头匹配成功: '脚本数量'
2025-08-04 11:07:48,093 - INFO - ✅ 表头匹配成功: '目标客户'
2025-08-04 11:07:48,093 - INFO - 🎯 匹配结果: 6/6 个表头匹配成功
2025-08-04 11:07:48,094 - INFO - 📤 数据提取成功: 38行 x 6列
2025-08-04 11:07:48,097 - INFO - ✨ 模板填充完成: 38行数据
2025-08-04 11:07:48,100 - INFO - 💾 结果保存成功: E:\template_filled.csv
2025-08-04 11:07:48,100 - INFO - 📊 输出数据: 38行 x 6列
2025-08-04 11:07:48,101 - INFO - 🎉 模板填充完成！
2025-08-04 11:54:10,251 - INFO - 🚀 开始执行模板自动填充...
2025-08-04 11:54:10,252 - INFO - 🔍 chardet检测结果: UTF-8-SIG (置信度: 1.00)
2025-08-04 11:54:10,252 - INFO - 📋 模板文件编码: UTF-8-SIG
2025-08-04 11:54:10,256 - INFO - 📋 模板表头加载成功: 5个字段
2025-08-04 11:54:10,256 - INFO - 表头: ['行业属性', '主营业务', '经营地点', '核心卖点/优势', '脚本数量']
2025-08-04 11:54:10,339 - INFO - 🔍 chardet检测结果: GB2312 (置信度: 0.99)
2025-08-04 11:54:10,340 - INFO - 📊 源数据文件编码: GB2312
2025-08-04 11:54:10,345 - INFO - 📊 源数据加载成功: 38行 x 49列
2025-08-04 11:54:10,346 - INFO - 源数据表头: ['产出数量', '脚本数量', '行业', '行业属性', '主营业务', '主营业务（例如火锅店、女装店、母婴店）', '主营业务（火锅店、美甲店、瑜伽馆...）', '品牌名称', '城市名称', '城市优点', '城市槽点', '居住年限', '本地优势（可选）', '行业专有名词（例如面料、材质、工艺等）', '经营地点', '开业时间', '店铺名称', '店铺特色', '核心卖点/优势', '优势亮点/服务特色', '优势特点（例如专业性高，款式多，价格实惠，设备先进，技术专业）', '投资金额', '人设标签（例如90后/富二代/博士毕业/东北人等）', '老板昵称', '老板性别', '职业身份', '经营理念', '新品名称', '服务/产品名称', '产品/服务名称', '产品/服务明细（包含的内容、特点、退款政策等）', '产品特点', '关键信息（例如款式多，价格实惠，设备先进，技术专业）', '产品/服务优势', '售价', '目标人群（上班族/90后/00后/宝妈/奶爸/二次元）', '目标人群', '目标客户', '客户需求（例如有法律纠纷/找门窗/找装修/找团建项目）', '客户顾虑', '关键痛点', '内幕（例如原材料、标价门道、经营秘诀、行业秘密等）', '店铺亮点', '店铺成果（例如天天爆满，回头客多，外地顾客远道而来，好评榜第一）', '呼吁行动（例如评论区留言、点赞收藏、私信咨询、到店体验、到店选购）', '呼吁行动（私信咨询、评论区提问）', '热点话题', '影响（负面影响例如成本上升、口碑破坏；正面影响例如消费者获益、推出全新套餐、优化新设备）', '变化（例如行业趋势、市场趋势、消费者习惯、顾客认知等）']
2025-08-04 11:54:10,346 - INFO - ✅ 表头匹配成功: '行业属性'
2025-08-04 11:54:10,346 - INFO - ✅ 表头匹配成功: '主营业务'
2025-08-04 11:54:10,347 - INFO - ✅ 表头匹配成功: '经营地点'
2025-08-04 11:54:10,347 - INFO - ✅ 表头匹配成功: '核心卖点/优势'
2025-08-04 11:54:10,347 - INFO - ✅ 表头匹配成功: '脚本数量'
2025-08-04 11:54:10,347 - INFO - 🎯 匹配结果: 5/5 个表头匹配成功
2025-08-04 11:54:10,349 - INFO - 📤 数据提取成功: 38行 x 5列
2025-08-04 11:54:10,352 - INFO - ✨ 模板填充完成: 38行数据
2025-08-04 11:54:10,355 - INFO - 💾 结果保存成功: E:\template_filled.csv
2025-08-04 11:54:10,355 - INFO - 📊 输出数据: 38行 x 5列
2025-08-04 11:54:10,356 - INFO - 🎉 模板填充完成！
