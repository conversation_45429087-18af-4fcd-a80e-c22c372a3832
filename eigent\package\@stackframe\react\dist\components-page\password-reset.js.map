{"version": 3, "sources": ["../../src/components-page/password-reset.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { yupResolver } from \"@hookform/resolvers/yup\";\nimport { KnownErrors } from \"@stackframe/stack-shared\";\nimport { getPasswordError } from \"@stackframe/stack-shared/dist/helpers/password\";\nimport { passwordSchema, yupObject, yupString } from \"@stackframe/stack-shared/dist/schema-fields\";\nimport { cacheFunction } from \"@stackframe/stack-shared/dist/utils/caches\";\nimport { runAsynchronouslyWithAlert } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { Button, Label, PasswordInput, Typography, cn } from \"@stackframe/stack-ui\";\nimport React, { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport * as yup from \"yup\";\nimport { StackClientApp, useStackApp } from \"..\";\nimport { FormWarningText } from \"../components/elements/form-warning\";\nimport { MaybeFullPage } from \"../components/elements/maybe-full-page\";\nimport { MessageCard } from \"../components/message-cards/message-card\";\nimport { PredefinedMessageCard } from \"../components/message-cards/predefined-message-card\";\nimport { useTranslation } from \"../lib/translations\";\n\nexport default function PasswordResetForm(props: {\n  code: string,\n  fullPage?: boolean,\n}) {\n  const { t } = useTranslation();\n\n  const schema = yupObject({\n    password: passwordSchema.defined(t(\"Please enter your password\")).nonEmpty(t(\"Please enter your password\")).test({\n      name: 'is-valid-password',\n      test: (value, ctx) => {\n        const error = getPasswordError(value);\n        if (error) {\n          return ctx.createError({ message: error.message });\n        } else {\n          return true;\n        }\n      }\n    }),\n    passwordRepeat: yupString().nullable().oneOf([yup.ref('password'), null], t(\"Passwords do not match\")).defined().nonEmpty(t(\"Please repeat your password\"))\n  });\n\n  const { register, handleSubmit, formState: { errors }, clearErrors } = useForm({\n    resolver: yupResolver(schema)\n  });\n  const stackApp = useStackApp();\n  const [finished, setFinished] = useState(false);\n  const [resetError, setResetError] = useState(false);\n  const [loading, setLoading] = useState(false);\n\n  const onSubmit = async (data: yup.InferType<typeof schema>) => {\n    setLoading(true);\n    try {\n      const { password } = data;\n      const result = await stackApp.resetPassword({ password, code: props.code });\n      if (result.status === 'error') {\n        setResetError(true);\n        return;\n      }\n\n      setFinished(true);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (finished) {\n    return <PredefinedMessageCard type='passwordReset' fullPage={!!props.fullPage} />;\n  }\n\n  if (resetError) {\n    return (\n      <MessageCard title={t(\"Failed to reset password\")} fullPage={!!props.fullPage}>\n        {t(\"Failed to reset password. Please request a new password reset link\")}\n      </MessageCard>\n    );\n  }\n\n\n  return (\n    <MaybeFullPage fullPage={!!props.fullPage}>\n      <div className={cn(\n        \"flex flex-col items-stretch max-w-[380px] flex-basis-[380px]\",\n        props.fullPage ? \"p-4\" : \"p-0\"\n      )}>\n        <div className=\"text-center mb-6\">\n          <Typography type='h2'>{t(\"Reset Your Password\")}</Typography>\n        </div>\n\n        <form\n          className=\"flex flex-col items-stretch\"\n          onSubmit={e => runAsynchronouslyWithAlert(handleSubmit(onSubmit)(e))}\n          noValidate\n        >\n          <Label htmlFor=\"password\" className=\"mb-1\">{t(\"New Password\")}</Label>\n          <PasswordInput\n            id=\"password\"\n            autoComplete=\"new-password\"\n            {...register('password')}\n            onChange={() => {\n              clearErrors('password');\n              clearErrors('passwordRepeat');\n            }}\n          />\n          <FormWarningText text={errors.password?.message?.toString()} />\n\n          <Label htmlFor=\"repeat-password\" className=\"mt-4 mb-1\">{t(\"Repeat New Password\")}</Label>\n          <PasswordInput\n            id=\"repeat-password\"\n            autoComplete=\"new-password\"\n            {...register('passwordRepeat')}\n            onChange={() => {\n              clearErrors('password');\n              clearErrors('passwordRepeat');\n            }}\n          />\n          <FormWarningText text={errors.passwordRepeat?.message?.toString()} />\n\n          <Button type=\"submit\" className=\"mt-6\" loading={loading}>\n            {t(\"Reset Password\")}\n          </Button>\n        </form>\n      </div>\n    </MaybeFullPage>\n  );\n}\n\n\nconst cachedVerifyPasswordResetCode = cacheFunction(async (stackApp: StackClientApp<true>, code: string) => {\n  return await stackApp.verifyPasswordResetCode(code);\n});\n\nexport function PasswordReset({\n  searchParams,\n  fullPage = false,\n}: {\n  searchParams: Record<string, string>,\n  fullPage?: boolean,\n}) {\n  const { t } = useTranslation();\n  const stackApp = useStackApp();\n\n  const invalidJsx = (\n    <MessageCard title={t(\"Invalid Password Reset Link\")} fullPage={fullPage}>\n      <Typography>{t(\"Please double check if you have the correct password reset link.\")}</Typography>\n    </MessageCard>\n  );\n\n  const expiredJsx = (\n    <MessageCard title={t(\"Expired Password Reset Link\")} fullPage={fullPage}>\n      <Typography>{t(\"Your password reset link has expired. Please request a new password reset link from the login page.\")}</Typography>\n    </MessageCard>\n  );\n\n  const usedJsx = (\n    <MessageCard title={t(\"Used Password Reset Link\")} fullPage={fullPage}>\n      <Typography>{t(\"This password reset link has already been used. If you need to reset your password again, please request a new password reset link from the login page.\")}</Typography>\n    </MessageCard>\n  );\n\n  const code = searchParams.code;\n  if (!code) {\n    return invalidJsx;\n  }\n\n  const result = React.use(cachedVerifyPasswordResetCode(stackApp, code));\n\n  if (result.status === 'error') {\n    if (KnownErrors.VerificationCodeNotFound.isInstance(result.error)) {\n      return invalidJsx;\n    } else if (KnownErrors.VerificationCodeExpired.isInstance(result.error)) {\n      return expiredJsx;\n    } else if (KnownErrors.VerificationCodeAlreadyUsed.isInstance(result.error)) {\n      return usedJsx;\n    } else {\n      throw result.error;\n    }\n  }\n\n  return <PasswordResetForm code={code} fullPage={fullPage} />;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,iBAA4B;AAC5B,0BAA4B;AAC5B,sBAAiC;AACjC,2BAAqD;AACrD,oBAA8B;AAC9B,sBAA2C;AAC3C,sBAA6D;AAC7D,mBAAgC;AAChC,6BAAwB;AACxB,UAAqB;AACrB,eAA4C;AAC5C,0BAAgC;AAChC,6BAA8B;AAC9B,0BAA4B;AAC5B,qCAAsC;AACtC,0BAA+B;AAgDpB;AA9CI,SAAR,kBAAmC,OAGvC;AACD,QAAM,EAAE,EAAE,QAAI,oCAAe;AAE7B,QAAM,aAAS,gCAAU;AAAA,IACvB,UAAU,oCAAe,QAAQ,EAAE,4BAA4B,CAAC,EAAE,SAAS,EAAE,4BAA4B,CAAC,EAAE,KAAK;AAAA,MAC/G,MAAM;AAAA,MACN,MAAM,CAAC,OAAO,QAAQ;AACpB,cAAM,YAAQ,kCAAiB,KAAK;AACpC,YAAI,OAAO;AACT,iBAAO,IAAI,YAAY,EAAE,SAAS,MAAM,QAAQ,CAAC;AAAA,QACnD,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF,CAAC;AAAA,IACD,oBAAgB,gCAAU,EAAE,SAAS,EAAE,MAAM,CAAK,QAAI,UAAU,GAAG,IAAI,GAAG,EAAE,wBAAwB,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,6BAA6B,CAAC;AAAA,EAC5J,CAAC;AAED,QAAM,EAAE,UAAU,cAAc,WAAW,EAAE,OAAO,GAAG,YAAY,QAAI,gCAAQ;AAAA,IAC7E,cAAU,wBAAY,MAAM;AAAA,EAC9B,CAAC;AACD,QAAM,eAAW,sBAAY;AAC7B,QAAM,CAAC,UAAU,WAAW,QAAI,uBAAS,KAAK;AAC9C,QAAM,CAAC,YAAY,aAAa,QAAI,uBAAS,KAAK;AAClD,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAS,KAAK;AAE5C,QAAM,WAAW,OAAO,SAAuC;AAC7D,eAAW,IAAI;AACf,QAAI;AACF,YAAM,EAAE,SAAS,IAAI;AACrB,YAAM,SAAS,MAAM,SAAS,cAAc,EAAE,UAAU,MAAM,MAAM,KAAK,CAAC;AAC1E,UAAI,OAAO,WAAW,SAAS;AAC7B,sBAAc,IAAI;AAClB;AAAA,MACF;AAEA,kBAAY,IAAI;AAAA,IAClB,UAAE;AACA,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AAEA,MAAI,UAAU;AACZ,WAAO,4CAAC,wDAAsB,MAAK,iBAAgB,UAAU,CAAC,CAAC,MAAM,UAAU;AAAA,EACjF;AAEA,MAAI,YAAY;AACd,WACE,4CAAC,mCAAY,OAAO,EAAE,0BAA0B,GAAG,UAAU,CAAC,CAAC,MAAM,UAClE,YAAE,oEAAoE,GACzE;AAAA,EAEJ;AAGA,SACE,4CAAC,wCAAc,UAAU,CAAC,CAAC,MAAM,UAC/B,uDAAC,SAAI,eAAW;AAAA,IACd;AAAA,IACA,MAAM,WAAW,QAAQ;AAAA,EAC3B,GACE;AAAA,gDAAC,SAAI,WAAU,oBACb,sDAAC,8BAAW,MAAK,MAAM,YAAE,qBAAqB,GAAE,GAClD;AAAA,IAEA;AAAA,MAAC;AAAA;AAAA,QACC,WAAU;AAAA,QACV,UAAU,WAAK,4CAA2B,aAAa,QAAQ,EAAE,CAAC,CAAC;AAAA,QACnE,YAAU;AAAA,QAEV;AAAA,sDAAC,yBAAM,SAAQ,YAAW,WAAU,QAAQ,YAAE,cAAc,GAAE;AAAA,UAC9D;AAAA,YAAC;AAAA;AAAA,cACC,IAAG;AAAA,cACH,cAAa;AAAA,cACZ,GAAG,SAAS,UAAU;AAAA,cACvB,UAAU,MAAM;AACd,4BAAY,UAAU;AACtB,4BAAY,gBAAgB;AAAA,cAC9B;AAAA;AAAA,UACF;AAAA,UACA,4CAAC,uCAAgB,MAAM,OAAO,UAAU,SAAS,SAAS,GAAG;AAAA,UAE7D,4CAAC,yBAAM,SAAQ,mBAAkB,WAAU,aAAa,YAAE,qBAAqB,GAAE;AAAA,UACjF;AAAA,YAAC;AAAA;AAAA,cACC,IAAG;AAAA,cACH,cAAa;AAAA,cACZ,GAAG,SAAS,gBAAgB;AAAA,cAC7B,UAAU,MAAM;AACd,4BAAY,UAAU;AACtB,4BAAY,gBAAgB;AAAA,cAC9B;AAAA;AAAA,UACF;AAAA,UACA,4CAAC,uCAAgB,MAAM,OAAO,gBAAgB,SAAS,SAAS,GAAG;AAAA,UAEnE,4CAAC,0BAAO,MAAK,UAAS,WAAU,QAAO,SACpC,YAAE,gBAAgB,GACrB;AAAA;AAAA;AAAA,IACF;AAAA,KACF,GACF;AAEJ;AAGA,IAAM,oCAAgC,6BAAc,OAAO,UAAgC,SAAiB;AAC1G,SAAO,MAAM,SAAS,wBAAwB,IAAI;AACpD,CAAC;AAEM,SAAS,cAAc;AAAA,EAC5B;AAAA,EACA,WAAW;AACb,GAGG;AACD,QAAM,EAAE,EAAE,QAAI,oCAAe;AAC7B,QAAM,eAAW,sBAAY;AAE7B,QAAM,aACJ,4CAAC,mCAAY,OAAO,EAAE,6BAA6B,GAAG,UACpD,sDAAC,8BAAY,YAAE,kEAAkE,GAAE,GACrF;AAGF,QAAM,aACJ,4CAAC,mCAAY,OAAO,EAAE,6BAA6B,GAAG,UACpD,sDAAC,8BAAY,YAAE,qGAAqG,GAAE,GACxH;AAGF,QAAM,UACJ,4CAAC,mCAAY,OAAO,EAAE,0BAA0B,GAAG,UACjD,sDAAC,8BAAY,YAAE,yJAAyJ,GAAE,GAC5K;AAGF,QAAM,OAAO,aAAa;AAC1B,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AAEA,QAAM,SAAS,aAAAA,QAAM,IAAI,8BAA8B,UAAU,IAAI,CAAC;AAEtE,MAAI,OAAO,WAAW,SAAS;AAC7B,QAAI,gCAAY,yBAAyB,WAAW,OAAO,KAAK,GAAG;AACjE,aAAO;AAAA,IACT,WAAW,gCAAY,wBAAwB,WAAW,OAAO,KAAK,GAAG;AACvE,aAAO;AAAA,IACT,WAAW,gCAAY,4BAA4B,WAAW,OAAO,KAAK,GAAG;AAC3E,aAAO;AAAA,IACT,OAAO;AACL,YAAM,OAAO;AAAA,IACf;AAAA,EACF;AAEA,SAAO,4CAAC,qBAAkB,MAAY,UAAoB;AAC5D;", "names": ["React"]}