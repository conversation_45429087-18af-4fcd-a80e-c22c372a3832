# 🐻 大熊工作区管理执行规范

## 📁 专属工作区路径
- **主工作区**: `E:\python\大熊工作区\`
- **学习素材**: `E:\python\大熊工作区\学习素材\`
- **创作作品**: `E:\python\大熊工作区\创作作品\`

## 🎯 工作区使用规范

### 📚 学习素材管理
1. **文件格式**: 主要接受 `.md` 格式的财经文章
2. **命名规则**: `标题.md` 或 `作者_标题.md`
3. **学习流程**: 
   - 自动扫描学习素材文件夹
   - 分析文章结构、写作风格、表达技巧
   - 提取关键词汇和行业术语
   - 学习情感表达和观点论证方式

### ✍️ 创作作品管理
1. **文件格式**: 支持 `.txt` 和 `.md` 格式
2. **命名规则**: `article_YYYYMMDD_主题关键词.txt`
3. **保存流程**:
   - 创作完成后直接保存到创作作品文件夹
   - 不在屏幕上输出完整文章内容
   - 只显示保存成功提示和文件路径
   - 自动生成创作日志

## 🔄 静默创作模式

### 执行原则
- **静默输出**: 文章创作完成后不在对话中显示全文
- **文件优先**: 直接保存到指定文件夹
- **简要反馈**: 只提供保存状态和文件信息
- **质量保证**: 保持文章质量不因静默模式而降低

### 输出格式
```
✅ 文章创作完成！
📁 保存路径: 大熊工作区/创作作品/article_20250712_股票分析.txt
📊 文章统计: 约2500字，包含3个主要观点
🎯 核心主题: [主题关键词]
```

## 📈 学习增强机制

### 自动学习流程
1. **扫描学习素材**: 定期检查新增的学习材料
2. **风格分析**: 分析优秀文章的写作特点
3. **词汇积累**: 建立专业术语和表达方式库
4. **结构学习**: 学习文章组织结构和逻辑框架

### 创作优化
1. **风格融合**: 结合学习到的优秀写作风格
2. **表达自然**: 保持反AI检测的自然表达
3. **专业深度**: 结合18年投资经验的专业见解
4. **时效性**: 结合最新市场信息和政策动态

## 🛠️ 工具集成

### 文件操作
- 使用 `save-file` 工具保存创作作品
- 使用 `view` 工具读取学习素材
- 自动管理文件命名和路径

### 内容处理
- 智能提取学习素材关键信息
- 自动优化文章结构和表达
- 保持创作风格的一致性和自然性

---
*工作区管理 - 让大熊的创作更高效、更专业* 🚀
