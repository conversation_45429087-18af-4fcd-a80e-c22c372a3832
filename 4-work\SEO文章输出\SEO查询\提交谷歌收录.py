# -*- coding: utf-8 -*-
# 导入必要的库
from googleapiclient.discovery import build
from google.oauth2.service_account import Credentials
import json
import socket
import socks  # 需要 pip install PySocks
import time   # <--- 新增：导入 time 库用于添加延迟

# --- 用户配置 ---
# 1. 服务帐号密钥文件:
SERVICE_ACCOUNT_FILE = 'E:/个人/kezexiong-6265cb231375.json' # <--- 请确保这是正确的路径

# 2. 您的网站 URL (Google Search Console 中的媒体资源):
SITE_URL = 'https://faisco.com/' # <--- 已根据您的信息修改

# 3. API 范围:
SCOPES_INDEXING_API = ['https://www.googleapis.com/auth/indexing']
SCOPES_SEARCH_CONSOLE_API = ['https://www.googleapis.com/auth/webmasters']

# 4. 代理配置 (如果需要)
PROXY_ENABLED = True
PROXY_HOST = "127.0.0.1"
PROXY_PORT = 7897
PROXY_TYPE = socks.SOCKS5

# 5. URL 输入文件配置 <--- 新增配置段落
#    指定包含要提交的 URL 列表的文本文件路径。
#    文件应为 UTF-8 编码，每行一个 URL。
URL_INPUT_FILE = 'E:/google_urls.txt'  # <--- 请将此替换为您的 URL 文件路径，例如 'E:/urls_to_submit.txt'

# 6. 提交速率控制 <--- 新增配置段落
#    每次通过 Indexing API 提交 URL 之间的延迟时间（秒）。
#    Google Indexing API 每日配额约为 200 个 URL_UPDATED 请求。
#    设置为 1-2 秒可以避免触发短期速率限制，并有助于在配额内平稳提交。
DELAY_BETWEEN_SUBMISSIONS_SECONDS = 2 # 建议至少为 1 或 2 秒

# --- 重要前提条件 ---
# (与之前版本相同，包括 GCP 项目设置、服务帐号权限、Indexing API 配额、PySocks 库)
# 1. Google Cloud Platform (GCP) 项目设置:
#    - 确保您已创建一个 GCP 项目。
#    - 在此项目中，务必启用以下两个 API：
#        a) "Indexing API" (用于提交单个 URL)
#        b) "Google Search Console API" (用于提交站点地图和进行 URL 检查等)
#
# 2. 服务帐号权限:
#    - 服务帐号必须是 SITE_URL 对应媒体资源的 "所有者 (Owner)"。
#
# 3. Indexing API 配额:
#    - 每日约 200 个 URL 更新请求。
#
# 4. PySocks 库:
#    - 如果启用代理，请确保已安装 PySocks: pip install PySocks

def apply_proxy():
    """如果配置了代理，则应用全局代理设置。"""
    if PROXY_ENABLED:
        try:
            socks.set_default_proxy(PROXY_TYPE, PROXY_HOST, PROXY_PORT)
            socket.socket = socks.socksocket
            print(f"已应用代理: {PROXY_HOST}:{PROXY_PORT} (类型: {PROXY_TYPE})")
        except Exception as e:
            print(f"设置代理时出错: {e}. 请确保代理服务器正在运行且配置正确，并且已安装 PySocks 库。")

def get_service_account_credentials(scopes):
    """从服务帐号 JSON 文件加载凭据。"""
    try:
        return Credentials.from_service_account_file(SERVICE_ACCOUNT_FILE, scopes=scopes)
    except FileNotFoundError:
        print(f"错误：找不到服务帐号密钥文件 '{SERVICE_ACCOUNT_FILE}'。请仔细检查路径是否正确。")
        return None
    except Exception as e:
        print(f"加载服务帐号凭据时发生错误: {e}")
        return None

def build_api_service(service_name, version, credentials):
    """构建 API 服务客户端。"""
    if not credentials:
        print(f"无法构建服务 '{service_name}'，因为凭据无效。")
        return None
    try:
        return build(service_name, version, credentials=credentials)
    except Exception as e:
        print(f"构建 API 服务 '{service_name}' (版本 '{version}') 时发生错误: {e}")
        return None

def submit_url_for_indexing(indexing_service, url_to_submit):
    """
    使用 Indexing API 请求 Google 编入单个 URL 的索引或更新。
    返回 True 表示 API 调用被接受，False 表示发生错误或服务不可用。
    """
    if not indexing_service:
        print("Indexing API 服务未初始化，无法提交 URL。")
        return False

    body = {
        "url": url_to_submit,
        "type": "URL_UPDATED"
    }
    try:
        response = indexing_service.urlNotifications().publish(body=body).execute()
        print(f"成功使用 Indexing API 提交 URL '{url_to_submit}'。")
        # API 响应通常只包含被提交的 URL，如果需要更详细的日志可以取消下面一行的注释
        # print(f"API 响应: {json.dumps(response, indent=2)}")
        return True
    except Exception as e:
        print(f"使用 Indexing API 提交 URL '{url_to_submit}' 时发生错误: {e}")
        # 可以在此处添加更详细的错误分类处理，例如区分权限错误和配额错误
        if "quota" in str(e).lower():
            print("错误可能与 API 配额有关。请检查您的 Google Cloud 项目配额。")
        elif "permission denied" in str(e).lower():
            print(f"错误可能与权限有关。请确保服务帐号对 '{SITE_URL}' 是 '所有者'。")
        # 其他错误检查提示
        # print("其他请检查：")
        # print("1. 'Indexing API' 是否已在您的 Google Cloud 项目中启用？")
        # print(f"2. URL '{url_to_submit}' 是否是您网站下的有效且可访问的 URL？")
        # if PROXY_ENABLED:
        #     print(f"3. 代理服务器 '{PROXY_HOST}:{PROXY_PORT}' 是否正常工作并允许访问 Google API？")
        return False

def read_urls_from_file(filepath):
    """
    从指定的文本文件中读取 URL 列表。
    每行应包含一个 URL。忽略空行和以 '#' 开头的注释行。
    :param filepath: URL 文本文件的路径。
    :return: URL 字符串列表，如果文件未找到或为空则返回空列表。
    """
    urls = []
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            for line in f:
                url = line.strip()
                if url and not url.startswith('#'): # 忽略空行和注释行
                    urls.append(url)
        if not urls:
            print(f"警告：文件 '{filepath}' 为空或不包含有效 URL。")
        else:
            print(f"从 '{filepath}' 文件中成功读取 {len(urls)} 个 URL。")
    except FileNotFoundError:
        print(f"错误：找不到 URL 输入文件 '{filepath}'。请检查路径是否正确。")
    except Exception as e:
        print(f"读取 URL 文件 '{filepath}' 时发生错误: {e}")
    return urls

def submit_sitemap_to_search_console(search_console_service, sitemap_full_url):
    """向 Google Search Console 提交站点地图。"""
    if not search_console_service:
        print("Search Console API 服务未初始化，无法提交站点地图。")
        return

    try:
        search_console_service.sitemaps().submit(siteUrl=SITE_URL, feedpath=sitemap_full_url).execute()
        print(f"站点地图 '{sitemap_full_url}' 已成功请求提交到媒体资源 '{SITE_URL}'。")
    except Exception as e:
        print(f"提交站点地图 '{sitemap_full_url}' 到媒体资源 '{SITE_URL}' 时发生错误: {e}")
        if PROXY_ENABLED:
            print(f"请额外检查代理服务器 '{PROXY_HOST}:{PROXY_PORT}' 是否正常工作并允许访问 Google API。")

if __name__ == '__main__':
    apply_proxy()

    print("--- Google Search Console 批量提交工具 ---")
    print(f"服务帐号文件: {SERVICE_ACCOUNT_FILE}")
    print(f"目标媒体资源 (SITE_URL): {SITE_URL}")
    print(f"URL 输入文件: {URL_INPUT_FILE}")
    print(f"每次提交间隔: {DELAY_BETWEEN_SUBMISSIONS_SECONDS} 秒")
    if PROXY_ENABLED:
        print(f"代理设置: 已启用 - {PROXY_HOST}:{PROXY_PORT} (类型: {PROXY_TYPE})")
    else:
        print("代理设置: 已禁用")
    print("-" * 30)

    # 初始化 API 服务
    indexing_api_credentials = get_service_account_credentials(SCOPES_INDEXING_API)
    indexing_api_service = None
    if indexing_api_credentials:
        indexing_api_service = build_api_service('indexing', 'v3', indexing_api_credentials)

    search_console_api_credentials = get_service_account_credentials(SCOPES_SEARCH_CONSOLE_API)
    search_console_api_service = None
    if search_console_api_credentials:
        search_console_api_service = build_api_service('searchconsole', 'v1', search_console_api_credentials)

    # --- 场景 A: 从文件批量提交 URL 进行索引 (使用 Indexing API) ---
    if indexing_api_service:
        print(f"\n场景 A: 尝试从文件 '{URL_INPUT_FILE}' 批量提交 URL 进行索引...")
        urls_to_submit = read_urls_from_file(URL_INPUT_FILE)

        if urls_to_submit:
            submitted_count = 0
            failed_count = 0
            total_urls = len(urls_to_submit)
            print(f"准备提交 {total_urls} 个 URL...")

            for i, url in enumerate(urls_to_submit):
                print(f"\n正在提交 URL ({i+1}/{total_urls}): {url}")
                if submit_url_for_indexing(indexing_api_service, url):
                    submitted_count += 1
                else:
                    failed_count += 1
                
                # 在每次提交后添加延迟 (除非是最后一个 URL)
                if i < total_urls - 1:
                    print(f"等待 {DELAY_BETWEEN_SUBMISSIONS_SECONDS} 秒后继续...")
                    time.sleep(DELAY_BETWEEN_SUBMISSIONS_SECONDS)
            
            print("\n--- 批量提交完成 ---")
            print(f"成功提交: {submitted_count} 个 URL")
            print(f"提交失败: {failed_count} 个 URL")
            if submitted_count >= 200: # 提醒用户每日配额
                 print("提醒: Indexing API 的每日配额大约为 200 个 URL。如果提交数量接近或超过此值，请注意后续提交可能会失败，直到配额重置。")
        else:
            print(f"没有从 '{URL_INPUT_FILE}' 中读取到任何 URL，跳过批量提交。")
    else:
        print("\n场景 A: 跳过批量提交 URL，因为 Indexing API 服务未成功初始化。")

    # --- 场景 B: 提交站点地图 (使用 Search Console API) ---
    # (此部分保持不变，如果需要可以取消注释相关代码)
    if search_console_api_service:
        sitemap_path = "sitemap.xml"
        full_sitemap_url = f"{SITE_URL.rstrip('/')}/{sitemap_path.lstrip('/')}"

        # print(f"\n场景 B: 尝试使用 Search Console API 提交站点地图...")
        # print(f"要提交的站点地图 URL: {full_sitemap_url}")
        # print(f"目标媒体资源 (siteUrl): {SITE_URL}")
        # submit_sitemap_to_search_console(search_console_service, full_sitemap_url) # 取消注释以运行
        # print("注意：提交站点地图功能当前已注释掉。如需运行，请在脚本中取消相应代码行的注释。")
        pass # 保持场景B的逻辑，但默认不执行
    # else:
    #     print("\n场景 B: 跳过提交站点地图，因为 Search Console API 服务未成功初始化。")

    print("-" * 30)
    print("\n脚本执行完毕。")
