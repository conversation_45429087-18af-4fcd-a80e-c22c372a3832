/* 复古印刷风格 - 突出特点：衬线字体、经典排版、书籍质感 */
body {
  font-family: 'Libre Baskerville', 'Georgia', serif;
  line-height: 1.7;
  color: #3d3d3d;
  max-width: 750px;
  margin: 0 auto;
  padding: 2rem;
  background-color: #fff;
  text-rendering: optimizeLegibility;
}

h1 {
  font-family: 'Playfair Display', 'Times New Roman', serif;
  font-size: 2.8rem;
  font-weight: 900;
  color: #2c2c2c;
  margin: 2rem 0 1.5rem;
  line-height: 1.2;
  text-align: center;
  letter-spacing: -0.02em;
  font-style: italic;
}

h1::after {
  content: "❦";
  display: block;
  text-align: center;
  margin-top: 0.5rem;
  font-size: 1.5rem;
  color: #8a0e0e;
  font-style: normal;
}

h2 {
  font-family: 'Playfair Display', 'Times New Roman', serif;
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c2c2c;
  margin-top: 3rem;
  margin-bottom: 1.2rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid #d1d1d1;
  padding-bottom: 0.5rem;
}

h2 .content {
  display: inline-block;
  position: relative;
}

h2 .content::before {
  content: "§";
  position: absolute;
  left: -1.8rem;
  top: 0.2rem;
  color: #8a0e0e;
  font-size: 1.5rem;
  font-weight: normal;
}

h3 {
  font-family: 'Playfair Display', 'Times New Roman', serif;
  font-size: 1.4rem;
  font-weight: 700;
  font-style: italic;
  color: #2c2c2c;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

h3 .content {
  display: inline-block;
  border-bottom: 1px dotted #8a0e0e;
}

p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  line-height: 1.8;
  text-align: justify;
  hyphens: auto;
}

p:first-of-type::first-letter {
  float: left;
  font-family: 'Playfair Display', serif;
  font-size: 3.5rem;
  line-height: 1;
  font-weight: bold;
  margin-right: 0.5rem;
  color: #8a0e0e;
}

a {
  color: #8a0e0e;
  text-decoration: none;
  border-bottom: 1px dotted #8a0e0e;
  transition: all 0.3s ease;
}

a:hover {
  border-bottom: 1px solid #8a0e0e;
  background-color: #f9f2f2;
}

blockquote {
  margin: 2rem 3rem;
  font-style: italic;
  color: #666;
  position: relative;
}

blockquote::before {
  content: """;
  font-family: 'Georgia', serif;
  font-size: 4rem;
  position: absolute;
  left: -3rem;
  top: -1rem;
  color: #d1d1d1;
}

blockquote::after {
  content: """;
  font-family: 'Georgia', serif;
  font-size: 4rem;
  position: absolute;
  right: -1.5rem;
  bottom: -3rem;
  color: #d1d1d1;
}

code {
  background: #f4f4f4;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  padding: 0.1em 0.3em;
  border: 1px solid #e0e0e0;
  border-radius: 2px;
}

ul, ol {
  margin: 1.5rem 0 1.5rem 2.5rem;
}

ul {
  list-style-type: square;
}

ul li, ol li {
  margin-bottom: 0.8rem;
  padding-left: 0.5rem;
  line-height: 1.7;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 2rem auto;
  border: 8px solid white;
  box-shadow: 0 0 15px rgba(0,0,0,0.15);
}

table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  font-size: 0.95rem;
}

th, td {
  padding: 0.75rem;
  border: 1px solid #d1d1d1;
  text-align: left;
}

th {
  background-color: #f4f4f4;
  font-weight: bold;
  border-bottom: 2px solid #8a0e0e;
}

tr:nth-child(even) {
  background-color: #f9f9f9;
}

/* 强调元素 */
.highlight-word {
  font-style: italic;
  color: #8a0e0e;
  font-weight: 600;
}