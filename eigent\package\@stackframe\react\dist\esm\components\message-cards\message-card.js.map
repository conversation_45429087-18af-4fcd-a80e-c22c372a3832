{"version": 3, "sources": ["../../../../src/components/message-cards/message-card.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport React from \"react\";\nimport { MaybeFullPage } from \"../elements/maybe-full-page\";\nimport { Button, Typography } from \"@stackframe/stack-ui\";\n\nexport function MessageCard(\n  { fullPage=false, ...props }:\n  {\n    children?: React.ReactNode,\n    title: string,\n    fullPage?: boolean,\n    primaryButtonText?: string,\n    primaryAction?: () => Promise<void> | void,\n    secondaryButtonText?: string,\n    secondaryAction?: () => Promise<void> | void,\n  }\n) {\n  return (\n    <MaybeFullPage fullPage={fullPage}>\n      <div className=\"text-center stack-scope flex flex-col gap-4\" style={{ maxWidth: '380px', flexBasis: '380px', padding: fullPage ? '1rem' : 0 }}>\n        <Typography type='h3'>{props.title}</Typography>\n        {props.children}\n        {(props.primaryButtonText || props.secondaryButtonText) && (\n          <div className=\"flex justify-center gap-4 my-5\">\n            {props.secondaryButtonText && (\n              <Button variant=\"secondary\" onClick={props.secondaryAction}>\n                {props.secondaryButtonText}\n              </Button>\n            )}\n            {props.primaryButtonText && (\n              <Button onClick={props.primaryAction}>\n                {props.primaryButtonText}\n              </Button>\n            )}\n          </div>\n        )}\n      </div>\n    </MaybeFullPage>\n  );\n}\n"], "mappings": ";;;AAQA,SAAS,qBAAqB;AAC9B,SAAS,QAAQ,kBAAkB;AAiB3B,cAGE,YAHF;AAfD,SAAS,YACd,EAAE,WAAS,OAAO,GAAG,MAAM,GAU3B;AACA,SACE,oBAAC,iBAAc,UACb,+BAAC,SAAI,WAAU,+CAA8C,OAAO,EAAE,UAAU,SAAS,WAAW,SAAS,SAAS,WAAW,SAAS,EAAE,GAC1I;AAAA,wBAAC,cAAW,MAAK,MAAM,gBAAM,OAAM;AAAA,IAClC,MAAM;AAAA,KACL,MAAM,qBAAqB,MAAM,wBACjC,qBAAC,SAAI,WAAU,kCACZ;AAAA,YAAM,uBACL,oBAAC,UAAO,SAAQ,aAAY,SAAS,MAAM,iBACxC,gBAAM,qBACT;AAAA,MAED,MAAM,qBACL,oBAAC,UAAO,SAAS,MAAM,eACpB,gBAAM,mBACT;AAAA,OAEJ;AAAA,KAEJ,GACF;AAEJ;", "names": []}