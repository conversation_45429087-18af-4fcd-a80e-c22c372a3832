{"version": 3, "sources": ["../../../src/utils/urls.tsx"], "sourcesContent": ["import { generateSecureRandomString } from \"./crypto\";\nimport { templateIdentity } from \"./strings\";\n\nexport function createUrlIfValid(...args: ConstructorParameters<typeof URL>) {\n  try {\n    return new URL(...args);\n  } catch (e) {\n    return null;\n  }\n}\nundefined?.test(\"createUrlIfValid\", ({ expect }) => {\n  // Test with valid URLs\n  expect(createUrlIfValid(\"https://example.com\")).toBeInstanceOf(URL);\n  expect(createUrlIfValid(\"https://example.com/path?query=value#hash\")).toBeInstanceOf(URL);\n  expect(createUrlIfValid(\"/path\", \"https://example.com\")).toBeInstanceOf(URL);\n\n  // Test with invalid URLs\n  expect(createUrlIfValid(\"\")).toBeNull();\n  expect(createUrlIfValid(\"not a url\")).toBeNull();\n  expect(createUrlIfValid(\"http://\")).toBeNull();\n});\n\nexport function isValidUrl(url: string) {\n  return !!createUrlIfValid(url);\n}\nundefined?.test(\"isValidUrl\", ({ expect }) => {\n  // Test with valid URLs\n  expect(isValidUrl(\"https://example.com\")).toBe(true);\n  expect(isValidUrl(\"http://localhost:3000\")).toBe(true);\n  expect(isValidUrl(\"ftp://example.com\")).toBe(true);\n\n  // Test with invalid URLs\n  expect(isValidUrl(\"\")).toBe(false);\n  expect(isValidUrl(\"not a url\")).toBe(false);\n  expect(isValidUrl(\"http://\")).toBe(false);\n});\n\nexport function isValidHostname(hostname: string) {\n  const url = createUrlIfValid(`https://${hostname}`);\n  if (!url) return false;\n  return url.hostname === hostname;\n}\nundefined?.test(\"isValidHostname\", ({ expect }) => {\n  // Test with valid hostnames\n  expect(isValidHostname(\"example.com\")).toBe(true);\n  expect(isValidHostname(\"localhost\")).toBe(true);\n  expect(isValidHostname(\"sub.domain.example.com\")).toBe(true);\n  expect(isValidHostname(\"127.0.0.1\")).toBe(true);\n\n  // Test with invalid hostnames\n  expect(isValidHostname(\"\")).toBe(false);\n  expect(isValidHostname(\"example.com/path\")).toBe(false);\n  expect(isValidHostname(\"https://example.com\")).toBe(false);\n  expect(isValidHostname(\"example com\")).toBe(false);\n});\n\nexport function isLocalhost(urlOrString: string | URL) {\n  const url = createUrlIfValid(urlOrString);\n  if (!url) return false;\n  if (url.hostname === \"localhost\" || url.hostname.endsWith(\".localhost\")) return true;\n  if (url.hostname.match(/^127\\.\\d+\\.\\d+\\.\\d+$/)) return true;\n  return false;\n}\nundefined?.test(\"isLocalhost\", ({ expect }) => {\n  // Test with localhost URLs\n  expect(isLocalhost(\"http://localhost\")).toBe(true);\n  expect(isLocalhost(\"https://localhost:8080\")).toBe(true);\n  expect(isLocalhost(\"http://sub.localhost\")).toBe(true);\n  expect(isLocalhost(\"http://127.0.0.1\")).toBe(true);\n  expect(isLocalhost(\"http://*********\")).toBe(true);\n\n  // Test with non-localhost URLs\n  expect(isLocalhost(\"https://example.com\")).toBe(false);\n  expect(isLocalhost(\"http://***********\")).toBe(false);\n  expect(isLocalhost(\"http://********\")).toBe(false);\n\n  // Test with URL objects\n  expect(isLocalhost(new URL(\"http://localhost\"))).toBe(true);\n  expect(isLocalhost(new URL(\"https://example.com\"))).toBe(false);\n\n  // Test with invalid URLs\n  expect(isLocalhost(\"not a url\")).toBe(false);\n  expect(isLocalhost(\"\")).toBe(false);\n});\n\nexport function isRelative(url: string) {\n  const randomDomain = `${generateSecureRandomString()}.stack-auth.example.com`;\n  const u = createUrlIfValid(url, `https://${randomDomain}`);\n  if (!u) return false;\n  if (u.host !== randomDomain) return false;\n  if (u.protocol !== \"https:\") return false;\n  return true;\n}\nundefined?.test(\"isRelative\", ({ expect }) => {\n  // We can't easily mock generateSecureRandomString in this context\n  // but we can still test the function's behavior\n\n  // Test with relative URLs\n  expect(isRelative(\"/\")).toBe(true);\n  expect(isRelative(\"/path\")).toBe(true);\n  expect(isRelative(\"/path?query=value#hash\")).toBe(true);\n\n  // Test with absolute URLs\n  expect(isRelative(\"https://example.com\")).toBe(false);\n  expect(isRelative(\"http://example.com\")).toBe(false);\n  expect(isRelative(\"//example.com\")).toBe(false);\n\n  // Note: The implementation treats empty strings and invalid URLs as relative\n  // This is because they can be resolved against a base URL\n  expect(isRelative(\"\")).toBe(true);\n  expect(isRelative(\"not a url\")).toBe(true);\n});\n\nexport function getRelativePart(url: URL) {\n  return url.pathname + url.search + url.hash;\n}\nundefined?.test(\"getRelativePart\", ({ expect }) => {\n  // Test with various URLs\n  expect(getRelativePart(new URL(\"https://example.com\"))).toBe(\"/\");\n  expect(getRelativePart(new URL(\"https://example.com/path\"))).toBe(\"/path\");\n  expect(getRelativePart(new URL(\"https://example.com/path?query=value\"))).toBe(\"/path?query=value\");\n  expect(getRelativePart(new URL(\"https://example.com/path#hash\"))).toBe(\"/path#hash\");\n  expect(getRelativePart(new URL(\"https://example.com/path?query=value#hash\"))).toBe(\"/path?query=value#hash\");\n\n  // Test with different domains but same paths\n  const url1 = new URL(\"https://example.com/path?query=value#hash\");\n  const url2 = new URL(\"https://different.com/path?query=value#hash\");\n  expect(getRelativePart(url1)).toBe(getRelativePart(url2));\n});\n\n/**\n * A template literal tag that returns a URL.\n *\n * Any values passed are encoded.\n */\nexport function url(strings: TemplateStringsArray | readonly string[], ...values: (string|number|boolean)[]): URL {\n  return new URL(urlString(strings, ...values));\n}\nundefined?.test(\"url\", ({ expect }) => {\n  // Test with no interpolation\n  expect(url`https://example.com`).toBeInstanceOf(URL);\n  expect(url`https://example.com`.href).toBe(\"https://example.com/\");\n\n  // Test with string interpolation\n  expect(url`https://example.com/${\"path\"}`).toBeInstanceOf(URL);\n  expect(url`https://example.com/${\"path\"}`.pathname).toBe(\"/path\");\n\n  // Test with number interpolation\n  expect(url`https://example.com/${42}`).toBeInstanceOf(URL);\n  expect(url`https://example.com/${42}`.pathname).toBe(\"/42\");\n\n  // Test with boolean interpolation\n  expect(url`https://example.com/${true}`).toBeInstanceOf(URL);\n  expect(url`https://example.com/${true}`.pathname).toBe(\"/true\");\n\n  // Test with special characters in interpolation\n  expect(url`https://example.com/${\"path with spaces\"}`).toBeInstanceOf(URL);\n  expect(url`https://example.com/${\"path with spaces\"}`.pathname).toBe(\"/path%20with%20spaces\");\n\n  // Test with multiple interpolations\n  expect(url`https://example.com/${\"path\"}?query=${\"value\"}`).toBeInstanceOf(URL);\n  expect(url`https://example.com/${\"path\"}?query=${\"value\"}`.pathname).toBe(\"/path\");\n  expect(url`https://example.com/${\"path\"}?query=${\"value\"}`.search).toBe(\"?query=value\");\n});\n\n\n/**\n * A template literal tag that returns a URL string.\n *\n * Any values passed are encoded.\n */\nexport function urlString(strings: TemplateStringsArray | readonly string[], ...values: (string|number|boolean)[]): string {\n  return templateIdentity(strings, ...values.map(encodeURIComponent));\n}\nundefined?.test(\"urlString\", ({ expect }) => {\n  // Test with no interpolation\n  expect(urlString`https://example.com`).toBe(\"https://example.com\");\n\n  // Test with string interpolation\n  expect(urlString`https://example.com/${\"path\"}`).toBe(\"https://example.com/path\");\n\n  // Test with number interpolation\n  expect(urlString`https://example.com/${42}`).toBe(\"https://example.com/42\");\n\n  // Test with boolean interpolation\n  expect(urlString`https://example.com/${true}`).toBe(\"https://example.com/true\");\n\n  // Test with special characters in interpolation\n  expect(urlString`https://example.com/${\"path with spaces\"}`).toBe(\"https://example.com/path%20with%20spaces\");\n  expect(urlString`https://example.com/${\"?&=\"}`).toBe(\"https://example.com/%3F%26%3D\");\n\n  // Test with multiple interpolations\n  expect(urlString`https://example.com/${\"path\"}?query=${\"value\"}`).toBe(\"https://example.com/path?query=value\");\n  expect(urlString`https://example.com/${\"path\"}?query=${\"value with spaces\"}`).toBe(\"https://example.com/path?query=value%20with%20spaces\");\n});\n\n\n"], "mappings": ";AAAA,SAAS,kCAAkC;AAC3C,SAAS,wBAAwB;AAE1B,SAAS,oBAAoB,MAAyC;AAC3E,MAAI;AACF,WAAO,IAAI,IAAI,GAAG,IAAI;AAAA,EACxB,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;AAaO,SAAS,WAAWA,MAAa;AACtC,SAAO,CAAC,CAAC,iBAAiBA,IAAG;AAC/B;AAaO,SAAS,gBAAgB,UAAkB;AAChD,QAAMA,OAAM,iBAAiB,WAAW,QAAQ,EAAE;AAClD,MAAI,CAACA,KAAK,QAAO;AACjB,SAAOA,KAAI,aAAa;AAC1B;AAeO,SAAS,YAAY,aAA2B;AACrD,QAAMA,OAAM,iBAAiB,WAAW;AACxC,MAAI,CAACA,KAAK,QAAO;AACjB,MAAIA,KAAI,aAAa,eAAeA,KAAI,SAAS,SAAS,YAAY,EAAG,QAAO;AAChF,MAAIA,KAAI,SAAS,MAAM,sBAAsB,EAAG,QAAO;AACvD,SAAO;AACT;AAuBO,SAAS,WAAWA,MAAa;AACtC,QAAM,eAAe,GAAG,2BAA2B,CAAC;AACpD,QAAM,IAAI,iBAAiBA,MAAK,WAAW,YAAY,EAAE;AACzD,MAAI,CAAC,EAAG,QAAO;AACf,MAAI,EAAE,SAAS,aAAc,QAAO;AACpC,MAAI,EAAE,aAAa,SAAU,QAAO;AACpC,SAAO;AACT;AAqBO,SAAS,gBAAgBA,MAAU;AACxC,SAAOA,KAAI,WAAWA,KAAI,SAASA,KAAI;AACzC;AAoBO,SAAS,IAAI,YAAsD,QAAwC;AAChH,SAAO,IAAI,IAAI,UAAU,SAAS,GAAG,MAAM,CAAC;AAC9C;AAkCO,SAAS,UAAU,YAAsD,QAA2C;AACzH,SAAO,iBAAiB,SAAS,GAAG,OAAO,IAAI,kBAAkB,CAAC;AACpE;", "names": ["url"]}