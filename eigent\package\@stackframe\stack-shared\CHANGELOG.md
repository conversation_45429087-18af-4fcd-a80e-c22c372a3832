# @stackframe/stack-shared

## 2.8.12

### Patch Changes

- Various changes

## 2.8.11

### Patch Changes

- Various changes

## 2.8.10

### Patch Changes

- Various changes

## 2.8.9

## 2.8.8

## 2.8.7

## 2.8.6

### Patch Changes

- Various changes

## 2.8.5

### Patch Changes

- Various changes

## 2.8.4

### Patch Changes

- Various changes

## 2.8.3

### Patch Changes

- Various changes

## 2.8.2

### Patch Changes

- Various changes

## 2.8.1

## 2.8.0

### Minor Changes

- Various changes

## 2.7.30

### Patch Changes

- Various changes

## 2.7.29

### Patch Changes

- Various changes

## 2.7.28

### Patch Changes

- Various changes

## 2.7.27

### Patch Changes

- Various changes

## 2.7.26

### Patch Changes

- Various changes

## 2.7.25

## 2.7.24

## 2.7.23

### Patch Changes

- Various changes

## 2.7.22

### Patch Changes

- Various changes

## 2.7.21

### Patch Changes

- Various changes

## 2.7.20

### Patch Changes

- Various changes

## 2.7.19

### Patch Changes

- @stackframe/stack-sc@2.7.19

## 2.7.18

### Patch Changes

- Various changes
- Updated dependencies
  - @stackframe/stack-sc@2.7.18

## 2.7.17

### Patch Changes

- Various changes
  - @stackframe/stack-sc@2.7.17

## 2.7.16

### Patch Changes

- Various changes
  - @stackframe/stack-sc@2.7.16

## 2.7.15

### Patch Changes

- Various changes
  - @stackframe/stack-sc@2.7.15

## 2.7.14

### Patch Changes

- Various changes
  - @stackframe/stack-sc@2.7.14

## 2.7.13

### Patch Changes

- Various changes
  - @stackframe/stack-sc@2.7.13

## 2.7.12

### Patch Changes

- Various changes
- Updated dependencies
  - @stackframe/stack-sc@2.7.12

## 2.7.11

### Patch Changes

- @stackframe/stack-sc@2.7.11

## 2.7.10

### Patch Changes

- Various changes
- Updated dependencies
  - @stackframe/stack-sc@2.7.10

## 2.7.9

### Patch Changes

- Various changes
  - @stackframe/stack-sc@2.7.9

## 2.7.8

### Patch Changes

- Various changes
- Updated dependencies
  - @stackframe/stack-sc@2.7.8

## 2.7.7

### Patch Changes

- Various changes
  - @stackframe/stack-sc@2.7.7

## 2.7.6

### Patch Changes

- Fixed bugs, updated Neon requirements
- Updated dependencies
  - @stackframe/stack-sc@2.7.6

## 2.7.5

### Patch Changes

- Various changes
  - @stackframe/stack-sc@2.7.5

## 2.7.4

### Patch Changes

- @stackframe/stack-sc@2.7.4

## 2.7.3

### Patch Changes

- Various changes
  - @stackframe/stack-sc@2.7.3

## 2.7.2

### Patch Changes

- Various changes
  - @stackframe/stack-sc@2.7.2

## 2.7.1

### Patch Changes

- Various changes
  - @stackframe/stack-sc@2.7.1

## 2.7.0

### Minor Changes

- Various changes

### Patch Changes

- @stackframe/stack-sc@2.7.0

## 2.6.39

### Patch Changes

- Various changes
  - @stackframe/stack-sc@2.6.39

## 2.6.38

### Patch Changes

- Various changes
  - @stackframe/stack-sc@2.6.38

## 2.6.37

### Patch Changes

- Various changes
  - @stackframe/stack-sc@2.6.37

## 2.6.36

### Patch Changes

- Various updates
  - @stackframe/stack-sc@2.6.36

## 2.6.35

### Patch Changes

- Bugfixes
  - @stackframe/stack-sc@2.6.35

## 2.6.34

### Patch Changes

- Bugfixes
  - @stackframe/stack-sc@2.6.34

## 2.6.33

### Patch Changes

- @stackframe/stack-sc@2.6.33

## 2.6.32

### Patch Changes

- Bugfixes
  - @stackframe/stack-sc@2.6.32

## 2.6.31

### Patch Changes

- Bugfixes
  - @stackframe/stack-sc@2.6.31

## 2.6.30

### Patch Changes

- Bugfixes
  - @stackframe/stack-sc@2.6.30

## 2.6.29

### Patch Changes

- Bugfixes
  - @stackframe/stack-sc@2.6.29

## 2.6.28

### Patch Changes

- Bugfixes
  - @stackframe/stack-sc@2.6.28

## 2.6.27

### Patch Changes

- Bugfixes
  - @stackframe/stack-sc@2.6.27

## 2.6.26

### Patch Changes

- Various bugfixes
- Updated dependencies
  - @stackframe/stack-sc@2.6.26

## 2.6.25

### Patch Changes

- Translation overrides
  - @stackframe/stack-sc@2.6.25

## 2.6.24

### Patch Changes

- Bugfixes
  - @stackframe/stack-sc@2.6.24

## 2.6.23

### Patch Changes

- Bugfixes
- Updated dependencies
  - @stackframe/stack-sc@2.6.23

## 2.6.22

### Patch Changes

- Bugfixes
- Updated dependencies
  - @stackframe/stack-sc@2.6.22

## 2.6.21

### Patch Changes

- Fixed inviteUser
- Updated dependencies
  - @stackframe/stack-sc@2.6.21

## 2.6.20

### Patch Changes

- Next.js 15 fixes
- Updated dependencies
  - @stackframe/stack-sc@2.6.20

## 2.6.19

### Patch Changes

- Bugfixes
- Updated dependencies
  - @stackframe/stack-sc@2.6.19

## 2.6.18

### Patch Changes

- fixed user update bug
- Updated dependencies
  - @stackframe/stack-sc@2.6.18

## 2.6.17

### Patch Changes

- Loading skeletons
- Updated dependencies
  - @stackframe/stack-sc@2.6.17

## 2.6.16

### Patch Changes

- - list user pagination
  - fixed visual glitches
  - @stackframe/stack-sc@2.6.16

## 2.6.15

### Patch Changes

- Passkeys
  - @stackframe/stack-sc@2.6.15

## 2.6.14

### Patch Changes

- @stackframe/stack-sc@2.6.14

## 2.6.13

### Patch Changes

- Updated docs
  - @stackframe/stack-sc@2.6.13

## 2.6.12

### Patch Changes

- Updated account settings page
- Updated dependencies
  - @stackframe/stack-sc@2.6.12

## 2.6.11

### Patch Changes

- fixed account settings bugs
- Updated dependencies
  - @stackframe/stack-sc@2.6.11

## 2.6.10

### Patch Changes

- Various bugfixes
- Updated dependencies
  - @stackframe/stack-sc@2.6.10

## 2.6.9

### Patch Changes

- - New contact channel API
  - Fixed some visual gitches and typos
  - Bug fixes
- Updated dependencies
  - @stackframe/stack-sc@2.6.9

## 2.6.8

### Patch Changes

- Bugfixes
  - @stackframe/stack-sc@2.6.8

## 2.6.7

### Patch Changes

- Updated dependencies
  - @stackframe/stack-sc@2.6.7

## 2.6.6

### Patch Changes

- @stackframe/stack-sc@2.6.6

## 2.6.5

### Patch Changes

- Minor improvements
- Updated dependencies
  - @stackframe/stack-sc@2.6.5

## 2.6.4

### Patch Changes

- fixed small problems
- Updated dependencies
  - @stackframe/stack-sc@2.6.4

## 2.6.3

### Patch Changes

- Bugfixes
  - @stackframe/stack-sc@2.6.3

## 2.6.2

### Patch Changes

- Several bugfixes & typos
  - @stackframe/stack-sc@2.6.2

## 2.6.1

### Patch Changes

- @stackframe/stack-sc@2.6.1

## 2.6.0

### Minor Changes

- OTP login, more providers, and styling improvements

### Patch Changes

- Updated dependencies
  - @stackframe/stack-sc@2.6.0

## 2.5.37

### Patch Changes

- client side account deletion; new account setting style;
  - @stackframe/stack-sc@2.5.37

## 2.5.36

### Patch Changes

- added apple oauth
- Updated dependencies
  - @stackframe/stack-sc@2.5.36

## 2.5.35

### Patch Changes

- Doc improvements
  - @stackframe/stack-sc@2.5.35

## 2.5.34

### Patch Changes

- Internationalization
  - @stackframe/stack-sc@2.5.34

## 2.5.33

### Patch Changes

- Team membership webhooks
  - @stackframe/stack-sc@2.5.33

## 2.5.32

### Patch Changes

- Improved connected account performance
  - @stackframe/stack-sc@2.5.32

## 2.5.31

### Patch Changes

- JWKS
  - @stackframe/stack-sc@2.5.31

## 2.5.30

### Patch Changes

- More OAuth providers
  - @stackframe/stack-sc@2.5.30

## 2.5.29

### Patch Changes

- @stackframe/stack-sc@2.5.29

## 2.5.28

### Patch Changes

- Bugfixes
  - @stackframe/stack-sc@2.5.28

## 2.5.27

### Patch Changes

- Bugfixes
- Updated dependencies
  - @stackframe/stack-sc@2.5.27

## 2.5.26

### Patch Changes

- Bugfixes
- Updated dependencies
  - @stackframe/stack-sc@2.5.26

## 2.5.25

### Patch Changes

- GitLab OAuth provider
  - @stackframe/stack-sc@2.5.25

## 2.5.24

### Patch Changes

- Various bugfixes
  - @stackframe/stack-sc@2.5.24

## 2.5.23

### Patch Changes

- @stackframe/stack-sc@2.5.23

## 2.5.22

### Patch Changes

- Team metadata
  - @stackframe/stack-sc@2.5.22

## 2.5.21

### Patch Changes

- Discord OAuth provider
  - @stackframe/stack-sc@2.5.21

## 2.5.20

### Patch Changes

- Improved account settings
  - @stackframe/stack-sc@2.5.20

## 2.5.19

### Patch Changes

- Team frontend components
- Updated dependencies
  - @stackframe/stack-sc@2.5.19

## 2.5.18

### Patch Changes

- Multi-factor authentication
  - @stackframe/stack-sc@2.5.18

## 2.5.17

### Patch Changes

- Bugfixes
  - @stackframe/stack-sc@2.5.17

## 2.5.16

### Patch Changes

- @stackframe/stack-sc@2.5.16

## 2.5.15

### Patch Changes

- Webhooks
- Updated dependencies
  - @stackframe/stack-sc@2.5.15

## 2.5.14

### Patch Changes

- @stackframe/stack-sc@2.5.14

## 2.5.13

### Patch Changes

- Add server side get connected account
  - @stackframe/stack-sc@2.5.13

## 2.5.12

### Patch Changes

- Bugfixes
  - @stackframe/stack-sc@2.5.12

## 2.5.11

### Patch Changes

- @stackframe/stack-sc@2.5.11

## 2.5.10

### Patch Changes

- Facebook Business support
  - @stackframe/stack-sc@2.5.10

## 2.5.9

### Patch Changes

- Impersonation
  - @stackframe/stack-sc@2.5.9

## 2.5.8

### Patch Changes

- Improved docs
  - @stackframe/stack-sc@2.5.8

## 2.5.7

### Patch Changes

- @stackframe/stack-sc@2.5.7

## 2.5.6

### Patch Changes

- Various bugfixes
  - @stackframe/stack-sc@2.5.6

## 2.5.5

### Patch Changes

- @stackframe/stack-sc@2.5.5

## 2.5.4

### Patch Changes

- Backend rework
- Updated dependencies
  - @stackframe/stack-sc@2.5.4

## 2.5.3

### Patch Changes

- Bugfixes
- Updated dependencies
  - @stackframe/stack-sc@2.5.3

## 2.5.2

### Patch Changes

- Team profile pictures
  - @stackframe/stack-sc@2.5.2

## 2.5.1

### Patch Changes

- New backend endpoints
  - @stackframe/stack-sc@2.5.1

## 2.5.0

### Minor Changes

- Client teams and many bugfixes

### Patch Changes

- @stackframe/stack-sc@2.5.0

## 2.4.28

### Patch Changes

- Bugfixes
  - @stackframe/stack-sc@2.4.28

## 2.4.27

### Patch Changes

- @stackframe/stack-sc@2.4.27

## 2.4.26

### Patch Changes

- Improve docs
- Updated dependencies
  - @stackframe/stack-sc@2.4.26

## 2.4.25

### Patch Changes

- Docs update
  - @stackframe/stack-sc@2.4.25

## 2.4.24

### Patch Changes

- Team switcher
  - @stackframe/stack-sc@2.4.24

## 2.4.23

### Patch Changes

- Bugfixes
  - @stackframe/stack-sc@2.4.23

## 2.4.22

### Patch Changes

- OAuth scopes
- Updated dependencies
  - @stackframe/stack-sc@2.4.22

## 2.4.21

### Patch Changes

- Bugfixes
- Updated dependencies
  - @stackframe/stack-sc@2.4.21

## 2.4.20

### Patch Changes

- Support multiple projects on the same domain
- Updated dependencies
  - @stackframe/stack-sc@2.4.20

## 2.4.19

### Patch Changes

- Sync package versions
- Updated dependencies
  - @stackframe/stack-sc@2.4.19

## 2.4.14

### Patch Changes

- Bugfixes

## 2.4.13

### Patch Changes

- Improve setup flow
- Updated dependencies
  - @stackframe/stack-sc@1.5.6

## 2.4.12

### Patch Changes

- Improved client styling, added login form, added spotify oauth

## 2.4.11

### Patch Changes

- Added email editor

## 2.4.10

### Patch Changes

- Bug fixes
- Updated dependencies
  - @stackframe/stack-sc@1.5.5

## 2.4.9

### Patch Changes

- Bugfixes
- Updated dependencies
  - @stackframe/stack-sc@1.5.4

## 2.4.8

### Patch Changes

- Improved UUID generation

## 2.4.7

### Patch Changes

- Bugfixes
- Updated dependencies
  - @stackframe/stack-sc@1.5.3

## 2.4.6

### Patch Changes

- Remove crypto-browserify dependency

## 2.4.5

### Patch Changes

- Team selection

## 2.4.4

### Patch Changes

- UX improvements

## 2.4.3

### Patch Changes

- CRUD schemas
- Updated dependencies
  - @stackframe/stack-sc@1.5.2

## 2.4.2

### Patch Changes

- New projects page

## 2.4.1

### Patch Changes

- Teams, permissions and RBAC
- Updated dependencies
  - @stackframe/stack-sc@1.5.1

## 2.4.0

### Minor Changes

- Middleware support

## 2.3.6

### Patch Changes

- Bugfixes

## 2.3.5

### Patch Changes

- CommonJS support

## 2.3.4

### Patch Changes

- Bugfixes

## 2.3.3

### Patch Changes

- Partial pre-rendering

## 2.3.2

### Patch Changes

- Magic link configuration

## 2.3.1

### Patch Changes

- Add README file

## 2.3.0

### Minor Changes

- 96c26a7: added magic link

### Patch Changes

- Various small improvements

## 2.2.2

### Patch Changes

- 5909ecd: fixed bugs

## 2.2.1

### Patch Changes

- fixed minor errors

## 2.2.0

### Minor Changes

- 2995d96: Added new UserButton component and Account setting page

## 2.1.0

### Minor Changes

- 9e9907f: Added new stack UI system

## 2.0.0

### Major Changes

- 948252f: removed redirect URL in function options, redirect automatically to default URL

## 1.2.1

### Patch Changes

- fixed import bugs

## 1.2.0

### Minor Changes

- Fixed infinite reload bug, added dashboard provider update

## 1.1.0

### Minor Changes

- fixed bugs
