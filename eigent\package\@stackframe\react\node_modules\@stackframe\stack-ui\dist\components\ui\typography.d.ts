import { type VariantProps } from "class-variance-authority";
import React from "react";
declare const Typography: React.FC<React.HTMLAttributes<HTMLHeadingElement> & VariantProps<(props?: ({
    type?: "h1" | "h2" | "h3" | "h4" | "label" | "p" | "footnote" | null | undefined;
    variant?: "destructive" | "secondary" | "success" | "primary" | null | undefined;
} & import("class-variance-authority/types").ClassProp) | undefined) => string> & {
    ref?: React.Ref<HTMLHeadingElement> | undefined;
}>;
export { Typography };
