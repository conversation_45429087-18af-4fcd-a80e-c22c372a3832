{"version": 3, "sources": ["../../../../../src/components-page/account-settings/teams/team-profile-user-section.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { Team } from \"../../..\";\nimport { useUser } from \"../../../lib/hooks\";\nimport { useTranslation } from \"../../../lib/translations\";\nimport { EditableText } from \"../editable-text\";\nimport { Section } from \"../section\";\n\nexport function TeamUserProfileSection(props: { team: Team }) {\n  const { t } = useTranslation();\n  const user = useUser({ or: 'redirect' });\n  const profile = user.useTeamProfile(props.team);\n\n  return (\n    <Section\n      title={t(\"Team user name\")}\n      description={t(\"Overwrite your user display name in this team\")}\n    >\n      <EditableText\n        value={profile.displayName || ''}\n        onSave={async (newDisplayName) => {\n          await profile.update({ displayName: newDisplayName });\n        }}\n      />\n    </Section>\n  );\n}\n"], "mappings": ";AAKA,SAAS,eAAe;AACxB,SAAS,sBAAsB;AAC/B,SAAS,oBAAoB;AAC7B,SAAS,eAAe;AAYlB;AAVC,SAAS,uBAAuB,OAAuB;AAC5D,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,OAAO,QAAQ,EAAE,IAAI,WAAW,CAAC;AACvC,QAAM,UAAU,KAAK,eAAe,MAAM,IAAI;AAE9C,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO,EAAE,gBAAgB;AAAA,MACzB,aAAa,EAAE,+CAA+C;AAAA,MAE9D;AAAA,QAAC;AAAA;AAAA,UACC,OAAO,QAAQ,eAAe;AAAA,UAC9B,QAAQ,OAAO,mBAAmB;AAChC,kBAAM,QAAQ,OAAO,EAAE,aAAa,eAAe,CAAC;AAAA,UACtD;AAAA;AAAA,MACF;AAAA;AAAA,EACF;AAEJ;", "names": []}