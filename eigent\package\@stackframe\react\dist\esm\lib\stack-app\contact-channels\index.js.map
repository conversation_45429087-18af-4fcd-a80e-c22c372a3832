{"version": 3, "sources": ["../../../../../src/lib/stack-app/contact-channels/index.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { ContactChannelsCrud } from \"@stackframe/stack-shared/dist/interface/crud/contact-channels\";\n\n\nexport type ContactChannel = {\n  id: string,\n  value: string,\n  type: 'email',\n  isPrimary: boolean,\n  isVerified: boolean,\n  usedForAuth: boolean,\n\n  sendVerificationEmail(): Promise<void>,\n  update(data: ContactChannelUpdateOptions): Promise<void>,\n  delete(): Promise<void>,\n}\n\nexport type ContactChannelCreateOptions = {\n  value: string,\n  type: 'email',\n  usedForAuth: boolean,\n  isPrimary?: boolean,\n}\n\nexport function contactChannelCreateOptionsToCrud(userId: string, options: ContactChannelCreateOptions): ContactChannelsCrud[\"Client\"][\"Create\"] {\n  return {\n    value: options.value,\n    type: options.type,\n    used_for_auth: options.usedForAuth,\n    is_primary: options.isPrimary,\n    user_id: userId,\n  };\n}\n\nexport type ContactChannelUpdateOptions = {\n  usedForAuth?: boolean,\n  value?: string,\n  isPrimary?: boolean,\n}\n\nexport function contactChannelUpdateOptionsToCrud(options: ContactChannelUpdateOptions): ContactChannelsCrud[\"Client\"][\"Update\"] {\n  return {\n    value: options.value,\n    used_for_auth: options.usedForAuth,\n    is_primary: options.isPrimary,\n  };\n}\n\nexport type ServerContactChannel = ContactChannel & {\n  update(data: ServerContactChannelUpdateOptions): Promise<void>,\n}\nexport type ServerContactChannelUpdateOptions = ContactChannelUpdateOptions & {\n  isVerified?: boolean,\n}\n\nexport function serverContactChannelUpdateOptionsToCrud(options: ServerContactChannelUpdateOptions): ContactChannelsCrud[\"Server\"][\"Update\"] {\n  return {\n    value: options.value,\n    is_verified: options.isVerified,\n    used_for_auth: options.usedForAuth,\n    is_primary: options.isPrimary,\n  };\n}\n\nexport type ServerContactChannelCreateOptions = ContactChannelCreateOptions & {\n  isVerified?: boolean,\n}\nexport function serverContactChannelCreateOptionsToCrud(userId: string, options: ServerContactChannelCreateOptions): ContactChannelsCrud[\"Server\"][\"Create\"] {\n  return {\n    type: options.type,\n    value: options.value,\n    is_verified: options.isVerified,\n    user_id: userId,\n    used_for_auth: options.usedForAuth,\n    is_primary: options.isPrimary,\n  };\n}\n"], "mappings": ";AA2BO,SAAS,kCAAkC,QAAgB,SAA+E;AAC/I,SAAO;AAAA,IACL,OAAO,QAAQ;AAAA,IACf,MAAM,QAAQ;AAAA,IACd,eAAe,QAAQ;AAAA,IACvB,YAAY,QAAQ;AAAA,IACpB,SAAS;AAAA,EACX;AACF;AAQO,SAAS,kCAAkC,SAA+E;AAC/H,SAAO;AAAA,IACL,OAAO,QAAQ;AAAA,IACf,eAAe,QAAQ;AAAA,IACvB,YAAY,QAAQ;AAAA,EACtB;AACF;AASO,SAAS,wCAAwC,SAAqF;AAC3I,SAAO;AAAA,IACL,OAAO,QAAQ;AAAA,IACf,aAAa,QAAQ;AAAA,IACrB,eAAe,QAAQ;AAAA,IACvB,YAAY,QAAQ;AAAA,EACtB;AACF;AAKO,SAAS,wCAAwC,QAAgB,SAAqF;AAC3J,SAAO;AAAA,IACL,MAAM,QAAQ;AAAA,IACd,OAAO,QAAQ;AAAA,IACf,aAAa,QAAQ;AAAA,IACrB,SAAS;AAAA,IACT,eAAe,QAAQ;AAAA,IACvB,YAAY,QAAQ;AAAA,EACtB;AACF;", "names": []}