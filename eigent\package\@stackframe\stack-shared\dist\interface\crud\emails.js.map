{"version": 3, "sources": ["../../../src/interface/crud/emails.ts"], "sourcesContent": ["import { createCrud, CrudTypeOf } from \"../../crud\";\nimport * as fieldSchema from \"../../schema-fields\";\nimport { emailConfigWithoutPasswordSchema } from \"./projects\";\n\n\nexport const sentEmailReadSchema = fieldSchema.yupObject({\n  id: fieldSchema.yupString().defined(),\n  subject: fieldSchema.yupString().defined(),\n  sent_at_millis: fieldSchema.yupNumber().defined(),\n  to: fieldSchema.yupArray(fieldSchema.yupString().defined()),\n  sender_config: emailConfigWithoutPasswordSchema.defined(),\n  error: fieldSchema.yupMixed().nullable().optional(),\n}).defined();\n\nexport const internalEmailsCrud = createCrud({\n  adminReadSchema: sentEmailReadSchema,\n});\n\nexport type InternalEmailsCrud = CrudTypeOf<typeof internalEmailsCrud>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAuC;AACvC,kBAA6B;AAC7B,sBAAiD;AAG1C,IAAM,sBAAkC,sBAAU;AAAA,EACvD,IAAgB,sBAAU,EAAE,QAAQ;AAAA,EACpC,SAAqB,sBAAU,EAAE,QAAQ;AAAA,EACzC,gBAA4B,sBAAU,EAAE,QAAQ;AAAA,EAChD,IAAgB,qBAAqB,sBAAU,EAAE,QAAQ,CAAC;AAAA,EAC1D,eAAe,iDAAiC,QAAQ;AAAA,EACxD,OAAmB,qBAAS,EAAE,SAAS,EAAE,SAAS;AACpD,CAAC,EAAE,QAAQ;AAEJ,IAAM,yBAAqB,wBAAW;AAAA,EAC3C,iBAAiB;AACnB,CAAC;", "names": []}