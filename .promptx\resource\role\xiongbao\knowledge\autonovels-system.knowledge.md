<knowledge>
  ## AutoNovels网文创作核心知识

  ### 核心创作标准（基于AutoNovels项目）
  - **大纲创作标准**：专业策划角度的故事结构设计，包含核心冲突提炼、转折点设计、人物塑造立体化
  - **章节目录标准**：合理的章节划分和标题设计，确保每章有明确推进作用和独立看点
  - **章节细纲标准**：精确的章节结构规划，包含核心看点、情节线索、感情线发展、伏笔设置
  - **正文内容标准**：专业写作技巧指导，多感官描写要求（视觉、听觉、触觉）、对话技巧、环境渲染

  ### 双风格写作体系（基于猫不秃和一月九十秋）

  #### 猫不秃风格特征
  - **现代网感强烈**：标题直击痛点，语言贴合网文读者习惯
  - **快节奏爽文**：重生抢机缘，直接开干，不拖泥带水
  - **游戏化思维**：用游戏术语和概念包装现实情节
  - **简洁有力**：语言直白，情节推进迅速，每章必有爽点

  #### 一月九十秋风格特征
  - **复杂世界观**：多层次概念构建，设定新颖独特
  - **创意融合**：巧妙结合多种元素（克系+游戏+末世）
  - **解谜推理**：在情节中埋设思考点和悬念
  - **幽默基调**：主角"乐子人"属性，轻松幽默表达

  #### 风格选择策略
  - **重生爽文、系统流** → 猫不秃风格（快节奏、高爽点）
  - **无限流、科幻解谜** → 一月九十秋风格（复杂设定、创意导向）
  - **都市异能、游戏竞技** → 混合风格（平衡爽点与创意）

  #### 融合技法原则
  - **开篇用猫不秃，深入用一月九十秋**
  - **爽点用猫不秃，悬念用一月九十秋**
  - **战斗用猫不秃，解谜用一月九十秋**
  - **对话用猫不秃，心理用一月九十秋**

  ### 基础创作流程
  1. **生成书名**：根据故事设定和风格特点生成吸引人的书名
  2. **大纲创作**：按照AutoNovels标准创建富有爆点的小说大纲
  3. **章节目录**：基于大纲合理划分章节，设计吸引人的章节标题
  4. **章节细纲**：展开具体章节的详细创作细纲
  5. **正文创作**：基于章节细纲创作高质量的章节内容
</knowledge>
