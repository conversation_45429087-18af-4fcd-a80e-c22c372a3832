{"version": 3, "sources": ["../../src/providers/theme-provider.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { deindent } from \"@stackframe/stack-shared/dist/utils/strings\";\nimport Color from \"color\";\nimport React from \"react\";\nimport { globalCSS } from \"../generated/global-css\";\nimport { BrowserScript } from \"../utils/browser-script\";\nimport { DEFAULT_THEME } from \"../utils/constants\";\n\ntype Colors = {\n  background: string,\n  foreground: string,\n  card: string,\n  cardForeground: string,\n  popover: string,\n  popoverForeground: string,\n  primary: string,\n  primaryForeground: string,\n  secondary: string,\n  secondaryForeground: string,\n  muted: string,\n  mutedForeground: string,\n  accent: string,\n  accentForeground: string,\n  destructive: string,\n  destructiveForeground: string,\n  border: string,\n  input: string,\n  ring: string,\n}\n\nexport type Theme = {\n  light: Colors,\n  dark: Colors,\n  radius: string,\n};\n\ntype ThemeConfig = {\n  light?: Partial<Colors>,\n  dark?: Partial<Colors>,\n} & Partial<Omit<Theme, 'light' | 'dark'>>;\n\nfunction convertColorToCSSVars(obj: Record<string, string>) {\n  return Object.fromEntries(Object.entries(obj).map(([key, value]) => {\n    const color = Color(value).hsl().array();\n    return [\n      // Convert camelCase key to dash-case\n      key.replace(/[A-Z]/g, m => `-${m.toLowerCase()}`),\n      // Convert color to CSS HSL string\n      `${color[0]} ${color[1]}% ${color[2]}%`\n    ];\n  }));\n}\n\nfunction convertColorsToCSS(theme: Theme) {\n  const { dark, light, ...rest } = theme;\n  const colors = {\n    light: { ...convertColorToCSSVars(light), ...rest },\n    dark: convertColorToCSSVars(dark),\n  };\n\n  function colorsToCSSVars(colors: Record<string, string>) {\n    return Object.entries(colors).map((params) => {\n      return `--${params[0]}: ${params[1]};\\n`;\n    }).join('');\n  }\n\n  return deindent`\n  .stack-scope {\n  ${colorsToCSSVars(colors.light)}\n  }\n  html:has(head > [data-stack-theme=\"dark\"]) .stack-scope {\n  ${colorsToCSSVars(colors.dark)}\n  }`;\n}\n\n\nexport function StackTheme({\n  theme,\n  children,\n  nonce,\n} : {\n  theme?: ThemeConfig,\n  children?: React.ReactNode,\n  nonce?: string,\n}) {\n  const themeValue: Theme = {\n    ...DEFAULT_THEME,\n    ...theme,\n    dark: { ...DEFAULT_THEME.dark, ...theme?.dark },\n    light: { ...DEFAULT_THEME.light, ...theme?.light },\n  };\n\n  return (\n    <>\n      <BrowserScript nonce={nonce} />\n      <style\n        suppressHydrationWarning // we need this since the nonce can differ between client and server\n        nonce={nonce}\n        dangerouslySetInnerHTML={{\n          __html: globalCSS + \"\\n\" + convertColorsToCSS(themeValue),\n        }}\n      />\n      {children}\n    </>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,qBAAyB;AACzB,mBAAkB;AAElB,wBAA0B;AAC1B,4BAA8B;AAC9B,uBAA8B;AAuF1B;AApDJ,SAAS,sBAAsB,KAA6B;AAC1D,SAAO,OAAO,YAAY,OAAO,QAAQ,GAAG,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AAClE,UAAM,YAAQ,aAAAA,SAAM,KAAK,EAAE,IAAI,EAAE,MAAM;AACvC,WAAO;AAAA;AAAA,MAEL,IAAI,QAAQ,UAAU,OAAK,IAAI,EAAE,YAAY,CAAC,EAAE;AAAA;AAAA,MAEhD,GAAG,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC;AAAA,IACtC;AAAA,EACF,CAAC,CAAC;AACJ;AAEA,SAAS,mBAAmB,OAAc;AACxC,QAAM,EAAE,MAAM,OAAO,GAAG,KAAK,IAAI;AACjC,QAAM,SAAS;AAAA,IACb,OAAO,EAAE,GAAG,sBAAsB,KAAK,GAAG,GAAG,KAAK;AAAA,IAClD,MAAM,sBAAsB,IAAI;AAAA,EAClC;AAEA,WAAS,gBAAgBC,SAAgC;AACvD,WAAO,OAAO,QAAQA,OAAM,EAAE,IAAI,CAAC,WAAW;AAC5C,aAAO,KAAK,OAAO,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC;AAAA;AAAA,IACrC,CAAC,EAAE,KAAK,EAAE;AAAA,EACZ;AAEA,SAAO;AAAA;AAAA,IAEL,gBAAgB,OAAO,KAAK,CAAC;AAAA;AAAA;AAAA,IAG7B,gBAAgB,OAAO,IAAI,CAAC;AAAA;AAEhC;AAGO,SAAS,WAAW;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AACF,GAIG;AACD,QAAM,aAAoB;AAAA,IACxB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,MAAM,EAAE,GAAG,+BAAc,MAAM,GAAG,OAAO,KAAK;AAAA,IAC9C,OAAO,EAAE,GAAG,+BAAc,OAAO,GAAG,OAAO,MAAM;AAAA,EACnD;AAEA,SACE,4EACE;AAAA,gDAAC,uCAAc,OAAc;AAAA,IAC7B;AAAA,MAAC;AAAA;AAAA,QACC,0BAAwB;AAAA,QACxB;AAAA,QACA,yBAAyB;AAAA,UACvB,QAAQ,8BAAY,OAAO,mBAAmB,UAAU;AAAA,QAC1D;AAAA;AAAA,IACF;AAAA,IACC;AAAA,KACH;AAEJ;", "names": ["Color", "colors"]}