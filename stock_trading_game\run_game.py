#!/usr/bin/env python3
"""
🐾 模拟炒股游戏启动器
Stock Trading Game Launcher

一键启动完整的模拟炒股游戏系统
"""

import os
import sys
import subprocess
import time
import threading
import webbrowser
from pathlib import Path

def print_welcome():
    """打印欢迎信息"""
    welcome = """
    🎮 模拟炒股游戏启动器
    =====================
    
    🐾 欢迎使用专业的A股模拟交易系统！
    
    系统特性:
    📊 实时股价模拟 - 基于真实市场数据的价格波动
    💰 完整交易系统 - 支持市价单、限价单等多种订单
    👥 用户管理系统 - 完整的用户注册、登录、资产管理
    📱 现代化界面 - React + TypeScript + Ant Design
    🔄 实时数据推送 - WebSocket实时股价和交易数据
    🎯 游戏化功能 - 排行榜、成就系统、投资组合分析
    
    正在启动系统...
    """
    print(welcome)

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major >= 3 and python_version.minor >= 9:
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro}")
        print("   需要Python 3.9或更高版本")
        return False
    
    # 检查项目文件
    required_files = [
        "backend/main.py",
        "backend/requirements.txt",
        "frontend/package.json",
        "frontend/src/App.tsx"
    ]
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ 缺少文件: {file_path}")
            return False
    
    return True

def install_dependencies():
    """安装依赖"""
    print("\n📦 检查和安装依赖...")
    
    # 安装后端依赖
    print("📦 安装后端Python依赖...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "backend/requirements.txt"
        ], check=True, capture_output=True)
        print("✅ 后端依赖安装完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 后端依赖安装失败: {e}")
        return False
    
    # 检查Node.js
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js版本: {result.stdout.strip()}")
            
            # 安装前端依赖
            print("📦 安装前端Node.js依赖...")
            try:
                subprocess.run(["npm", "install"], cwd="frontend", check=True, capture_output=True)
                print("✅ 前端依赖安装完成")
            except subprocess.CalledProcessError as e:
                print(f"❌ 前端依赖安装失败: {e}")
                return False
        else:
            print("❌ Node.js未安装，跳过前端依赖安装")
            print("   请安装Node.js后手动运行: cd frontend && npm install")
            return False
    except FileNotFoundError:
        print("❌ Node.js未安装，跳过前端依赖安装")
        print("   请安装Node.js后手动运行: cd frontend && npm install")
        return False
    
    return True

def init_database():
    """初始化数据库"""
    print("\n🗄️ 初始化数据库...")
    try:
        # 切换到后端目录并初始化数据库
        subprocess.run([
            sys.executable, "-c", 
            "from app.database import init_db; init_db()"
        ], cwd="backend", check=True)
        print("✅ 数据库初始化完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    try:
        # 启动FastAPI服务
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000",
            "--reload"
        ], cwd="backend")
        
        # 等待服务启动
        time.sleep(3)
        print("✅ 后端服务已启动 (http://localhost:8000)")
        return process
        
    except Exception as e:
        print(f"❌ 后端服务启动失败: {e}")
        return None

def start_frontend():
    """启动前端服务"""
    print("🚀 启动前端服务...")
    try:
        # 等待后端完全启动
        time.sleep(5)
        
        # 启动React开发服务器
        process = subprocess.Popen([
            "npm", "start"
        ], cwd="frontend")
        
        # 等待前端启动
        time.sleep(10)
        print("✅ 前端服务已启动 (http://localhost:3000)")
        
        # 自动打开浏览器
        try:
            webbrowser.open("http://localhost:3000")
            print("🌐 已自动打开浏览器")
        except:
            print("🌐 请手动打开浏览器访问: http://localhost:3000")
        
        return process
        
    except Exception as e:
        print(f"❌ 前端服务启动失败: {e}")
        return None

def show_usage_info():
    """显示使用说明"""
    info = """
    🎮 系统已启动完成！
    ==================
    
    📍 访问地址:
    • 前端应用: http://localhost:3000
    • 后端API: http://localhost:8000
    • API文档: http://localhost:8000/docs
    
    🎯 功能说明:
    • 市场概览 - 查看整体市场数据和热门股票
    • 股票列表 - 浏览所有股票信息和实时价格
    • 交易中心 - 进行买卖交易操作
    • 个人资产 - 查看投资组合和盈亏情况
    
    👤 默认账户:
    • 用户名: demo
    • 密码: demo123
    • 初始资金: 100,000元
    
    🛑 停止服务:
    按 Ctrl+C 停止所有服务
    
    🐾 祝您投资愉快！Happy Trading!
    """
    print(info)

def main():
    """主函数"""
    print_welcome()
    
    # 检查环境
    if not check_environment():
        print("❌ 环境检查失败，请解决上述问题后重试")
        return
    
    # 询问是否安装依赖
    install_deps = input("\n是否需要安装/更新依赖？(y/N): ").lower().strip()
    if install_deps in ['y', 'yes']:
        if not install_dependencies():
            print("❌ 依赖安装失败")
            return
    
    # 初始化数据库
    if not init_database():
        print("❌ 数据库初始化失败")
        return
    
    print("\n🎮 启动游戏服务...")
    
    try:
        # 启动后端服务
        backend_process = start_backend()
        if not backend_process:
            print("❌ 后端服务启动失败")
            return
        
        # 在新线程中启动前端服务
        frontend_process = None
        def start_frontend_thread():
            nonlocal frontend_process
            frontend_process = start_frontend()
        
        frontend_thread = threading.Thread(target=start_frontend_thread, daemon=True)
        frontend_thread.start()
        
        # 等待前端启动
        frontend_thread.join(timeout=30)
        
        # 显示使用说明
        show_usage_info()
        
        # 等待用户中断
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务...")
            
            # 停止后端服务
            if backend_process:
                backend_process.terminate()
                backend_process.wait()
                print("✅ 后端服务已停止")
            
            # 停止前端服务
            if frontend_process:
                frontend_process.terminate()
                frontend_process.wait()
                print("✅ 前端服务已停止")
            
            print("👋 感谢使用模拟炒股游戏！")
            
    except Exception as e:
        print(f"❌ 启动过程中发生错误: {e}")

if __name__ == "__main__":
    main()
