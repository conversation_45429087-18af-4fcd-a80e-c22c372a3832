import requests
import json
from typing import List
import time


def read_urls_from_file(file_path: str) -> List[str]:
    """
    从文本文件中读取URL列表

    Args:
        file_path: URL文件的路径

    Returns:
        List[str]: URL列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            # 读取所有行,去除空白字符,过滤掉空行
            urls = [line.strip() for line in f.readlines() if line.strip()]
        return urls
    except Exception as e:
        print(f"读取文件出错: {str(e)}")
        return []


def submit_urls_to_indexnow(urls: List[str], api_key: str) -> dict:
    """
    向IndexNow提交URL列表以进行索引

    Args:
        urls: 要提交的URL列表
        api_key: IndexNow API密钥

    Returns:
        dict: API响应结果
    """
    # API端点
    endpoint = "https://api.indexnow.org/IndexNow"

    # 准备请求数据
    payload = {
        "host": "faisco.com",
        "key": api_key,
        "keyLocation": f"https://faisco.com/{api_key}.txt",
        "urlList": urls
    }

    # 设置请求头
    headers = {
        "Content-Type": "application/json; charset=utf-8"
    }

    try:
        # 发送POST请求
        response = requests.post(
            endpoint,
            headers=headers,
            json=payload
        )

        # 检查响应状态
        response.raise_for_status()

        # 尝试解析JSON响应
        try:
            return response.json()
        except json.JSONDecodeError:
            return {"status": "success", "message": response.text}

    except requests.exceptions.RequestException as e:
        return {
            "status": "error",
            "message": str(e)
        }


def batch_submit_urls(urls: List[str], api_key: str, batch_size: int = 500) -> List[dict]:
    """
    批量提交URL列表,每批最多提交指定数量的URL

    Args:
        urls: 完整的URL列表
        api_key: IndexNow API密钥
        batch_size: 每批提交的最大URL数量

    Returns:
        List[dict]: 每批提交的响应结果列表
    """
    results = []
    total_urls = len(urls)

    # 计算需要分几批处理
    batch_count = (total_urls + batch_size - 1) // batch_size

    for i in range(batch_count):
        start_idx = i * batch_size
        end_idx = min((i + 1) * batch_size, total_urls)
        batch_urls = urls[start_idx:end_idx]

        print(f"\n处理第 {i + 1}/{batch_count} 批, URLs: {start_idx + 1} - {end_idx}")

        # 提交当前批次的URL
        result = submit_urls_to_indexnow(batch_urls, api_key)
        results.append(result)

        # 如果不是最后一批,等待1秒再提交下一批
        if i < batch_count - 1:
            print("等待1秒后处理下一批...")
            time.sleep(1)

    return results


# 使用示例
if __name__ == "__main__":
    # 你的API密钥
    api_key = "31ee8dc121764ef5ba4a6d114c841abc"

    # 从文件读取URL列表
    urls_to_submit = read_urls_from_file("E:/urls.txt")

    # 检查是否成功读取到URL
    if not urls_to_submit:
        print("没有读取到任何URL,请检查文件内容")
        exit()

    # 打印读取到的URL数量
    total_urls = len(urls_to_submit)
    print(f"从文件中读取到 {total_urls} 个URL")

    # 批量提交URL
    results = batch_submit_urls(urls_to_submit, api_key)

    # 打印每批次的处理结果
    print("\n处理结果汇总:")
    for i, result in enumerate(results, 1):
        print(f"\n第 {i} 批结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))