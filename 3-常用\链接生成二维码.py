import qrcode


def generate_qrcode(url, filename):
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(url)
    qr.make(fit=True)

    img = qr.make_image(fill_color="black", back_color="white")
    img.save(filename)


url = input("请输入要转成二维码的链接：")
filename = "qrcode.png"
generate_qrcode(url, filename)
ddd = input("生成完毕，按回车键结束对话。")
