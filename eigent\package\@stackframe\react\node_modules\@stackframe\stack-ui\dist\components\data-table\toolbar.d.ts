import { ColumnFiltersState, SortingState, Table } from "@tanstack/react-table";
type DataTableToolbarProps<TData> = {
    table: Table<TData>;
    toolbarRender?: (table: Table<TData>) => React.ReactNode;
    showDefaultToolbar?: boolean;
    defaultColumnFilters: ColumnFiltersState;
    defaultSorting: SortingState;
};
export declare function DataTableToolbar<TData>({ table, toolbarRender, showDefaultToolbar, defaultColumnFilters, defaultSorting, }: DataTableToolbarProps<TData>): import("react/jsx-runtime").JSX.Element;
export {};
