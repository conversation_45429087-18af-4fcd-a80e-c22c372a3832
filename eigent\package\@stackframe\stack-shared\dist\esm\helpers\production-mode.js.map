{"version": 3, "sources": ["../../../src/helpers/production-mode.ts"], "sourcesContent": ["import { ProjectsCrud } from \"../interface/crud/projects\";\nimport { StackAssertionError, captureError } from \"../utils/errors\";\nimport { isLocalhost } from \"../utils/urls\";\n\nexport type ProductionModeError = {\n  message: string,\n  relativeFixUrl: `/${string}`,\n};\n\nexport function getProductionModeErrors(project: ProjectsCrud[\"Admin\"][\"Read\"]): ProductionModeError[] {\n  const errors: ProductionModeError[] = [];\n  const domainsFixUrl = `/projects/${project.id}/domains` as const;\n\n  if (project.config.allow_localhost) {\n    errors.push({\n      message: \"Localhost is not allowed in production mode, turn off 'Allow localhost' in project settings\",\n      relativeFixUrl: domainsFixUrl,\n    });\n  }\n\n  for (const { domain } of project.config.domains) {\n    let url;\n    try {\n      url = new URL(domain);\n    } catch (e) {\n      captureError(\"production-mode-domain-not-valid\", new StackAssertionError(\"Domain was somehow not a valid URL; we should've caught this when setting the domain in the first place\", {\n        domain,\n        projectId: project\n      }));\n      errors.push({\n        message: \"Trusted domain is not a valid URL: \" + domain,\n        relativeFixUrl: domainsFixUrl,\n      });\n      continue;\n    }\n\n    if (isLocalhost(url)) {\n      errors.push({\n        message: \"Localhost domains are not allowed to be trusted in production mode: \" + domain,\n        relativeFixUrl: domainsFixUrl,\n      });\n    } else if (url.hostname.match(/^\\d+(\\.\\d+)*$/)) {\n      errors.push({\n        message: \"Direct IPs are not valid for trusted domains in production mode: \" + domain,\n        relativeFixUrl: domainsFixUrl,\n      });\n    } else if (url.protocol !== \"https:\") {\n      errors.push({\n        message: \"Trusted domains should be HTTPS: \" + domain,\n        relativeFixUrl: domainsFixUrl,\n      });\n    }\n  }\n\n  return errors;\n}\n"], "mappings": ";AACA,SAAS,qBAAqB,oBAAoB;AAClD,SAAS,mBAAmB;AAOrB,SAAS,wBAAwB,SAA+D;AACrG,QAAM,SAAgC,CAAC;AACvC,QAAM,gBAAgB,aAAa,QAAQ,EAAE;AAE7C,MAAI,QAAQ,OAAO,iBAAiB;AAClC,WAAO,KAAK;AAAA,MACV,SAAS;AAAA,MACT,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAEA,aAAW,EAAE,OAAO,KAAK,QAAQ,OAAO,SAAS;AAC/C,QAAI;AACJ,QAAI;AACF,YAAM,IAAI,IAAI,MAAM;AAAA,IACtB,SAAS,GAAG;AACV,mBAAa,oCAAoC,IAAI,oBAAoB,2GAA2G;AAAA,QAClL;AAAA,QACA,WAAW;AAAA,MACb,CAAC,CAAC;AACF,aAAO,KAAK;AAAA,QACV,SAAS,wCAAwC;AAAA,QACjD,gBAAgB;AAAA,MAClB,CAAC;AACD;AAAA,IACF;AAEA,QAAI,YAAY,GAAG,GAAG;AACpB,aAAO,KAAK;AAAA,QACV,SAAS,yEAAyE;AAAA,QAClF,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH,WAAW,IAAI,SAAS,MAAM,eAAe,GAAG;AAC9C,aAAO,KAAK;AAAA,QACV,SAAS,sEAAsE;AAAA,QAC/E,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH,WAAW,IAAI,aAAa,UAAU;AACpC,aAAO,KAAK;AAAA,QACV,SAAS,sCAAsC;AAAA,QAC/C,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO;AACT;", "names": []}