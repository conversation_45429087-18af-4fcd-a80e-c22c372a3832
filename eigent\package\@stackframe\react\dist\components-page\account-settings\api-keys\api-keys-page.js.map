{"version": 3, "sources": ["../../../../src/components-page/account-settings/api-keys/api-keys-page.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { Button } from \"@stackframe/stack-ui\";\nimport { useState } from \"react\";\nimport { CreateApiKeyDialog, ShowApiKeyDialog } from \"../../../components/api-key-dialogs\";\nimport { ApiKeyTable } from \"../../../components/api-key-table\";\nimport { useUser } from \"../../../lib/hooks\";\nimport { ApiKey, ApiKeyCreationOptions } from \"../../../lib/stack-app/api-keys\";\nimport { useTranslation } from \"../../../lib/translations\";\nimport { PageLayout } from \"../page-layout\";\n\n\nexport function ApiKeysPage() {\n  const { t } = useTranslation();\n\n  const user = useUser({ or: 'redirect' });\n  const apiKeys = user.useApiKeys();\n\n  const [isNewApiKeyDialogOpen, setIsNewApiKeyDialogOpen] = useState(false);\n  const [returnedApiKey, setReturnedApiKey] = useState<ApiKey<\"user\", true>   | null>(null);\n\n  const CreateDialog = CreateApiKeyDialog<\"user\">;\n  const ShowDialog = ShowApiKeyDialog<\"user\">;\n\n  return (\n    <PageLayout>\n      <Button onClick={() => setIsNewApiKeyDialogOpen(true)}>\n        {t(\"Create API Key\")}\n      </Button>\n      <ApiKeyTable apiKeys={apiKeys} />\n      <CreateDialog\n        open={isNewApiKeyDialogOpen}\n        onOpenChange={setIsNewApiKeyDialogOpen}\n        onKeyCreated={setReturnedApiKey}\n        createApiKey={async (data: ApiKeyCreationOptions<\"user\">) => {\n          const apiKey = await user.createApiKey(data);\n          return apiKey;\n        }}\n      />\n      <ShowDialog\n        apiKey={returnedApiKey}\n        onClose={() => setReturnedApiKey(null)}\n      />\n    </PageLayout>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,sBAAuB;AACvB,mBAAyB;AACzB,6BAAqD;AACrD,2BAA4B;AAC5B,mBAAwB;AAExB,0BAA+B;AAC/B,yBAA2B;AAgBvB;AAbG,SAAS,cAAc;AAC5B,QAAM,EAAE,EAAE,QAAI,oCAAe;AAE7B,QAAM,WAAO,sBAAQ,EAAE,IAAI,WAAW,CAAC;AACvC,QAAM,UAAU,KAAK,WAAW;AAEhC,QAAM,CAAC,uBAAuB,wBAAwB,QAAI,uBAAS,KAAK;AACxE,QAAM,CAAC,gBAAgB,iBAAiB,QAAI,uBAAwC,IAAI;AAExF,QAAM,eAAe;AACrB,QAAM,aAAa;AAEnB,SACE,6CAAC,iCACC;AAAA,gDAAC,0BAAO,SAAS,MAAM,yBAAyB,IAAI,GACjD,YAAE,gBAAgB,GACrB;AAAA,IACA,4CAAC,oCAAY,SAAkB;AAAA,IAC/B;AAAA,MAAC;AAAA;AAAA,QACC,MAAM;AAAA,QACN,cAAc;AAAA,QACd,cAAc;AAAA,QACd,cAAc,OAAO,SAAwC;AAC3D,gBAAM,SAAS,MAAM,KAAK,aAAa,IAAI;AAC3C,iBAAO;AAAA,QACT;AAAA;AAAA,IACF;AAAA,IACA;AAAA,MAAC;AAAA;AAAA,QACC,QAAQ;AAAA,QACR,SAAS,MAAM,kBAAkB,IAAI;AAAA;AAAA,IACvC;AAAA,KACF;AAEJ;", "names": []}