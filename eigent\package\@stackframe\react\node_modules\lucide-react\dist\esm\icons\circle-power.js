/**
 * @license lucide-react v0.378.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const CirclePower = createLucideIcon("CirclePower", [
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }],
  ["path", { d: "M12 12V7", key: "1tf3mz" }],
  ["path", { d: "M16 9a5 5 0 1 1-8 0", key: "174bae" }]
]);

export { CirclePower as default };
//# sourceMappingURL=circle-power.js.map
