{"version": 3, "sources": ["../../src/interface/clientInterface.ts", "../../../../node_modules/.pnpm/oauth4webapi@2.10.4/node_modules/oauth4webapi/build/index.js"], "sourcesContent": ["import * as oauth from 'oauth4webapi';\n\nimport * as yup from 'yup';\nimport { KnownError, KnownErrors } from '../known-errors';\nimport { AccessToken, InternalSession, RefreshToken } from '../sessions';\nimport { generateSecureRandomString } from '../utils/crypto';\nimport { StackAssertionError, throwErr } from '../utils/errors';\nimport { globalVar } from '../utils/globals';\nimport { HTTP_METHODS, HttpMethod } from '../utils/http';\nimport { ReadonlyJson } from '../utils/json';\nimport { filterUndefined, filterUndefinedOrNull } from '../utils/objects';\nimport { AuthenticationResponseJSON, PublicKeyCredentialCreationOptionsJSON, PublicKeyCredentialRequestOptionsJSON, RegistrationResponseJSON } from '../utils/passkey';\nimport { wait } from '../utils/promises';\nimport { Result } from \"../utils/results\";\nimport { deindent } from '../utils/strings';\nimport { ContactChannelsCrud } from './crud/contact-channels';\nimport { CurrentUserCrud } from './crud/current-user';\nimport { ConnectedAccountAccessTokenCrud } from './crud/oauth';\nimport { TeamApiKeysCrud, UserApiKeysCrud, teamApiKeysCreateInputSchema, teamApiKeysCreateOutputSchema, userApiKeysCreateInputSchema, userApiKeysCreateOutputSchema } from './crud/project-api-keys';\nimport { ProjectPermissionsCrud } from './crud/project-permissions';\nimport { AdminUserProjectsCrud, ClientProjectsCrud } from './crud/projects';\nimport { SessionsCrud } from './crud/sessions';\nimport { TeamInvitationCrud } from './crud/team-invitation';\nimport { TeamMemberProfilesCrud } from './crud/team-member-profiles';\nimport { TeamPermissionsCrud } from './crud/team-permissions';\nimport { TeamsCrud } from './crud/teams';\n\nexport type ClientInterfaceOptions = {\n  clientVersion: string,\n  // This is a function instead of a string because it might be different based on the environment (for example client vs server)\n  getBaseUrl: () => string,\n  extraRequestHeaders: Record<string, string>,\n  projectId: string,\n  prepareRequest?: () => Promise<void>,\n} & ({\n  publishableClientKey: string,\n} | {\n  projectOwnerSession: InternalSession,\n});\n\nexport class StackClientInterface {\n  constructor(public readonly options: ClientInterfaceOptions) {\n    // nothing here\n  }\n\n  get projectId() {\n    return this.options.projectId;\n  }\n\n  getApiUrl() {\n    return this.options.getBaseUrl() + \"/api/v1\";\n  }\n\n  public async runNetworkDiagnostics(session?: InternalSession | null, requestType?: \"client\" | \"server\" | \"admin\") {\n    const tryRequest = async (cb: () => Promise<void>) => {\n      try {\n        await cb();\n        return \"OK\";\n      } catch (e) {\n        return `${e}`;\n      }\n    };\n    const cfTrace = await tryRequest(async () => {\n      const res = await fetch(\"https://1.1.1.1/cdn-cgi/trace\");\n      if (!res.ok) {\n        throw new Error(`${res.status} ${res.statusText}: ${await res.text()}`);\n      }\n    });\n    const apiRoot = session !== undefined && requestType !== undefined ? await tryRequest(async () => {\n      const res = await this.sendClientRequestInner(\"/\", {}, session!, requestType);\n      if (res.status === \"error\") {\n        throw res.error;\n      }\n    }) : \"Not tested\";\n    const baseUrlBackend = await tryRequest(async () => {\n      const res = await fetch(new URL(\"/health\", this.getApiUrl()));\n      if (!res.ok) {\n        throw new Error(`${res.status} ${res.statusText}: ${await res.text()}`);\n      }\n    });\n    const prodDashboard = await tryRequest(async () => {\n      const res = await fetch(\"https://app.stack-auth.com/health\");\n      if (!res.ok) {\n        throw new Error(`${res.status} ${res.statusText}: ${await res.text()}`);\n      }\n    });\n    const prodBackend = await tryRequest(async () => {\n      const res = await fetch(\"https://api.stack-auth.com/health\");\n      if (!res.ok) {\n        throw new Error(`${res.status} ${res.statusText}: ${await res.text()}`);\n      }\n    });\n    return {\n      \"navigator?.onLine\": globalVar.navigator?.onLine,\n      cfTrace,\n      apiRoot,\n      baseUrlBackend,\n      prodDashboard,\n      prodBackend,\n    };\n  }\n\n  protected async _createNetworkError(cause: Error, session?: InternalSession | null, requestType?: \"client\" | \"server\" | \"admin\") {\n    return new Error(deindent`\n      Stack Auth is unable to connect to the server. Please check your internet connection and try again.\n      \n      If the problem persists, please contact support and provide a screenshot of your entire browser console.\n\n      ${cause}\n      \n      ${JSON.stringify(await this.runNetworkDiagnostics(session, requestType), null, 2)}\n    `, { cause: cause });\n  }\n\n  protected async _networkRetry<T>(cb: () => Promise<Result<T, any>>, session?: InternalSession | null, requestType?: \"client\" | \"server\" | \"admin\"): Promise<T> {\n    const retriedResult = await Result.retry(\n      cb,\n      5,\n      { exponentialDelayBase: 1000 },\n    );\n\n    // try to diagnose the error for the user\n    if (retriedResult.status === \"error\") {\n      if (globalVar.navigator && !globalVar.navigator.onLine) {\n        throw new Error(\"Failed to send Stack network request. It seems like you are offline, please check your internet connection and try again. This is not an error with Stack Auth. (window.navigator.onLine is falsy)\", { cause: retriedResult.error });\n      }\n      throw await this._createNetworkError(retriedResult.error, session, requestType);\n    }\n    return retriedResult.data;\n  }\n\n  protected async _networkRetryException<T>(cb: () => Promise<T>, session?: InternalSession | null, requestType?: \"client\" | \"server\" | \"admin\"): Promise<T> {\n    return await this._networkRetry(async () => await Result.fromThrowingAsync(cb), session, requestType);\n  }\n\n  public async fetchNewAccessToken(refreshToken: RefreshToken) {\n    if (!('publishableClientKey' in this.options)) {\n      // TODO support it\n      throw new Error(\"Admin session token is currently not supported for fetching new access token. Did you try to log in on a StackApp initiated with the admin session?\");\n    }\n\n    const as = {\n      issuer: this.options.getBaseUrl(),\n      algorithm: 'oauth2',\n      token_endpoint: this.getApiUrl() + '/auth/oauth/token',\n    };\n    const client: oauth.Client = {\n      client_id: this.projectId,\n      client_secret: this.options.publishableClientKey,\n      token_endpoint_auth_method: 'client_secret_post',\n    };\n\n    const rawResponse = await this._networkRetryException(\n      async () => await oauth.refreshTokenGrantRequest(\n        as,\n        client,\n        refreshToken.token,\n      )\n    );\n    const response = await this._processResponse(rawResponse);\n\n    if (response.status === \"error\") {\n      const error = response.error;\n      if (KnownErrors.RefreshTokenError.isInstance(error)) {\n        return null;\n      }\n      throw error;\n    }\n\n    if (!response.data.ok) {\n      const body = await response.data.text();\n      throw new Error(`Failed to send refresh token request: ${response.status} ${body}`);\n    }\n\n    const result = await oauth.processRefreshTokenResponse(as, client, response.data);\n    if (oauth.isOAuth2Error(result)) {\n      // TODO Handle OAuth 2.0 response body error\n      throw new StackAssertionError(\"OAuth error\", { result });\n    }\n\n    if (!result.access_token) {\n      throw new StackAssertionError(\"Access token not found in token endpoint response, this is weird!\");\n    }\n\n    return new AccessToken(result.access_token);\n  }\n\n  public async sendClientRequest(\n    path: string,\n    requestOptions: RequestInit,\n    session: InternalSession | null,\n    requestType: \"client\" | \"server\" | \"admin\" = \"client\",\n  ) {\n    session ??= this.createSession({\n      refreshToken: null,\n    });\n\n\n    return await this._networkRetry(\n      () => this.sendClientRequestInner(path, requestOptions, session!, requestType),\n      session,\n      requestType,\n    );\n  }\n\n  public createSession(options: Omit<ConstructorParameters<typeof InternalSession>[0], \"refreshAccessTokenCallback\">): InternalSession {\n    const session = new InternalSession({\n      refreshAccessTokenCallback: async (refreshToken) => await this.fetchNewAccessToken(refreshToken),\n      ...options,\n    });\n    return session;\n  }\n\n  protected async sendClientRequestAndCatchKnownError<E extends typeof KnownErrors[keyof KnownErrors]>(\n    path: string,\n    requestOptions: RequestInit,\n    tokenStoreOrNull: InternalSession | null,\n    errorsToCatch: readonly E[],\n  ): Promise<Result<\n    Response & {\n      usedTokens: {\n        accessToken: AccessToken,\n        refreshToken: RefreshToken | null,\n      } | null,\n    },\n    InstanceType<E>\n  >> {\n    try {\n      return Result.ok(await this.sendClientRequest(path, requestOptions, tokenStoreOrNull));\n    } catch (e) {\n      for (const errorType of errorsToCatch) {\n        if (errorType.isInstance(e)) {\n          return Result.error(e as InstanceType<E>);\n        }\n      }\n      throw e;\n    }\n  }\n\n  private async sendClientRequestInner(\n    path: string,\n    options: RequestInit,\n    session: InternalSession,\n    requestType: \"client\" | \"server\" | \"admin\",\n  ): Promise<Result<Response & {\n    usedTokens: {\n      accessToken: AccessToken,\n      refreshToken: RefreshToken | null,\n    } | null,\n  }>> {\n    /**\n     * `tokenObj === null` means the session is invalid/not logged in\n     */\n    let tokenObj = await session.getOrFetchLikelyValidTokens(20_000);\n\n    let adminSession = \"projectOwnerSession\" in this.options ? this.options.projectOwnerSession : null;\n    let adminTokenObj = adminSession ? await adminSession.getOrFetchLikelyValidTokens(20_000) : null;\n\n    // all requests should be dynamic to prevent Next.js caching\n    await this.options.prepareRequest?.();\n\n    let url = this.getApiUrl() + path;\n    if (url.endsWith(\"/\")) {\n      url = url.slice(0, -1);\n    }\n    const params: RequestInit = {\n      /**\n       * This fetch may be cross-origin, in which case we don't want to send cookies of the\n       * original origin (this is the default behavior of `credentials`).\n       *\n       * To help debugging, also omit cookies on same-origin, so we don't accidentally\n       * implement reliance on cookies anywhere.\n       *\n       * However, Cloudflare Workers don't actually support `credentials`, so we only set it\n       * if Cloudflare-exclusive globals are not detected. https://github.com/cloudflare/workers-sdk/issues/2514\n       */\n      ...(\"WebSocketPair\" in globalVar ? {} : {\n        credentials: \"omit\",\n      }),\n      ...options,\n      headers: {\n        \"X-Stack-Override-Error-Status\": \"true\",\n        \"X-Stack-Project-Id\": this.projectId,\n        \"X-Stack-Access-Type\": requestType,\n        \"X-Stack-Client-Version\": this.options.clientVersion,\n        ...(tokenObj ? {\n          \"X-Stack-Access-Token\": tokenObj.accessToken.token,\n        } : {}),\n        ...(tokenObj?.refreshToken ? {\n          \"X-Stack-Refresh-Token\": tokenObj.refreshToken.token,\n        } : {}),\n        ...('publishableClientKey' in this.options ? {\n          \"X-Stack-Publishable-Client-Key\": this.options.publishableClientKey,\n        } : {}),\n        ...(adminTokenObj ? {\n          \"X-Stack-Admin-Access-Token\": adminTokenObj.accessToken.token,\n        } : {}),\n        /**\n         * Next.js until v15 would cache fetch requests by default, and forcefully disabling it was nearly impossible.\n         *\n         * This header is used to change the cache key and hence always disable it, because we do our own caching.\n         *\n         * When we drop support for Next.js <15, we may be able to remove this header, but please make sure that this is\n         * the case (I haven't actually tested.)\n         */\n        \"X-Stack-Random-Nonce\": generateSecureRandomString(),\n        // don't show a warning when proxying the API through ngrok (only relevant if the API url is an ngrok site)\n        'ngrok-skip-browser-warning': 'true',\n        ...this.options.extraRequestHeaders,\n        ...options.headers,\n      },\n      /**\n       * Cloudflare Workers does not support cache, so don't pass it there\n       */\n      ...(\"WebSocketPair\" in globalVar ? {} : {\n        cache: \"no-store\",\n      }),\n    };\n\n    let rawRes;\n    try {\n      rawRes = await fetch(url, params);\n    } catch (e) {\n      if (e instanceof TypeError) {\n        // Likely to be a network error. Retry if the request is idempotent, throw network error otherwise.\n        if (HTTP_METHODS[(params.method ?? \"GET\") as HttpMethod].idempotent) {\n          return Result.error(e);\n        } else {\n          throw await this._createNetworkError(e, session, requestType);\n        }\n      }\n      throw e;\n    }\n\n    const processedRes = await this._processResponse(rawRes);\n    if (processedRes.status === \"error\") {\n      // If the access token is invalid, reset it and retry\n      if (KnownErrors.InvalidAccessToken.isInstance(processedRes.error)) {\n        if (!tokenObj) {\n          throw new StackAssertionError(\"Received invalid access token, but session is not logged in\", { tokenObj, processedRes });\n        }\n        session.markAccessTokenExpired(tokenObj.accessToken);\n        return Result.error(processedRes.error);\n      }\n\n      // Same for the admin access token\n      // TODO HACK: Some of the backend hasn't been ported to use the new error codes, so if we have project owner tokens we need to check for ApiKeyNotFound too. Once the migration to smartRouteHandlers is complete, we can check for InvalidAdminAccessToken only.\n      if (adminSession && (KnownErrors.InvalidAdminAccessToken.isInstance(processedRes.error) || KnownErrors.ApiKeyNotFound.isInstance(processedRes.error))) {\n        if (!adminTokenObj) {\n          throw new StackAssertionError(\"Received invalid admin access token, but admin session is not logged in\", { adminTokenObj, processedRes });\n        }\n        adminSession.markAccessTokenExpired(adminTokenObj.accessToken);\n        return Result.error(processedRes.error);\n      }\n\n      // Known errors are client side errors, so except for the ones above they should not be retried\n      // Hence, throw instead of returning an error\n      throw processedRes.error;\n    }\n\n\n    const res = Object.assign(processedRes.data, {\n      usedTokens: tokenObj,\n    });\n    if (res.ok) {\n      return Result.ok(res);\n    } else if (res.status === 429) {\n      // Rate limited, so retry if we can\n      const retryAfter = res.headers.get(\"Retry-After\");\n      if (retryAfter !== null) {\n        console.log(`Rate limited while sending request to ${url}. Will retry after ${retryAfter} seconds...`);\n        await wait(Number(retryAfter) * 1000);\n        return Result.error(new Error(`Rate limited, retrying after ${retryAfter} seconds`));\n      }\n      console.log(`Rate limited while sending request to ${url}, no retry-after header received. Retrying...`);\n      return Result.error(new Error(\"Rate limited, no retry-after header received\"));\n    } else {\n      const error = await res.text();\n\n      const errorObj = new StackAssertionError(`Failed to send request to ${url}: ${res.status} ${error}`, { request: params, res, path });\n\n      if (res.status === 508 && error.includes(\"INFINITE_LOOP_DETECTED\")) {\n        // Some Vercel deployments seem to have an odd infinite loop bug. In that case, retry.\n        // See: https://github.com/stack-auth/stack-auth/issues/319\n        return Result.error(errorObj);\n      }\n\n      // Do not retry, throw error instead of returning one\n      throw errorObj;\n    }\n  }\n\n  private async _processResponse(rawRes: Response): Promise<Result<Response, KnownError>> {\n    let res = rawRes;\n    if (rawRes.headers.has(\"x-stack-actual-status\")) {\n      const actualStatus = Number(rawRes.headers.get(\"x-stack-actual-status\"));\n      res = new Response(rawRes.body, {\n        status: actualStatus,\n        statusText: rawRes.statusText,\n        headers: rawRes.headers,\n      });\n    }\n\n    // Handle known errors\n    if (res.headers.has(\"x-stack-known-error\")) {\n      const errorJson = await res.json();\n      if (res.headers.get(\"x-stack-known-error\") !== errorJson.code) {\n        throw new StackAssertionError(\"Mismatch between x-stack-known-error header and error code in body; the server's response is invalid\");\n      }\n      const error = KnownError.fromJson(errorJson);\n      return Result.error(error);\n    }\n\n    return Result.ok(res);\n  }\n\n  public async checkFeatureSupport(options: { featureName?: string } & ReadonlyJson): Promise<never> {\n    const res = await this.sendClientRequest(\"/check-feature-support\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify(options),\n    }, null);\n\n    throw new StackAssertionError(await res.text());\n  }\n\n  async sendForgotPasswordEmail(\n    email: string,\n    callbackUrl: string,\n  ): Promise<Result<undefined, KnownErrors[\"UserNotFound\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/password/send-reset-code\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          email,\n          callback_url: callbackUrl,\n        }),\n      },\n      null,\n      [KnownErrors.UserNotFound],\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    } else {\n      return Result.ok(undefined);\n    }\n  }\n\n  async sendVerificationEmail(\n    email: string,\n    callbackUrl: string,\n    session: InternalSession\n  ): Promise<KnownErrors[\"EmailAlreadyVerified\"] | undefined> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/contact-channels/send-verification-code\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          email,\n          callback_url: callbackUrl,\n        }),\n      },\n      session,\n      [KnownErrors.EmailAlreadyVerified]\n    );\n\n    if (res.status === \"error\") {\n      return res.error;\n    }\n  }\n\n  async sendMagicLinkEmail(\n    email: string,\n    callbackUrl: string,\n  ): Promise<Result<{ nonce: string }, KnownErrors[\"RedirectUrlNotWhitelisted\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/otp/send-sign-in-code\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          email,\n          callback_url: callbackUrl,\n        }),\n      },\n      null,\n      [KnownErrors.RedirectUrlNotWhitelisted]\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    } else {\n      return Result.ok(await res.data.json());\n    }\n  }\n\n  async resetPassword(\n    options: { code: string } & ({ password: string } | { onlyVerifyCode: true })\n  ): Promise<Result<undefined, KnownErrors[\"VerificationCodeError\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"onlyVerifyCode\" in options ? \"/auth/password/reset/check-code\" : \"/auth/password/reset\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          code: options.code,\n          ...(\"password\" in options ? { password: options.password } : {}),\n        }),\n      },\n      null,\n      [KnownErrors.VerificationCodeError]\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    } else {\n      return Result.ok(undefined);\n    }\n  }\n\n  async updatePassword(\n    options: { oldPassword: string, newPassword: string },\n    session: InternalSession\n  ): Promise<KnownErrors[\"PasswordConfirmationMismatch\"] | KnownErrors[\"PasswordRequirementsNotMet\"] | undefined> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/password/update\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          old_password: options.oldPassword,\n          new_password: options.newPassword,\n        }),\n      },\n      session,\n      [KnownErrors.PasswordConfirmationMismatch, KnownErrors.PasswordRequirementsNotMet]\n    );\n\n    if (res.status === \"error\") {\n      return res.error;\n    }\n  }\n\n  async setPassword(\n    options: { password: string },\n    session: InternalSession\n  ): Promise<KnownErrors[\"PasswordRequirementsNotMet\"] | undefined> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/password/set\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(options),\n      },\n      session,\n      [KnownErrors.PasswordRequirementsNotMet]\n    );\n\n    if (res.status === \"error\") {\n      return res.error;\n    }\n  }\n\n  async verifyPasswordResetCode(code: string): Promise<Result<undefined, KnownErrors[\"VerificationCodeError\"]>> {\n    const res = await this.resetPassword({ code, onlyVerifyCode: true });\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    } else {\n      return Result.ok(undefined);\n    }\n  }\n\n  async verifyEmail(code: string): Promise<Result<undefined, KnownErrors[\"VerificationCodeError\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/contact-channels/verify\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          code,\n        }),\n      },\n      null,\n      [KnownErrors.VerificationCodeError]\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    } else {\n      return Result.ok(undefined);\n    }\n  }\n\n  async initiatePasskeyRegistration(\n    options: {},\n    session: InternalSession\n  ): Promise<Result<{ options_json: PublicKeyCredentialCreationOptionsJSON, code: string }, KnownErrors[]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/passkey/initiate-passkey-registration\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(options),\n      },\n      session,\n      []\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    }\n\n    return Result.ok(await res.data.json());\n  }\n\n  async registerPasskey(\n    options: { credential: RegistrationResponseJSON, code: string },\n    session: InternalSession\n  ): Promise<Result<undefined, KnownErrors[\"PasskeyRegistrationFailed\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/passkey/register\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(options),\n      },\n      session,\n      [KnownErrors.PasskeyRegistrationFailed]\n    );\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    }\n    return Result.ok(undefined);\n  }\n\n  async initiatePasskeyAuthentication(\n    options: {\n    },\n    session: InternalSession\n  ): Promise<Result<{ options_json: PublicKeyCredentialRequestOptionsJSON, code: string }, KnownErrors[]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/passkey/initiate-passkey-authentication\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(options),\n      },\n      session,\n      []\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    }\n\n    return Result.ok(await res.data.json());\n  }\n\n  async sendTeamInvitation(options: {\n    email: string,\n    teamId: string,\n    callbackUrl: string,\n    session: InternalSession,\n  }): Promise<void> {\n    await this.sendClientRequest(\n      \"/team-invitations/send-code\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          email: options.email,\n          team_id: options.teamId,\n          callback_url: options.callbackUrl,\n        }),\n      },\n      options.session,\n    );\n  }\n\n  async acceptTeamInvitation<T extends 'use' | 'details' | 'check'>(options: {\n    code: string,\n    session: InternalSession,\n    type: T,\n  }): Promise<Result<T extends 'details' ? { team_display_name: string } : undefined, KnownErrors[\"VerificationCodeError\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      options.type === 'check' ?\n        \"/team-invitations/accept/check-code\" :\n        options.type === 'details' ?\n          \"/team-invitations/accept/details\" :\n          \"/team-invitations/accept\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          code: options.code,\n        }),\n      },\n      options.session,\n      [KnownErrors.VerificationCodeError]\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    } else {\n      return Result.ok(await res.data.json());\n    }\n  }\n\n  async totpMfa(\n    attemptCode: string,\n    totp: string,\n    session: InternalSession\n  ) {\n    const res = await this.sendClientRequest(\"/auth/mfa/sign-in\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify({\n        code: attemptCode,\n        type: \"totp\",\n        totp: totp,\n      }),\n    }, session);\n\n    const result = await res.json();\n    return {\n      accessToken: result.access_token,\n      refreshToken: result.refresh_token,\n      newUser: result.is_new_user,\n    };\n  }\n\n  async signInWithCredential(\n    email: string,\n    password: string,\n    session: InternalSession\n  ): Promise<Result<{ accessToken: string, refreshToken: string }, KnownErrors[\"EmailPasswordMismatch\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/password/sign-in\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          email,\n          password,\n        }),\n      },\n      session,\n      [KnownErrors.EmailPasswordMismatch]\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    }\n\n    const result = await res.data.json();\n    return Result.ok({\n      accessToken: result.access_token,\n      refreshToken: result.refresh_token,\n    });\n  }\n\n  async signUpWithCredential(\n    email: string,\n    password: string,\n    emailVerificationRedirectUrl: string,\n    session: InternalSession,\n  ): Promise<Result<{ accessToken: string, refreshToken: string }, KnownErrors[\"UserWithEmailAlreadyExists\"] | KnownErrors[\"PasswordRequirementsNotMet\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/password/sign-up\",\n      {\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        method: \"POST\",\n        body: JSON.stringify({\n          email,\n          password,\n          verification_callback_url: emailVerificationRedirectUrl,\n        }),\n      },\n      session,\n      [KnownErrors.UserWithEmailAlreadyExists, KnownErrors.PasswordRequirementsNotMet]\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    }\n\n    const result = await res.data.json();\n    return Result.ok({\n      accessToken: result.access_token,\n      refreshToken: result.refresh_token,\n    });\n  }\n\n  async signUpAnonymously(session: InternalSession): Promise<Result<{ accessToken: string, refreshToken: string }, never>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/anonymous/sign-up\",\n      {\n        method: \"POST\",\n      },\n      session,\n      [],\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    }\n\n    const result = await res.data.json();\n    return Result.ok({\n      accessToken: result.access_token,\n      refreshToken: result.refresh_token,\n    });\n  }\n\n  async signInWithMagicLink(code: string): Promise<Result<{ newUser: boolean, accessToken: string, refreshToken: string }, KnownErrors[\"VerificationCodeError\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/otp/sign-in\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          code,\n        }),\n      },\n      null,\n      [KnownErrors.VerificationCodeError]\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    }\n\n    const result = await res.data.json();\n    return Result.ok({\n      accessToken: result.access_token,\n      refreshToken: result.refresh_token,\n      newUser: result.is_new_user,\n    });\n  }\n\n  async signInWithPasskey(body: { authentication_response: AuthenticationResponseJSON, code: string }): Promise<Result<{accessToken: string, refreshToken: string }, KnownErrors[\"PasskeyAuthenticationFailed\"]>> {\n    const res = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/passkey/sign-in\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(body),\n      },\n      null,\n      [KnownErrors.PasskeyAuthenticationFailed]\n    );\n\n    if (res.status === \"error\") {\n      return Result.error(res.error);\n    }\n\n    const result = await res.data.json();\n    return Result.ok({\n      accessToken: result.access_token,\n      refreshToken: result.refresh_token,\n    });\n  }\n\n  async getOAuthUrl(\n    options: {\n      provider: string,\n      redirectUrl: string,\n      errorRedirectUrl: string,\n      afterCallbackRedirectUrl?: string,\n      codeChallenge: string,\n      state: string,\n      type: \"authenticate\" | \"link\",\n      providerScope?: string,\n    } & ({ type: \"authenticate\" } | { type: \"link\", session: InternalSession })\n  ): Promise<string> {\n    const updatedRedirectUrl = new URL(options.redirectUrl);\n    for (const key of [\"code\", \"state\"]) {\n      if (updatedRedirectUrl.searchParams.has(key)) {\n        console.warn(\"Redirect URL already contains \" + key + \" parameter, removing it as it will be overwritten by the OAuth callback\");\n      }\n      updatedRedirectUrl.searchParams.delete(key);\n    }\n\n    if (!('publishableClientKey' in this.options)) {\n      // TODO fix\n      throw new Error(\"Admin session token is currently not supported for OAuth\");\n    }\n    const url = new URL(this.getApiUrl() + \"/auth/oauth/authorize/\" + options.provider.toLowerCase());\n    url.searchParams.set(\"client_id\", this.projectId);\n    url.searchParams.set(\"client_secret\", this.options.publishableClientKey);\n    url.searchParams.set(\"redirect_uri\", updatedRedirectUrl.toString());\n    url.searchParams.set(\"scope\", \"legacy\");\n    url.searchParams.set(\"state\", options.state);\n    url.searchParams.set(\"grant_type\", \"authorization_code\");\n    url.searchParams.set(\"code_challenge\", options.codeChallenge);\n    url.searchParams.set(\"code_challenge_method\", \"S256\");\n    url.searchParams.set(\"response_type\", \"code\");\n    url.searchParams.set(\"type\", options.type);\n    url.searchParams.set(\"error_redirect_url\", options.errorRedirectUrl);\n\n    if (options.afterCallbackRedirectUrl) {\n      url.searchParams.set(\"after_callback_redirect_url\", options.afterCallbackRedirectUrl);\n    }\n\n    if (options.type === \"link\") {\n      const tokens = await options.session.getOrFetchLikelyValidTokens(20_000);\n      url.searchParams.set(\"token\", tokens?.accessToken.token || \"\");\n\n      if (options.providerScope) {\n        url.searchParams.set(\"provider_scope\", options.providerScope);\n      }\n    }\n\n    return url.toString();\n  }\n\n  async callOAuthCallback(options: {\n    oauthParams: URLSearchParams,\n    redirectUri: string,\n    codeVerifier: string,\n    state: string,\n  }): Promise<{ newUser: boolean, afterCallbackRedirectUrl?: string, accessToken: string, refreshToken: string }> {\n    if (!('publishableClientKey' in this.options)) {\n      // TODO fix\n      throw new Error(\"Admin session token is currently not supported for OAuth\");\n    }\n    const as = {\n      issuer: this.options.getBaseUrl(),\n      algorithm: 'oauth2',\n      token_endpoint: this.getApiUrl() + '/auth/oauth/token',\n    };\n    const client: oauth.Client = {\n      client_id: this.projectId,\n      client_secret: this.options.publishableClientKey,\n      token_endpoint_auth_method: 'client_secret_post',\n    };\n    const params = await this._networkRetryException(\n      async () => oauth.validateAuthResponse(as, client, options.oauthParams, options.state),\n    );\n    if (oauth.isOAuth2Error(params)) {\n      throw new StackAssertionError(\"Error validating outer OAuth response\", { params }); // Handle OAuth 2.0 redirect error\n    }\n    const response = await oauth.authorizationCodeGrantRequest(\n      as,\n      client,\n      params,\n      options.redirectUri,\n      options.codeVerifier,\n    );\n\n    const result = await oauth.processAuthorizationCodeOAuth2Response(as, client, response);\n    if (oauth.isOAuth2Error(result)) {\n      if (\"code\" in result && result.code === \"MULTI_FACTOR_AUTHENTICATION_REQUIRED\") {\n        throw new KnownErrors.MultiFactorAuthenticationRequired((result as any).details.attempt_code);\n      }\n      // TODO Handle OAuth 2.0 response body error\n      throw new StackAssertionError(\"Outer OAuth error during authorization code response\", { result });\n    }\n    return {\n      newUser: result.is_new_user as boolean,\n      afterCallbackRedirectUrl: result.after_callback_redirect_url as string | undefined,\n      accessToken: result.access_token,\n      refreshToken: result.refresh_token ?? throwErr(\"Refresh token not found in outer OAuth response\"),\n    };\n  }\n\n  async signOut(session: InternalSession): Promise<void> {\n    const tokenObj = await session.getOrFetchLikelyValidTokens(20_000);\n    if (tokenObj) {\n      const resOrError = await this.sendClientRequestAndCatchKnownError(\n        \"/auth/sessions/current\",\n        {\n          method: \"DELETE\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({}),\n        },\n        session,\n        [KnownErrors.RefreshTokenError]\n      );\n      if (resOrError.status === \"error\") {\n        if (KnownErrors.RefreshTokenError.isInstance(resOrError.error)) {\n          // refresh token was already invalid, just continue like nothing happened\n        } else {\n          // this should never happen\n          throw new StackAssertionError(\"Unexpected error\", { error: resOrError.error });\n        }\n      } else {\n        // user was signed out successfully, all good\n      }\n    }\n    session.markInvalid();\n  }\n\n  async getClientUserByToken(session: InternalSession): Promise<CurrentUserCrud[\"Client\"][\"Read\"] | null> {\n    const responseOrError = await this.sendClientRequestAndCatchKnownError(\n      \"/users/me\",\n      {},\n      session,\n      [KnownErrors.CannotGetOwnUserWithoutUser],\n    );\n    if (responseOrError.status === \"error\") {\n      if (KnownErrors.CannotGetOwnUserWithoutUser.isInstance(responseOrError.error)) {\n        return null;\n      } else {\n        throw new StackAssertionError(\"Unexpected uncaught error\", { cause: responseOrError.error });\n      }\n    }\n    const response = responseOrError.data;\n    const user: CurrentUserCrud[\"Client\"][\"Read\"] = await response.json();\n    if (!(user as any)) throw new StackAssertionError(\"User endpoint returned null; this should never happen\");\n    return user;\n  }\n\n  async listTeamInvitations(\n    options: {\n      teamId: string,\n    },\n    session: InternalSession,\n  ): Promise<TeamInvitationCrud['Client']['Read'][]> {\n    const response = await this.sendClientRequest(\n      \"/team-invitations?\" + new URLSearchParams({ team_id: options.teamId }),\n      {},\n      session,\n    );\n    const result = await response.json() as TeamInvitationCrud['Client']['List'];\n    return result.items;\n  }\n\n  async revokeTeamInvitation(\n    invitationId: string,\n    teamId: string,\n    session: InternalSession,\n  ) {\n    await this.sendClientRequest(\n      `/team-invitations/${invitationId}?team_id=${teamId}`,\n      { method: \"DELETE\" },\n      session,\n    );\n  }\n\n  async listTeamMemberProfiles(\n    options: {\n      teamId?: string,\n      userId?: string,\n    },\n    session: InternalSession,\n  ): Promise<TeamMemberProfilesCrud['Client']['Read'][]> {\n    const response = await this.sendClientRequest(\n      \"/team-member-profiles?\" + new URLSearchParams(filterUndefined({\n        team_id: options.teamId,\n        user_id: options.userId,\n      })),\n      {},\n      session,\n    );\n    const result = await response.json() as TeamMemberProfilesCrud['Client']['List'];\n    return result.items;\n  }\n\n  async getTeamMemberProfile(\n    options: {\n      teamId: string,\n      userId: string,\n    },\n    session: InternalSession,\n  ): Promise<TeamMemberProfilesCrud['Client']['Read']> {\n    const response = await this.sendClientRequest(\n      `/team-member-profiles/${options.teamId}/${options.userId}`,\n      {},\n      session,\n    );\n    return await response.json();\n  }\n\n  async leaveTeam(\n    teamId: string,\n    session: InternalSession,\n  ) {\n    await this.sendClientRequest(\n      `/team-memberships/${teamId}/me`,\n      {\n        method: \"DELETE\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      session,\n    );\n  }\n\n  async updateTeamMemberProfile(\n    options: {\n      teamId: string,\n      userId: string,\n      profile: TeamMemberProfilesCrud['Client']['Update'],\n    },\n    session: InternalSession,\n  ) {\n    await this.sendClientRequest(\n      `/team-member-profiles/${options.teamId}/${options.userId}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(options.profile),\n      },\n      session,\n    );\n  }\n\n  async updateTeam(\n    options: {\n      teamId: string,\n      data: TeamsCrud['Client']['Update'],\n    },\n    session: InternalSession,\n  ) {\n    await this.sendClientRequest(\n      `/teams/${options.teamId}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(options.data),\n      },\n      session,\n    );\n  }\n\n  async listCurrentUserTeamPermissions(\n    options: {\n      teamId: string,\n      recursive: boolean,\n    },\n    session: InternalSession\n  ): Promise<TeamPermissionsCrud['Client']['Read'][]> {\n    const response = await this.sendClientRequest(\n      `/team-permissions?team_id=${options.teamId}&user_id=me&recursive=${options.recursive}`,\n      {},\n      session,\n    );\n    const result = await response.json() as TeamPermissionsCrud['Client']['List'];\n    return result.items;\n  }\n\n  async listCurrentUserProjectPermissions(\n    options: {\n      recursive: boolean,\n    },\n    session: InternalSession\n  ): Promise<ProjectPermissionsCrud['Client']['Read'][]> {\n    const response = await this.sendClientRequest(\n      `/project-permissions?user_id=me&recursive=${options.recursive}`,\n      {},\n      session,\n    );\n    const result = await response.json() as ProjectPermissionsCrud['Client']['List'];\n    return result.items;\n  }\n\n  async listCurrentUserTeams(session: InternalSession): Promise<TeamsCrud[\"Client\"][\"Read\"][]> {\n    const response = await this.sendClientRequest(\n      \"/teams?user_id=me\",\n      {},\n      session,\n    );\n    const result = await response.json() as TeamsCrud[\"Client\"][\"List\"];\n    return result.items;\n  }\n\n  async getClientProject(): Promise<Result<ClientProjectsCrud['Client']['Read'], KnownErrors[\"ProjectNotFound\"]>> {\n    const responseOrError = await this.sendClientRequestAndCatchKnownError(\"/projects/current\", {}, null, [KnownErrors.ProjectNotFound]);\n    if (responseOrError.status === \"error\") {\n      return Result.error(responseOrError.error);\n    }\n    const response = responseOrError.data;\n    const project: ClientProjectsCrud['Client']['Read'] = await response.json();\n    return Result.ok(project);\n  }\n\n  async updateClientUser(update: CurrentUserCrud[\"Client\"][\"Update\"], session: InternalSession) {\n    await this.sendClientRequest(\n      \"/users/me\",\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(update),\n      },\n      session,\n    );\n  }\n\n  async listProjects(session: InternalSession): Promise<AdminUserProjectsCrud['Client']['Read'][]> {\n    const response = await this.sendClientRequest(\"/internal/projects\", {}, session);\n    if (!response.ok) {\n      throw new Error(\"Failed to list projects: \" + response.status + \" \" + (await response.text()));\n    }\n\n    const json = await response.json() as AdminUserProjectsCrud['Client']['List'];\n    return json.items;\n  }\n\n  async createProject(\n    project: AdminUserProjectsCrud['Client']['Create'],\n    session: InternalSession,\n  ): Promise<AdminUserProjectsCrud['Client']['Read']> {\n    const fetchResponse = await this.sendClientRequest(\n      \"/internal/projects\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(project),\n      },\n      session,\n    );\n    if (!fetchResponse.ok) {\n      throw new Error(\"Failed to create project: \" + fetchResponse.status + \" \" + (await fetchResponse.text()));\n    }\n\n    const json = await fetchResponse.json();\n    return json;\n  }\n\n  async createProviderAccessToken(\n    provider: string,\n    scope: string,\n    session: InternalSession,\n  ): Promise<ConnectedAccountAccessTokenCrud['Client']['Read']> {\n    const response = await this.sendClientRequest(\n      `/connected-accounts/me/${provider}/access-token`,\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({ scope }),\n      },\n      session,\n    );\n    return await response.json();\n  }\n\n  async createClientTeam(\n    data: TeamsCrud['Client']['Create'],\n    session: InternalSession,\n  ): Promise<TeamsCrud['Client']['Read']> {\n    const response = await this.sendClientRequest(\n      \"/teams\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      session,\n    );\n    return await response.json();\n  }\n\n  async deleteTeam(\n    teamId: string,\n    session: InternalSession,\n  ) {\n    await this.sendClientRequest(\n      `/teams/${teamId}`,\n      {\n        method: \"DELETE\",\n      },\n      session,\n    );\n  }\n\n  async deleteCurrentUser(session: InternalSession) {\n    await this.sendClientRequest(\n      \"/users/me\",\n      {\n        method: \"DELETE\",\n      },\n      session,\n    );\n  }\n\n  async createClientContactChannel(\n    data: ContactChannelsCrud['Client']['Create'],\n    session: InternalSession,\n  ): Promise<ContactChannelsCrud['Client']['Read']> {\n    const response = await this.sendClientRequest(\n      \"/contact-channels\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      session,\n    );\n    return await response.json();\n  }\n\n  async updateClientContactChannel(\n    id: string,\n    data: ContactChannelsCrud['Client']['Update'],\n    session: InternalSession,\n  ): Promise<ContactChannelsCrud['Client']['Read']> {\n    const response = await this.sendClientRequest(\n      `/contact-channels/me/${id}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      session,\n    );\n    return await response.json();\n  }\n\n  async deleteClientContactChannel(\n    id: string,\n    session: InternalSession,\n  ): Promise<void> {\n    await this.sendClientRequest(\n      `/contact-channels/me/${id}`,\n      {\n        method: \"DELETE\",\n      },\n      session,\n    );\n  }\n\n  async deleteSession(\n    sessionId: string,\n    session: InternalSession,\n  ): Promise<void> {\n    await this.sendClientRequest(\n      `/auth/sessions/${sessionId}?user_id=me`,\n      {\n        method: \"DELETE\",\n      },\n      session,\n    );\n  }\n\n  async listSessions(\n    session: InternalSession,\n  ): Promise<SessionsCrud['Client']['List']> {\n    const response = await this.sendClientRequest(\n      \"/auth/sessions?user_id=me\",\n      {\n        method: \"GET\",\n      },\n      session,\n    );\n    return await response.json();\n  }\n\n\n  async listClientContactChannels(\n    session: InternalSession,\n  ): Promise<ContactChannelsCrud['Client']['Read'][]> {\n    const response = await this.sendClientRequest(\n      \"/contact-channels?user_id=me\",\n      {\n        method: \"GET\",\n      },\n      session,\n    );\n    const json = await response.json() as ContactChannelsCrud['Client']['List'];\n    return json.items;\n  }\n\n  async sendCurrentUserContactChannelVerificationEmail(\n    contactChannelId: string,\n    callbackUrl: string,\n    session: InternalSession,\n  ): Promise<Result<undefined, KnownErrors[\"EmailAlreadyVerified\"]>> {\n    const responseOrError = await this.sendClientRequestAndCatchKnownError(\n      `/contact-channels/me/${contactChannelId}/send-verification-code`,\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({ callback_url: callbackUrl }),\n      },\n      session,\n      [KnownErrors.EmailAlreadyVerified]\n    );\n\n    if (responseOrError.status === \"error\") {\n      return Result.error(responseOrError.error);\n    }\n    return Result.ok(undefined);\n  }\n\n  async cliLogin(\n    loginCode: string,\n    refreshToken: string,\n    session: InternalSession\n  ): Promise<Result<undefined, KnownErrors[\"SchemaError\"]>> {\n    const responseOrError = await this.sendClientRequestAndCatchKnownError(\n      \"/auth/cli/complete\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          login_code: loginCode,\n          refresh_token: refreshToken,\n        }),\n      },\n      session,\n      [KnownErrors.SchemaError]\n    );\n\n    if (responseOrError.status === \"error\") {\n      return Result.error(responseOrError.error);\n    }\n    return Result.ok(undefined);\n  }\n\n  private async _getApiKeyRequestInfo(options: { user_id: string | null } | { team_id: string }) {\n    if (\"user_id\" in options && \"team_id\" in options) {\n      throw new StackAssertionError(\"Cannot specify both user_id and team_id in _getApiKeyRequestInfo\");\n    }\n\n    return {\n      endpoint: \"team_id\" in options ? \"/team-api-keys\" : \"/user-api-keys\",\n      queryParams: new URLSearchParams(filterUndefinedOrNull(options)),\n    };\n  }\n\n  // API Keys CRUD operations\n  listProjectApiKeys(options: { user_id: string }, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<UserApiKeysCrud['Client']['Read'][]>;\n  listProjectApiKeys(options: { team_id: string }, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<TeamApiKeysCrud['Client']['Read'][]>;\n  listProjectApiKeys(options: { user_id: string } | { team_id: string }, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<(UserApiKeysCrud['Client']['Read'] | TeamApiKeysCrud['Client']['Read'])[]>;\n  async listProjectApiKeys(\n    options: { user_id: string } | { team_id: string },\n    session: InternalSession | null,\n    requestType: \"client\" | \"server\" | \"admin\",\n  ): Promise<(UserApiKeysCrud['Client']['Read'] | TeamApiKeysCrud['Client']['Read'])[]> {\n    const sendRequest = (requestType === \"client\" ? this.sendClientRequest : (this as any).sendServerRequest as never).bind(this);\n    const { endpoint, queryParams } = await this._getApiKeyRequestInfo(options);\n\n    const response = await sendRequest(\n      `${endpoint}?${queryParams.toString()}`,\n      {\n        method: \"GET\",\n      },\n      session,\n      requestType,\n    );\n    const json = await response.json();\n    return json.items;\n  }\n\n  createProjectApiKey(data: yup.InferType<typeof userApiKeysCreateInputSchema>, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<yup.InferType<typeof userApiKeysCreateOutputSchema>>;\n  createProjectApiKey(data: yup.InferType<typeof teamApiKeysCreateInputSchema>, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<yup.InferType<typeof teamApiKeysCreateOutputSchema>>;\n  createProjectApiKey(data: yup.InferType<typeof userApiKeysCreateInputSchema> | yup.InferType<typeof teamApiKeysCreateInputSchema>, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<yup.InferType<typeof userApiKeysCreateOutputSchema> | yup.InferType<typeof teamApiKeysCreateOutputSchema>>;\n  async createProjectApiKey(\n    data: yup.InferType<typeof userApiKeysCreateInputSchema> | yup.InferType<typeof teamApiKeysCreateInputSchema>,\n    session: InternalSession | null,\n    requestType: \"client\" | \"server\" | \"admin\",\n  ): Promise<yup.InferType<typeof userApiKeysCreateOutputSchema> | yup.InferType<typeof teamApiKeysCreateOutputSchema>> {\n    const sendRequest = (requestType === \"client\" ? this.sendClientRequest : (this as any).sendServerRequest as never).bind(this);\n    const { endpoint } = await this._getApiKeyRequestInfo(data);\n\n    const response = await sendRequest(\n      `${endpoint}`,\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      session,\n      requestType,\n    );\n    return await response.json();\n  }\n\n  getProjectApiKey(options: { user_id: string | null }, keyId: string, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<UserApiKeysCrud['Client']['Read']>;\n  getProjectApiKey(options: { team_id: string }, keyId: string, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<TeamApiKeysCrud['Client']['Read']>;\n  getProjectApiKey(options: { user_id: string | null } | { team_id: string }, keyId: string, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<UserApiKeysCrud['Client']['Read'] | TeamApiKeysCrud['Client']['Read']>;\n  async getProjectApiKey(\n    options: { user_id: string | null } | { team_id: string },\n    keyId: string,\n    session: InternalSession | null,\n    requestType: \"client\" | \"server\" | \"admin\",\n  ): Promise<UserApiKeysCrud['Client']['Read'] | TeamApiKeysCrud['Client']['Read']> {\n    const sendRequest = (requestType === \"client\" ? this.sendClientRequest : (this as any).sendServerRequest as never).bind(this);\n    const { endpoint, queryParams } = await this._getApiKeyRequestInfo(options);\n\n    const response = await sendRequest(\n      `${endpoint}/${keyId}?${queryParams.toString()}`,\n      {\n        method: \"GET\",\n      },\n      session,\n      requestType,\n    );\n    return await response.json();\n  }\n\n  updateProjectApiKey(options: { user_id: string }, keyId: string, data: UserApiKeysCrud['Client']['Update'], session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<UserApiKeysCrud['Client']['Read']>;\n  updateProjectApiKey(options: { team_id: string }, keyId: string, data: TeamApiKeysCrud['Client']['Update'], session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<TeamApiKeysCrud['Client']['Read']>;\n  updateProjectApiKey(options: { user_id: string } | { team_id: string }, keyId: string, data: UserApiKeysCrud['Client']['Update'] | TeamApiKeysCrud['Client']['Update'], session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<UserApiKeysCrud['Client']['Read'] | TeamApiKeysCrud['Client']['Read']>;\n  async updateProjectApiKey(\n    options: { user_id: string } | { team_id: string },\n    keyId: string,\n    data: UserApiKeysCrud['Client']['Update'] | TeamApiKeysCrud['Client']['Update'],\n    session: InternalSession | null,\n    requestType: \"client\" | \"server\" | \"admin\",\n  ): Promise<UserApiKeysCrud['Client']['Read'] | TeamApiKeysCrud['Client']['Read']> {\n    const sendRequest = (requestType === \"client\" ? this.sendClientRequest : (this as any).sendServerRequest as never).bind(this);\n    const { endpoint, queryParams } = await this._getApiKeyRequestInfo(options);\n\n    const response = await sendRequest(\n      `${endpoint}/${keyId}?${queryParams.toString()}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      session,\n      requestType,\n    );\n    return await response.json();\n  }\n\n  checkProjectApiKey(type: \"user\", apiKey: string, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<UserApiKeysCrud['Client']['Read'] | null>;\n  checkProjectApiKey(type: \"team\", apiKey: string, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<TeamApiKeysCrud['Client']['Read'] | null>;\n  checkProjectApiKey(type: \"user\" | \"team\", apiKey: string, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<UserApiKeysCrud['Client']['Read'] | TeamApiKeysCrud['Client']['Read'] | null>;\n  async checkProjectApiKey(type: \"user\" | \"team\", apiKey: string, session: InternalSession | null, requestType: \"client\" | \"server\" | \"admin\"): Promise<UserApiKeysCrud['Client']['Read'] | TeamApiKeysCrud['Client']['Read'] | null> {\n    const sendRequest = (requestType === \"client\" ? this.sendClientRequestAndCatchKnownError : (this as any).sendServerRequestAndCatchKnownError as never).bind(this);\n    const result = await sendRequest(\n      `/${type}-api-keys/check`,\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({ api_key: apiKey }),\n      },\n      session,\n      [KnownErrors.ApiKeyNotValid]\n    );\n    if (result.status === \"error\") {\n      return null;\n    }\n    return await result.data.json();\n  }\n}\n\n", "let USER_AGENT;\nif (typeof navigator === 'undefined' || !navigator.userAgent?.startsWith?.('Mozilla/5.0 ')) {\n    const NAME = 'oauth4webapi';\n    const VERSION = 'v2.10.4';\n    USER_AGENT = `${NAME}/${VERSION}`;\n}\nfunction looseInstanceOf(input, expected) {\n    if (input == null) {\n        return false;\n    }\n    try {\n        return (input instanceof expected ||\n            Object.getPrototypeOf(input)[Symbol.toStringTag] === expected.prototype[Symbol.toStringTag]);\n    }\n    catch {\n        return false;\n    }\n}\nexport const clockSkew = Symbol();\nexport const clockTolerance = Symbol();\nexport const customFetch = Symbol();\nexport const useMtlsAlias = Symbol();\nconst encoder = new TextEncoder();\nconst decoder = new TextDecoder();\nfunction buf(input) {\n    if (typeof input === 'string') {\n        return encoder.encode(input);\n    }\n    return decoder.decode(input);\n}\nconst CHUNK_SIZE = 0x8000;\nfunction encodeBase64Url(input) {\n    if (input instanceof ArrayBuffer) {\n        input = new Uint8Array(input);\n    }\n    const arr = [];\n    for (let i = 0; i < input.byteLength; i += CHUNK_SIZE) {\n        arr.push(String.fromCharCode.apply(null, input.subarray(i, i + CHUNK_SIZE)));\n    }\n    return btoa(arr.join('')).replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\nfunction decodeBase64Url(input) {\n    try {\n        const binary = atob(input.replace(/-/g, '+').replace(/_/g, '/').replace(/\\s/g, ''));\n        const bytes = new Uint8Array(binary.length);\n        for (let i = 0; i < binary.length; i++) {\n            bytes[i] = binary.charCodeAt(i);\n        }\n        return bytes;\n    }\n    catch (cause) {\n        throw new OPE('The input to be decoded is not correctly encoded.', { cause });\n    }\n}\nfunction b64u(input) {\n    if (typeof input === 'string') {\n        return decodeBase64Url(input);\n    }\n    return encodeBase64Url(input);\n}\nclass LRU {\n    constructor(maxSize) {\n        this.cache = new Map();\n        this._cache = new Map();\n        this.maxSize = maxSize;\n    }\n    get(key) {\n        let v = this.cache.get(key);\n        if (v) {\n            return v;\n        }\n        if ((v = this._cache.get(key))) {\n            this.update(key, v);\n            return v;\n        }\n        return undefined;\n    }\n    has(key) {\n        return this.cache.has(key) || this._cache.has(key);\n    }\n    set(key, value) {\n        if (this.cache.has(key)) {\n            this.cache.set(key, value);\n        }\n        else {\n            this.update(key, value);\n        }\n        return this;\n    }\n    delete(key) {\n        if (this.cache.has(key)) {\n            return this.cache.delete(key);\n        }\n        if (this._cache.has(key)) {\n            return this._cache.delete(key);\n        }\n        return false;\n    }\n    update(key, value) {\n        this.cache.set(key, value);\n        if (this.cache.size >= this.maxSize) {\n            this._cache = this.cache;\n            this.cache = new Map();\n        }\n    }\n}\nexport class UnsupportedOperationError extends Error {\n    constructor(message) {\n        super(message ?? 'operation not supported');\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nexport class OperationProcessingError extends Error {\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nconst OPE = OperationProcessingError;\nconst dpopNonces = new LRU(100);\nfunction isCryptoKey(key) {\n    return key instanceof CryptoKey;\n}\nfunction isPrivateKey(key) {\n    return isCryptoKey(key) && key.type === 'private';\n}\nfunction isPublicKey(key) {\n    return isCryptoKey(key) && key.type === 'public';\n}\nconst SUPPORTED_JWS_ALGS = [\n    'PS256',\n    'ES256',\n    'RS256',\n    'PS384',\n    'ES384',\n    'RS384',\n    'PS512',\n    'ES512',\n    'RS512',\n    'EdDSA',\n];\nfunction processDpopNonce(response) {\n    try {\n        const nonce = response.headers.get('dpop-nonce');\n        if (nonce) {\n            dpopNonces.set(new URL(response.url).origin, nonce);\n        }\n    }\n    catch { }\n    return response;\n}\nfunction normalizeTyp(value) {\n    return value.toLowerCase().replace(/^application\\//, '');\n}\nfunction isJsonObject(input) {\n    if (input === null || typeof input !== 'object' || Array.isArray(input)) {\n        return false;\n    }\n    return true;\n}\nfunction prepareHeaders(input) {\n    if (looseInstanceOf(input, Headers)) {\n        input = Object.fromEntries(input.entries());\n    }\n    const headers = new Headers(input);\n    if (USER_AGENT && !headers.has('user-agent')) {\n        headers.set('user-agent', USER_AGENT);\n    }\n    if (headers.has('authorization')) {\n        throw new TypeError('\"options.headers\" must not include the \"authorization\" header name');\n    }\n    if (headers.has('dpop')) {\n        throw new TypeError('\"options.headers\" must not include the \"dpop\" header name');\n    }\n    return headers;\n}\nfunction signal(value) {\n    if (typeof value === 'function') {\n        value = value();\n    }\n    if (!(value instanceof AbortSignal)) {\n        throw new TypeError('\"options.signal\" must return or be an instance of AbortSignal');\n    }\n    return value;\n}\nexport async function discoveryRequest(issuerIdentifier, options) {\n    if (!(issuerIdentifier instanceof URL)) {\n        throw new TypeError('\"issuerIdentifier\" must be an instance of URL');\n    }\n    if (issuerIdentifier.protocol !== 'https:' && issuerIdentifier.protocol !== 'http:') {\n        throw new TypeError('\"issuer.protocol\" must be \"https:\" or \"http:\"');\n    }\n    const url = new URL(issuerIdentifier.href);\n    switch (options?.algorithm) {\n        case undefined:\n        case 'oidc':\n            url.pathname = `${url.pathname}/.well-known/openid-configuration`.replace('//', '/');\n            break;\n        case 'oauth2':\n            if (url.pathname === '/') {\n                url.pathname = '.well-known/oauth-authorization-server';\n            }\n            else {\n                url.pathname = `.well-known/oauth-authorization-server/${url.pathname}`.replace('//', '/');\n            }\n            break;\n        default:\n            throw new TypeError('\"options.algorithm\" must be \"oidc\" (default), or \"oauth2\"');\n    }\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    return (options?.[customFetch] || fetch)(url.href, {\n        headers: Object.fromEntries(headers.entries()),\n        method: 'GET',\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : null,\n    }).then(processDpopNonce);\n}\nfunction validateString(input) {\n    return typeof input === 'string' && input.length !== 0;\n}\nexport async function processDiscoveryResponse(expectedIssuerIdentifier, response) {\n    if (!(expectedIssuerIdentifier instanceof URL)) {\n        throw new TypeError('\"expectedIssuer\" must be an instance of URL');\n    }\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        throw new OPE('\"response\" is not a conform Authorization Server Metadata response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.issuer)) {\n        throw new OPE('\"response\" body \"issuer\" property must be a non-empty string');\n    }\n    if (new URL(json.issuer).href !== expectedIssuerIdentifier.href) {\n        throw new OPE('\"response\" body \"issuer\" does not match \"expectedIssuer\"');\n    }\n    return json;\n}\nfunction randomBytes() {\n    return b64u(crypto.getRandomValues(new Uint8Array(32)));\n}\nexport function generateRandomCodeVerifier() {\n    return randomBytes();\n}\nexport function generateRandomState() {\n    return randomBytes();\n}\nexport function generateRandomNonce() {\n    return randomBytes();\n}\nexport async function calculatePKCECodeChallenge(codeVerifier) {\n    if (!validateString(codeVerifier)) {\n        throw new TypeError('\"codeVerifier\" must be a non-empty string');\n    }\n    return b64u(await crypto.subtle.digest('SHA-256', buf(codeVerifier)));\n}\nfunction getKeyAndKid(input) {\n    if (input instanceof CryptoKey) {\n        return { key: input };\n    }\n    if (!(input?.key instanceof CryptoKey)) {\n        return {};\n    }\n    if (input.kid !== undefined && !validateString(input.kid)) {\n        throw new TypeError('\"kid\" must be a non-empty string');\n    }\n    return { key: input.key, kid: input.kid };\n}\nfunction formUrlEncode(token) {\n    return encodeURIComponent(token).replace(/%20/g, '+');\n}\nfunction clientSecretBasic(clientId, clientSecret) {\n    const username = formUrlEncode(clientId);\n    const password = formUrlEncode(clientSecret);\n    const credentials = btoa(`${username}:${password}`);\n    return `Basic ${credentials}`;\n}\nfunction psAlg(key) {\n    switch (key.algorithm.hash.name) {\n        case 'SHA-256':\n            return 'PS256';\n        case 'SHA-384':\n            return 'PS384';\n        case 'SHA-512':\n            return 'PS512';\n        default:\n            throw new UnsupportedOperationError('unsupported RsaHashedKeyAlgorithm hash name');\n    }\n}\nfunction rsAlg(key) {\n    switch (key.algorithm.hash.name) {\n        case 'SHA-256':\n            return 'RS256';\n        case 'SHA-384':\n            return 'RS384';\n        case 'SHA-512':\n            return 'RS512';\n        default:\n            throw new UnsupportedOperationError('unsupported RsaHashedKeyAlgorithm hash name');\n    }\n}\nfunction esAlg(key) {\n    switch (key.algorithm.namedCurve) {\n        case 'P-256':\n            return 'ES256';\n        case 'P-384':\n            return 'ES384';\n        case 'P-521':\n            return 'ES512';\n        default:\n            throw new UnsupportedOperationError('unsupported EcKeyAlgorithm namedCurve');\n    }\n}\nfunction keyToJws(key) {\n    switch (key.algorithm.name) {\n        case 'RSA-PSS':\n            return psAlg(key);\n        case 'RSASSA-PKCS1-v1_5':\n            return rsAlg(key);\n        case 'ECDSA':\n            return esAlg(key);\n        case 'Ed25519':\n        case 'Ed448':\n            return 'EdDSA';\n        default:\n            throw new UnsupportedOperationError('unsupported CryptoKey algorithm name');\n    }\n}\nfunction getClockSkew(client) {\n    const skew = client?.[clockSkew];\n    return typeof skew === 'number' && Number.isFinite(skew) ? skew : 0;\n}\nfunction getClockTolerance(client) {\n    const tolerance = client?.[clockTolerance];\n    return typeof tolerance === 'number' && Number.isFinite(tolerance) && Math.sign(tolerance) !== -1\n        ? tolerance\n        : 30;\n}\nfunction epochTime() {\n    return Math.floor(Date.now() / 1000);\n}\nfunction clientAssertion(as, client) {\n    const now = epochTime() + getClockSkew(client);\n    return {\n        jti: randomBytes(),\n        aud: [as.issuer, as.token_endpoint],\n        exp: now + 60,\n        iat: now,\n        nbf: now,\n        iss: client.client_id,\n        sub: client.client_id,\n    };\n}\nasync function privateKeyJwt(as, client, key, kid) {\n    return jwt({\n        alg: keyToJws(key),\n        kid,\n    }, clientAssertion(as, client), key);\n}\nfunction assertAs(as) {\n    if (typeof as !== 'object' || as === null) {\n        throw new TypeError('\"as\" must be an object');\n    }\n    if (!validateString(as.issuer)) {\n        throw new TypeError('\"as.issuer\" property must be a non-empty string');\n    }\n    return true;\n}\nfunction assertClient(client) {\n    if (typeof client !== 'object' || client === null) {\n        throw new TypeError('\"client\" must be an object');\n    }\n    if (!validateString(client.client_id)) {\n        throw new TypeError('\"client.client_id\" property must be a non-empty string');\n    }\n    return true;\n}\nfunction assertClientSecret(clientSecret) {\n    if (!validateString(clientSecret)) {\n        throw new TypeError('\"client.client_secret\" property must be a non-empty string');\n    }\n    return clientSecret;\n}\nfunction assertNoClientPrivateKey(clientAuthMethod, clientPrivateKey) {\n    if (clientPrivateKey !== undefined) {\n        throw new TypeError(`\"options.clientPrivateKey\" property must not be provided when ${clientAuthMethod} client authentication method is used.`);\n    }\n}\nfunction assertNoClientSecret(clientAuthMethod, clientSecret) {\n    if (clientSecret !== undefined) {\n        throw new TypeError(`\"client.client_secret\" property must not be provided when ${clientAuthMethod} client authentication method is used.`);\n    }\n}\nasync function clientAuthentication(as, client, body, headers, clientPrivateKey) {\n    body.delete('client_secret');\n    body.delete('client_assertion_type');\n    body.delete('client_assertion');\n    switch (client.token_endpoint_auth_method) {\n        case undefined:\n        case 'client_secret_basic': {\n            assertNoClientPrivateKey('client_secret_basic', clientPrivateKey);\n            headers.set('authorization', clientSecretBasic(client.client_id, assertClientSecret(client.client_secret)));\n            break;\n        }\n        case 'client_secret_post': {\n            assertNoClientPrivateKey('client_secret_post', clientPrivateKey);\n            body.set('client_id', client.client_id);\n            body.set('client_secret', assertClientSecret(client.client_secret));\n            break;\n        }\n        case 'private_key_jwt': {\n            assertNoClientSecret('private_key_jwt', client.client_secret);\n            if (clientPrivateKey === undefined) {\n                throw new TypeError('\"options.clientPrivateKey\" must be provided when \"client.token_endpoint_auth_method\" is \"private_key_jwt\"');\n            }\n            const { key, kid } = getKeyAndKid(clientPrivateKey);\n            if (!isPrivateKey(key)) {\n                throw new TypeError('\"options.clientPrivateKey.key\" must be a private CryptoKey');\n            }\n            body.set('client_id', client.client_id);\n            body.set('client_assertion_type', 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer');\n            body.set('client_assertion', await privateKeyJwt(as, client, key, kid));\n            break;\n        }\n        case 'tls_client_auth':\n        case 'self_signed_tls_client_auth':\n        case 'none': {\n            assertNoClientSecret(client.token_endpoint_auth_method, client.client_secret);\n            assertNoClientPrivateKey(client.token_endpoint_auth_method, clientPrivateKey);\n            body.set('client_id', client.client_id);\n            break;\n        }\n        default:\n            throw new UnsupportedOperationError('unsupported client token_endpoint_auth_method');\n    }\n}\nasync function jwt(header, claimsSet, key) {\n    if (!key.usages.includes('sign')) {\n        throw new TypeError('CryptoKey instances used for signing assertions must include \"sign\" in their \"usages\"');\n    }\n    const input = `${b64u(buf(JSON.stringify(header)))}.${b64u(buf(JSON.stringify(claimsSet)))}`;\n    const signature = b64u(await crypto.subtle.sign(keyToSubtle(key), key, buf(input)));\n    return `${input}.${signature}`;\n}\nexport async function issueRequestObject(as, client, parameters, privateKey) {\n    assertAs(as);\n    assertClient(client);\n    parameters = new URLSearchParams(parameters);\n    const { key, kid } = getKeyAndKid(privateKey);\n    if (!isPrivateKey(key)) {\n        throw new TypeError('\"privateKey.key\" must be a private CryptoKey');\n    }\n    parameters.set('client_id', client.client_id);\n    const now = epochTime() + getClockSkew(client);\n    const claims = {\n        ...Object.fromEntries(parameters.entries()),\n        jti: randomBytes(),\n        aud: as.issuer,\n        exp: now + 60,\n        iat: now,\n        nbf: now,\n        iss: client.client_id,\n    };\n    let resource;\n    if (parameters.has('resource') &&\n        (resource = parameters.getAll('resource')) &&\n        resource.length > 1) {\n        claims.resource = resource;\n    }\n    {\n        let value = parameters.get('max_age');\n        if (value !== null) {\n            claims.max_age = parseInt(value, 10);\n            if (!Number.isFinite(claims.max_age)) {\n                throw new OPE('\"max_age\" parameter must be a number');\n            }\n        }\n    }\n    {\n        let value = parameters.get('claims');\n        if (value !== null) {\n            try {\n                claims.claims = JSON.parse(value);\n            }\n            catch (cause) {\n                throw new OPE('failed to parse the \"claims\" parameter as JSON', { cause });\n            }\n            if (!isJsonObject(claims.claims)) {\n                throw new OPE('\"claims\" parameter must be a JSON with a top level object');\n            }\n        }\n    }\n    {\n        let value = parameters.get('authorization_details');\n        if (value !== null) {\n            try {\n                claims.authorization_details = JSON.parse(value);\n            }\n            catch (cause) {\n                throw new OPE('failed to parse the \"authorization_details\" parameter as JSON', { cause });\n            }\n            if (!Array.isArray(claims.authorization_details)) {\n                throw new OPE('\"authorization_details\" parameter must be a JSON with a top level array');\n            }\n        }\n    }\n    return jwt({\n        alg: keyToJws(key),\n        typ: 'oauth-authz-req+jwt',\n        kid,\n    }, claims, key);\n}\nasync function dpopProofJwt(headers, options, url, htm, clockSkew, accessToken) {\n    const { privateKey, publicKey, nonce = dpopNonces.get(url.origin) } = options;\n    if (!isPrivateKey(privateKey)) {\n        throw new TypeError('\"DPoP.privateKey\" must be a private CryptoKey');\n    }\n    if (!isPublicKey(publicKey)) {\n        throw new TypeError('\"DPoP.publicKey\" must be a public CryptoKey');\n    }\n    if (nonce !== undefined && !validateString(nonce)) {\n        throw new TypeError('\"DPoP.nonce\" must be a non-empty string or undefined');\n    }\n    if (!publicKey.extractable) {\n        throw new TypeError('\"DPoP.publicKey.extractable\" must be true');\n    }\n    const now = epochTime() + clockSkew;\n    const proof = await jwt({\n        alg: keyToJws(privateKey),\n        typ: 'dpop+jwt',\n        jwk: await publicJwk(publicKey),\n    }, {\n        iat: now,\n        jti: randomBytes(),\n        htm,\n        nonce,\n        htu: `${url.origin}${url.pathname}`,\n        ath: accessToken ? b64u(await crypto.subtle.digest('SHA-256', buf(accessToken))) : undefined,\n    }, privateKey);\n    headers.set('dpop', proof);\n}\nlet jwkCache;\nasync function getSetPublicJwkCache(key) {\n    const { kty, e, n, x, y, crv } = await crypto.subtle.exportKey('jwk', key);\n    const jwk = { kty, e, n, x, y, crv };\n    jwkCache.set(key, jwk);\n    return jwk;\n}\nasync function publicJwk(key) {\n    jwkCache || (jwkCache = new WeakMap());\n    return jwkCache.get(key) || getSetPublicJwkCache(key);\n}\nfunction validateEndpoint(value, endpoint, options) {\n    if (typeof value !== 'string') {\n        if (options?.[useMtlsAlias]) {\n            throw new TypeError(`\"as.mtls_endpoint_aliases.${endpoint}\" must be a string`);\n        }\n        throw new TypeError(`\"as.${endpoint}\" must be a string`);\n    }\n    return new URL(value);\n}\nfunction resolveEndpoint(as, endpoint, options) {\n    if (options?.[useMtlsAlias] && as.mtls_endpoint_aliases && endpoint in as.mtls_endpoint_aliases) {\n        return validateEndpoint(as.mtls_endpoint_aliases[endpoint], endpoint, options);\n    }\n    return validateEndpoint(as[endpoint], endpoint);\n}\nexport async function pushedAuthorizationRequest(as, client, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'pushed_authorization_request_endpoint', options);\n    const body = new URLSearchParams(parameters);\n    body.set('client_id', client.client_id);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    if (options?.DPoP !== undefined) {\n        await dpopProofJwt(headers, options.DPoP, url, 'POST', getClockSkew(client));\n    }\n    return authenticatedRequest(as, client, 'POST', url, body, headers, options);\n}\nexport function isOAuth2Error(input) {\n    const value = input;\n    if (typeof value !== 'object' || Array.isArray(value) || value === null) {\n        return false;\n    }\n    return value.error !== undefined;\n}\nfunction unquote(value) {\n    if (value.length >= 2 && value[0] === '\"' && value[value.length - 1] === '\"') {\n        return value.slice(1, -1);\n    }\n    return value;\n}\nconst SPLIT_REGEXP = /((?:,|, )?[0-9a-zA-Z!#$%&'*+-.^_`|~]+=)/;\nconst SCHEMES_REGEXP = /(?:^|, ?)([0-9a-zA-Z!#$%&'*+\\-.^_`|~]+)(?=$|[ ,])/g;\nfunction wwwAuth(scheme, params) {\n    const arr = params.split(SPLIT_REGEXP).slice(1);\n    if (!arr.length) {\n        return { scheme: scheme.toLowerCase(), parameters: {} };\n    }\n    arr[arr.length - 1] = arr[arr.length - 1].replace(/,$/, '');\n    const parameters = {};\n    for (let i = 1; i < arr.length; i += 2) {\n        const idx = i;\n        if (arr[idx][0] === '\"') {\n            while (arr[idx].slice(-1) !== '\"' && ++i < arr.length) {\n                arr[idx] += arr[i];\n            }\n        }\n        const key = arr[idx - 1].replace(/^(?:, ?)|=$/g, '').toLowerCase();\n        parameters[key] = unquote(arr[idx]);\n    }\n    return {\n        scheme: scheme.toLowerCase(),\n        parameters,\n    };\n}\nexport function parseWwwAuthenticateChallenges(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    const header = response.headers.get('www-authenticate');\n    if (header === null) {\n        return undefined;\n    }\n    const result = [];\n    for (const { 1: scheme, index } of header.matchAll(SCHEMES_REGEXP)) {\n        result.push([scheme, index]);\n    }\n    if (!result.length) {\n        return undefined;\n    }\n    const challenges = result.map(([scheme, indexOf], i, others) => {\n        const next = others[i + 1];\n        let parameters;\n        if (next) {\n            parameters = header.slice(indexOf, next[1]);\n        }\n        else {\n            parameters = header.slice(indexOf);\n        }\n        return wwwAuth(scheme, parameters);\n    });\n    return challenges;\n}\nexport async function processPushedAuthorizationResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 201) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Pushed Authorization Request Endpoint response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.request_uri)) {\n        throw new OPE('\"response\" body \"request_uri\" property must be a non-empty string');\n    }\n    if (typeof json.expires_in !== 'number' || json.expires_in <= 0) {\n        throw new OPE('\"response\" body \"expires_in\" property must be a positive number');\n    }\n    return json;\n}\nexport async function protectedResourceRequest(accessToken, method, url, headers, body, options) {\n    if (!validateString(accessToken)) {\n        throw new TypeError('\"accessToken\" must be a non-empty string');\n    }\n    if (!(url instanceof URL)) {\n        throw new TypeError('\"url\" must be an instance of URL');\n    }\n    headers = prepareHeaders(headers);\n    if (options?.DPoP === undefined) {\n        headers.set('authorization', `Bearer ${accessToken}`);\n    }\n    else {\n        await dpopProofJwt(headers, options.DPoP, url, 'GET', getClockSkew({ [clockSkew]: options?.[clockSkew] }), accessToken);\n        headers.set('authorization', `DPoP ${accessToken}`);\n    }\n    return (options?.[customFetch] || fetch)(url.href, {\n        body,\n        headers: Object.fromEntries(headers.entries()),\n        method,\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : null,\n    }).then(processDpopNonce);\n}\nexport async function userInfoRequest(as, client, accessToken, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'userinfo_endpoint', options);\n    const headers = prepareHeaders(options?.headers);\n    if (client.userinfo_signed_response_alg) {\n        headers.set('accept', 'application/jwt');\n    }\n    else {\n        headers.set('accept', 'application/json');\n        headers.append('accept', 'application/jwt');\n    }\n    return protectedResourceRequest(accessToken, 'GET', url, headers, null, {\n        ...options,\n        [clockSkew]: getClockSkew(client),\n    });\n}\nlet jwksCache;\nasync function getPublicSigKeyFromIssuerJwksUri(as, options, header) {\n    const { alg, kid } = header;\n    checkSupportedJwsAlg(alg);\n    let jwks;\n    let age;\n    jwksCache || (jwksCache = new WeakMap());\n    if (jwksCache.has(as)) {\n        ;\n        ({ jwks, age } = jwksCache.get(as));\n        if (age >= 300) {\n            jwksCache.delete(as);\n            return getPublicSigKeyFromIssuerJwksUri(as, options, header);\n        }\n    }\n    else {\n        jwks = await jwksRequest(as, options).then(processJwksResponse);\n        age = 0;\n        jwksCache.set(as, {\n            jwks,\n            iat: epochTime(),\n            get age() {\n                return epochTime() - this.iat;\n            },\n        });\n    }\n    let kty;\n    switch (alg.slice(0, 2)) {\n        case 'RS':\n        case 'PS':\n            kty = 'RSA';\n            break;\n        case 'ES':\n            kty = 'EC';\n            break;\n        case 'Ed':\n            kty = 'OKP';\n            break;\n        default:\n            throw new UnsupportedOperationError();\n    }\n    const candidates = jwks.keys.filter((jwk) => {\n        if (jwk.kty !== kty) {\n            return false;\n        }\n        if (kid !== undefined && kid !== jwk.kid) {\n            return false;\n        }\n        if (jwk.alg !== undefined && alg !== jwk.alg) {\n            return false;\n        }\n        if (jwk.use !== undefined && jwk.use !== 'sig') {\n            return false;\n        }\n        if (jwk.key_ops?.includes('verify') === false) {\n            return false;\n        }\n        switch (true) {\n            case alg === 'ES256' && jwk.crv !== 'P-256':\n            case alg === 'ES384' && jwk.crv !== 'P-384':\n            case alg === 'ES512' && jwk.crv !== 'P-521':\n            case alg === 'EdDSA' && !(jwk.crv === 'Ed25519' || jwk.crv === 'Ed448'):\n                return false;\n        }\n        return true;\n    });\n    const { 0: jwk, length } = candidates;\n    if (!length) {\n        if (age >= 60) {\n            jwksCache.delete(as);\n            return getPublicSigKeyFromIssuerJwksUri(as, options, header);\n        }\n        throw new OPE('error when selecting a JWT verification key, no applicable keys found');\n    }\n    if (length !== 1) {\n        throw new OPE('error when selecting a JWT verification key, multiple applicable keys found, a \"kid\" JWT Header Parameter is required');\n    }\n    const key = await importJwk(alg, jwk);\n    if (key.type !== 'public') {\n        throw new OPE('jwks_uri must only contain public keys');\n    }\n    return key;\n}\nexport const skipSubjectCheck = Symbol();\nfunction getContentType(response) {\n    return response.headers.get('content-type')?.split(';')[0];\n}\nexport async function processUserInfoResponse(as, client, expectedSubject, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        throw new OPE('\"response\" is not a conform UserInfo Endpoint response');\n    }\n    let json;\n    if (getContentType(response) === 'application/jwt') {\n        assertReadableResponse(response);\n        const { claims } = await validateJwt(await response.text(), checkSigningAlgorithm.bind(undefined, client.userinfo_signed_response_alg, as.userinfo_signing_alg_values_supported), noSignatureCheck, getClockSkew(client), getClockTolerance(client))\n            .then(validateOptionalAudience.bind(undefined, client.client_id))\n            .then(validateOptionalIssuer.bind(undefined, as.issuer));\n        json = claims;\n    }\n    else {\n        if (client.userinfo_signed_response_alg) {\n            throw new OPE('JWT UserInfo Response expected');\n        }\n        assertReadableResponse(response);\n        try {\n            json = await response.json();\n        }\n        catch (cause) {\n            throw new OPE('failed to parse \"response\" body as JSON', { cause });\n        }\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.sub)) {\n        throw new OPE('\"response\" body \"sub\" property must be a non-empty string');\n    }\n    switch (expectedSubject) {\n        case skipSubjectCheck:\n            break;\n        default:\n            if (!validateString(expectedSubject)) {\n                throw new OPE('\"expectedSubject\" must be a non-empty string');\n            }\n            if (json.sub !== expectedSubject) {\n                throw new OPE('unexpected \"response\" body \"sub\" value');\n            }\n    }\n    return json;\n}\nasync function authenticatedRequest(as, client, method, url, body, headers, options) {\n    await clientAuthentication(as, client, body, headers, options?.clientPrivateKey);\n    headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n    return (options?.[customFetch] || fetch)(url.href, {\n        body,\n        headers: Object.fromEntries(headers.entries()),\n        method,\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : null,\n    }).then(processDpopNonce);\n}\nasync function tokenEndpointRequest(as, client, grantType, parameters, options) {\n    const url = resolveEndpoint(as, 'token_endpoint', options);\n    parameters.set('grant_type', grantType);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    if (options?.DPoP !== undefined) {\n        await dpopProofJwt(headers, options.DPoP, url, 'POST', getClockSkew(client));\n    }\n    return authenticatedRequest(as, client, 'POST', url, parameters, headers, options);\n}\nexport async function refreshTokenGrantRequest(as, client, refreshToken, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!validateString(refreshToken)) {\n        throw new TypeError('\"refreshToken\" must be a non-empty string');\n    }\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('refresh_token', refreshToken);\n    return tokenEndpointRequest(as, client, 'refresh_token', parameters, options);\n}\nconst idTokenClaims = new WeakMap();\nexport function getValidatedIdTokenClaims(ref) {\n    if (!ref.id_token) {\n        return undefined;\n    }\n    const claims = idTokenClaims.get(ref);\n    if (!claims) {\n        throw new TypeError('\"ref\" was already garbage collected or did not resolve from the proper sources');\n    }\n    return claims;\n}\nasync function processGenericAccessTokenResponse(as, client, response, ignoreIdToken = false, ignoreRefreshToken = false) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Token Endpoint response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.access_token)) {\n        throw new OPE('\"response\" body \"access_token\" property must be a non-empty string');\n    }\n    if (!validateString(json.token_type)) {\n        throw new OPE('\"response\" body \"token_type\" property must be a non-empty string');\n    }\n    json.token_type = json.token_type.toLowerCase();\n    if (json.token_type !== 'dpop' && json.token_type !== 'bearer') {\n        throw new UnsupportedOperationError('unsupported `token_type` value');\n    }\n    if (json.expires_in !== undefined &&\n        (typeof json.expires_in !== 'number' || json.expires_in <= 0)) {\n        throw new OPE('\"response\" body \"expires_in\" property must be a positive number');\n    }\n    if (!ignoreRefreshToken &&\n        json.refresh_token !== undefined &&\n        !validateString(json.refresh_token)) {\n        throw new OPE('\"response\" body \"refresh_token\" property must be a non-empty string');\n    }\n    if (json.scope !== undefined && typeof json.scope !== 'string') {\n        throw new OPE('\"response\" body \"scope\" property must be a string');\n    }\n    if (!ignoreIdToken) {\n        if (json.id_token !== undefined && !validateString(json.id_token)) {\n            throw new OPE('\"response\" body \"id_token\" property must be a non-empty string');\n        }\n        if (json.id_token) {\n            const { claims } = await validateJwt(json.id_token, checkSigningAlgorithm.bind(undefined, client.id_token_signed_response_alg, as.id_token_signing_alg_values_supported), noSignatureCheck, getClockSkew(client), getClockTolerance(client))\n                .then(validatePresence.bind(undefined, ['aud', 'exp', 'iat', 'iss', 'sub']))\n                .then(validateIssuer.bind(undefined, as.issuer))\n                .then(validateAudience.bind(undefined, client.client_id));\n            if (Array.isArray(claims.aud) && claims.aud.length !== 1 && claims.azp !== client.client_id) {\n                throw new OPE('unexpected ID Token \"azp\" (authorized party) claim value');\n            }\n            if (client.require_auth_time && typeof claims.auth_time !== 'number') {\n                throw new OPE('unexpected ID Token \"auth_time\" (authentication time) claim value');\n            }\n            idTokenClaims.set(json, claims);\n        }\n    }\n    return json;\n}\nexport async function processRefreshTokenResponse(as, client, response) {\n    return processGenericAccessTokenResponse(as, client, response);\n}\nfunction validateOptionalAudience(expected, result) {\n    if (result.claims.aud !== undefined) {\n        return validateAudience(expected, result);\n    }\n    return result;\n}\nfunction validateAudience(expected, result) {\n    if (Array.isArray(result.claims.aud)) {\n        if (!result.claims.aud.includes(expected)) {\n            throw new OPE('unexpected JWT \"aud\" (audience) claim value');\n        }\n    }\n    else if (result.claims.aud !== expected) {\n        throw new OPE('unexpected JWT \"aud\" (audience) claim value');\n    }\n    return result;\n}\nfunction validateOptionalIssuer(expected, result) {\n    if (result.claims.iss !== undefined) {\n        return validateIssuer(expected, result);\n    }\n    return result;\n}\nfunction validateIssuer(expected, result) {\n    if (result.claims.iss !== expected) {\n        throw new OPE('unexpected JWT \"iss\" (issuer) claim value');\n    }\n    return result;\n}\nconst branded = new WeakSet();\nfunction brand(searchParams) {\n    branded.add(searchParams);\n    return searchParams;\n}\nexport async function authorizationCodeGrantRequest(as, client, callbackParameters, redirectUri, codeVerifier, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!branded.has(callbackParameters)) {\n        throw new TypeError('\"callbackParameters\" must be an instance of URLSearchParams obtained from \"validateAuthResponse()\", or \"validateJwtAuthResponse()');\n    }\n    if (!validateString(redirectUri)) {\n        throw new TypeError('\"redirectUri\" must be a non-empty string');\n    }\n    if (!validateString(codeVerifier)) {\n        throw new TypeError('\"codeVerifier\" must be a non-empty string');\n    }\n    const code = getURLSearchParameter(callbackParameters, 'code');\n    if (!code) {\n        throw new OPE('no authorization code in \"callbackParameters\"');\n    }\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('redirect_uri', redirectUri);\n    parameters.set('code_verifier', codeVerifier);\n    parameters.set('code', code);\n    return tokenEndpointRequest(as, client, 'authorization_code', parameters, options);\n}\nconst jwtClaimNames = {\n    aud: 'audience',\n    c_hash: 'code hash',\n    client_id: 'client id',\n    exp: 'expiration time',\n    iat: 'issued at',\n    iss: 'issuer',\n    jti: 'jwt id',\n    nonce: 'nonce',\n    s_hash: 'state hash',\n    sub: 'subject',\n    ath: 'access token hash',\n    htm: 'http method',\n    htu: 'http uri',\n    cnf: 'confirmation',\n};\nfunction validatePresence(required, result) {\n    for (const claim of required) {\n        if (result.claims[claim] === undefined) {\n            throw new OPE(`JWT \"${claim}\" (${jwtClaimNames[claim]}) claim missing`);\n        }\n    }\n    return result;\n}\nexport const expectNoNonce = Symbol();\nexport const skipAuthTimeCheck = Symbol();\nexport async function processAuthorizationCodeOpenIDResponse(as, client, response, expectedNonce, maxAge) {\n    const result = await processGenericAccessTokenResponse(as, client, response);\n    if (isOAuth2Error(result)) {\n        return result;\n    }\n    if (!validateString(result.id_token)) {\n        throw new OPE('\"response\" body \"id_token\" property must be a non-empty string');\n    }\n    maxAge ?? (maxAge = client.default_max_age ?? skipAuthTimeCheck);\n    const claims = getValidatedIdTokenClaims(result);\n    if ((client.require_auth_time || maxAge !== skipAuthTimeCheck) &&\n        claims.auth_time === undefined) {\n        throw new OPE('ID Token \"auth_time\" (authentication time) claim missing');\n    }\n    if (maxAge !== skipAuthTimeCheck) {\n        if (typeof maxAge !== 'number' || maxAge < 0) {\n            throw new TypeError('\"options.max_age\" must be a non-negative number');\n        }\n        const now = epochTime() + getClockSkew(client);\n        const tolerance = getClockTolerance(client);\n        if (claims.auth_time + maxAge < now - tolerance) {\n            throw new OPE('too much time has elapsed since the last End-User authentication');\n        }\n    }\n    switch (expectedNonce) {\n        case undefined:\n        case expectNoNonce:\n            if (claims.nonce !== undefined) {\n                throw new OPE('unexpected ID Token \"nonce\" claim value');\n            }\n            break;\n        default:\n            if (!validateString(expectedNonce)) {\n                throw new TypeError('\"expectedNonce\" must be a non-empty string');\n            }\n            if (claims.nonce === undefined) {\n                throw new OPE('ID Token \"nonce\" claim missing');\n            }\n            if (claims.nonce !== expectedNonce) {\n                throw new OPE('unexpected ID Token \"nonce\" claim value');\n            }\n    }\n    return result;\n}\nexport async function processAuthorizationCodeOAuth2Response(as, client, response) {\n    const result = await processGenericAccessTokenResponse(as, client, response, true);\n    if (isOAuth2Error(result)) {\n        return result;\n    }\n    if (result.id_token !== undefined) {\n        if (typeof result.id_token === 'string' && result.id_token.length) {\n            throw new OPE('Unexpected ID Token returned, use processAuthorizationCodeOpenIDResponse() for OpenID Connect callback processing');\n        }\n        delete result.id_token;\n    }\n    return result;\n}\nfunction checkJwtType(expected, result) {\n    if (typeof result.header.typ !== 'string' || normalizeTyp(result.header.typ) !== expected) {\n        throw new OPE('unexpected JWT \"typ\" header parameter value');\n    }\n    return result;\n}\nexport async function clientCredentialsGrantRequest(as, client, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    return tokenEndpointRequest(as, client, 'client_credentials', new URLSearchParams(parameters), options);\n}\nexport async function processClientCredentialsResponse(as, client, response) {\n    const result = await processGenericAccessTokenResponse(as, client, response, true, true);\n    if (isOAuth2Error(result)) {\n        return result;\n    }\n    return result;\n}\nexport async function revocationRequest(as, client, token, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!validateString(token)) {\n        throw new TypeError('\"token\" must be a non-empty string');\n    }\n    const url = resolveEndpoint(as, 'revocation_endpoint', options);\n    const body = new URLSearchParams(options?.additionalParameters);\n    body.set('token', token);\n    const headers = prepareHeaders(options?.headers);\n    headers.delete('accept');\n    return authenticatedRequest(as, client, 'POST', url, body, headers, options);\n}\nexport async function processRevocationResponse(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Revocation Endpoint response');\n    }\n    return undefined;\n}\nfunction assertReadableResponse(response) {\n    if (response.bodyUsed) {\n        throw new TypeError('\"response\" body has been used already');\n    }\n}\nexport async function introspectionRequest(as, client, token, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!validateString(token)) {\n        throw new TypeError('\"token\" must be a non-empty string');\n    }\n    const url = resolveEndpoint(as, 'introspection_endpoint', options);\n    const body = new URLSearchParams(options?.additionalParameters);\n    body.set('token', token);\n    const headers = prepareHeaders(options?.headers);\n    if (options?.requestJwtResponse ?? client.introspection_signed_response_alg) {\n        headers.set('accept', 'application/token-introspection+jwt');\n    }\n    else {\n        headers.set('accept', 'application/json');\n    }\n    return authenticatedRequest(as, client, 'POST', url, body, headers, options);\n}\nexport async function processIntrospectionResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Introspection Endpoint response');\n    }\n    let json;\n    if (getContentType(response) === 'application/token-introspection+jwt') {\n        assertReadableResponse(response);\n        const { claims } = await validateJwt(await response.text(), checkSigningAlgorithm.bind(undefined, client.introspection_signed_response_alg, as.introspection_signing_alg_values_supported), noSignatureCheck, getClockSkew(client), getClockTolerance(client))\n            .then(checkJwtType.bind(undefined, 'token-introspection+jwt'))\n            .then(validatePresence.bind(undefined, ['aud', 'iat', 'iss']))\n            .then(validateIssuer.bind(undefined, as.issuer))\n            .then(validateAudience.bind(undefined, client.client_id));\n        json = claims.token_introspection;\n        if (!isJsonObject(json)) {\n            throw new OPE('JWT \"token_introspection\" claim must be a JSON object');\n        }\n    }\n    else {\n        assertReadableResponse(response);\n        try {\n            json = await response.json();\n        }\n        catch (cause) {\n            throw new OPE('failed to parse \"response\" body as JSON', { cause });\n        }\n        if (!isJsonObject(json)) {\n            throw new OPE('\"response\" body must be a top level object');\n        }\n    }\n    if (typeof json.active !== 'boolean') {\n        throw new OPE('\"response\" body \"active\" property must be a boolean');\n    }\n    return json;\n}\nasync function jwksRequest(as, options) {\n    assertAs(as);\n    const url = resolveEndpoint(as, 'jwks_uri');\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    headers.append('accept', 'application/jwk-set+json');\n    return (options?.[customFetch] || fetch)(url.href, {\n        headers: Object.fromEntries(headers.entries()),\n        method: 'GET',\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : null,\n    }).then(processDpopNonce);\n}\nasync function processJwksResponse(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        throw new OPE('\"response\" is not a conform JSON Web Key Set response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!Array.isArray(json.keys)) {\n        throw new OPE('\"response\" body \"keys\" property must be an array');\n    }\n    if (!Array.prototype.every.call(json.keys, isJsonObject)) {\n        throw new OPE('\"response\" body \"keys\" property members must be JWK formatted objects');\n    }\n    return json;\n}\nasync function handleOAuthBodyError(response) {\n    if (response.status > 399 && response.status < 500) {\n        assertReadableResponse(response);\n        try {\n            const json = await response.json();\n            if (isJsonObject(json) && typeof json.error === 'string' && json.error.length) {\n                if (json.error_description !== undefined && typeof json.error_description !== 'string') {\n                    delete json.error_description;\n                }\n                if (json.error_uri !== undefined && typeof json.error_uri !== 'string') {\n                    delete json.error_uri;\n                }\n                if (json.algs !== undefined && typeof json.algs !== 'string') {\n                    delete json.algs;\n                }\n                if (json.scope !== undefined && typeof json.scope !== 'string') {\n                    delete json.scope;\n                }\n                return json;\n            }\n        }\n        catch { }\n    }\n    return undefined;\n}\nfunction checkSupportedJwsAlg(alg) {\n    if (!SUPPORTED_JWS_ALGS.includes(alg)) {\n        throw new UnsupportedOperationError('unsupported JWS \"alg\" identifier');\n    }\n    return alg;\n}\nfunction checkRsaKeyAlgorithm(algorithm) {\n    if (typeof algorithm.modulusLength !== 'number' || algorithm.modulusLength < 2048) {\n        throw new OPE(`${algorithm.name} modulusLength must be at least 2048 bits`);\n    }\n}\nfunction ecdsaHashName(namedCurve) {\n    switch (namedCurve) {\n        case 'P-256':\n            return 'SHA-256';\n        case 'P-384':\n            return 'SHA-384';\n        case 'P-521':\n            return 'SHA-512';\n        default:\n            throw new UnsupportedOperationError();\n    }\n}\nfunction keyToSubtle(key) {\n    switch (key.algorithm.name) {\n        case 'ECDSA':\n            return {\n                name: key.algorithm.name,\n                hash: ecdsaHashName(key.algorithm.namedCurve),\n            };\n        case 'RSA-PSS': {\n            checkRsaKeyAlgorithm(key.algorithm);\n            switch (key.algorithm.hash.name) {\n                case 'SHA-256':\n                case 'SHA-384':\n                case 'SHA-512':\n                    return {\n                        name: key.algorithm.name,\n                        saltLength: parseInt(key.algorithm.hash.name.slice(-3), 10) >> 3,\n                    };\n                default:\n                    throw new UnsupportedOperationError();\n            }\n        }\n        case 'RSASSA-PKCS1-v1_5':\n            checkRsaKeyAlgorithm(key.algorithm);\n            return key.algorithm.name;\n        case 'Ed448':\n        case 'Ed25519':\n            return key.algorithm.name;\n    }\n    throw new UnsupportedOperationError();\n}\nconst noSignatureCheck = Symbol();\nasync function validateJwt(jws, checkAlg, getKey, clockSkew, clockTolerance) {\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature, length } = jws.split('.');\n    if (length === 5) {\n        throw new UnsupportedOperationError('JWE structure JWTs are not supported');\n    }\n    if (length !== 3) {\n        throw new OPE('Invalid JWT');\n    }\n    let header;\n    try {\n        header = JSON.parse(buf(b64u(protectedHeader)));\n    }\n    catch (cause) {\n        throw new OPE('failed to parse JWT Header body as base64url encoded JSON', { cause });\n    }\n    if (!isJsonObject(header)) {\n        throw new OPE('JWT Header must be a top level object');\n    }\n    checkAlg(header);\n    if (header.crit !== undefined) {\n        throw new OPE('unexpected JWT \"crit\" header parameter');\n    }\n    const signature = b64u(encodedSignature);\n    let key;\n    if (getKey !== noSignatureCheck) {\n        key = await getKey(header);\n        const input = `${protectedHeader}.${payload}`;\n        const verified = await crypto.subtle.verify(keyToSubtle(key), key, signature, buf(input));\n        if (!verified) {\n            throw new OPE('JWT signature verification failed');\n        }\n    }\n    let claims;\n    try {\n        claims = JSON.parse(buf(b64u(payload)));\n    }\n    catch (cause) {\n        throw new OPE('failed to parse JWT Payload body as base64url encoded JSON', { cause });\n    }\n    if (!isJsonObject(claims)) {\n        throw new OPE('JWT Payload must be a top level object');\n    }\n    const now = epochTime() + clockSkew;\n    if (claims.exp !== undefined) {\n        if (typeof claims.exp !== 'number') {\n            throw new OPE('unexpected JWT \"exp\" (expiration time) claim type');\n        }\n        if (claims.exp <= now - clockTolerance) {\n            throw new OPE('unexpected JWT \"exp\" (expiration time) claim value, timestamp is <= now()');\n        }\n    }\n    if (claims.iat !== undefined) {\n        if (typeof claims.iat !== 'number') {\n            throw new OPE('unexpected JWT \"iat\" (issued at) claim type');\n        }\n    }\n    if (claims.iss !== undefined) {\n        if (typeof claims.iss !== 'string') {\n            throw new OPE('unexpected JWT \"iss\" (issuer) claim type');\n        }\n    }\n    if (claims.nbf !== undefined) {\n        if (typeof claims.nbf !== 'number') {\n            throw new OPE('unexpected JWT \"nbf\" (not before) claim type');\n        }\n        if (claims.nbf > now + clockTolerance) {\n            throw new OPE('unexpected JWT \"nbf\" (not before) claim value, timestamp is > now()');\n        }\n    }\n    if (claims.aud !== undefined) {\n        if (typeof claims.aud !== 'string' && !Array.isArray(claims.aud)) {\n            throw new OPE('unexpected JWT \"aud\" (audience) claim type');\n        }\n    }\n    return { header, claims, signature, key };\n}\nexport async function validateJwtAuthResponse(as, client, parameters, expectedState, options) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        parameters = parameters.searchParams;\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw new TypeError('\"parameters\" must be an instance of URLSearchParams, or URL');\n    }\n    const response = getURLSearchParameter(parameters, 'response');\n    if (!response) {\n        throw new OPE('\"parameters\" does not contain a JARM response');\n    }\n    if (typeof as.jwks_uri !== 'string') {\n        throw new TypeError('\"as.jwks_uri\" must be a string');\n    }\n    const { claims } = await validateJwt(response, checkSigningAlgorithm.bind(undefined, client.authorization_signed_response_alg, as.authorization_signing_alg_values_supported), getPublicSigKeyFromIssuerJwksUri.bind(undefined, as, options), getClockSkew(client), getClockTolerance(client))\n        .then(validatePresence.bind(undefined, ['aud', 'exp', 'iss']))\n        .then(validateIssuer.bind(undefined, as.issuer))\n        .then(validateAudience.bind(undefined, client.client_id));\n    const result = new URLSearchParams();\n    for (const [key, value] of Object.entries(claims)) {\n        if (typeof value === 'string' && key !== 'aud') {\n            result.set(key, value);\n        }\n    }\n    return validateAuthResponse(as, client, result, expectedState);\n}\nasync function idTokenHash(alg, data, key) {\n    let algorithm;\n    switch (alg) {\n        case 'RS256':\n        case 'PS256':\n        case 'ES256':\n            algorithm = 'SHA-256';\n            break;\n        case 'RS384':\n        case 'PS384':\n        case 'ES384':\n            algorithm = 'SHA-384';\n            break;\n        case 'RS512':\n        case 'PS512':\n        case 'ES512':\n            algorithm = 'SHA-512';\n            break;\n        case 'EdDSA':\n            if (key.algorithm.name === 'Ed25519') {\n                algorithm = 'SHA-512';\n                break;\n            }\n            throw new UnsupportedOperationError();\n        default:\n            throw new UnsupportedOperationError();\n    }\n    const digest = await crypto.subtle.digest(algorithm, buf(data));\n    return b64u(digest.slice(0, digest.byteLength / 2));\n}\nasync function idTokenHashMatches(data, actual, alg, key) {\n    const expected = await idTokenHash(alg, data, key);\n    return actual === expected;\n}\nexport async function validateDetachedSignatureResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        if (!parameters.hash.length) {\n            throw new TypeError('\"parameters\" as an instance of URL must contain a hash (fragment) with the Authorization Response parameters');\n        }\n        parameters = new URLSearchParams(parameters.hash.slice(1));\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw new TypeError('\"parameters\" must be an instance of URLSearchParams');\n    }\n    parameters = new URLSearchParams(parameters);\n    const id_token = getURLSearchParameter(parameters, 'id_token');\n    parameters.delete('id_token');\n    switch (expectedState) {\n        case undefined:\n        case expectNoState:\n            break;\n        default:\n            if (!validateString(expectedState)) {\n                throw new TypeError('\"expectedState\" must be a non-empty string');\n            }\n    }\n    const result = validateAuthResponse({\n        ...as,\n        authorization_response_iss_parameter_supported: false,\n    }, client, parameters, expectedState);\n    if (isOAuth2Error(result)) {\n        return result;\n    }\n    if (!id_token) {\n        throw new OPE('\"parameters\" does not contain an ID Token');\n    }\n    const code = getURLSearchParameter(parameters, 'code');\n    if (!code) {\n        throw new OPE('\"parameters\" does not contain an Authorization Code');\n    }\n    if (typeof as.jwks_uri !== 'string') {\n        throw new TypeError('\"as.jwks_uri\" must be a string');\n    }\n    const requiredClaims = [\n        'aud',\n        'exp',\n        'iat',\n        'iss',\n        'sub',\n        'nonce',\n        'c_hash',\n    ];\n    if (typeof expectedState === 'string') {\n        requiredClaims.push('s_hash');\n    }\n    const { claims, header, key } = await validateJwt(id_token, checkSigningAlgorithm.bind(undefined, client.id_token_signed_response_alg, as.id_token_signing_alg_values_supported), getPublicSigKeyFromIssuerJwksUri.bind(undefined, as, options), getClockSkew(client), getClockTolerance(client))\n        .then(validatePresence.bind(undefined, requiredClaims))\n        .then(validateIssuer.bind(undefined, as.issuer))\n        .then(validateAudience.bind(undefined, client.client_id));\n    const clockSkew = getClockSkew(client);\n    const now = epochTime() + clockSkew;\n    if (claims.iat < now - 3600) {\n        throw new OPE('unexpected JWT \"iat\" (issued at) claim value, it is too far in the past');\n    }\n    if (typeof claims.c_hash !== 'string' ||\n        (await idTokenHashMatches(code, claims.c_hash, header.alg, key)) !== true) {\n        throw new OPE('invalid ID Token \"c_hash\" (code hash) claim value');\n    }\n    if (claims.s_hash !== undefined && typeof expectedState !== 'string') {\n        throw new OPE('could not verify ID Token \"s_hash\" (state hash) claim value');\n    }\n    if (typeof expectedState === 'string' &&\n        (typeof claims.s_hash !== 'string' ||\n            (await idTokenHashMatches(expectedState, claims.s_hash, header.alg, key)) !== true)) {\n        throw new OPE('invalid ID Token \"s_hash\" (state hash) claim value');\n    }\n    if (client.require_auth_time !== undefined && typeof claims.auth_time !== 'number') {\n        throw new OPE('unexpected ID Token \"auth_time\" (authentication time) claim value');\n    }\n    maxAge ?? (maxAge = client.default_max_age ?? skipAuthTimeCheck);\n    if ((client.require_auth_time || maxAge !== skipAuthTimeCheck) &&\n        claims.auth_time === undefined) {\n        throw new OPE('ID Token \"auth_time\" (authentication time) claim missing');\n    }\n    if (maxAge !== skipAuthTimeCheck) {\n        if (typeof maxAge !== 'number' || maxAge < 0) {\n            throw new TypeError('\"options.max_age\" must be a non-negative number');\n        }\n        const now = epochTime() + getClockSkew(client);\n        const tolerance = getClockTolerance(client);\n        if (claims.auth_time + maxAge < now - tolerance) {\n            throw new OPE('too much time has elapsed since the last End-User authentication');\n        }\n    }\n    if (!validateString(expectedNonce)) {\n        throw new TypeError('\"expectedNonce\" must be a non-empty string');\n    }\n    if (claims.nonce !== expectedNonce) {\n        throw new OPE('unexpected ID Token \"nonce\" claim value');\n    }\n    if (Array.isArray(claims.aud) && claims.aud.length !== 1 && claims.azp !== client.client_id) {\n        throw new OPE('unexpected ID Token \"azp\" (authorized party) claim value');\n    }\n    return result;\n}\nfunction checkSigningAlgorithm(client, issuer, header) {\n    if (client !== undefined) {\n        if (header.alg !== client) {\n            throw new OPE('unexpected JWT \"alg\" header parameter');\n        }\n        return;\n    }\n    if (Array.isArray(issuer)) {\n        if (!issuer.includes(header.alg)) {\n            throw new OPE('unexpected JWT \"alg\" header parameter');\n        }\n        return;\n    }\n    if (header.alg !== 'RS256') {\n        throw new OPE('unexpected JWT \"alg\" header parameter');\n    }\n}\nfunction getURLSearchParameter(parameters, name) {\n    const { 0: value, length } = parameters.getAll(name);\n    if (length > 1) {\n        throw new OPE(`\"${name}\" parameter must be provided only once`);\n    }\n    return value;\n}\nexport const skipStateCheck = Symbol();\nexport const expectNoState = Symbol();\nexport function validateAuthResponse(as, client, parameters, expectedState) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        parameters = parameters.searchParams;\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw new TypeError('\"parameters\" must be an instance of URLSearchParams, or URL');\n    }\n    if (getURLSearchParameter(parameters, 'response')) {\n        throw new OPE('\"parameters\" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()');\n    }\n    const iss = getURLSearchParameter(parameters, 'iss');\n    const state = getURLSearchParameter(parameters, 'state');\n    if (!iss && as.authorization_response_iss_parameter_supported) {\n        throw new OPE('response parameter \"iss\" (issuer) missing');\n    }\n    if (iss && iss !== as.issuer) {\n        throw new OPE('unexpected \"iss\" (issuer) response parameter value');\n    }\n    switch (expectedState) {\n        case undefined:\n        case expectNoState:\n            if (state !== undefined) {\n                throw new OPE('unexpected \"state\" response parameter encountered');\n            }\n            break;\n        case skipStateCheck:\n            break;\n        default:\n            if (!validateString(expectedState)) {\n                throw new OPE('\"expectedState\" must be a non-empty string');\n            }\n            if (state === undefined) {\n                throw new OPE('response parameter \"state\" missing');\n            }\n            if (state !== expectedState) {\n                throw new OPE('unexpected \"state\" response parameter value');\n            }\n    }\n    const error = getURLSearchParameter(parameters, 'error');\n    if (error) {\n        return {\n            error,\n            error_description: getURLSearchParameter(parameters, 'error_description'),\n            error_uri: getURLSearchParameter(parameters, 'error_uri'),\n        };\n    }\n    const id_token = getURLSearchParameter(parameters, 'id_token');\n    const token = getURLSearchParameter(parameters, 'token');\n    if (id_token !== undefined || token !== undefined) {\n        throw new UnsupportedOperationError('implicit and hybrid flows are not supported');\n    }\n    return brand(new URLSearchParams(parameters));\n}\nfunction algToSubtle(alg, crv) {\n    switch (alg) {\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            return { name: 'RSA-PSS', hash: `SHA-${alg.slice(-3)}` };\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            return { name: 'RSASSA-PKCS1-v1_5', hash: `SHA-${alg.slice(-3)}` };\n        case 'ES256':\n        case 'ES384':\n            return { name: 'ECDSA', namedCurve: `P-${alg.slice(-3)}` };\n        case 'ES512':\n            return { name: 'ECDSA', namedCurve: 'P-521' };\n        case 'EdDSA': {\n            switch (crv) {\n                case 'Ed25519':\n                case 'Ed448':\n                    return crv;\n                default:\n                    throw new UnsupportedOperationError();\n            }\n        }\n        default:\n            throw new UnsupportedOperationError();\n    }\n}\nasync function importJwk(alg, jwk) {\n    const { ext, key_ops, use, ...key } = jwk;\n    return crypto.subtle.importKey('jwk', key, algToSubtle(alg, jwk.crv), true, ['verify']);\n}\nexport async function deviceAuthorizationRequest(as, client, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'device_authorization_endpoint', options);\n    const body = new URLSearchParams(parameters);\n    body.set('client_id', client.client_id);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    return authenticatedRequest(as, client, 'POST', url, body, headers, options);\n}\nexport async function processDeviceAuthorizationResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Device Authorization Endpoint response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.device_code)) {\n        throw new OPE('\"response\" body \"device_code\" property must be a non-empty string');\n    }\n    if (!validateString(json.user_code)) {\n        throw new OPE('\"response\" body \"user_code\" property must be a non-empty string');\n    }\n    if (!validateString(json.verification_uri)) {\n        throw new OPE('\"response\" body \"verification_uri\" property must be a non-empty string');\n    }\n    if (typeof json.expires_in !== 'number' || json.expires_in <= 0) {\n        throw new OPE('\"response\" body \"expires_in\" property must be a positive number');\n    }\n    if (json.verification_uri_complete !== undefined &&\n        !validateString(json.verification_uri_complete)) {\n        throw new OPE('\"response\" body \"verification_uri_complete\" property must be a non-empty string');\n    }\n    if (json.interval !== undefined && (typeof json.interval !== 'number' || json.interval <= 0)) {\n        throw new OPE('\"response\" body \"interval\" property must be a positive number');\n    }\n    return json;\n}\nexport async function deviceCodeGrantRequest(as, client, deviceCode, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!validateString(deviceCode)) {\n        throw new TypeError('\"deviceCode\" must be a non-empty string');\n    }\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('device_code', deviceCode);\n    return tokenEndpointRequest(as, client, 'urn:ietf:params:oauth:grant-type:device_code', parameters, options);\n}\nexport async function processDeviceCodeResponse(as, client, response) {\n    return processGenericAccessTokenResponse(as, client, response);\n}\nexport async function generateKeyPair(alg, options) {\n    if (!validateString(alg)) {\n        throw new TypeError('\"alg\" must be a non-empty string');\n    }\n    const algorithm = algToSubtle(alg, alg === 'EdDSA' ? options?.crv ?? 'Ed25519' : undefined);\n    if (alg.startsWith('PS') || alg.startsWith('RS')) {\n        Object.assign(algorithm, {\n            modulusLength: options?.modulusLength ?? 2048,\n            publicExponent: new Uint8Array([0x01, 0x00, 0x01]),\n        });\n    }\n    return (crypto.subtle.generateKey(algorithm, options?.extractable ?? false, ['sign', 'verify']));\n}\nfunction normalizeHtu(htu) {\n    const url = new URL(htu);\n    url.search = '';\n    url.hash = '';\n    return url.href;\n}\nasync function validateDPoP(as, request, accessToken, accessTokenClaims, options) {\n    const header = request.headers.get('dpop');\n    if (header === null) {\n        throw new OPE('operation indicated DPoP use but the request has no DPoP HTTP Header');\n    }\n    if (request.headers.get('authorization')?.toLowerCase().startsWith('dpop ') === false) {\n        throw new OPE(`operation indicated DPoP use but the request's Authorization HTTP Header scheme is not DPoP`);\n    }\n    if (typeof accessTokenClaims.cnf?.jkt !== 'string') {\n        throw new OPE('operation indicated DPoP use but the JWT Access Token has no jkt confirmation claim');\n    }\n    const clockSkew = getClockSkew(options);\n    const proof = await validateJwt(header, checkSigningAlgorithm.bind(undefined, undefined, as?.dpop_signing_alg_values_supported || SUPPORTED_JWS_ALGS), async ({ jwk, alg }) => {\n        if (!jwk) {\n            throw new OPE('DPoP Proof is missing the jwk header parameter');\n        }\n        const key = await importJwk(alg, jwk);\n        if (key.type !== 'public') {\n            throw new OPE('DPoP Proof jwk header parameter must contain a public key');\n        }\n        return key;\n    }, clockSkew, getClockTolerance(options))\n        .then(checkJwtType.bind(undefined, 'dpop+jwt'))\n        .then(validatePresence.bind(undefined, ['iat', 'jti', 'ath', 'htm', 'htu']));\n    const now = epochTime() + clockSkew;\n    const diff = Math.abs(now - proof.claims.iat);\n    if (diff > 300) {\n        throw new OPE('DPoP Proof iat is not recent enough');\n    }\n    if (proof.claims.htm !== request.method) {\n        throw new OPE('DPoP Proof htm mismatch');\n    }\n    if (typeof proof.claims.htu !== 'string' ||\n        normalizeHtu(proof.claims.htu) !== normalizeHtu(request.url)) {\n        throw new OPE('DPoP Proof htu mismatch');\n    }\n    {\n        const expected = b64u(await crypto.subtle.digest('SHA-256', encoder.encode(accessToken)));\n        if (proof.claims.ath !== expected) {\n            throw new OPE('DPoP Proof ath mismatch');\n        }\n    }\n    {\n        let components;\n        switch (proof.header.jwk.kty) {\n            case 'EC':\n                components = {\n                    crv: proof.header.jwk.crv,\n                    kty: proof.header.jwk.kty,\n                    x: proof.header.jwk.x,\n                    y: proof.header.jwk.y,\n                };\n                break;\n            case 'OKP':\n                components = {\n                    crv: proof.header.jwk.crv,\n                    kty: proof.header.jwk.kty,\n                    x: proof.header.jwk.x,\n                };\n                break;\n            case 'RSA':\n                components = {\n                    e: proof.header.jwk.e,\n                    kty: proof.header.jwk.kty,\n                    n: proof.header.jwk.n,\n                };\n                break;\n            default:\n                throw new UnsupportedOperationError();\n        }\n        const expected = b64u(await crypto.subtle.digest('SHA-256', encoder.encode(JSON.stringify(components))));\n        if (accessTokenClaims.cnf.jkt !== expected) {\n            throw new OPE('JWT Access Token confirmation mismatch');\n        }\n    }\n}\nexport async function validateJwtAccessToken(as, request, expectedAudience, options) {\n    assertAs(as);\n    if (!looseInstanceOf(request, Request)) {\n        throw new TypeError('\"request\" must be an instance of Request');\n    }\n    if (!validateString(expectedAudience)) {\n        throw new OPE('\"expectedAudience\" must be a non-empty string');\n    }\n    const authorization = request.headers.get('authorization');\n    if (authorization === null) {\n        throw new OPE('\"request\" is missing an Authorization HTTP Header');\n    }\n    let { 0: scheme, 1: accessToken, length } = authorization.split(' ');\n    scheme = scheme.toLowerCase();\n    switch (scheme) {\n        case 'dpop':\n        case 'bearer':\n            break;\n        default:\n            throw new UnsupportedOperationError('unsupported Authorization HTTP Header scheme');\n    }\n    if (length !== 2) {\n        throw new OPE('invalid Authorization HTTP Header format');\n    }\n    const requiredClaims = [\n        'iss',\n        'exp',\n        'aud',\n        'sub',\n        'iat',\n        'jti',\n        'client_id',\n    ];\n    if (options?.requireDPoP || scheme === 'dpop' || request.headers.has('dpop')) {\n        requiredClaims.push('cnf');\n    }\n    const { claims } = await validateJwt(accessToken, checkSigningAlgorithm.bind(undefined, undefined, SUPPORTED_JWS_ALGS), getPublicSigKeyFromIssuerJwksUri.bind(undefined, as, options), getClockSkew(options), getClockTolerance(options))\n        .then(checkJwtType.bind(undefined, 'at+jwt'))\n        .then(validatePresence.bind(undefined, requiredClaims))\n        .then(validateIssuer.bind(undefined, as.issuer))\n        .then(validateAudience.bind(undefined, expectedAudience));\n    for (const claim of ['client_id', 'jti', 'sub']) {\n        if (typeof claims[claim] !== 'string') {\n            throw new OPE(`unexpected JWT \"${claim}\" claim type`);\n        }\n    }\n    if ('cnf' in claims) {\n        if (!isJsonObject(claims.cnf)) {\n            throw new OPE('unexpected JWT \"cnf\" (confirmation) claim value');\n        }\n        const { 0: cnf, length } = Object.keys(claims.cnf);\n        if (length) {\n            if (length !== 1) {\n                throw new UnsupportedOperationError('multiple confirmation claims are not supported');\n            }\n            if (cnf !== 'jkt') {\n                throw new UnsupportedOperationError('unsupported JWT Confirmation method');\n            }\n        }\n    }\n    if (options?.requireDPoP ||\n        scheme === 'dpop' ||\n        claims.cnf?.jkt !== undefined ||\n        request.headers.has('dpop')) {\n        await validateDPoP(as, request, accessToken, claims, options);\n    }\n    return claims;\n}\nexport const experimentalCustomFetch = customFetch;\nexport const experimental_customFetch = customFetch;\nexport const experimentalUseMtlsAlias = useMtlsAlias;\nexport const experimental_useMtlsAlias = useMtlsAlias;\nexport const experimental_validateDetachedSignatureResponse = validateDetachedSignatureResponse;\nexport const experimental_validateJwtAccessToken = validateJwtAccessToken;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAI;AACJ,IAAI,OAAO,cAAc,eAAe,CAAC,UAAU,WAAW,aAAa,cAAc,GAAG;AACxF,QAAM,OAAO;AACb,QAAM,UAAU;AAChB,eAAa,GAAG,IAAI,IAAI,OAAO;AACnC;AACA,SAAS,gBAAgB,OAAO,UAAU;AACtC,MAAI,SAAS,MAAM;AACf,WAAO;AAAA,EACX;AACA,MAAI;AACA,WAAQ,iBAAiB,YACrB,OAAO,eAAe,KAAK,EAAE,OAAO,WAAW,MAAM,SAAS,UAAU,OAAO,WAAW;AAAA,EAClG,QACM;AACF,WAAO;AAAA,EACX;AACJ;AACO,IAAM,YAAY,OAAO;AACzB,IAAM,iBAAiB,OAAO;AAC9B,IAAM,cAAc,OAAO;AAC3B,IAAM,eAAe,OAAO;AACnC,IAAM,UAAU,IAAI,YAAY;AAChC,IAAM,UAAU,IAAI,YAAY;AAChC,SAAS,IAAI,OAAO;AAChB,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO,QAAQ,OAAO,KAAK;AAAA,EAC/B;AACA,SAAO,QAAQ,OAAO,KAAK;AAC/B;AACA,IAAM,aAAa;AACnB,SAAS,gBAAgB,OAAO;AAC5B,MAAI,iBAAiB,aAAa;AAC9B,YAAQ,IAAI,WAAW,KAAK;AAAA,EAChC;AACA,QAAM,MAAM,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,YAAY,KAAK,YAAY;AACnD,QAAI,KAAK,OAAO,aAAa,MAAM,MAAM,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,CAAC;AAAA,EAC/E;AACA,SAAO,KAAK,IAAI,KAAK,EAAE,CAAC,EAAE,QAAQ,MAAM,EAAE,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG;AACtF;AACA,SAAS,gBAAgB,OAAO;AAC5B,MAAI;AACA,UAAM,SAAS,KAAK,MAAM,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG,EAAE,QAAQ,OAAO,EAAE,CAAC;AAClF,UAAM,QAAQ,IAAI,WAAW,OAAO,MAAM;AAC1C,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,YAAM,CAAC,IAAI,OAAO,WAAW,CAAC;AAAA,IAClC;AACA,WAAO;AAAA,EACX,SACO,OAAO;AACV,UAAM,IAAI,IAAI,qDAAqD,EAAE,MAAM,CAAC;AAAA,EAChF;AACJ;AACA,SAAS,KAAK,OAAO;AACjB,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO,gBAAgB,KAAK;AAAA,EAChC;AACA,SAAO,gBAAgB,KAAK;AAChC;AACA,IAAM,MAAN,MAAU;AAAA,EACN,YAAY,SAAS;AACjB,SAAK,QAAQ,oBAAI,IAAI;AACrB,SAAK,SAAS,oBAAI,IAAI;AACtB,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,IAAI,KAAK;AACL,QAAI,IAAI,KAAK,MAAM,IAAI,GAAG;AAC1B,QAAI,GAAG;AACH,aAAO;AAAA,IACX;AACA,QAAK,IAAI,KAAK,OAAO,IAAI,GAAG,GAAI;AAC5B,WAAK,OAAO,KAAK,CAAC;AAClB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,IAAI,KAAK;AACL,WAAO,KAAK,MAAM,IAAI,GAAG,KAAK,KAAK,OAAO,IAAI,GAAG;AAAA,EACrD;AAAA,EACA,IAAI,KAAK,OAAO;AACZ,QAAI,KAAK,MAAM,IAAI,GAAG,GAAG;AACrB,WAAK,MAAM,IAAI,KAAK,KAAK;AAAA,IAC7B,OACK;AACD,WAAK,OAAO,KAAK,KAAK;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,KAAK;AACR,QAAI,KAAK,MAAM,IAAI,GAAG,GAAG;AACrB,aAAO,KAAK,MAAM,OAAO,GAAG;AAAA,IAChC;AACA,QAAI,KAAK,OAAO,IAAI,GAAG,GAAG;AACtB,aAAO,KAAK,OAAO,OAAO,GAAG;AAAA,IACjC;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,KAAK,OAAO;AACf,SAAK,MAAM,IAAI,KAAK,KAAK;AACzB,QAAI,KAAK,MAAM,QAAQ,KAAK,SAAS;AACjC,WAAK,SAAS,KAAK;AACnB,WAAK,QAAQ,oBAAI,IAAI;AAAA,IACzB;AAAA,EACJ;AACJ;AACO,IAAM,4BAAN,cAAwC,MAAM;AAAA,EACjD,YAAY,SAAS;AACjB,UAAM,WAAW,yBAAyB;AAC1C,SAAK,OAAO,KAAK,YAAY;AAC7B,UAAM,oBAAoB,MAAM,KAAK,WAAW;AAAA,EACpD;AACJ;AACO,IAAM,2BAAN,cAAuC,MAAM;AAAA,EAChD,YAAY,SAAS,SAAS;AAC1B,UAAM,SAAS,OAAO;AACtB,SAAK,OAAO,KAAK,YAAY;AAC7B,UAAM,oBAAoB,MAAM,KAAK,WAAW;AAAA,EACpD;AACJ;AACA,IAAM,MAAM;AACZ,IAAM,aAAa,IAAI,IAAI,GAAG;AAC9B,SAAS,YAAY,KAAK;AACtB,SAAO,eAAe;AAC1B;AACA,SAAS,aAAa,KAAK;AACvB,SAAO,YAAY,GAAG,KAAK,IAAI,SAAS;AAC5C;AACA,SAAS,YAAY,KAAK;AACtB,SAAO,YAAY,GAAG,KAAK,IAAI,SAAS;AAC5C;AAaA,SAAS,iBAAiB,UAAU;AAChC,MAAI;AACA,UAAM,QAAQ,SAAS,QAAQ,IAAI,YAAY;AAC/C,QAAI,OAAO;AACP,iBAAW,IAAI,IAAI,IAAI,SAAS,GAAG,EAAE,QAAQ,KAAK;AAAA,IACtD;AAAA,EACJ,QACM;AAAA,EAAE;AACR,SAAO;AACX;AAIA,SAAS,aAAa,OAAO;AACzB,MAAI,UAAU,QAAQ,OAAO,UAAU,YAAY,MAAM,QAAQ,KAAK,GAAG;AACrE,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,eAAe,OAAO;AAC3B,MAAI,gBAAgB,OAAO,OAAO,GAAG;AACjC,YAAQ,OAAO,YAAY,MAAM,QAAQ,CAAC;AAAA,EAC9C;AACA,QAAM,UAAU,IAAI,QAAQ,KAAK;AACjC,MAAI,cAAc,CAAC,QAAQ,IAAI,YAAY,GAAG;AAC1C,YAAQ,IAAI,cAAc,UAAU;AAAA,EACxC;AACA,MAAI,QAAQ,IAAI,eAAe,GAAG;AAC9B,UAAM,IAAI,UAAU,oEAAoE;AAAA,EAC5F;AACA,MAAI,QAAQ,IAAI,MAAM,GAAG;AACrB,UAAM,IAAI,UAAU,2DAA2D;AAAA,EACnF;AACA,SAAO;AACX;AACA,SAAS,OAAO,OAAO;AACnB,MAAI,OAAO,UAAU,YAAY;AAC7B,YAAQ,MAAM;AAAA,EAClB;AACA,MAAI,EAAE,iBAAiB,cAAc;AACjC,UAAM,IAAI,UAAU,+DAA+D;AAAA,EACvF;AACA,SAAO;AACX;AAkCA,SAAS,eAAe,OAAO;AAC3B,SAAO,OAAO,UAAU,YAAY,MAAM,WAAW;AACzD;AA8BA,SAAS,cAAc;AACnB,SAAO,KAAK,OAAO,gBAAgB,IAAI,WAAW,EAAE,CAAC,CAAC;AAC1D;AAgBA,SAAS,aAAa,OAAO;AACzB,MAAI,iBAAiB,WAAW;AAC5B,WAAO,EAAE,KAAK,MAAM;AAAA,EACxB;AACA,MAAI,EAAE,OAAO,eAAe,YAAY;AACpC,WAAO,CAAC;AAAA,EACZ;AACA,MAAI,MAAM,QAAQ,UAAa,CAAC,eAAe,MAAM,GAAG,GAAG;AACvD,UAAM,IAAI,UAAU,kCAAkC;AAAA,EAC1D;AACA,SAAO,EAAE,KAAK,MAAM,KAAK,KAAK,MAAM,IAAI;AAC5C;AACA,SAAS,cAAc,OAAO;AAC1B,SAAO,mBAAmB,KAAK,EAAE,QAAQ,QAAQ,GAAG;AACxD;AACA,SAAS,kBAAkB,UAAU,cAAc;AAC/C,QAAM,WAAW,cAAc,QAAQ;AACvC,QAAM,WAAW,cAAc,YAAY;AAC3C,QAAM,cAAc,KAAK,GAAG,QAAQ,IAAI,QAAQ,EAAE;AAClD,SAAO,SAAS,WAAW;AAC/B;AACA,SAAS,MAAM,KAAK;AAChB,UAAQ,IAAI,UAAU,KAAK,MAAM;AAAA,IAC7B,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,0BAA0B,6CAA6C;AAAA,EACzF;AACJ;AACA,SAAS,MAAM,KAAK;AAChB,UAAQ,IAAI,UAAU,KAAK,MAAM;AAAA,IAC7B,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,0BAA0B,6CAA6C;AAAA,EACzF;AACJ;AACA,SAAS,MAAM,KAAK;AAChB,UAAQ,IAAI,UAAU,YAAY;AAAA,IAC9B,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,0BAA0B,uCAAuC;AAAA,EACnF;AACJ;AACA,SAAS,SAAS,KAAK;AACnB,UAAQ,IAAI,UAAU,MAAM;AAAA,IACxB,KAAK;AACD,aAAO,MAAM,GAAG;AAAA,IACpB,KAAK;AACD,aAAO,MAAM,GAAG;AAAA,IACpB,KAAK;AACD,aAAO,MAAM,GAAG;AAAA,IACpB,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,0BAA0B,sCAAsC;AAAA,EAClF;AACJ;AACA,SAAS,aAAa,QAAQ;AAC1B,QAAM,OAAO,SAAS,SAAS;AAC/B,SAAO,OAAO,SAAS,YAAY,OAAO,SAAS,IAAI,IAAI,OAAO;AACtE;AACA,SAAS,kBAAkB,QAAQ;AAC/B,QAAM,YAAY,SAAS,cAAc;AACzC,SAAO,OAAO,cAAc,YAAY,OAAO,SAAS,SAAS,KAAK,KAAK,KAAK,SAAS,MAAM,KACzF,YACA;AACV;AACA,SAAS,YAAY;AACjB,SAAO,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AACvC;AACA,SAAS,gBAAgB,IAAI,QAAQ;AACjC,QAAM,MAAM,UAAU,IAAI,aAAa,MAAM;AAC7C,SAAO;AAAA,IACH,KAAK,YAAY;AAAA,IACjB,KAAK,CAAC,GAAG,QAAQ,GAAG,cAAc;AAAA,IAClC,KAAK,MAAM;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,OAAO;AAAA,IACZ,KAAK,OAAO;AAAA,EAChB;AACJ;AACA,eAAe,cAAc,IAAI,QAAQ,KAAK,KAAK;AAC/C,SAAO,IAAI;AAAA,IACP,KAAK,SAAS,GAAG;AAAA,IACjB;AAAA,EACJ,GAAG,gBAAgB,IAAI,MAAM,GAAG,GAAG;AACvC;AACA,SAAS,SAAS,IAAI;AAClB,MAAI,OAAO,OAAO,YAAY,OAAO,MAAM;AACvC,UAAM,IAAI,UAAU,wBAAwB;AAAA,EAChD;AACA,MAAI,CAAC,eAAe,GAAG,MAAM,GAAG;AAC5B,UAAM,IAAI,UAAU,iDAAiD;AAAA,EACzE;AACA,SAAO;AACX;AACA,SAAS,aAAa,QAAQ;AAC1B,MAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AAC/C,UAAM,IAAI,UAAU,4BAA4B;AAAA,EACpD;AACA,MAAI,CAAC,eAAe,OAAO,SAAS,GAAG;AACnC,UAAM,IAAI,UAAU,wDAAwD;AAAA,EAChF;AACA,SAAO;AACX;AACA,SAAS,mBAAmB,cAAc;AACtC,MAAI,CAAC,eAAe,YAAY,GAAG;AAC/B,UAAM,IAAI,UAAU,4DAA4D;AAAA,EACpF;AACA,SAAO;AACX;AACA,SAAS,yBAAyB,kBAAkB,kBAAkB;AAClE,MAAI,qBAAqB,QAAW;AAChC,UAAM,IAAI,UAAU,iEAAiE,gBAAgB,wCAAwC;AAAA,EACjJ;AACJ;AACA,SAAS,qBAAqB,kBAAkB,cAAc;AAC1D,MAAI,iBAAiB,QAAW;AAC5B,UAAM,IAAI,UAAU,6DAA6D,gBAAgB,wCAAwC;AAAA,EAC7I;AACJ;AACA,eAAe,qBAAqB,IAAI,QAAQ,MAAM,SAAS,kBAAkB;AAC7E,OAAK,OAAO,eAAe;AAC3B,OAAK,OAAO,uBAAuB;AACnC,OAAK,OAAO,kBAAkB;AAC9B,UAAQ,OAAO,4BAA4B;AAAA,IACvC,KAAK;AAAA,IACL,KAAK,uBAAuB;AACxB,+BAAyB,uBAAuB,gBAAgB;AAChE,cAAQ,IAAI,iBAAiB,kBAAkB,OAAO,WAAW,mBAAmB,OAAO,aAAa,CAAC,CAAC;AAC1G;AAAA,IACJ;AAAA,IACA,KAAK,sBAAsB;AACvB,+BAAyB,sBAAsB,gBAAgB;AAC/D,WAAK,IAAI,aAAa,OAAO,SAAS;AACtC,WAAK,IAAI,iBAAiB,mBAAmB,OAAO,aAAa,CAAC;AAClE;AAAA,IACJ;AAAA,IACA,KAAK,mBAAmB;AACpB,2BAAqB,mBAAmB,OAAO,aAAa;AAC5D,UAAI,qBAAqB,QAAW;AAChC,cAAM,IAAI,UAAU,2GAA2G;AAAA,MACnI;AACA,YAAM,EAAE,KAAK,IAAI,IAAI,aAAa,gBAAgB;AAClD,UAAI,CAAC,aAAa,GAAG,GAAG;AACpB,cAAM,IAAI,UAAU,4DAA4D;AAAA,MACpF;AACA,WAAK,IAAI,aAAa,OAAO,SAAS;AACtC,WAAK,IAAI,yBAAyB,wDAAwD;AAC1F,WAAK,IAAI,oBAAoB,MAAM,cAAc,IAAI,QAAQ,KAAK,GAAG,CAAC;AACtE;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,QAAQ;AACT,2BAAqB,OAAO,4BAA4B,OAAO,aAAa;AAC5E,+BAAyB,OAAO,4BAA4B,gBAAgB;AAC5E,WAAK,IAAI,aAAa,OAAO,SAAS;AACtC;AAAA,IACJ;AAAA,IACA;AACI,YAAM,IAAI,0BAA0B,+CAA+C;AAAA,EAC3F;AACJ;AACA,eAAe,IAAI,QAAQ,WAAW,KAAK;AACvC,MAAI,CAAC,IAAI,OAAO,SAAS,MAAM,GAAG;AAC9B,UAAM,IAAI,UAAU,uFAAuF;AAAA,EAC/G;AACA,QAAM,QAAQ,GAAG,KAAK,IAAI,KAAK,UAAU,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,KAAK,UAAU,SAAS,CAAC,CAAC,CAAC;AAC1F,QAAM,YAAY,KAAK,MAAM,OAAO,OAAO,KAAK,YAAY,GAAG,GAAG,KAAK,IAAI,KAAK,CAAC,CAAC;AAClF,SAAO,GAAG,KAAK,IAAI,SAAS;AAChC;AAqEA,eAAe,aAAa,SAAS,SAAS,KAAK,KAAKA,YAAW,aAAa;AAC5E,QAAM,EAAE,YAAY,WAAW,QAAQ,WAAW,IAAI,IAAI,MAAM,EAAE,IAAI;AACtE,MAAI,CAAC,aAAa,UAAU,GAAG;AAC3B,UAAM,IAAI,UAAU,+CAA+C;AAAA,EACvE;AACA,MAAI,CAAC,YAAY,SAAS,GAAG;AACzB,UAAM,IAAI,UAAU,6CAA6C;AAAA,EACrE;AACA,MAAI,UAAU,UAAa,CAAC,eAAe,KAAK,GAAG;AAC/C,UAAM,IAAI,UAAU,sDAAsD;AAAA,EAC9E;AACA,MAAI,CAAC,UAAU,aAAa;AACxB,UAAM,IAAI,UAAU,2CAA2C;AAAA,EACnE;AACA,QAAM,MAAM,UAAU,IAAIA;AAC1B,QAAM,QAAQ,MAAM,IAAI;AAAA,IACpB,KAAK,SAAS,UAAU;AAAA,IACxB,KAAK;AAAA,IACL,KAAK,MAAM,UAAU,SAAS;AAAA,EAClC,GAAG;AAAA,IACC,KAAK;AAAA,IACL,KAAK,YAAY;AAAA,IACjB;AAAA,IACA;AAAA,IACA,KAAK,GAAG,IAAI,MAAM,GAAG,IAAI,QAAQ;AAAA,IACjC,KAAK,cAAc,KAAK,MAAM,OAAO,OAAO,OAAO,WAAW,IAAI,WAAW,CAAC,CAAC,IAAI;AAAA,EACvF,GAAG,UAAU;AACb,UAAQ,IAAI,QAAQ,KAAK;AAC7B;AACA,IAAI;AACJ,eAAe,qBAAqB,KAAK;AACrC,QAAM,EAAE,KAAK,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,MAAM,OAAO,OAAO,UAAU,OAAO,GAAG;AACzE,QAAM,MAAM,EAAE,KAAK,GAAG,GAAG,GAAG,GAAG,IAAI;AACnC,WAAS,IAAI,KAAK,GAAG;AACrB,SAAO;AACX;AACA,eAAe,UAAU,KAAK;AAC1B,eAAa,WAAW,oBAAI,QAAQ;AACpC,SAAO,SAAS,IAAI,GAAG,KAAK,qBAAqB,GAAG;AACxD;AACA,SAAS,iBAAiB,OAAO,UAAU,SAAS;AAChD,MAAI,OAAO,UAAU,UAAU;AAC3B,QAAI,UAAU,YAAY,GAAG;AACzB,YAAM,IAAI,UAAU,6BAA6B,QAAQ,oBAAoB;AAAA,IACjF;AACA,UAAM,IAAI,UAAU,OAAO,QAAQ,oBAAoB;AAAA,EAC3D;AACA,SAAO,IAAI,IAAI,KAAK;AACxB;AACA,SAAS,gBAAgB,IAAI,UAAU,SAAS;AAC5C,MAAI,UAAU,YAAY,KAAK,GAAG,yBAAyB,YAAY,GAAG,uBAAuB;AAC7F,WAAO,iBAAiB,GAAG,sBAAsB,QAAQ,GAAG,UAAU,OAAO;AAAA,EACjF;AACA,SAAO,iBAAiB,GAAG,QAAQ,GAAG,QAAQ;AAClD;AAcO,SAAS,cAAc,OAAO;AACjC,QAAM,QAAQ;AACd,MAAI,OAAO,UAAU,YAAY,MAAM,QAAQ,KAAK,KAAK,UAAU,MAAM;AACrE,WAAO;AAAA,EACX;AACA,SAAO,MAAM,UAAU;AAC3B;AAsNO,IAAM,mBAAmB,OAAO;AAoDvC,eAAe,qBAAqB,IAAI,QAAQ,QAAQ,KAAK,MAAM,SAAS,SAAS;AACjF,QAAM,qBAAqB,IAAI,QAAQ,MAAM,SAAS,SAAS,gBAAgB;AAC/E,UAAQ,IAAI,gBAAgB,iDAAiD;AAC7E,UAAQ,UAAU,WAAW,KAAK,OAAO,IAAI,MAAM;AAAA,IAC/C;AAAA,IACA,SAAS,OAAO,YAAY,QAAQ,QAAQ,CAAC;AAAA,IAC7C;AAAA,IACA,UAAU;AAAA,IACV,QAAQ,SAAS,SAAS,OAAO,QAAQ,MAAM,IAAI;AAAA,EACvD,CAAC,EAAE,KAAK,gBAAgB;AAC5B;AACA,eAAe,qBAAqB,IAAI,QAAQ,WAAW,YAAY,SAAS;AAC5E,QAAM,MAAM,gBAAgB,IAAI,kBAAkB,OAAO;AACzD,aAAW,IAAI,cAAc,SAAS;AACtC,QAAM,UAAU,eAAe,SAAS,OAAO;AAC/C,UAAQ,IAAI,UAAU,kBAAkB;AACxC,MAAI,SAAS,SAAS,QAAW;AAC7B,UAAM,aAAa,SAAS,QAAQ,MAAM,KAAK,QAAQ,aAAa,MAAM,CAAC;AAAA,EAC/E;AACA,SAAO,qBAAqB,IAAI,QAAQ,QAAQ,KAAK,YAAY,SAAS,OAAO;AACrF;AACA,eAAsB,yBAAyB,IAAI,QAAQ,cAAc,SAAS;AAC9E,WAAS,EAAE;AACX,eAAa,MAAM;AACnB,MAAI,CAAC,eAAe,YAAY,GAAG;AAC/B,UAAM,IAAI,UAAU,2CAA2C;AAAA,EACnE;AACA,QAAM,aAAa,IAAI,gBAAgB,SAAS,oBAAoB;AACpE,aAAW,IAAI,iBAAiB,YAAY;AAC5C,SAAO,qBAAqB,IAAI,QAAQ,iBAAiB,YAAY,OAAO;AAChF;AACA,IAAM,gBAAgB,oBAAI,QAAQ;AAWlC,eAAe,kCAAkC,IAAI,QAAQ,UAAU,gBAAgB,OAAO,qBAAqB,OAAO;AACtH,WAAS,EAAE;AACX,eAAa,MAAM;AACnB,MAAI,CAAC,gBAAgB,UAAU,QAAQ,GAAG;AACtC,UAAM,IAAI,UAAU,4CAA4C;AAAA,EACpE;AACA,MAAI,SAAS,WAAW,KAAK;AACzB,QAAI;AACJ,QAAK,MAAM,MAAM,qBAAqB,QAAQ,GAAI;AAC9C,aAAO;AAAA,IACX;AACA,UAAM,IAAI,IAAI,qDAAqD;AAAA,EACvE;AACA,yBAAuB,QAAQ;AAC/B,MAAI;AACJ,MAAI;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC/B,SACO,OAAO;AACV,UAAM,IAAI,IAAI,2CAA2C,EAAE,MAAM,CAAC;AAAA,EACtE;AACA,MAAI,CAAC,aAAa,IAAI,GAAG;AACrB,UAAM,IAAI,IAAI,4CAA4C;AAAA,EAC9D;AACA,MAAI,CAAC,eAAe,KAAK,YAAY,GAAG;AACpC,UAAM,IAAI,IAAI,oEAAoE;AAAA,EACtF;AACA,MAAI,CAAC,eAAe,KAAK,UAAU,GAAG;AAClC,UAAM,IAAI,IAAI,kEAAkE;AAAA,EACpF;AACA,OAAK,aAAa,KAAK,WAAW,YAAY;AAC9C,MAAI,KAAK,eAAe,UAAU,KAAK,eAAe,UAAU;AAC5D,UAAM,IAAI,0BAA0B,gCAAgC;AAAA,EACxE;AACA,MAAI,KAAK,eAAe,WACnB,OAAO,KAAK,eAAe,YAAY,KAAK,cAAc,IAAI;AAC/D,UAAM,IAAI,IAAI,iEAAiE;AAAA,EACnF;AACA,MAAI,CAAC,sBACD,KAAK,kBAAkB,UACvB,CAAC,eAAe,KAAK,aAAa,GAAG;AACrC,UAAM,IAAI,IAAI,qEAAqE;AAAA,EACvF;AACA,MAAI,KAAK,UAAU,UAAa,OAAO,KAAK,UAAU,UAAU;AAC5D,UAAM,IAAI,IAAI,mDAAmD;AAAA,EACrE;AACA,MAAI,CAAC,eAAe;AAChB,QAAI,KAAK,aAAa,UAAa,CAAC,eAAe,KAAK,QAAQ,GAAG;AAC/D,YAAM,IAAI,IAAI,gEAAgE;AAAA,IAClF;AACA,QAAI,KAAK,UAAU;AACf,YAAM,EAAE,OAAO,IAAI,MAAM,YAAY,KAAK,UAAU,sBAAsB,KAAK,QAAW,OAAO,8BAA8B,GAAG,qCAAqC,GAAG,kBAAkB,aAAa,MAAM,GAAG,kBAAkB,MAAM,CAAC,EACtO,KAAK,iBAAiB,KAAK,QAAW,CAAC,OAAO,OAAO,OAAO,OAAO,KAAK,CAAC,CAAC,EAC1E,KAAK,eAAe,KAAK,QAAW,GAAG,MAAM,CAAC,EAC9C,KAAK,iBAAiB,KAAK,QAAW,OAAO,SAAS,CAAC;AAC5D,UAAI,MAAM,QAAQ,OAAO,GAAG,KAAK,OAAO,IAAI,WAAW,KAAK,OAAO,QAAQ,OAAO,WAAW;AACzF,cAAM,IAAI,IAAI,0DAA0D;AAAA,MAC5E;AACA,UAAI,OAAO,qBAAqB,OAAO,OAAO,cAAc,UAAU;AAClE,cAAM,IAAI,IAAI,mEAAmE;AAAA,MACrF;AACA,oBAAc,IAAI,MAAM,MAAM;AAAA,IAClC;AAAA,EACJ;AACA,SAAO;AACX;AACA,eAAsB,4BAA4B,IAAI,QAAQ,UAAU;AACpE,SAAO,kCAAkC,IAAI,QAAQ,QAAQ;AACjE;AAOA,SAAS,iBAAiB,UAAU,QAAQ;AACxC,MAAI,MAAM,QAAQ,OAAO,OAAO,GAAG,GAAG;AAClC,QAAI,CAAC,OAAO,OAAO,IAAI,SAAS,QAAQ,GAAG;AACvC,YAAM,IAAI,IAAI,6CAA6C;AAAA,IAC/D;AAAA,EACJ,WACS,OAAO,OAAO,QAAQ,UAAU;AACrC,UAAM,IAAI,IAAI,6CAA6C;AAAA,EAC/D;AACA,SAAO;AACX;AAOA,SAAS,eAAe,UAAU,QAAQ;AACtC,MAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,UAAM,IAAI,IAAI,2CAA2C;AAAA,EAC7D;AACA,SAAO;AACX;AACA,IAAM,UAAU,oBAAI,QAAQ;AAC5B,SAAS,MAAM,cAAc;AACzB,UAAQ,IAAI,YAAY;AACxB,SAAO;AACX;AACA,eAAsB,8BAA8B,IAAI,QAAQ,oBAAoB,aAAa,cAAc,SAAS;AACpH,WAAS,EAAE;AACX,eAAa,MAAM;AACnB,MAAI,CAAC,QAAQ,IAAI,kBAAkB,GAAG;AAClC,UAAM,IAAI,UAAU,mIAAmI;AAAA,EAC3J;AACA,MAAI,CAAC,eAAe,WAAW,GAAG;AAC9B,UAAM,IAAI,UAAU,0CAA0C;AAAA,EAClE;AACA,MAAI,CAAC,eAAe,YAAY,GAAG;AAC/B,UAAM,IAAI,UAAU,2CAA2C;AAAA,EACnE;AACA,QAAM,OAAO,sBAAsB,oBAAoB,MAAM;AAC7D,MAAI,CAAC,MAAM;AACP,UAAM,IAAI,IAAI,+CAA+C;AAAA,EACjE;AACA,QAAM,aAAa,IAAI,gBAAgB,SAAS,oBAAoB;AACpE,aAAW,IAAI,gBAAgB,WAAW;AAC1C,aAAW,IAAI,iBAAiB,YAAY;AAC5C,aAAW,IAAI,QAAQ,IAAI;AAC3B,SAAO,qBAAqB,IAAI,QAAQ,sBAAsB,YAAY,OAAO;AACrF;AACA,IAAM,gBAAgB;AAAA,EAClB,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACT;AACA,SAAS,iBAAiB,UAAU,QAAQ;AACxC,aAAW,SAAS,UAAU;AAC1B,QAAI,OAAO,OAAO,KAAK,MAAM,QAAW;AACpC,YAAM,IAAI,IAAI,QAAQ,KAAK,MAAM,cAAc,KAAK,CAAC,iBAAiB;AAAA,IAC1E;AAAA,EACJ;AACA,SAAO;AACX;AACO,IAAM,gBAAgB,OAAO;AAC7B,IAAM,oBAAoB,OAAO;AA6CxC,eAAsB,uCAAuC,IAAI,QAAQ,UAAU;AAC/E,QAAM,SAAS,MAAM,kCAAkC,IAAI,QAAQ,UAAU,IAAI;AACjF,MAAI,cAAc,MAAM,GAAG;AACvB,WAAO;AAAA,EACX;AACA,MAAI,OAAO,aAAa,QAAW;AAC/B,QAAI,OAAO,OAAO,aAAa,YAAY,OAAO,SAAS,QAAQ;AAC/D,YAAM,IAAI,IAAI,mHAAmH;AAAA,IACrI;AACA,WAAO,OAAO;AAAA,EAClB;AACA,SAAO;AACX;AA6CA,SAAS,uBAAuB,UAAU;AACtC,MAAI,SAAS,UAAU;AACnB,UAAM,IAAI,UAAU,uCAAuC;AAAA,EAC/D;AACJ;AAqGA,eAAe,qBAAqB,UAAU;AAC1C,MAAI,SAAS,SAAS,OAAO,SAAS,SAAS,KAAK;AAChD,2BAAuB,QAAQ;AAC/B,QAAI;AACA,YAAM,OAAO,MAAM,SAAS,KAAK;AACjC,UAAI,aAAa,IAAI,KAAK,OAAO,KAAK,UAAU,YAAY,KAAK,MAAM,QAAQ;AAC3E,YAAI,KAAK,sBAAsB,UAAa,OAAO,KAAK,sBAAsB,UAAU;AACpF,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI,KAAK,cAAc,UAAa,OAAO,KAAK,cAAc,UAAU;AACpE,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI,KAAK,SAAS,UAAa,OAAO,KAAK,SAAS,UAAU;AAC1D,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI,KAAK,UAAU,UAAa,OAAO,KAAK,UAAU,UAAU;AAC5D,iBAAO,KAAK;AAAA,QAChB;AACA,eAAO;AAAA,MACX;AAAA,IACJ,QACM;AAAA,IAAE;AAAA,EACZ;AACA,SAAO;AACX;AAOA,SAAS,qBAAqB,WAAW;AACrC,MAAI,OAAO,UAAU,kBAAkB,YAAY,UAAU,gBAAgB,MAAM;AAC/E,UAAM,IAAI,IAAI,GAAG,UAAU,IAAI,2CAA2C;AAAA,EAC9E;AACJ;AACA,SAAS,cAAc,YAAY;AAC/B,UAAQ,YAAY;AAAA,IAChB,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,0BAA0B;AAAA,EAC5C;AACJ;AACA,SAAS,YAAY,KAAK;AACtB,UAAQ,IAAI,UAAU,MAAM;AAAA,IACxB,KAAK;AACD,aAAO;AAAA,QACH,MAAM,IAAI,UAAU;AAAA,QACpB,MAAM,cAAc,IAAI,UAAU,UAAU;AAAA,MAChD;AAAA,IACJ,KAAK,WAAW;AACZ,2BAAqB,IAAI,SAAS;AAClC,cAAQ,IAAI,UAAU,KAAK,MAAM;AAAA,QAC7B,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD,iBAAO;AAAA,YACH,MAAM,IAAI,UAAU;AAAA,YACpB,YAAY,SAAS,IAAI,UAAU,KAAK,KAAK,MAAM,EAAE,GAAG,EAAE,KAAK;AAAA,UACnE;AAAA,QACJ;AACI,gBAAM,IAAI,0BAA0B;AAAA,MAC5C;AAAA,IACJ;AAAA,IACA,KAAK;AACD,2BAAqB,IAAI,SAAS;AAClC,aAAO,IAAI,UAAU;AAAA,IACzB,KAAK;AAAA,IACL,KAAK;AACD,aAAO,IAAI,UAAU;AAAA,EAC7B;AACA,QAAM,IAAI,0BAA0B;AACxC;AACA,IAAM,mBAAmB,OAAO;AAChC,eAAe,YAAY,KAAK,UAAU,QAAQC,YAAWC,iBAAgB;AACzE,QAAM,EAAE,GAAG,iBAAiB,GAAG,SAAS,GAAG,kBAAkB,OAAO,IAAI,IAAI,MAAM,GAAG;AACrF,MAAI,WAAW,GAAG;AACd,UAAM,IAAI,0BAA0B,sCAAsC;AAAA,EAC9E;AACA,MAAI,WAAW,GAAG;AACd,UAAM,IAAI,IAAI,aAAa;AAAA,EAC/B;AACA,MAAI;AACJ,MAAI;AACA,aAAS,KAAK,MAAM,IAAI,KAAK,eAAe,CAAC,CAAC;AAAA,EAClD,SACO,OAAO;AACV,UAAM,IAAI,IAAI,6DAA6D,EAAE,MAAM,CAAC;AAAA,EACxF;AACA,MAAI,CAAC,aAAa,MAAM,GAAG;AACvB,UAAM,IAAI,IAAI,uCAAuC;AAAA,EACzD;AACA,WAAS,MAAM;AACf,MAAI,OAAO,SAAS,QAAW;AAC3B,UAAM,IAAI,IAAI,wCAAwC;AAAA,EAC1D;AACA,QAAM,YAAY,KAAK,gBAAgB;AACvC,MAAI;AACJ,MAAI,WAAW,kBAAkB;AAC7B,UAAM,MAAM,OAAO,MAAM;AACzB,UAAM,QAAQ,GAAG,eAAe,IAAI,OAAO;AAC3C,UAAM,WAAW,MAAM,OAAO,OAAO,OAAO,YAAY,GAAG,GAAG,KAAK,WAAW,IAAI,KAAK,CAAC;AACxF,QAAI,CAAC,UAAU;AACX,YAAM,IAAI,IAAI,mCAAmC;AAAA,IACrD;AAAA,EACJ;AACA,MAAI;AACJ,MAAI;AACA,aAAS,KAAK,MAAM,IAAI,KAAK,OAAO,CAAC,CAAC;AAAA,EAC1C,SACO,OAAO;AACV,UAAM,IAAI,IAAI,8DAA8D,EAAE,MAAM,CAAC;AAAA,EACzF;AACA,MAAI,CAAC,aAAa,MAAM,GAAG;AACvB,UAAM,IAAI,IAAI,wCAAwC;AAAA,EAC1D;AACA,QAAM,MAAM,UAAU,IAAID;AAC1B,MAAI,OAAO,QAAQ,QAAW;AAC1B,QAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,YAAM,IAAI,IAAI,mDAAmD;AAAA,IACrE;AACA,QAAI,OAAO,OAAO,MAAMC,iBAAgB;AACpC,YAAM,IAAI,IAAI,2EAA2E;AAAA,IAC7F;AAAA,EACJ;AACA,MAAI,OAAO,QAAQ,QAAW;AAC1B,QAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,YAAM,IAAI,IAAI,6CAA6C;AAAA,IAC/D;AAAA,EACJ;AACA,MAAI,OAAO,QAAQ,QAAW;AAC1B,QAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,YAAM,IAAI,IAAI,0CAA0C;AAAA,IAC5D;AAAA,EACJ;AACA,MAAI,OAAO,QAAQ,QAAW;AAC1B,QAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,YAAM,IAAI,IAAI,8CAA8C;AAAA,IAChE;AACA,QAAI,OAAO,MAAM,MAAMA,iBAAgB;AACnC,YAAM,IAAI,IAAI,qEAAqE;AAAA,IACvF;AAAA,EACJ;AACA,MAAI,OAAO,QAAQ,QAAW;AAC1B,QAAI,OAAO,OAAO,QAAQ,YAAY,CAAC,MAAM,QAAQ,OAAO,GAAG,GAAG;AAC9D,YAAM,IAAI,IAAI,4CAA4C;AAAA,IAC9D;AAAA,EACJ;AACA,SAAO,EAAE,QAAQ,QAAQ,WAAW,IAAI;AAC5C;AAsKA,SAAS,sBAAsB,QAAQ,QAAQ,QAAQ;AACnD,MAAI,WAAW,QAAW;AACtB,QAAI,OAAO,QAAQ,QAAQ;AACvB,YAAM,IAAI,IAAI,uCAAuC;AAAA,IACzD;AACA;AAAA,EACJ;AACA,MAAI,MAAM,QAAQ,MAAM,GAAG;AACvB,QAAI,CAAC,OAAO,SAAS,OAAO,GAAG,GAAG;AAC9B,YAAM,IAAI,IAAI,uCAAuC;AAAA,IACzD;AACA;AAAA,EACJ;AACA,MAAI,OAAO,QAAQ,SAAS;AACxB,UAAM,IAAI,IAAI,uCAAuC;AAAA,EACzD;AACJ;AACA,SAAS,sBAAsB,YAAY,MAAM;AAC7C,QAAM,EAAE,GAAG,OAAO,OAAO,IAAI,WAAW,OAAO,IAAI;AACnD,MAAI,SAAS,GAAG;AACZ,UAAM,IAAI,IAAI,IAAI,IAAI,wCAAwC;AAAA,EAClE;AACA,SAAO;AACX;AACO,IAAM,iBAAiB,OAAO;AAC9B,IAAM,gBAAgB,OAAO;AAC7B,SAAS,qBAAqB,IAAI,QAAQ,YAAY,eAAe;AACxE,WAAS,EAAE;AACX,eAAa,MAAM;AACnB,MAAI,sBAAsB,KAAK;AAC3B,iBAAa,WAAW;AAAA,EAC5B;AACA,MAAI,EAAE,sBAAsB,kBAAkB;AAC1C,UAAM,IAAI,UAAU,6DAA6D;AAAA,EACrF;AACA,MAAI,sBAAsB,YAAY,UAAU,GAAG;AAC/C,UAAM,IAAI,IAAI,wGAAwG;AAAA,EAC1H;AACA,QAAM,MAAM,sBAAsB,YAAY,KAAK;AACnD,QAAM,QAAQ,sBAAsB,YAAY,OAAO;AACvD,MAAI,CAAC,OAAO,GAAG,gDAAgD;AAC3D,UAAM,IAAI,IAAI,2CAA2C;AAAA,EAC7D;AACA,MAAI,OAAO,QAAQ,GAAG,QAAQ;AAC1B,UAAM,IAAI,IAAI,oDAAoD;AAAA,EACtE;AACA,UAAQ,eAAe;AAAA,IACnB,KAAK;AAAA,IACL,KAAK;AACD,UAAI,UAAU,QAAW;AACrB,cAAM,IAAI,IAAI,mDAAmD;AAAA,MACrE;AACA;AAAA,IACJ,KAAK;AACD;AAAA,IACJ;AACI,UAAI,CAAC,eAAe,aAAa,GAAG;AAChC,cAAM,IAAI,IAAI,4CAA4C;AAAA,MAC9D;AACA,UAAI,UAAU,QAAW;AACrB,cAAM,IAAI,IAAI,oCAAoC;AAAA,MACtD;AACA,UAAI,UAAU,eAAe;AACzB,cAAM,IAAI,IAAI,6CAA6C;AAAA,MAC/D;AAAA,EACR;AACA,QAAM,QAAQ,sBAAsB,YAAY,OAAO;AACvD,MAAI,OAAO;AACP,WAAO;AAAA,MACH;AAAA,MACA,mBAAmB,sBAAsB,YAAY,mBAAmB;AAAA,MACxE,WAAW,sBAAsB,YAAY,WAAW;AAAA,IAC5D;AAAA,EACJ;AACA,QAAM,WAAW,sBAAsB,YAAY,UAAU;AAC7D,QAAM,QAAQ,sBAAsB,YAAY,OAAO;AACvD,MAAI,aAAa,UAAa,UAAU,QAAW;AAC/C,UAAM,IAAI,0BAA0B,6CAA6C;AAAA,EACrF;AACA,SAAO,MAAM,IAAI,gBAAgB,UAAU,CAAC;AAChD;;;AD9nDA,0BAAwC;AACxC,sBAA2D;AAC3D,oBAA2C;AAC3C,oBAA8C;AAC9C,qBAA0B;AAC1B,kBAAyC;AAEzC,qBAAuD;AAEvD,sBAAqB;AACrB,qBAAuB;AACvB,qBAAyB;AA0BlB,IAAM,uBAAN,MAA2B;AAAA,EAChC,YAA4B,SAAiC;AAAjC;AAAA,EAE5B;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EAEA,YAAY;AACV,WAAO,KAAK,QAAQ,WAAW,IAAI;AAAA,EACrC;AAAA,EAEA,MAAa,sBAAsB,SAAkC,aAA6C;AAChH,UAAM,aAAa,OAAO,OAA4B;AACpD,UAAI;AACF,cAAM,GAAG;AACT,eAAO;AAAA,MACT,SAAS,GAAG;AACV,eAAO,GAAG,CAAC;AAAA,MACb;AAAA,IACF;AACA,UAAM,UAAU,MAAM,WAAW,YAAY;AAC3C,YAAM,MAAM,MAAM,MAAM,+BAA+B;AACvD,UAAI,CAAC,IAAI,IAAI;AACX,cAAM,IAAI,MAAM,GAAG,IAAI,MAAM,IAAI,IAAI,UAAU,KAAK,MAAM,IAAI,KAAK,CAAC,EAAE;AAAA,MACxE;AAAA,IACF,CAAC;AACD,UAAM,UAAU,YAAY,UAAa,gBAAgB,SAAY,MAAM,WAAW,YAAY;AAChG,YAAM,MAAM,MAAM,KAAK,uBAAuB,KAAK,CAAC,GAAG,SAAU,WAAW;AAC5E,UAAI,IAAI,WAAW,SAAS;AAC1B,cAAM,IAAI;AAAA,MACZ;AAAA,IACF,CAAC,IAAI;AACL,UAAM,iBAAiB,MAAM,WAAW,YAAY;AAClD,YAAM,MAAM,MAAM,MAAM,IAAI,IAAI,WAAW,KAAK,UAAU,CAAC,CAAC;AAC5D,UAAI,CAAC,IAAI,IAAI;AACX,cAAM,IAAI,MAAM,GAAG,IAAI,MAAM,IAAI,IAAI,UAAU,KAAK,MAAM,IAAI,KAAK,CAAC,EAAE;AAAA,MACxE;AAAA,IACF,CAAC;AACD,UAAM,gBAAgB,MAAM,WAAW,YAAY;AACjD,YAAM,MAAM,MAAM,MAAM,mCAAmC;AAC3D,UAAI,CAAC,IAAI,IAAI;AACX,cAAM,IAAI,MAAM,GAAG,IAAI,MAAM,IAAI,IAAI,UAAU,KAAK,MAAM,IAAI,KAAK,CAAC,EAAE;AAAA,MACxE;AAAA,IACF,CAAC;AACD,UAAM,cAAc,MAAM,WAAW,YAAY;AAC/C,YAAM,MAAM,MAAM,MAAM,mCAAmC;AAC3D,UAAI,CAAC,IAAI,IAAI;AACX,cAAM,IAAI,MAAM,GAAG,IAAI,MAAM,IAAI,IAAI,UAAU,KAAK,MAAM,IAAI,KAAK,CAAC,EAAE;AAAA,MACxE;AAAA,IACF,CAAC;AACD,WAAO;AAAA,MACL,qBAAqB,yBAAU,WAAW;AAAA,MAC1C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAgB,oBAAoB,OAAc,SAAkC,aAA6C;AAC/H,WAAO,IAAI,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,QAKb,KAAK;AAAA;AAAA,QAEL,KAAK,UAAU,MAAM,KAAK,sBAAsB,SAAS,WAAW,GAAG,MAAM,CAAC,CAAC;AAAA,OAChF,EAAE,MAAa,CAAC;AAAA,EACrB;AAAA,EAEA,MAAgB,cAAiB,IAAmC,SAAkC,aAAyD;AAC7J,UAAM,gBAAgB,MAAM,sBAAO;AAAA,MACjC;AAAA,MACA;AAAA,MACA,EAAE,sBAAsB,IAAK;AAAA,IAC/B;AAGA,QAAI,cAAc,WAAW,SAAS;AACpC,UAAI,yBAAU,aAAa,CAAC,yBAAU,UAAU,QAAQ;AACtD,cAAM,IAAI,MAAM,sMAAsM,EAAE,OAAO,cAAc,MAAM,CAAC;AAAA,MACtP;AACA,YAAM,MAAM,KAAK,oBAAoB,cAAc,OAAO,SAAS,WAAW;AAAA,IAChF;AACA,WAAO,cAAc;AAAA,EACvB;AAAA,EAEA,MAAgB,uBAA0B,IAAsB,SAAkC,aAAyD;AACzJ,WAAO,MAAM,KAAK,cAAc,YAAY,MAAM,sBAAO,kBAAkB,EAAE,GAAG,SAAS,WAAW;AAAA,EACtG;AAAA,EAEA,MAAa,oBAAoB,cAA4B;AAC3D,QAAI,EAAE,0BAA0B,KAAK,UAAU;AAE7C,YAAM,IAAI,MAAM,qJAAqJ;AAAA,IACvK;AAEA,UAAM,KAAK;AAAA,MACT,QAAQ,KAAK,QAAQ,WAAW;AAAA,MAChC,WAAW;AAAA,MACX,gBAAgB,KAAK,UAAU,IAAI;AAAA,IACrC;AACA,UAAM,SAAuB;AAAA,MAC3B,WAAW,KAAK;AAAA,MAChB,eAAe,KAAK,QAAQ;AAAA,MAC5B,4BAA4B;AAAA,IAC9B;AAEA,UAAM,cAAc,MAAM,KAAK;AAAA,MAC7B,YAAY,MAAY;AAAA,QACtB;AAAA,QACA;AAAA,QACA,aAAa;AAAA,MACf;AAAA,IACF;AACA,UAAM,WAAW,MAAM,KAAK,iBAAiB,WAAW;AAExD,QAAI,SAAS,WAAW,SAAS;AAC/B,YAAM,QAAQ,SAAS;AACvB,UAAI,gCAAY,kBAAkB,WAAW,KAAK,GAAG;AACnD,eAAO;AAAA,MACT;AACA,YAAM;AAAA,IACR;AAEA,QAAI,CAAC,SAAS,KAAK,IAAI;AACrB,YAAM,OAAO,MAAM,SAAS,KAAK,KAAK;AACtC,YAAM,IAAI,MAAM,yCAAyC,SAAS,MAAM,IAAI,IAAI,EAAE;AAAA,IACpF;AAEA,UAAM,SAAS,MAAY,4BAA4B,IAAI,QAAQ,SAAS,IAAI;AAChF,QAAU,cAAc,MAAM,GAAG;AAE/B,YAAM,IAAI,kCAAoB,eAAe,EAAE,OAAO,CAAC;AAAA,IACzD;AAEA,QAAI,CAAC,OAAO,cAAc;AACxB,YAAM,IAAI,kCAAoB,mEAAmE;AAAA,IACnG;AAEA,WAAO,IAAI,4BAAY,OAAO,YAAY;AAAA,EAC5C;AAAA,EAEA,MAAa,kBACX,MACA,gBACA,SACA,cAA6C,UAC7C;AACA,gBAAY,KAAK,cAAc;AAAA,MAC7B,cAAc;AAAA,IAChB,CAAC;AAGD,WAAO,MAAM,KAAK;AAAA,MAChB,MAAM,KAAK,uBAAuB,MAAM,gBAAgB,SAAU,WAAW;AAAA,MAC7E;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEO,cAAc,SAAgH;AACnI,UAAM,UAAU,IAAI,gCAAgB;AAAA,MAClC,4BAA4B,OAAO,iBAAiB,MAAM,KAAK,oBAAoB,YAAY;AAAA,MAC/F,GAAG;AAAA,IACL,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EAEA,MAAgB,oCACd,MACA,gBACA,kBACA,eASC;AACD,QAAI;AACF,aAAO,sBAAO,GAAG,MAAM,KAAK,kBAAkB,MAAM,gBAAgB,gBAAgB,CAAC;AAAA,IACvF,SAAS,GAAG;AACV,iBAAW,aAAa,eAAe;AACrC,YAAI,UAAU,WAAW,CAAC,GAAG;AAC3B,iBAAO,sBAAO,MAAM,CAAoB;AAAA,QAC1C;AAAA,MACF;AACA,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAc,uBACZ,MACA,SACA,SACA,aAME;AAIF,QAAI,WAAW,MAAM,QAAQ,4BAA4B,GAAM;AAE/D,QAAI,eAAe,yBAAyB,KAAK,UAAU,KAAK,QAAQ,sBAAsB;AAC9F,QAAI,gBAAgB,eAAe,MAAM,aAAa,4BAA4B,GAAM,IAAI;AAG5F,UAAM,KAAK,QAAQ,iBAAiB;AAEpC,QAAI,MAAM,KAAK,UAAU,IAAI;AAC7B,QAAI,IAAI,SAAS,GAAG,GAAG;AACrB,YAAM,IAAI,MAAM,GAAG,EAAE;AAAA,IACvB;AACA,UAAM,SAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAW1B,GAAI,mBAAmB,2BAAY,CAAC,IAAI;AAAA,QACtC,aAAa;AAAA,MACf;AAAA,MACA,GAAG;AAAA,MACH,SAAS;AAAA,QACP,iCAAiC;AAAA,QACjC,sBAAsB,KAAK;AAAA,QAC3B,uBAAuB;AAAA,QACvB,0BAA0B,KAAK,QAAQ;AAAA,QACvC,GAAI,WAAW;AAAA,UACb,wBAAwB,SAAS,YAAY;AAAA,QAC/C,IAAI,CAAC;AAAA,QACL,GAAI,UAAU,eAAe;AAAA,UAC3B,yBAAyB,SAAS,aAAa;AAAA,QACjD,IAAI,CAAC;AAAA,QACL,GAAI,0BAA0B,KAAK,UAAU;AAAA,UAC3C,kCAAkC,KAAK,QAAQ;AAAA,QACjD,IAAI,CAAC;AAAA,QACL,GAAI,gBAAgB;AAAA,UAClB,8BAA8B,cAAc,YAAY;AAAA,QAC1D,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QASL,4BAAwB,0CAA2B;AAAA;AAAA,QAEnD,8BAA8B;AAAA,QAC9B,GAAG,KAAK,QAAQ;AAAA,QAChB,GAAG,QAAQ;AAAA,MACb;AAAA;AAAA;AAAA;AAAA,MAIA,GAAI,mBAAmB,2BAAY,CAAC,IAAI;AAAA,QACtC,OAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI;AACJ,QAAI;AACF,eAAS,MAAM,MAAM,KAAK,MAAM;AAAA,IAClC,SAAS,GAAG;AACV,UAAI,aAAa,WAAW;AAE1B,YAAI,yBAAc,OAAO,UAAU,KAAoB,EAAE,YAAY;AACnE,iBAAO,sBAAO,MAAM,CAAC;AAAA,QACvB,OAAO;AACL,gBAAM,MAAM,KAAK,oBAAoB,GAAG,SAAS,WAAW;AAAA,QAC9D;AAAA,MACF;AACA,YAAM;AAAA,IACR;AAEA,UAAM,eAAe,MAAM,KAAK,iBAAiB,MAAM;AACvD,QAAI,aAAa,WAAW,SAAS;AAEnC,UAAI,gCAAY,mBAAmB,WAAW,aAAa,KAAK,GAAG;AACjE,YAAI,CAAC,UAAU;AACb,gBAAM,IAAI,kCAAoB,+DAA+D,EAAE,UAAU,aAAa,CAAC;AAAA,QACzH;AACA,gBAAQ,uBAAuB,SAAS,WAAW;AACnD,eAAO,sBAAO,MAAM,aAAa,KAAK;AAAA,MACxC;AAIA,UAAI,iBAAiB,gCAAY,wBAAwB,WAAW,aAAa,KAAK,KAAK,gCAAY,eAAe,WAAW,aAAa,KAAK,IAAI;AACrJ,YAAI,CAAC,eAAe;AAClB,gBAAM,IAAI,kCAAoB,2EAA2E,EAAE,eAAe,aAAa,CAAC;AAAA,QAC1I;AACA,qBAAa,uBAAuB,cAAc,WAAW;AAC7D,eAAO,sBAAO,MAAM,aAAa,KAAK;AAAA,MACxC;AAIA,YAAM,aAAa;AAAA,IACrB;AAGA,UAAM,MAAM,OAAO,OAAO,aAAa,MAAM;AAAA,MAC3C,YAAY;AAAA,IACd,CAAC;AACD,QAAI,IAAI,IAAI;AACV,aAAO,sBAAO,GAAG,GAAG;AAAA,IACtB,WAAW,IAAI,WAAW,KAAK;AAE7B,YAAM,aAAa,IAAI,QAAQ,IAAI,aAAa;AAChD,UAAI,eAAe,MAAM;AACvB,gBAAQ,IAAI,yCAAyC,GAAG,sBAAsB,UAAU,aAAa;AACrG,kBAAM,sBAAK,OAAO,UAAU,IAAI,GAAI;AACpC,eAAO,sBAAO,MAAM,IAAI,MAAM,gCAAgC,UAAU,UAAU,CAAC;AAAA,MACrF;AACA,cAAQ,IAAI,yCAAyC,GAAG,+CAA+C;AACvG,aAAO,sBAAO,MAAM,IAAI,MAAM,8CAA8C,CAAC;AAAA,IAC/E,OAAO;AACL,YAAM,QAAQ,MAAM,IAAI,KAAK;AAE7B,YAAM,WAAW,IAAI,kCAAoB,6BAA6B,GAAG,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,EAAE,SAAS,QAAQ,KAAK,KAAK,CAAC;AAEnI,UAAI,IAAI,WAAW,OAAO,MAAM,SAAS,wBAAwB,GAAG;AAGlE,eAAO,sBAAO,MAAM,QAAQ;AAAA,MAC9B;AAGA,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAc,iBAAiB,QAAyD;AACtF,QAAI,MAAM;AACV,QAAI,OAAO,QAAQ,IAAI,uBAAuB,GAAG;AAC/C,YAAM,eAAe,OAAO,OAAO,QAAQ,IAAI,uBAAuB,CAAC;AACvE,YAAM,IAAI,SAAS,OAAO,MAAM;AAAA,QAC9B,QAAQ;AAAA,QACR,YAAY,OAAO;AAAA,QACnB,SAAS,OAAO;AAAA,MAClB,CAAC;AAAA,IACH;AAGA,QAAI,IAAI,QAAQ,IAAI,qBAAqB,GAAG;AAC1C,YAAM,YAAY,MAAM,IAAI,KAAK;AACjC,UAAI,IAAI,QAAQ,IAAI,qBAAqB,MAAM,UAAU,MAAM;AAC7D,cAAM,IAAI,kCAAoB,sGAAsG;AAAA,MACtI;AACA,YAAM,QAAQ,+BAAW,SAAS,SAAS;AAC3C,aAAO,sBAAO,MAAM,KAAK;AAAA,IAC3B;AAEA,WAAO,sBAAO,GAAG,GAAG;AAAA,EACtB;AAAA,EAEA,MAAa,oBAAoB,SAAkE;AACjG,UAAM,MAAM,MAAM,KAAK,kBAAkB,0BAA0B;AAAA,MACjE,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,MACA,MAAM,KAAK,UAAU,OAAO;AAAA,IAC9B,GAAG,IAAI;AAEP,UAAM,IAAI,kCAAoB,MAAM,IAAI,KAAK,CAAC;AAAA,EAChD;AAAA,EAEA,MAAM,wBACJ,OACA,aACyD;AACzD,UAAM,MAAM,MAAM,KAAK;AAAA,MACrB;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB;AAAA,UACA,cAAc;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,MACA;AAAA,MACA,CAAC,gCAAY,YAAY;AAAA,IAC3B;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,sBAAO,MAAM,IAAI,KAAK;AAAA,IAC/B,OAAO;AACL,aAAO,sBAAO,GAAG,MAAS;AAAA,IAC5B;AAAA,EACF;AAAA,EAEA,MAAM,sBACJ,OACA,aACA,SAC0D;AAC1D,UAAM,MAAM,MAAM,KAAK;AAAA,MACrB;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB;AAAA,UACA,cAAc;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,MACA;AAAA,MACA,CAAC,gCAAY,oBAAoB;AAAA,IACnC;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,IAAI;AAAA,IACb;AAAA,EACF;AAAA,EAEA,MAAM,mBACJ,OACA,aAC8E;AAC9E,UAAM,MAAM,MAAM,KAAK;AAAA,MACrB;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB;AAAA,UACA,cAAc;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,MACA;AAAA,MACA,CAAC,gCAAY,yBAAyB;AAAA,IACxC;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,sBAAO,MAAM,IAAI,KAAK;AAAA,IAC/B,OAAO;AACL,aAAO,sBAAO,GAAG,MAAM,IAAI,KAAK,KAAK,CAAC;AAAA,IACxC;AAAA,EACF;AAAA,EAEA,MAAM,cACJ,SACkE;AAClE,UAAM,MAAM,MAAM,KAAK;AAAA,MACrB,oBAAoB,UAAU,oCAAoC;AAAA,MAClE;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,MAAM,QAAQ;AAAA,UACd,GAAI,cAAc,UAAU,EAAE,UAAU,QAAQ,SAAS,IAAI,CAAC;AAAA,QAChE,CAAC;AAAA,MACH;AAAA,MACA;AAAA,MACA,CAAC,gCAAY,qBAAqB;AAAA,IACpC;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,sBAAO,MAAM,IAAI,KAAK;AAAA,IAC/B,OAAO;AACL,aAAO,sBAAO,GAAG,MAAS;AAAA,IAC5B;AAAA,EACF;AAAA,EAEA,MAAM,eACJ,SACA,SAC8G;AAC9G,UAAM,MAAM,MAAM,KAAK;AAAA,MACrB;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,cAAc,QAAQ;AAAA,UACtB,cAAc,QAAQ;AAAA,QACxB,CAAC;AAAA,MACH;AAAA,MACA;AAAA,MACA,CAAC,gCAAY,8BAA8B,gCAAY,0BAA0B;AAAA,IACnF;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,IAAI;AAAA,IACb;AAAA,EACF;AAAA,EAEA,MAAM,YACJ,SACA,SACgE;AAChE,UAAM,MAAM,MAAM,KAAK;AAAA,MACrB;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,OAAO;AAAA,MAC9B;AAAA,MACA;AAAA,MACA,CAAC,gCAAY,0BAA0B;AAAA,IACzC;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,IAAI;AAAA,IACb;AAAA,EACF;AAAA,EAEA,MAAM,wBAAwB,MAAgF;AAC5G,UAAM,MAAM,MAAM,KAAK,cAAc,EAAE,MAAM,gBAAgB,KAAK,CAAC;AACnE,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,sBAAO,MAAM,IAAI,KAAK;AAAA,IAC/B,OAAO;AACL,aAAO,sBAAO,GAAG,MAAS;AAAA,IAC5B;AAAA,EACF;AAAA,EAEA,MAAM,YAAY,MAAgF;AAChG,UAAM,MAAM,MAAM,KAAK;AAAA,MACrB;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA;AAAA,MACA,CAAC,gCAAY,qBAAqB;AAAA,IACpC;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,sBAAO,MAAM,IAAI,KAAK;AAAA,IAC/B,OAAO;AACL,aAAO,sBAAO,GAAG,MAAS;AAAA,IAC5B;AAAA,EACF;AAAA,EAEA,MAAM,4BACJ,SACA,SACwG;AACxG,UAAM,MAAM,MAAM,KAAK;AAAA,MACrB;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,OAAO;AAAA,MAC9B;AAAA,MACA;AAAA,MACA,CAAC;AAAA,IACH;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,sBAAO,MAAM,IAAI,KAAK;AAAA,IAC/B;AAEA,WAAO,sBAAO,GAAG,MAAM,IAAI,KAAK,KAAK,CAAC;AAAA,EACxC;AAAA,EAEA,MAAM,gBACJ,SACA,SACsE;AACtE,UAAM,MAAM,MAAM,KAAK;AAAA,MACrB;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,OAAO;AAAA,MAC9B;AAAA,MACA;AAAA,MACA,CAAC,gCAAY,yBAAyB;AAAA,IACxC;AACA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,sBAAO,MAAM,IAAI,KAAK;AAAA,IAC/B;AACA,WAAO,sBAAO,GAAG,MAAS;AAAA,EAC5B;AAAA,EAEA,MAAM,8BACJ,SAEA,SACuG;AACvG,UAAM,MAAM,MAAM,KAAK;AAAA,MACrB;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,OAAO;AAAA,MAC9B;AAAA,MACA;AAAA,MACA,CAAC;AAAA,IACH;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,sBAAO,MAAM,IAAI,KAAK;AAAA,IAC/B;AAEA,WAAO,sBAAO,GAAG,MAAM,IAAI,KAAK,KAAK,CAAC;AAAA,EACxC;AAAA,EAEA,MAAM,mBAAmB,SAKP;AAChB,UAAM,KAAK;AAAA,MACT;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,OAAO,QAAQ;AAAA,UACf,SAAS,QAAQ;AAAA,UACjB,cAAc,QAAQ;AAAA,QACxB,CAAC;AAAA,MACH;AAAA,MACA,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EAEA,MAAM,qBAA4D,SAIyD;AACzH,UAAM,MAAM,MAAM,KAAK;AAAA,MACrB,QAAQ,SAAS,UACf,wCACA,QAAQ,SAAS,YACf,qCACA;AAAA,MACJ;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,MAAM,QAAQ;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,MACA,QAAQ;AAAA,MACR,CAAC,gCAAY,qBAAqB;AAAA,IACpC;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,sBAAO,MAAM,IAAI,KAAK;AAAA,IAC/B,OAAO;AACL,aAAO,sBAAO,GAAG,MAAM,IAAI,KAAK,KAAK,CAAC;AAAA,IACxC;AAAA,EACF;AAAA,EAEA,MAAM,QACJ,aACA,MACA,SACA;AACA,UAAM,MAAM,MAAM,KAAK,kBAAkB,qBAAqB;AAAA,MAC5D,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,MACA,MAAM,KAAK,UAAU;AAAA,QACnB,MAAM;AAAA,QACN,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH,GAAG,OAAO;AAEV,UAAM,SAAS,MAAM,IAAI,KAAK;AAC9B,WAAO;AAAA,MACL,aAAa,OAAO;AAAA,MACpB,cAAc,OAAO;AAAA,MACrB,SAAS,OAAO;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,MAAM,qBACJ,OACA,UACA,SACsG;AACtG,UAAM,MAAM,MAAM,KAAK;AAAA,MACrB;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA;AAAA,MACA,CAAC,gCAAY,qBAAqB;AAAA,IACpC;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,sBAAO,MAAM,IAAI,KAAK;AAAA,IAC/B;AAEA,UAAM,SAAS,MAAM,IAAI,KAAK,KAAK;AACnC,WAAO,sBAAO,GAAG;AAAA,MACf,aAAa,OAAO;AAAA,MACpB,cAAc,OAAO;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,qBACJ,OACA,UACA,8BACA,SACuJ;AACvJ,UAAM,MAAM,MAAM,KAAK;AAAA,MACrB;AAAA,MACA;AAAA,QACE,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,QAAQ;AAAA,QACR,MAAM,KAAK,UAAU;AAAA,UACnB;AAAA,UACA;AAAA,UACA,2BAA2B;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,MACA;AAAA,MACA,CAAC,gCAAY,4BAA4B,gCAAY,0BAA0B;AAAA,IACjF;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,sBAAO,MAAM,IAAI,KAAK;AAAA,IAC/B;AAEA,UAAM,SAAS,MAAM,IAAI,KAAK,KAAK;AACnC,WAAO,sBAAO,GAAG;AAAA,MACf,aAAa,OAAO;AAAA,MACpB,cAAc,OAAO;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,kBAAkB,SAAiG;AACvH,UAAM,MAAM,MAAM,KAAK;AAAA,MACrB;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,MACA,CAAC;AAAA,IACH;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,sBAAO,MAAM,IAAI,KAAK;AAAA,IAC/B;AAEA,UAAM,SAAS,MAAM,IAAI,KAAK,KAAK;AACnC,WAAO,sBAAO,GAAG;AAAA,MACf,aAAa,OAAO;AAAA,MACpB,cAAc,OAAO;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,oBAAoB,MAAsI;AAC9J,UAAM,MAAM,MAAM,KAAK;AAAA,MACrB;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA;AAAA,MACA,CAAC,gCAAY,qBAAqB;AAAA,IACpC;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,sBAAO,MAAM,IAAI,KAAK;AAAA,IAC/B;AAEA,UAAM,SAAS,MAAM,IAAI,KAAK,KAAK;AACnC,WAAO,sBAAO,GAAG;AAAA,MACf,aAAa,OAAO;AAAA,MACpB,cAAc,OAAO;AAAA,MACrB,SAAS,OAAO;AAAA,IAClB,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,kBAAkB,MAAwL;AAC9M,UAAM,MAAM,MAAM,KAAK;AAAA,MACrB;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,MACA,CAAC,gCAAY,2BAA2B;AAAA,IAC1C;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,sBAAO,MAAM,IAAI,KAAK;AAAA,IAC/B;AAEA,UAAM,SAAS,MAAM,IAAI,KAAK,KAAK;AACnC,WAAO,sBAAO,GAAG;AAAA,MACf,aAAa,OAAO;AAAA,MACpB,cAAc,OAAO;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,YACJ,SAUiB;AACjB,UAAM,qBAAqB,IAAI,IAAI,QAAQ,WAAW;AACtD,eAAW,OAAO,CAAC,QAAQ,OAAO,GAAG;AACnC,UAAI,mBAAmB,aAAa,IAAI,GAAG,GAAG;AAC5C,gBAAQ,KAAK,mCAAmC,MAAM,yEAAyE;AAAA,MACjI;AACA,yBAAmB,aAAa,OAAO,GAAG;AAAA,IAC5C;AAEA,QAAI,EAAE,0BAA0B,KAAK,UAAU;AAE7C,YAAM,IAAI,MAAM,0DAA0D;AAAA,IAC5E;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,UAAU,IAAI,2BAA2B,QAAQ,SAAS,YAAY,CAAC;AAChG,QAAI,aAAa,IAAI,aAAa,KAAK,SAAS;AAChD,QAAI,aAAa,IAAI,iBAAiB,KAAK,QAAQ,oBAAoB;AACvE,QAAI,aAAa,IAAI,gBAAgB,mBAAmB,SAAS,CAAC;AAClE,QAAI,aAAa,IAAI,SAAS,QAAQ;AACtC,QAAI,aAAa,IAAI,SAAS,QAAQ,KAAK;AAC3C,QAAI,aAAa,IAAI,cAAc,oBAAoB;AACvD,QAAI,aAAa,IAAI,kBAAkB,QAAQ,aAAa;AAC5D,QAAI,aAAa,IAAI,yBAAyB,MAAM;AACpD,QAAI,aAAa,IAAI,iBAAiB,MAAM;AAC5C,QAAI,aAAa,IAAI,QAAQ,QAAQ,IAAI;AACzC,QAAI,aAAa,IAAI,sBAAsB,QAAQ,gBAAgB;AAEnE,QAAI,QAAQ,0BAA0B;AACpC,UAAI,aAAa,IAAI,+BAA+B,QAAQ,wBAAwB;AAAA,IACtF;AAEA,QAAI,QAAQ,SAAS,QAAQ;AAC3B,YAAM,SAAS,MAAM,QAAQ,QAAQ,4BAA4B,GAAM;AACvE,UAAI,aAAa,IAAI,SAAS,QAAQ,YAAY,SAAS,EAAE;AAE7D,UAAI,QAAQ,eAAe;AACzB,YAAI,aAAa,IAAI,kBAAkB,QAAQ,aAAa;AAAA,MAC9D;AAAA,IACF;AAEA,WAAO,IAAI,SAAS;AAAA,EACtB;AAAA,EAEA,MAAM,kBAAkB,SAKwF;AAC9G,QAAI,EAAE,0BAA0B,KAAK,UAAU;AAE7C,YAAM,IAAI,MAAM,0DAA0D;AAAA,IAC5E;AACA,UAAM,KAAK;AAAA,MACT,QAAQ,KAAK,QAAQ,WAAW;AAAA,MAChC,WAAW;AAAA,MACX,gBAAgB,KAAK,UAAU,IAAI;AAAA,IACrC;AACA,UAAM,SAAuB;AAAA,MAC3B,WAAW,KAAK;AAAA,MAChB,eAAe,KAAK,QAAQ;AAAA,MAC5B,4BAA4B;AAAA,IAC9B;AACA,UAAM,SAAS,MAAM,KAAK;AAAA,MACxB,YAAkB,qBAAqB,IAAI,QAAQ,QAAQ,aAAa,QAAQ,KAAK;AAAA,IACvF;AACA,QAAU,cAAc,MAAM,GAAG;AAC/B,YAAM,IAAI,kCAAoB,yCAAyC,EAAE,OAAO,CAAC;AAAA,IACnF;AACA,UAAM,WAAW,MAAY;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAEA,UAAM,SAAS,MAAY,uCAAuC,IAAI,QAAQ,QAAQ;AACtF,QAAU,cAAc,MAAM,GAAG;AAC/B,UAAI,UAAU,UAAU,OAAO,SAAS,wCAAwC;AAC9E,cAAM,IAAI,gCAAY,kCAAmC,OAAe,QAAQ,YAAY;AAAA,MAC9F;AAEA,YAAM,IAAI,kCAAoB,wDAAwD,EAAE,OAAO,CAAC;AAAA,IAClG;AACA,WAAO;AAAA,MACL,SAAS,OAAO;AAAA,MAChB,0BAA0B,OAAO;AAAA,MACjC,aAAa,OAAO;AAAA,MACpB,cAAc,OAAO,qBAAiB,wBAAS,iDAAiD;AAAA,IAClG;AAAA,EACF;AAAA,EAEA,MAAM,QAAQ,SAAyC;AACrD,UAAM,WAAW,MAAM,QAAQ,4BAA4B,GAAM;AACjE,QAAI,UAAU;AACZ,YAAM,aAAa,MAAM,KAAK;AAAA,QAC5B;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,UACR,SAAS;AAAA,YACP,gBAAgB;AAAA,UAClB;AAAA,UACA,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,QACzB;AAAA,QACA;AAAA,QACA,CAAC,gCAAY,iBAAiB;AAAA,MAChC;AACA,UAAI,WAAW,WAAW,SAAS;AACjC,YAAI,gCAAY,kBAAkB,WAAW,WAAW,KAAK,GAAG;AAAA,QAEhE,OAAO;AAEL,gBAAM,IAAI,kCAAoB,oBAAoB,EAAE,OAAO,WAAW,MAAM,CAAC;AAAA,QAC/E;AAAA,MACF,OAAO;AAAA,MAEP;AAAA,IACF;AACA,YAAQ,YAAY;AAAA,EACtB;AAAA,EAEA,MAAM,qBAAqB,SAA6E;AACtG,UAAM,kBAAkB,MAAM,KAAK;AAAA,MACjC;AAAA,MACA,CAAC;AAAA,MACD;AAAA,MACA,CAAC,gCAAY,2BAA2B;AAAA,IAC1C;AACA,QAAI,gBAAgB,WAAW,SAAS;AACtC,UAAI,gCAAY,4BAA4B,WAAW,gBAAgB,KAAK,GAAG;AAC7E,eAAO;AAAA,MACT,OAAO;AACL,cAAM,IAAI,kCAAoB,6BAA6B,EAAE,OAAO,gBAAgB,MAAM,CAAC;AAAA,MAC7F;AAAA,IACF;AACA,UAAM,WAAW,gBAAgB;AACjC,UAAM,OAA0C,MAAM,SAAS,KAAK;AACpE,QAAI,CAAE,KAAc,OAAM,IAAI,kCAAoB,uDAAuD;AACzG,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,oBACJ,SAGA,SACiD;AACjD,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,uBAAuB,IAAI,gBAAgB,EAAE,SAAS,QAAQ,OAAO,CAAC;AAAA,MACtE,CAAC;AAAA,MACD;AAAA,IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;AAAA,EAChB;AAAA,EAEA,MAAM,qBACJ,cACA,QACA,SACA;AACA,UAAM,KAAK;AAAA,MACT,qBAAqB,YAAY,YAAY,MAAM;AAAA,MACnD,EAAE,QAAQ,SAAS;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,uBACJ,SAIA,SACqD;AACrD,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,2BAA2B,IAAI,oBAAgB,gCAAgB;AAAA,QAC7D,SAAS,QAAQ;AAAA,QACjB,SAAS,QAAQ;AAAA,MACnB,CAAC,CAAC;AAAA,MACF,CAAC;AAAA,MACD;AAAA,IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;AAAA,EAChB;AAAA,EAEA,MAAM,qBACJ,SAIA,SACmD;AACnD,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,yBAAyB,QAAQ,MAAM,IAAI,QAAQ,MAAM;AAAA,MACzD,CAAC;AAAA,MACD;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,UACJ,QACA,SACA;AACA,UAAM,KAAK;AAAA,MACT,qBAAqB,MAAM;AAAA,MAC3B;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,MACzB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,wBACJ,SAKA,SACA;AACA,UAAM,KAAK;AAAA,MACT,yBAAyB,QAAQ,MAAM,IAAI,QAAQ,MAAM;AAAA,MACzD;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,QAAQ,OAAO;AAAA,MACtC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,WACJ,SAIA,SACA;AACA,UAAM,KAAK;AAAA,MACT,UAAU,QAAQ,MAAM;AAAA,MACxB;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,QAAQ,IAAI;AAAA,MACnC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,+BACJ,SAIA,SACkD;AAClD,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,6BAA6B,QAAQ,MAAM,yBAAyB,QAAQ,SAAS;AAAA,MACrF,CAAC;AAAA,MACD;AAAA,IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;AAAA,EAChB;AAAA,EAEA,MAAM,kCACJ,SAGA,SACqD;AACrD,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,6CAA6C,QAAQ,SAAS;AAAA,MAC9D,CAAC;AAAA,MACD;AAAA,IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;AAAA,EAChB;AAAA,EAEA,MAAM,qBAAqB,SAAkE;AAC3F,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B;AAAA,MACA,CAAC;AAAA,MACD;AAAA,IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;AAAA,EAChB;AAAA,EAEA,MAAM,mBAA0G;AAC9G,UAAM,kBAAkB,MAAM,KAAK,oCAAoC,qBAAqB,CAAC,GAAG,MAAM,CAAC,gCAAY,eAAe,CAAC;AACnI,QAAI,gBAAgB,WAAW,SAAS;AACtC,aAAO,sBAAO,MAAM,gBAAgB,KAAK;AAAA,IAC3C;AACA,UAAM,WAAW,gBAAgB;AACjC,UAAM,UAAgD,MAAM,SAAS,KAAK;AAC1E,WAAO,sBAAO,GAAG,OAAO;AAAA,EAC1B;AAAA,EAEA,MAAM,iBAAiB,QAA6C,SAA0B;AAC5F,UAAM,KAAK;AAAA,MACT;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,MAAM;AAAA,MAC7B;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,aAAa,SAA8E;AAC/F,UAAM,WAAW,MAAM,KAAK,kBAAkB,sBAAsB,CAAC,GAAG,OAAO;AAC/E,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,IAAI,MAAM,8BAA8B,SAAS,SAAS,MAAO,MAAM,SAAS,KAAK,CAAE;AAAA,IAC/F;AAEA,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,MAAM,cACJ,SACA,SACkD;AAClD,UAAM,gBAAgB,MAAM,KAAK;AAAA,MAC/B;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,OAAO;AAAA,MAC9B;AAAA,MACA;AAAA,IACF;AACA,QAAI,CAAC,cAAc,IAAI;AACrB,YAAM,IAAI,MAAM,+BAA+B,cAAc,SAAS,MAAO,MAAM,cAAc,KAAK,CAAE;AAAA,IAC1G;AAEA,UAAM,OAAO,MAAM,cAAc,KAAK;AACtC,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,0BACJ,UACA,OACA,SAC4D;AAC5D,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,0BAA0B,QAAQ;AAAA,MAClC;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,EAAE,MAAM,CAAC;AAAA,MAChC;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,iBACJ,MACA,SACsC;AACtC,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,WACJ,QACA,SACA;AACA,UAAM,KAAK;AAAA,MACT,UAAU,MAAM;AAAA,MAChB;AAAA,QACE,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,kBAAkB,SAA0B;AAChD,UAAM,KAAK;AAAA,MACT;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,2BACJ,MACA,SACgD;AAChD,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,2BACJ,IACA,MACA,SACgD;AAChD,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,wBAAwB,EAAE;AAAA,MAC1B;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,2BACJ,IACA,SACe;AACf,UAAM,KAAK;AAAA,MACT,wBAAwB,EAAE;AAAA,MAC1B;AAAA,QACE,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,cACJ,WACA,SACe;AACf,UAAM,KAAK;AAAA,MACT,kBAAkB,SAAS;AAAA,MAC3B;AAAA,QACE,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,aACJ,SACyC;AACzC,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAGA,MAAM,0BACJ,SACkD;AAClD,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,IACF;AACA,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,MAAM,+CACJ,kBACA,aACA,SACiE;AACjE,UAAM,kBAAkB,MAAM,KAAK;AAAA,MACjC,wBAAwB,gBAAgB;AAAA,MACxC;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,EAAE,cAAc,YAAY,CAAC;AAAA,MACpD;AAAA,MACA;AAAA,MACA,CAAC,gCAAY,oBAAoB;AAAA,IACnC;AAEA,QAAI,gBAAgB,WAAW,SAAS;AACtC,aAAO,sBAAO,MAAM,gBAAgB,KAAK;AAAA,IAC3C;AACA,WAAO,sBAAO,GAAG,MAAS;AAAA,EAC5B;AAAA,EAEA,MAAM,SACJ,WACA,cACA,SACwD;AACxD,UAAM,kBAAkB,MAAM,KAAK;AAAA,MACjC;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,YAAY;AAAA,UACZ,eAAe;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,MACA;AAAA,MACA,CAAC,gCAAY,WAAW;AAAA,IAC1B;AAEA,QAAI,gBAAgB,WAAW,SAAS;AACtC,aAAO,sBAAO,MAAM,gBAAgB,KAAK;AAAA,IAC3C;AACA,WAAO,sBAAO,GAAG,MAAS;AAAA,EAC5B;AAAA,EAEA,MAAc,sBAAsB,SAA2D;AAC7F,QAAI,aAAa,WAAW,aAAa,SAAS;AAChD,YAAM,IAAI,kCAAoB,kEAAkE;AAAA,IAClG;AAEA,WAAO;AAAA,MACL,UAAU,aAAa,UAAU,mBAAmB;AAAA,MACpD,aAAa,IAAI,oBAAgB,sCAAsB,OAAO,CAAC;AAAA,IACjE;AAAA,EACF;AAAA,EAMA,MAAM,mBACJ,SACA,SACA,aACoF;AACpF,UAAM,eAAe,gBAAgB,WAAW,KAAK,oBAAqB,KAAa,mBAA4B,KAAK,IAAI;AAC5H,UAAM,EAAE,UAAU,YAAY,IAAI,MAAM,KAAK,sBAAsB,OAAO;AAE1E,UAAM,WAAW,MAAM;AAAA,MACrB,GAAG,QAAQ,IAAI,YAAY,SAAS,CAAC;AAAA,MACrC;AAAA,QACE,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,WAAO,KAAK;AAAA,EACd;AAAA,EAKA,MAAM,oBACJ,MACA,SACA,aACoH;AACpH,UAAM,eAAe,gBAAgB,WAAW,KAAK,oBAAqB,KAAa,mBAA4B,KAAK,IAAI;AAC5H,UAAM,EAAE,SAAS,IAAI,MAAM,KAAK,sBAAsB,IAAI;AAE1D,UAAM,WAAW,MAAM;AAAA,MACrB,GAAG,QAAQ;AAAA,MACX;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAKA,MAAM,iBACJ,SACA,OACA,SACA,aACgF;AAChF,UAAM,eAAe,gBAAgB,WAAW,KAAK,oBAAqB,KAAa,mBAA4B,KAAK,IAAI;AAC5H,UAAM,EAAE,UAAU,YAAY,IAAI,MAAM,KAAK,sBAAsB,OAAO;AAE1E,UAAM,WAAW,MAAM;AAAA,MACrB,GAAG,QAAQ,IAAI,KAAK,IAAI,YAAY,SAAS,CAAC;AAAA,MAC9C;AAAA,QACE,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAKA,MAAM,oBACJ,SACA,OACA,MACA,SACA,aACgF;AAChF,UAAM,eAAe,gBAAgB,WAAW,KAAK,oBAAqB,KAAa,mBAA4B,KAAK,IAAI;AAC5H,UAAM,EAAE,UAAU,YAAY,IAAI,MAAM,KAAK,sBAAsB,OAAO;AAE1E,UAAM,WAAW,MAAM;AAAA,MACrB,GAAG,QAAQ,IAAI,KAAK,IAAI,YAAY,SAAS,CAAC;AAAA,MAC9C;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAKA,MAAM,mBAAmB,MAAuB,QAAgB,SAAiC,aAAmI;AAClO,UAAM,eAAe,gBAAgB,WAAW,KAAK,sCAAuC,KAAa,qCAA8C,KAAK,IAAI;AAChK,UAAM,SAAS,MAAM;AAAA,MACnB,IAAI,IAAI;AAAA,MACR;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,EAAE,SAAS,OAAO,CAAC;AAAA,MAC1C;AAAA,MACA;AAAA,MACA,CAAC,gCAAY,cAAc;AAAA,IAC7B;AACA,QAAI,OAAO,WAAW,SAAS;AAC7B,aAAO;AAAA,IACT;AACA,WAAO,MAAM,OAAO,KAAK,KAAK;AAAA,EAChC;AACF;", "names": ["clockSkew", "clockSkew", "clockTolerance"]}