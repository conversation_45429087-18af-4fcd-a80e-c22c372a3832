{"version": 3, "sources": ["../../src/components/selected-team-switcher.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { runAsynchronouslyWithAlert } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport {\n  Button,\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n  Skeleton,\n  Typography\n} from \"@stackframe/stack-ui\";\nimport { PlusCircle, Settings } from \"lucide-react\";\nimport { Suspense, useEffect, useMemo } from \"react\";\nimport { Team, useStackApp, useUser } from \"..\";\nimport { useTranslation } from \"../lib/translations\";\nimport { TeamIcon } from \"./team-icon\";\n\ntype SelectedTeamSwitcherProps = {\n  urlMap?: (team: Team) => string,\n  selectedTeam?: Team,\n  noUpdateSelectedTeam?: boolean,\n};\n\nexport function SelectedTeamSwitcher(props: SelectedTeamSwitcherProps) {\n  return <Suspense fallback={<Fallback />}>\n    <Inner {...props} />\n  </Suspense>;\n}\n\nfunction Fallback() {\n  return <Skeleton className=\"h-9 w-full max-w-64 stack-scope\" />;\n}\n\nfunction Inner(props: SelectedTeamSwitcherProps) {\n  const { t } = useTranslation();\n  const app = useStackApp();\n  const user = useUser();\n  const project = app.useProject();\n  const navigate = app.useNavigate();\n  const selectedTeam = user?.selectedTeam || props.selectedTeam;\n  const rawTeams = user?.useTeams();\n  const teams = useMemo(() => rawTeams?.sort((a, b) => b.id === selectedTeam?.id ? 1 : -1), [rawTeams, selectedTeam]);\n\n  useEffect(() => {\n    if (!props.noUpdateSelectedTeam && props.selectedTeam) {\n      runAsynchronouslyWithAlert(user?.setSelectedTeam(props.selectedTeam));\n    }\n  }, [props.noUpdateSelectedTeam, props.selectedTeam]);\n\n  return (\n    <Select\n      value={selectedTeam?.id}\n      onValueChange={(value) => {\n        runAsynchronouslyWithAlert(async () => {\n          const team = teams?.find(team => team.id === value);\n          if (!team) {\n            throw new Error('Team not found, this should not happen');\n          }\n\n          if (!props.noUpdateSelectedTeam) {\n            await user?.setSelectedTeam(team);\n          }\n          if (props.urlMap) {\n            navigate(props.urlMap(team));\n          }\n        });\n      }}\n    >\n      <SelectTrigger className=\"stack-scope max-w-64\">\n        <SelectValue placeholder=\"Select team\"/>\n      </SelectTrigger>\n      <SelectContent className=\"stack-scope\">\n        {user?.selectedTeam ? <SelectGroup>\n          <SelectLabel>\n            <div className=\"flex items-center justify-between\">\n              <span>\n                {t('Current team')}\n              </span>\n              <Button variant='ghost' size='icon' className=\"h-6 w-6\" onClick={() => navigate(`${app.urls.accountSettings}#team-${user.selectedTeam?.id}`)}>\n                <Settings className=\"h-4 w-4\"/>\n              </Button>\n            </div>\n          </SelectLabel>\n          <SelectItem value={user.selectedTeam.id}>\n            <div className=\"flex items-center gap-2\">\n              <TeamIcon team={user.selectedTeam} />\n              <Typography className=\"max-w-40 truncate\">{user.selectedTeam.displayName}</Typography>\n            </div>\n          </SelectItem>\n        </SelectGroup> : undefined}\n\n        {teams?.length ?\n          <SelectGroup>\n            <SelectLabel>{t('Other teams')}</SelectLabel>\n            {teams.filter(team => team.id !== user?.selectedTeam?.id)\n              .map(team => (\n                <SelectItem value={team.id} key={team.id}>\n                  <div className=\"flex items-center gap-2\">\n                    <TeamIcon team={team} />\n                    <Typography className=\"max-w-64 truncate\">{team.displayName}</Typography>\n                  </div>\n                </SelectItem>\n              ))}\n          </SelectGroup> :\n          <SelectGroup>\n            <SelectLabel>{t('No teams yet')}</SelectLabel>\n          </SelectGroup>}\n\n        {project.config.clientTeamCreationEnabled && <>\n          <SelectSeparator/>\n          <div>\n            <Button\n              onClick={() => navigate(`${app.urls.accountSettings}#team-creation`)}\n              className=\"w-full\"\n              variant='ghost'\n            >\n              <PlusCircle className=\"mr-2 h-4 w-4\"/> {t('Create a team')}\n            </Button>\n          </div>\n        </>}\n      </SelectContent>\n    </Select>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,sBAA2C;AAC3C,sBAYO;AACP,0BAAqC;AACrC,mBAA6C;AAC7C,eAA2C;AAC3C,0BAA+B;AAC/B,uBAAyB;AASI;AADtB,SAAS,qBAAqB,OAAkC;AACrE,SAAO,4CAAC,yBAAS,UAAU,4CAAC,YAAS,GACnC,sDAAC,SAAO,GAAG,OAAO,GACpB;AACF;AAEA,SAAS,WAAW;AAClB,SAAO,4CAAC,4BAAS,WAAU,mCAAkC;AAC/D;AAEA,SAAS,MAAM,OAAkC;AAC/C,QAAM,EAAE,EAAE,QAAI,oCAAe;AAC7B,QAAM,UAAM,sBAAY;AACxB,QAAM,WAAO,kBAAQ;AACrB,QAAM,UAAU,IAAI,WAAW;AAC/B,QAAM,WAAW,IAAI,YAAY;AACjC,QAAM,eAAe,MAAM,gBAAgB,MAAM;AACjD,QAAM,WAAW,MAAM,SAAS;AAChC,QAAM,YAAQ,sBAAQ,MAAM,UAAU,KAAK,CAAC,GAAG,MAAM,EAAE,OAAO,cAAc,KAAK,IAAI,EAAE,GAAG,CAAC,UAAU,YAAY,CAAC;AAElH,8BAAU,MAAM;AACd,QAAI,CAAC,MAAM,wBAAwB,MAAM,cAAc;AACrD,sDAA2B,MAAM,gBAAgB,MAAM,YAAY,CAAC;AAAA,IACtE;AAAA,EACF,GAAG,CAAC,MAAM,sBAAsB,MAAM,YAAY,CAAC;AAEnD,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO,cAAc;AAAA,MACrB,eAAe,CAAC,UAAU;AACxB,wDAA2B,YAAY;AACrC,gBAAM,OAAO,OAAO,KAAK,CAAAA,UAAQA,MAAK,OAAO,KAAK;AAClD,cAAI,CAAC,MAAM;AACT,kBAAM,IAAI,MAAM,wCAAwC;AAAA,UAC1D;AAEA,cAAI,CAAC,MAAM,sBAAsB;AAC/B,kBAAM,MAAM,gBAAgB,IAAI;AAAA,UAClC;AACA,cAAI,MAAM,QAAQ;AAChB,qBAAS,MAAM,OAAO,IAAI,CAAC;AAAA,UAC7B;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MAEA;AAAA,oDAAC,iCAAc,WAAU,wBACvB,sDAAC,+BAAY,aAAY,eAAa,GACxC;AAAA,QACA,6CAAC,iCAAc,WAAU,eACtB;AAAA,gBAAM,eAAe,6CAAC,+BACrB;AAAA,wDAAC,+BACC,uDAAC,SAAI,WAAU,qCACb;AAAA,0DAAC,UACE,YAAE,cAAc,GACnB;AAAA,cACA,4CAAC,0BAAO,SAAQ,SAAQ,MAAK,QAAO,WAAU,WAAU,SAAS,MAAM,SAAS,GAAG,IAAI,KAAK,eAAe,SAAS,KAAK,cAAc,EAAE,EAAE,GACzI,sDAAC,gCAAS,WAAU,WAAS,GAC/B;AAAA,eACF,GACF;AAAA,YACA,4CAAC,8BAAW,OAAO,KAAK,aAAa,IACnC,uDAAC,SAAI,WAAU,2BACb;AAAA,0DAAC,6BAAS,MAAM,KAAK,cAAc;AAAA,cACnC,4CAAC,8BAAW,WAAU,qBAAqB,eAAK,aAAa,aAAY;AAAA,eAC3E,GACF;AAAA,aACF,IAAiB;AAAA,UAEhB,OAAO,SACN,6CAAC,+BACC;AAAA,wDAAC,+BAAa,YAAE,aAAa,GAAE;AAAA,YAC9B,MAAM,OAAO,UAAQ,KAAK,OAAO,MAAM,cAAc,EAAE,EACrD,IAAI,UACH,4CAAC,8BAAW,OAAO,KAAK,IACtB,uDAAC,SAAI,WAAU,2BACb;AAAA,0DAAC,6BAAS,MAAY;AAAA,cACtB,4CAAC,8BAAW,WAAU,qBAAqB,eAAK,aAAY;AAAA,eAC9D,KAJ+B,KAAK,EAKtC,CACD;AAAA,aACL,IACA,4CAAC,+BACC,sDAAC,+BAAa,YAAE,cAAc,GAAE,GAClC;AAAA,UAED,QAAQ,OAAO,6BAA6B,4EAC3C;AAAA,wDAAC,mCAAe;AAAA,YAChB,4CAAC,SACC;AAAA,cAAC;AAAA;AAAA,gBACC,SAAS,MAAM,SAAS,GAAG,IAAI,KAAK,eAAe,gBAAgB;AAAA,gBACnE,WAAU;AAAA,gBACV,SAAQ;AAAA,gBAER;AAAA,8DAAC,kCAAW,WAAU,gBAAc;AAAA,kBAAE;AAAA,kBAAE,EAAE,eAAe;AAAA;AAAA;AAAA,YAC3D,GACF;AAAA,aACF;AAAA,WACF;AAAA;AAAA;AAAA,EACF;AAEJ;", "names": ["team"]}