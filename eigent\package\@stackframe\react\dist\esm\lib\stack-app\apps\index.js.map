{"version": 3, "sources": ["../../../../../src/lib/stack-app/apps/index.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nexport {\n  StackClientApp\n} from \"./interfaces/client-app\";\nexport type {\n  StackClientAppConstructor,\n  StackClientAppConstructorOptions,\n  StackClientAppJson\n} from \"./interfaces/client-app\";\n\nexport {\n  StackServerApp\n} from \"./interfaces/server-app\";\nexport type {\n  StackServerAppConstructor,\n  StackServerAppConstructorOptions\n} from \"./interfaces/server-app\";\n\nexport {\n  StackAdminApp\n} from \"./interfaces/admin-app\";\nexport type {\n  StackAdminAppConstructor,\n  StackAdminAppConstructorOptions\n} from \"./interfaces/admin-app\";\n\n"], "mappings": ";AAIA;AAAA,EACE;AAAA,OACK;AAOP;AAAA,EACE;AAAA,OACK;AAMP;AAAA,EACE;AAAA,OACK;", "names": []}