{"version": 3, "sources": ["../../../../../src/lib/stack-app/permissions/index.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { ProjectPermissionDefinitionsCrud } from \"@stackframe/stack-shared/dist/interface/crud/project-permissions\";\nimport { TeamPermissionDefinitionsCrud } from \"@stackframe/stack-shared/dist/interface/crud/team-permissions\";\n\n\nexport type TeamPermission = {\n  id: string,\n};\n\nexport type AdminTeamPermission = TeamPermission;\n\nexport type AdminTeamPermissionDefinition = {\n  id: string,\n  description?: string,\n  containedPermissionIds: string[],\n  isDefaultUserPermission?: boolean,\n};\n\nexport type AdminTeamPermissionDefinitionCreateOptions = {\n  id: string,\n  description?: string,\n  containedPermissionIds: string[],\n  isDefaultUserPermission?: boolean,\n};\nexport function adminTeamPermissionDefinitionCreateOptionsToCrud(options: AdminTeamPermissionDefinitionCreateOptions): TeamPermissionDefinitionsCrud[\"Admin\"][\"Create\"] {\n  return {\n    id: options.id,\n    description: options.description,\n    contained_permission_ids: options.containedPermissionIds,\n  };\n}\n\nexport type AdminTeamPermissionDefinitionUpdateOptions = Pick<Partial<AdminTeamPermissionDefinitionCreateOptions>, \"description\" | \"containedPermissionIds\">;\nexport function adminTeamPermissionDefinitionUpdateOptionsToCrud(options: AdminTeamPermissionDefinitionUpdateOptions): TeamPermissionDefinitionsCrud[\"Admin\"][\"Update\"] {\n  return {\n    description: options.description,\n    contained_permission_ids: options.containedPermissionIds,\n  };\n}\n\nexport type ProjectPermission = {\n  id: string,\n};\n\nexport type AdminProjectPermission = ProjectPermission;\n\nexport type AdminProjectPermissionDefinition = {\n  id: string,\n  description?: string,\n  containedPermissionIds: string[],\n};\n\nexport type AdminProjectPermissionDefinitionCreateOptions = {\n  id: string,\n  description?: string,\n  containedPermissionIds: string[],\n};\nexport function adminProjectPermissionDefinitionCreateOptionsToCrud(options: AdminProjectPermissionDefinitionCreateOptions): ProjectPermissionDefinitionsCrud[\"Admin\"][\"Create\"] {\n  return {\n    id: options.id,\n    description: options.description,\n    contained_permission_ids: options.containedPermissionIds,\n  };\n}\n\nexport type AdminProjectPermissionDefinitionUpdateOptions = Pick<Partial<AdminProjectPermissionDefinitionCreateOptions>, \"description\" | \"containedPermissionIds\">;\nexport function adminProjectPermissionDefinitionUpdateOptionsToCrud(options: AdminProjectPermissionDefinitionUpdateOptions): ProjectPermissionDefinitionsCrud[\"Admin\"][\"Update\"] {\n  return {\n    description: options.description,\n    contained_permission_ids: options.containedPermissionIds,\n  };\n}\n"], "mappings": ";AA2BO,SAAS,iDAAiD,SAAuG;AACtK,SAAO;AAAA,IACL,IAAI,QAAQ;AAAA,IACZ,aAAa,QAAQ;AAAA,IACrB,0BAA0B,QAAQ;AAAA,EACpC;AACF;AAGO,SAAS,iDAAiD,SAAuG;AACtK,SAAO;AAAA,IACL,aAAa,QAAQ;AAAA,IACrB,0BAA0B,QAAQ;AAAA,EACpC;AACF;AAmBO,SAAS,oDAAoD,SAA6G;AAC/K,SAAO;AAAA,IACL,IAAI,QAAQ;AAAA,IACZ,aAAa,QAAQ;AAAA,IACrB,0BAA0B,QAAQ;AAAA,EACpC;AACF;AAGO,SAAS,oDAAoD,SAA6G;AAC/K,SAAO;AAAA,IACL,aAAa,QAAQ;AAAA,IACrB,0BAA0B,QAAQ;AAAA,EACpC;AACF;", "names": []}