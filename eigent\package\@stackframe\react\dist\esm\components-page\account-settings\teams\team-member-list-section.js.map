{"version": 3, "sources": ["../../../../../src/components-page/account-settings/teams/team-member-list-section.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow, Typography } from \"@stackframe/stack-ui\";\nimport { Team } from \"../../..\";\nimport { UserAvatar } from \"../../../components/elements/user-avatar\";\nimport { useUser } from \"../../../lib/hooks\";\nimport { useTranslation } from \"../../../lib/translations\";\n\nexport function TeamMemberListSection(props: { team: Team }) {\n  const user = useUser({ or: 'redirect' });\n  const readMemberPermission = user.usePermission(props.team, '$read_members');\n  const inviteMemberPermission = user.usePermission(props.team, '$invite_members');\n\n  if (!readMemberPermission && !inviteMemberPermission) {\n    return null;\n  }\n\n  return <MemberListSectionInner team={props.team} />;\n}\n\nfunction MemberListSectionInner(props: { team: Team }) {\n  const { t } = useTranslation();\n  const users = props.team.useUsers();\n\n  return (\n    <div>\n      <Typography className='font-medium mb-2'>{t(\"Members\")}</Typography>\n      <div className='border rounded-md'>\n        <Table>\n          <TableHeader>\n            <TableRow>\n              <TableHead className=\"w-[100px]\">{t(\"User\")}</TableHead>\n              <TableHead className=\"w-[200px]\">{t(\"Name\")}</TableHead>\n            </TableRow>\n          </TableHeader>\n          <TableBody>\n            {users.map(({ id, teamProfile }, i) => (\n              <TableRow key={id}>\n                <TableCell>\n                  <UserAvatar user={teamProfile} />\n                </TableCell>\n                <TableCell>\n                  {teamProfile.displayName && (\n                    <Typography>{teamProfile.displayName}</Typography>\n                  )}\n                  {!teamProfile.displayName && (\n                    <Typography className=\"text-muted-foreground italic\">{t(\"No display name set\")}</Typography>\n                  )}\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </div>\n    </div>\n  );\n}\n"], "mappings": ";AAIA,SAAS,OAAO,WAAW,WAAW,WAAW,aAAa,UAAU,kBAAkB;AAE1F,SAAS,kBAAkB;AAC3B,SAAS,eAAe;AACxB,SAAS,sBAAsB;AAWtB,cAaG,YAbH;AATF,SAAS,sBAAsB,OAAuB;AAC3D,QAAM,OAAO,QAAQ,EAAE,IAAI,WAAW,CAAC;AACvC,QAAM,uBAAuB,KAAK,cAAc,MAAM,MAAM,eAAe;AAC3E,QAAM,yBAAyB,KAAK,cAAc,MAAM,MAAM,iBAAiB;AAE/E,MAAI,CAAC,wBAAwB,CAAC,wBAAwB;AACpD,WAAO;AAAA,EACT;AAEA,SAAO,oBAAC,0BAAuB,MAAM,MAAM,MAAM;AACnD;AAEA,SAAS,uBAAuB,OAAuB;AACrD,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,QAAQ,MAAM,KAAK,SAAS;AAElC,SACE,qBAAC,SACC;AAAA,wBAAC,cAAW,WAAU,oBAAoB,YAAE,SAAS,GAAE;AAAA,IACvD,oBAAC,SAAI,WAAU,qBACb,+BAAC,SACC;AAAA,0BAAC,eACC,+BAAC,YACC;AAAA,4BAAC,aAAU,WAAU,aAAa,YAAE,MAAM,GAAE;AAAA,QAC5C,oBAAC,aAAU,WAAU,aAAa,YAAE,MAAM,GAAE;AAAA,SAC9C,GACF;AAAA,MACA,oBAAC,aACE,gBAAM,IAAI,CAAC,EAAE,IAAI,YAAY,GAAG,MAC/B,qBAAC,YACC;AAAA,4BAAC,aACC,8BAAC,cAAW,MAAM,aAAa,GACjC;AAAA,QACA,qBAAC,aACE;AAAA,sBAAY,eACX,oBAAC,cAAY,sBAAY,aAAY;AAAA,UAEtC,CAAC,YAAY,eACZ,oBAAC,cAAW,WAAU,gCAAgC,YAAE,qBAAqB,GAAE;AAAA,WAEnF;AAAA,WAXa,EAYf,CACD,GACH;AAAA,OACF,GACF;AAAA,KACF;AAEJ;", "names": []}