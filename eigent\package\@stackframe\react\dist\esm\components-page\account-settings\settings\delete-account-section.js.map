{"version": 3, "sources": ["../../../../../src/components-page/account-settings/settings/delete-account-section.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { Accordion, AccordionContent, AccordionItem, AccordionTrigger, Button, Typography } from \"@stackframe/stack-ui\";\nimport { useState } from \"react\";\nimport { useStackApp, useUser } from \"../../../lib/hooks\";\nimport { useTranslation } from \"../../../lib/translations\";\nimport { Section } from \"../section\";\n\nexport function DeleteAccountSection() {\n  const { t } = useTranslation();\n  const user = useUser({ or: 'redirect' });\n  const app = useStackApp();\n  const project = app.useProject();\n  const [deleting, setDeleting] = useState(false);\n  if (!project.config.clientUserDeletionEnabled) {\n    return null;\n  }\n\n  return (\n    <Section\n      title={t(\"Delete Account\")}\n      description={t(\"Permanently remove your account and all associated data\")}\n    >\n      <div className='stack-scope flex flex-col items-stretch'>\n        <Accordion type=\"single\" collapsible className=\"w-full\">\n          <AccordionItem value=\"item-1\">\n            <AccordionTrigger>{t(\"Danger zone\")}</AccordionTrigger>\n            <AccordionContent>\n              {!deleting ? (\n                <div>\n                  <Button\n                    variant='destructive'\n                    onClick={() => setDeleting(true)}\n                  >\n                    {t(\"Delete account\")}\n                  </Button>\n                </div>\n              ) : (\n                <div className='flex flex-col gap-2'>\n                  <Typography variant='destructive'>\n                    {t(\"Are you sure you want to delete your account? This action is IRREVERSIBLE and will delete ALL associated data.\")}\n                  </Typography>\n                  <div className='flex gap-2'>\n                    <Button\n                      variant='destructive'\n                      onClick={async () => {\n                        await user.delete();\n                        await app.redirectToHome();\n                      }}\n                    >\n                      {t(\"Delete Account\")}\n                    </Button>\n                    <Button\n                      variant='secondary'\n                      onClick={() => setDeleting(false)}\n                    >\n                      {t(\"Cancel\")}\n                    </Button>\n                  </div>\n                </div>\n              )}\n            </AccordionContent>\n          </AccordionItem>\n        </Accordion>\n      </div>\n    </Section>\n  );\n}\n"], "mappings": ";AAIA,SAAS,WAAW,kBAAkB,eAAe,kBAAkB,QAAQ,kBAAkB;AACjG,SAAS,gBAAgB;AACzB,SAAS,aAAa,eAAe;AACrC,SAAS,sBAAsB;AAC/B,SAAS,eAAe;AAoBZ,cAgBM,YAhBN;AAlBL,SAAS,uBAAuB;AACrC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,OAAO,QAAQ,EAAE,IAAI,WAAW,CAAC;AACvC,QAAM,MAAM,YAAY;AACxB,QAAM,UAAU,IAAI,WAAW;AAC/B,QAAM,CAAC,UAAU,WAAW,IAAI,SAAS,KAAK;AAC9C,MAAI,CAAC,QAAQ,OAAO,2BAA2B;AAC7C,WAAO;AAAA,EACT;AAEA,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO,EAAE,gBAAgB;AAAA,MACzB,aAAa,EAAE,yDAAyD;AAAA,MAExE,8BAAC,SAAI,WAAU,2CACb,8BAAC,aAAU,MAAK,UAAS,aAAW,MAAC,WAAU,UAC7C,+BAAC,iBAAc,OAAM,UACnB;AAAA,4BAAC,oBAAkB,YAAE,aAAa,GAAE;AAAA,QACpC,oBAAC,oBACE,WAAC,WACA,oBAAC,SACC;AAAA,UAAC;AAAA;AAAA,YACC,SAAQ;AAAA,YACR,SAAS,MAAM,YAAY,IAAI;AAAA,YAE9B,YAAE,gBAAgB;AAAA;AAAA,QACrB,GACF,IAEA,qBAAC,SAAI,WAAU,uBACb;AAAA,8BAAC,cAAW,SAAQ,eACjB,YAAE,gHAAgH,GACrH;AAAA,UACA,qBAAC,SAAI,WAAU,cACb;AAAA;AAAA,cAAC;AAAA;AAAA,gBACC,SAAQ;AAAA,gBACR,SAAS,YAAY;AACnB,wBAAM,KAAK,OAAO;AAClB,wBAAM,IAAI,eAAe;AAAA,gBAC3B;AAAA,gBAEC,YAAE,gBAAgB;AAAA;AAAA,YACrB;AAAA,YACA;AAAA,cAAC;AAAA;AAAA,gBACC,SAAQ;AAAA,gBACR,SAAS,MAAM,YAAY,KAAK;AAAA,gBAE/B,YAAE,QAAQ;AAAA;AAAA,YACb;AAAA,aACF;AAAA,WACF,GAEJ;AAAA,SACF,GACF,GACF;AAAA;AAAA,EACF;AAEJ;", "names": []}