# 🐾 模拟炒股游戏环境配置示例
# Stock Trading Game Environment Configuration Example

# 环境设置
ENVIRONMENT=development

# 应用基础配置
APP_NAME=模拟炒股游戏
APP_VERSION=1.0.0
DEBUG=true
HOST=0.0.0.0
PORT=8000

# 安全配置
SECRET_KEY=your-super-secret-key-at-least-32-characters-long-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
ALGORITHM=HS256

# 数据库配置
DATABASE_URL=postgresql://postgres:password@localhost:5432/stock_game
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_POOL_SIZE=10

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# CORS配置
ALLOWED_HOSTS=*

# 静态文件配置
SERVE_STATIC=true
STATIC_DIR=static

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=

# 市场模拟配置
MARKET_OPEN_HOUR=9
MARKET_OPEN_MINUTE=30
MARKET_CLOSE_HOUR=15
MARKET_CLOSE_MINUTE=0
MARKET_UPDATE_INTERVAL=1.0

# 股票配置
INITIAL_STOCK_COUNT=50
INITIAL_STOCK_PRICE_MIN=10.0
INITIAL_STOCK_PRICE_MAX=100.0
MAX_PRICE_CHANGE_PERCENT=0.1

# 交易配置
INITIAL_USER_BALANCE=100000.0
MIN_ORDER_AMOUNT=100.0
TRANSACTION_FEE_RATE=0.0003

# 机器人配置
BOT_COUNT=10
BOT_INITIAL_BALANCE=50000.0
BOT_TRADE_INTERVAL_MIN=30
BOT_TRADE_INTERVAL_MAX=300

# WebSocket配置
WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=1000

# 缓存配置
CACHE_TTL=300

# 邮件配置（可选）
SMTP_HOST=
SMTP_PORT=
SMTP_USER=
SMTP_PASSWORD=

# 第三方API配置（可选）
STOCK_DATA_API_KEY=
NEWS_API_KEY=
