#!/usr/bin/env python3
"""
Simple FastAPI server for testing Eigent backend
"""

from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Create FastAPI app
app = FastAPI(
    title="Eigent Backend API",
    description="Multi-agent workforce desktop application backend",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Eigent Backend API is running!", "status": "ok"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "eigent-backend"}

@app.get("/api/v1/status")
async def api_status():
    """API status endpoint"""
    return {
        "api_version": "v1",
        "status": "running",
        "features": {
            "chat": "disabled (camel-ai integration pending)",
            "agents": "disabled (camel-ai integration pending)",
            "mcp": "disabled (camel-ai integration pending)"
        }
    }

if __name__ == "__main__":
    print("Starting Eigent Backend Server...")
    print("Server will be available at: http://localhost:5001")
    print("API documentation at: http://localhost:5001/docs")
    
    uvicorn.run(
        "simple_main:app",
        host="0.0.0.0",
        port=5001,
        reload=True,
        log_level="info"
    )
