import os
import re
import json
import time
import markdown
from bs4 import BeautifulSoup
import http.client

# 不再需要代理设置
# 不再需要Google API库

# 使用第三方API服务配置
API_HOST = "ai98.vip"
API_KEY = "sk-1r4ApCAZjjmjZLUJ9691C2B7946e47669254254c89066a46"  # 使用您提供的示例密钥
MODEL_NAME = "gemini-2.5-pro-exp-03-25"  # 保持使用相同的模型

# 定义API请求的重试设置
MAX_RETRIES = 3        # 最大重试次数
RETRY_DELAY = 60       # 重试间隔（秒）
QUOTA_ERROR_CODE = 429 # 配额超限错误码

# 配置参数
DEFAULT_INPUT_FILE = "article_sample.md"  # 默认输入文件
DEFAULT_OUTPUT_DIR = "output_gemini"      # 默认输出目录
BATCH_SIZE = 5                           # 每批处理的段落数
PARAGRAPH_INTERVAL = 2                   # 段落间隔

class ArticleProcessor:
    def __init__(self):
        self.model_name = MODEL_NAME
        self.api_host = API_HOST
        self.api_key = API_KEY
    
    def call_api(self, prompt):
        """调用第三方API服务"""
        conn = http.client.HTTPSConnection(self.api_host)
        payload = json.dumps({
            "model": self.model_name,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.7,
            "presence_penalty": 0.0,
            "frequency_penalty": 0.0
        })
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': f'Bearer {self.api_key}',
        }
        
        conn.request("POST", "/v1/chat/completions", payload, headers)
        res = conn.getresponse()
        data = res.read()
        
        response_data = json.loads(data.decode("utf-8"))
        content = response_data['choices'][0]['message']['content']
        return content
    
    def call_api_with_retry(self, prompt):
        """使用重试机制调用API"""
        retries = 0
        while retries < MAX_RETRIES:
            try:
                return self.call_api(prompt)
            except Exception as e:
                error_str = str(e)
                if str(QUOTA_ERROR_CODE) in error_str and "quota" in error_str.lower():
                    retry_seconds = RETRY_DELAY
                    # 尝试从错误消息中提取建议的等待时间
                    retry_match = re.search(r'retry_delay\s*{\s*seconds:\s*(\d+)', error_str)
                    if retry_match:
                        retry_seconds = int(retry_match.group(1)) + 5  # 额外等待5秒以确保安全
                    
                    retries += 1
                    if retries >= MAX_RETRIES:
                        print(f"达到最大重试次数 ({MAX_RETRIES})，放弃操作。")
                        raise
                    
                    print(f"API配额限制，等待 {retry_seconds} 秒后重试 ({retries}/{MAX_RETRIES})...")
                    time.sleep(retry_seconds)
                else:
                    # 非配额限制错误，直接抛出
                    raise
    
    def parse_markdown(self, markdown_text):
        """将Markdown文本解析为HTML，然后提取段落"""
        html = markdown.markdown(markdown_text)
        soup = BeautifulSoup(html, 'html.parser')
        
        # 提取所有段落、标题等内容块
        paragraphs = []
        for tag in soup.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote', 'li']):
            text = tag.get_text().strip()
            if text:  # 只保留非空段落
                paragraphs.append(text)
        
        return paragraphs
    
    def summarize_paragraph(self, paragraph):
        """使用第三方API总结段落大意"""
        try:
            prompt = f"你是一个专业的文章总结助手，请用简洁的一句话总结以下段落的核心内容: {paragraph}"
            return self.call_api_with_retry(prompt)
        except Exception as e:
            print(f"总结段落时出错: {e}")
            return "无法总结该段落内容"
    
    def generate_svg_for_summary(self, summary):
        """使用第三方API根据段落总结生成SVG图片"""
        svg_prompt = f"""
        请为以下内容生成一个高质量的SVG图像代码:
        
        内容: {summary}
        
        我需要一个风格设计精美的SVG图像，参考以下设计风格和元素：
        
        技术要求:
        1. 图像尺寸: 宽800px, 高600px (4:3比例)
        2. 使用SVG高级功能如渐变、滤镜、阴影效果
        3. 使用适当的视觉层次和排版布局
        4. 所有文字使用中文
        
        设计元素:
        1. 使用优雅的背景渐变，可以是深色系的高级感底色
        2. 包含精美的卡片式布局，具有圆角和阴影效果
        3. 适当使用图标或emoji装饰重点内容
        4. 为标题和关键元素添加辉光或其他醒目效果
        5. 文字排版清晰，使用不同大小和颜色区分层级
        
        设计风格:
        - 时代感: 现代、未来
        - 情绪值: 专业、优雅
        - 密度: 信息清晰但视觉丰富
        - 材质: 玻璃感、渐变质感
        - 颜色: 和谐且具有高级感的配色
        
        请直接返回完整的SVG代码，不要有任何解释。确保代码可以直接使用并且风格精美。
        """
        
        try:
            print(f"正在使用 {self.model_name} 生成SVG图片，请稍候...")
            svg_content = self.call_api_with_retry(svg_prompt)
            
            # 提取SVG代码(去除可能的markdown格式)
            svg_match = re.search(r'<svg.*?<\/svg>', svg_content, re.DOTALL)
            if svg_match:
                return svg_match.group(0)
            return svg_content
            
        except Exception as e:
            print(f"生成SVG时出错: {e}")
            return self.get_fallback_svg(summary)
    
    def get_fallback_svg(self, text):
        """当API调用失败时生成一个简单的后备SVG"""
        # 创建一个简单的SVG，显示文本内容
        return f"""<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
            <rect width="800" height="600" fill="#f0f0f0"/>
            <text x="50" y="50" font-family="Arial" font-size="24" fill="#333">{text}</text>
            <text x="50" y="90" font-family="Arial" font-size="16" fill="#666">无法生成SVG图像，API请求失败</text>
            <text x="50" y="120" font-family="Arial" font-size="14" fill="#666">请稍后再试或检查API服务</text>
        </svg>"""
    
    def process_article(self, markdown_text):
        """处理整篇文章，只处理部分段落并自动分批"""
        # 解析所有段落
        all_paragraphs = self.parse_markdown(markdown_text)
        total_paragraphs = len(all_paragraphs)
        print(f"共找到 {total_paragraphs} 个段落")
        
        # 选择要处理的段落（按间隔）
        selected_paragraphs = []
        selected_indices = []
        for i in range(0, total_paragraphs, PARAGRAPH_INTERVAL):
            selected_paragraphs.append(all_paragraphs[i])
            selected_indices.append(i)
        
        print(f"将处理 {len(selected_paragraphs)} 个段落 (间隔为 {PARAGRAPH_INTERVAL})")
        
        # 分批处理段落
        all_results = []
        
        # 为所有段落创建占位结果（仅包含段落文本）
        for i, paragraph in enumerate(all_paragraphs):
            # 初始化一个基本结果结构
            result = {
                "paragraph": paragraph,
                "summary": "",
                "svg": "",
                "is_processed": False  # 标记是否被实际处理
            }
            all_results.append(result)
        
        # 处理选定的段落
        for batch_start in range(0, len(selected_paragraphs), BATCH_SIZE):
            batch_end = min(batch_start + BATCH_SIZE, len(selected_paragraphs))
            batch_paragraphs = selected_paragraphs[batch_start:batch_end]
            batch_indices = selected_indices[batch_start:batch_end]
            
            print(f"\n处理批次 {batch_start//BATCH_SIZE + 1}, 段落 {batch_start+1}-{batch_end} / {len(selected_paragraphs)}")
            
            for i, (paragraph, orig_index) in enumerate(zip(batch_paragraphs, batch_indices)):
                print(f"正在处理段落 {orig_index+1}/{total_paragraphs} (选定段落中的第 {batch_start+i+1} 个)...")
                try:
                    # 获取段落摘要
                    summary = self.summarize_paragraph(paragraph)
                    # 生成SVG图片
                    svg = self.generate_svg_for_summary(summary)
                    
                    # 更新结果
                    all_results[orig_index]["summary"] = summary
                    all_results[orig_index]["svg"] = svg
                    all_results[orig_index]["is_processed"] = True
                except Exception as e:
                    print(f"处理段落时出错: {e}")
                    all_results[orig_index]["summary"] = "处理此段落时出错"
                    all_results[orig_index]["svg"] = self.get_fallback_svg("处理此段落时出错")
                    all_results[orig_index]["is_processed"] = True
            
            # 如果不是最后一批，等待一段时间再继续下一批
            if batch_end < len(selected_paragraphs):
                wait_seconds = 30  # 减少等待时间
                print(f"\n批次完成，等待 {wait_seconds} 秒后继续下一批...")
                time.sleep(wait_seconds)
        
        return all_results
    
    def save_results(self, results, output_dir=DEFAULT_OUTPUT_DIR):
        """保存处理结果到文件"""
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建HTML文件展示结果
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>文章段落与配图 (Gemini)</title>
            <style>
                body {{ font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; background-color: #f5f5f5; }}
                .section {{ margin-bottom: 40px; border-radius: 10px; padding: 20px; background-color: white; box-shadow: 0 0 15px rgba(0,0,0,0.05); }}
                .paragraph {{ margin-bottom: 15px; line-height: 1.6; color: #333; }}
                .summary {{ font-weight: bold; margin-bottom: 15px; color: #2c3e50; background-color: #ecf0f1; padding: 10px; border-radius: 5px; }}
                .svg-container {{ max-width: 800px; margin: 20px auto; border-radius: 5px; overflow: hidden; box-shadow: 0 0 10px rgba(0,0,0,0.1); }}
                .model-info {{ color: #666; font-style: italic; margin-bottom: 20px; }}
                .error {{ color: #e74c3c; }}
                .not-processed {{ background-color: #f9f9f9; opacity: 0.7; }}
                h1 {{ color: #2c3e50; margin-bottom: 30px; }}
                h2 {{ color: #3498db; }}
            </style>
        </head>
        <body>
            <h1>文章段落与配图 (Gemini)</h1>
            <div class="model-info">使用模型: {self.model_name}</div>
            <div class="model-info">处理间隔: 每 {PARAGRAPH_INTERVAL} 个段落</div>
        """
        
        # 过滤出已处理的段落，单独保存为SVG文件
        svg_count = 0
        for i, item in enumerate(results):
            is_processed = item.get("is_processed", False)
            
            # 确定CSS类
            section_class = "section"
            if not is_processed:
                section_class += " not-processed"
                
            is_error = "处理此段落时出错" in item.get('summary', '')
            summary_class = "summary error" if is_error else "summary"
            
            html_content += f"""
            <div class="{section_class}">
                <h2>段落 {i+1}</h2>
                <div class="paragraph">{item['paragraph']}</div>
            """
            
            # 只有处理过的段落才显示摘要和SVG
            if is_processed:
                html_content += f"""
                <div class="{summary_class}">总结: {item['summary']}</div>
                <div class="svg-container">{item['svg']}</div>
                """
                
                # 保存SVG文件
                if item['svg'] and not is_error:
                    svg_count += 1
                    with open(f"{output_dir}/paragraph_{i+1}.svg", "w", encoding="utf-8") as f:
                        f.write(item['svg'])
            else:
                html_content += f"""
                <div class="summary not-processed">此段落未处理（根据间隔设置）</div>
                """
                
            html_content += "</div>"
        
        html_content += """
        </body>
        </html>
        """
        
        with open(f"{output_dir}/article_with_images.html", "w", encoding="utf-8") as f:
            f.write(html_content)
        
        # 保存JSON结果
        with open(f"{output_dir}/results.json", "w", encoding="utf-8") as f:
            # 我们不保存SVG内容到JSON，因为它可能非常大
            simplified_results = []
            for r in results:
                if r.get("is_processed", False):
                    simplified_results.append({"paragraph": r["paragraph"], "summary": r["summary"]})
            json.dump(simplified_results, f, ensure_ascii=False, indent=2)
        
        print(f"结果已保存到 {output_dir} 目录")
        print(f"共生成了 {svg_count} 个SVG图像")

def main():
    """主程序入口，简化版本，使用默认设置"""
    print("欢迎使用文章图像生成器！(Gemini版 - 自动模式)")
    print(f"使用模型: gemini-2.5-pro-exp-03-25")
    print(f"默认输入文件: {DEFAULT_INPUT_FILE}")
    print(f"默认输出目录: {DEFAULT_OUTPUT_DIR}")
    print(f"段落处理间隔: 每 {PARAGRAPH_INTERVAL} 个段落处理一次")
    print(f"每批处理段落数: {BATCH_SIZE}")
    
    try:
        # 尝试读取默认文件
        with open(DEFAULT_INPUT_FILE, "r", encoding="utf-8") as f:
            markdown_text = f.read()
        print(f"成功读取文件: {DEFAULT_INPUT_FILE}")
    except FileNotFoundError:
        print(f"未找到默认文件 {DEFAULT_INPUT_FILE}")
        print("请确保该文件存在于当前目录，或修改DEFAULT_INPUT_FILE变量")
        return
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return
    
    processor = ArticleProcessor()
    print("\n开始处理文章...")
    results = processor.process_article(markdown_text)
    
    print("\n处理完成！正在保存结果...")
    processor.save_results(results, DEFAULT_OUTPUT_DIR)
    
    print(f"\n全部完成！您可以打开 {DEFAULT_OUTPUT_DIR}/article_with_images.html 查看结果")

if __name__ == "__main__":
    main() 