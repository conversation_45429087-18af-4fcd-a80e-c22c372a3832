{"version": 3, "sources": ["../../../src/interface/crud/sessions.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport { yupBoolean, yupMixed, yupNumber, yupObject, yupString } from \"../../schema-fields\";\nimport { geoInfoSchema } from \"../../utils/geo\";\n\n\n// Create\nexport const sessionsCrudServerCreateSchema = yupObject({\n  user_id: yupString().uuid().defined(),\n  expires_in_millis: yupNumber().max(1000 * 60 * 60 * 24 * 367).default(1000 * 60 * 60 * 24 * 365),\n  is_impersonation: yupBoolean().default(false),\n}).defined();\n\n\nexport const sessionsCreateOutputSchema = yupObject({\n  refresh_token: yupString().defined(),\n  access_token: yupString().defined(),\n}).defined();\n\n\nexport const sessionsCrudReadSchema = yupObject({\n  id: yupString().defined(),\n  user_id: yupString().uuid().defined(),\n  created_at: yupNumber().defined(),\n  is_impersonation: yupBoolean().defined(),\n  last_used_at: yupNumber().optional(),\n  is_current_session: yupBoolean(),\n  // TODO move this to a shared type\n  // TODO: what about if not trusted?\n  last_used_at_end_user_ip_info: geoInfoSchema.optional(),\n}).defined();\n\n\n// Delete\nexport const sessionsCrudDeleteSchema = yupMixed();\n\n\nexport const sessionsCrud = createCrud({\n  // serverCreateSchema: sessionsCrudServerCreateSchema,\n  serverReadSchema: sessionsCrudReadSchema,\n  serverDeleteSchema: sessionsCrudDeleteSchema,\n  clientReadSchema: sessionsCrudReadSchema,\n  clientDeleteSchema: sessionsCrudDeleteSchema,\n  docs: {\n    serverList: {\n      summary: \"List sessions\",\n      description: \"List all sessions for the current user.\",\n      tags: [\"Sessions\"],\n    },\n    serverDelete: {\n      summary: \"Delete session\",\n      description: \"Delete a session by ID.\",\n      tags: [\"Sessions\"],\n    },\n    clientList: {\n      summary: \"List sessions\",\n      description: \"List all sessions for the current user.\",\n      tags: [\"Sessions\"],\n    },\n    clientDelete: {\n      summary: \"Delete session\",\n      description: \"Delete a session by ID.\",\n      tags: [\"Sessions\"],\n    },\n  },\n});\nexport type SessionsCrud = CrudTypeOf<typeof sessionsCrud>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAuC;AACvC,2BAAsE;AACtE,iBAA8B;AAIvB,IAAM,qCAAiC,gCAAU;AAAA,EACtD,aAAS,gCAAU,EAAE,KAAK,EAAE,QAAQ;AAAA,EACpC,uBAAmB,gCAAU,EAAE,IAAI,MAAO,KAAK,KAAK,KAAK,GAAG,EAAE,QAAQ,MAAO,KAAK,KAAK,KAAK,GAAG;AAAA,EAC/F,sBAAkB,iCAAW,EAAE,QAAQ,KAAK;AAC9C,CAAC,EAAE,QAAQ;AAGJ,IAAM,iCAA6B,gCAAU;AAAA,EAClD,mBAAe,gCAAU,EAAE,QAAQ;AAAA,EACnC,kBAAc,gCAAU,EAAE,QAAQ;AACpC,CAAC,EAAE,QAAQ;AAGJ,IAAM,6BAAyB,gCAAU;AAAA,EAC9C,QAAI,gCAAU,EAAE,QAAQ;AAAA,EACxB,aAAS,gCAAU,EAAE,KAAK,EAAE,QAAQ;AAAA,EACpC,gBAAY,gCAAU,EAAE,QAAQ;AAAA,EAChC,sBAAkB,iCAAW,EAAE,QAAQ;AAAA,EACvC,kBAAc,gCAAU,EAAE,SAAS;AAAA,EACnC,wBAAoB,iCAAW;AAAA;AAAA;AAAA,EAG/B,+BAA+B,yBAAc,SAAS;AACxD,CAAC,EAAE,QAAQ;AAIJ,IAAM,+BAA2B,+BAAS;AAG1C,IAAM,mBAAe,wBAAW;AAAA;AAAA,EAErC,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,MAAM;AAAA,IACJ,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,UAAU;AAAA,IACnB;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,UAAU;AAAA,IACnB;AAAA,IACA,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,UAAU;AAAA,IACnB;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,UAAU;AAAA,IACnB;AAAA,EACF;AACF,CAAC;", "names": []}