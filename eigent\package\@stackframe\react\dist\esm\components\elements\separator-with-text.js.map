{"version": 3, "sources": ["../../../../src/components/elements/separator-with-text.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { Separator } from \"@stackframe/stack-ui\";\n\nexport function SeparatorWithText({ text }: { text: string }) {\n  return (\n    <div className=\"flex items-center justify-center my-6 stack-scope\">\n      <div className=\"flex-1\">\n        <Separator />\n      </div>\n      <div className=\"mx-2 text-sm text-zinc-500\">{text}</div>\n      <div className=\"flex-1\">\n        <Separator />\n      </div>\n    </div>\n  );\n}\n"], "mappings": ";;;AAOA,SAAS,iBAAiB;AAItB,SAEI,KAFJ;AAFG,SAAS,kBAAkB,EAAE,KAAK,GAAqB;AAC5D,SACE,qBAAC,SAAI,WAAU,qDACb;AAAA,wBAAC,SAAI,WAAU,UACb,8BAAC,aAAU,GACb;AAAA,IACA,oBAAC,SAAI,WAAU,8BAA8B,gBAAK;AAAA,IAClD,oBAAC,SAAI,WAAU,UACb,8BAAC,aAAU,GACb;AAAA,KACF;AAEJ;", "names": []}