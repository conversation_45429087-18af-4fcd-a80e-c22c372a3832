{"version": 3, "sources": ["../../src/lib/hooks.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { useContext } from \"react\";\nimport { StackContext } from \"../providers/stack-provider-client\";\nimport { GetUserOptions as AppGetUserOptions, CurrentInternalUser, CurrentUser, StackClientApp } from \"./stack-app\";\n\ntype GetUserOptions = AppGetUserOptions<true> & {\n  projectIdMustMatch?: string,\n};\n\n/**\n * Returns the current user object. Equivalent to `useStackApp().useUser()`.\n *\n * @returns the current user\n */\nexport function useUser(options: GetUserOptions & { or: 'redirect' | 'throw', projectIdMustMatch: \"internal\" }): CurrentInternalUser;\nexport function useUser(options: GetUserOptions & { or: 'redirect' | 'throw' }): CurrentUser;\nexport function useUser(options: GetUserOptions & { projectIdMustMatch: \"internal\" }): CurrentInternalUser | null;\nexport function useUser(options?: GetUserOptions): CurrentUser | CurrentInternalUser | null;\nexport function useUser(options: GetUserOptions = {}): CurrentUser | CurrentInternalUser | null {\n  const stackApp = useStackApp(options);\n  if (options.projectIdMustMatch && stackApp.projectId !== options.projectIdMustMatch) {\n    throw new Error(\"Unexpected project ID in useStackApp: \" + stackApp.projectId);\n  }\n  if (options.projectIdMustMatch === \"internal\") {\n    return stackApp.useUser(options) as CurrentInternalUser;\n  } else {\n    return stackApp.useUser(options) as CurrentUser;\n  }\n}\n\n/**\n * Returns the current Stack app associated with the StackProvider.\n *\n * @returns the current Stack app\n */\nexport function useStackApp<ProjectId extends string>(options: { projectIdMustMatch?: ProjectId } = {}): StackClientApp<true, ProjectId> {\n  const context = useContext(StackContext);\n  if (context === null) {\n    throw new Error(\"useStackApp must be used within a StackProvider\");\n  }\n  const stackApp = context.app;\n  if (options.projectIdMustMatch && stackApp.projectId !== options.projectIdMustMatch) {\n    throw new Error(\"Unexpected project ID in useStackApp: \" + stackApp.projectId);\n  }\n  return stackApp as StackClientApp<true, ProjectId>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,mBAA2B;AAC3B,mCAA6B;AAgBtB,SAAS,QAAQ,UAA0B,CAAC,GAA6C;AAC9F,QAAM,WAAW,YAAY,OAAO;AACpC,MAAI,QAAQ,sBAAsB,SAAS,cAAc,QAAQ,oBAAoB;AACnF,UAAM,IAAI,MAAM,2CAA2C,SAAS,SAAS;AAAA,EAC/E;AACA,MAAI,QAAQ,uBAAuB,YAAY;AAC7C,WAAO,SAAS,QAAQ,OAAO;AAAA,EACjC,OAAO;AACL,WAAO,SAAS,QAAQ,OAAO;AAAA,EACjC;AACF;AAOO,SAAS,YAAsC,UAA8C,CAAC,GAAoC;AACvI,QAAM,cAAU,yBAAW,yCAAY;AACvC,MAAI,YAAY,MAAM;AACpB,UAAM,IAAI,MAAM,iDAAiD;AAAA,EACnE;AACA,QAAM,WAAW,QAAQ;AACzB,MAAI,QAAQ,sBAAsB,SAAS,cAAc,QAAQ,oBAAoB;AACnF,UAAM,IAAI,MAAM,2CAA2C,SAAS,SAAS;AAAA,EAC/E;AACA,SAAO;AACT;", "names": []}