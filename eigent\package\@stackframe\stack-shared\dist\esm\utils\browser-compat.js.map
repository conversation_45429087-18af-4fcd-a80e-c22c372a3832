{"version": 3, "sources": ["../../../src/utils/browser-compat.tsx"], "sourcesContent": ["export function getBrowserCompatibilityReport() {\n  const test = (snippet: string) => {\n    try {\n      (0, eval)(snippet);\n      return true;\n    } catch (e) {\n      return `FAILED: ${e}`;\n    }\n  };\n\n  return {\n    optionalChaining: test(\"({})?.b?.c\"),\n    nullishCoalescing: test(\"0 ?? 1\"),\n    weakRef: test(\"new WeakRef({})\"),\n    cryptoUuid: test(\"crypto.randomUUID()\"),\n  };\n}\n"], "mappings": ";AAAO,SAAS,gCAAgC;AAC9C,QAAM,OAAO,CAAC,YAAoB;AAChC,QAAI;AACF,OAAC,GAAG,MAAM,OAAO;AACjB,aAAO;AAAA,IACT,SAAS,GAAG;AACV,aAAO,WAAW,CAAC;AAAA,IACrB;AAAA,EACF;AAEA,SAAO;AAAA,IACL,kBAAkB,KAAK,YAAY;AAAA,IACnC,mBAAmB,KAAK,QAAQ;AAAA,IAChC,SAAS,KAAK,iBAAiB;AAAA,IAC/B,YAAY,KAAK,qBAAqB;AAAA,EACxC;AACF;", "names": []}