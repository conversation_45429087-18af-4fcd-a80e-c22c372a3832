import { Result } from './results.js';

type Json = null | boolean | number | string | Json[] | {
    [key: string]: J<PERSON>;
};
type ReadonlyJson = null | boolean | number | string | readonly Readonly<PERSON><PERSON>[] | {
    readonly [key: string]: <PERSON>on<PERSON><PERSON><PERSON>;
};
declare function isJson(value: unknown): value is Json;
declare function parseJson(json: string): Result<Json>;
declare function stringifyJson(json: Json): Result<string>;

export { type Json, type Readonly<PERSON>son, isJson, parseJson, stringifyJson };
