name: Build

on:
  push:
    tags:
      - "v*"
    paths-ignore:
      - "**.md"
      - "**.spec.js"
      - ".idea"
      - ".vscode"
      - ".dockerignore"
      - "Dockerfile"
      - ".gitignore"
      - ".github/**"
      - "!.github/workflows/build.yml"

permissions:
  contents: write

jobs:
  build:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [macos-latest, macos-13, windows-latest]

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install Dependencies
        run: npm install

      - name: Build Release Files
        run: npm run build
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: release_on_${{ matrix.os }}
          path: release/
          retention-days: 5

      - name: Create Release
        if: startsWith(github.ref, 'refs/tags/')
        uses: softprops/action-gh-release@v1
        with:
          files: |
            release/*.exe
            release/*.dmg
            release/*.zip
          draft: false
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
