[project]
name = "backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10,<3.13"

[tool.setuptools]
packages = ["app"]
dependencies = [
    "camel-ai[owl]>=0.2.73a11",
    "fastapi>=0.115.12",
    "fastapi-babel>=1.0.0",
    "fastapi-pagination>=0.12.34",
    "fastapi-filter>=2.0.1",
    "uvicorn[standard]>=0.34.2",
    "pydantic-i18n>=0.4.5",
    "pydantic[email]>=2.11.1",
    "python-dotenv>=1.1.0",
    "httpx[socks]>=0.28.1",
    "chunkr-ai>=0.0.49",
    "docx2markdown>=0.1.1",
    "mcp-simple-arxiv>=0.2.2",
    "mcp-server-fetch>=2025.1.17",
    "xmltodict>=0.14.2",
    "firecrawl>=2.5.3",
    "pypdf2>=3.0.1",
    "loguru>=0.7.3",
    "pydash>=8.0.5",
    "qdrant-client==1.14.3",
    "slack-sdk>=3.35.0",
    "inflection>=0.5.1",
    "aiofiles>=24.1.0",
    "google-auth-httplib2>=0.2.0",
    "google-auth-oauthlib>=1.2.1",
    "google-api-python-client>=2.154.0",
]


[dependency-groups]
dev = ["babel>=2.17.0", "taskipy>=1.14.1"]

[tool.taskipy.tasks]
babel = 'pybabel compile -d lang'

[tool.ruff]
line-length = 120

[tool.ruff.lint]
extend-select = [
    "B006", # forbid def demo(mutation = [])
]
