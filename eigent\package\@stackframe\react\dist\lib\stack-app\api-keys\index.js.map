{"version": 3, "sources": ["../../../../src/lib/stack-app/api-keys/index.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { TeamApiKeysCrud, UserApiKeysCrud, teamApiKeysCreateInputSchema, userApiKeysCreateInputSchema } from \"@stackframe/stack-shared/dist/interface/crud/project-api-keys\";\nimport { filterUndefined } from \"@stackframe/stack-shared/dist/utils/objects\";\nimport { IfAndOnlyIf, PrettifyType } from \"@stackframe/stack-shared/dist/utils/types\";\nimport type * as yup from \"yup\";\n\nexport type ApiKeyType = \"user\" | \"team\";\n\nexport type ApiKey<Type extends ApiKeyType = ApiKeyType, IsFirstView extends boolean = false> =\n  & {\n      id: string,\n      description: string,\n      expiresAt?: Date,\n      manuallyRevokedAt?: Date | null,\n      createdAt: Date,\n      value: IfAndOnlyIf<IsFirstView, true, string, { lastFour: string }>,\n      update(options: ApiKeyUpdateOptions<Type>): Promise<void>,\n      revoke: () => Promise<void>,\n      isValid: () => boolean,\n      whyInvalid: () => \"manually-revoked\" | \"expired\" | null,\n    }\n  & (\n    | (\"user\" extends Type ? { type: \"user\", userId: string } : never)\n    | (\"team\" extends Type ? { type: \"team\", teamId: string } : never)\n  );\n\nexport type UserApiKeyFirstView = PrettifyType<ApiKey<\"user\", true>>;\nexport type UserApiKey = PrettifyType<ApiKey<\"user\", false>>;\n\nexport type TeamApiKeyFirstView = PrettifyType<ApiKey<\"team\", true>>;\nexport type TeamApiKey = PrettifyType<ApiKey<\"team\", false>>;\n\nexport type ApiKeyCreationOptions<Type extends ApiKeyType = ApiKeyType> =\n  & {\n    description: string,\n    expiresAt: Date | null,\n    /**\n     * Whether the API key should be considered public. A public API key will not be detected by the secret scanner, which\n     * automatically revokes API keys when it detects that they may have been exposed to the public.\n     */\n    isPublic?: boolean,\n  };\nexport function apiKeyCreationOptionsToCrud(type: \"user\", userId: string, options: ApiKeyCreationOptions<\"user\">): Promise<yup.InferType<typeof userApiKeysCreateInputSchema>>;\nexport function apiKeyCreationOptionsToCrud(type: \"team\", teamId: string, options: ApiKeyCreationOptions<\"team\">): Promise<yup.InferType<typeof teamApiKeysCreateInputSchema>>;\nexport function apiKeyCreationOptionsToCrud(type: ApiKeyType, userIdOrTeamId: string, options: ApiKeyCreationOptions): Promise<yup.InferType<typeof userApiKeysCreateInputSchema> | yup.InferType<typeof teamApiKeysCreateInputSchema>>;\nexport async function apiKeyCreationOptionsToCrud(type: ApiKeyType, userIdOrTeamId: string, options: ApiKeyCreationOptions): Promise<yup.InferType<typeof userApiKeysCreateInputSchema> | yup.InferType<typeof teamApiKeysCreateInputSchema>> {\n  return {\n    description: options.description,\n    expires_at_millis: options.expiresAt == null ? options.expiresAt : options.expiresAt.getTime(),\n    is_public: options.isPublic,\n    ...(type === \"user\" ? { user_id: userIdOrTeamId } : { team_id: userIdOrTeamId }),\n  };\n}\n\n\nexport type ApiKeyUpdateOptions<Type extends ApiKeyType = ApiKeyType> = {\n  description?: string,\n  expiresAt?: Date | null,\n  revoked?: boolean,\n};\nexport function apiKeyUpdateOptionsToCrud(type: \"user\", options: ApiKeyUpdateOptions<\"user\">): Promise<UserApiKeysCrud[\"Client\"][\"Update\"]>;\nexport function apiKeyUpdateOptionsToCrud(type: \"team\", options: ApiKeyUpdateOptions<\"team\">): Promise<TeamApiKeysCrud[\"Client\"][\"Update\"]>;\nexport function apiKeyUpdateOptionsToCrud(type: ApiKeyType, options: ApiKeyUpdateOptions): Promise<UserApiKeysCrud[\"Client\"][\"Update\"] | TeamApiKeysCrud[\"Client\"][\"Update\"]>;\nexport async function apiKeyUpdateOptionsToCrud(type: ApiKeyType, options: ApiKeyUpdateOptions): Promise<UserApiKeysCrud[\"Client\"][\"Update\"] | TeamApiKeysCrud[\"Client\"][\"Update\"]> {\n  return filterUndefined({\n    description: options.description,\n    expires_at_millis: options.expiresAt == null ? options.expiresAt : options.expiresAt.getTime(),\n    revoked: options.revoked,\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,qBAAgC;AA2ChC,eAAsB,4BAA4B,MAAkB,gBAAwB,SAAkJ;AAC5O,SAAO;AAAA,IACL,aAAa,QAAQ;AAAA,IACrB,mBAAmB,QAAQ,aAAa,OAAO,QAAQ,YAAY,QAAQ,UAAU,QAAQ;AAAA,IAC7F,WAAW,QAAQ;AAAA,IACnB,GAAI,SAAS,SAAS,EAAE,SAAS,eAAe,IAAI,EAAE,SAAS,eAAe;AAAA,EAChF;AACF;AAWA,eAAsB,0BAA0B,MAAkB,SAAkH;AAClL,aAAO,gCAAgB;AAAA,IACrB,aAAa,QAAQ;AAAA,IACrB,mBAAmB,QAAQ,aAAa,OAAO,QAAQ,YAAY,QAAQ,UAAU,QAAQ;AAAA,IAC7F,SAAS,QAAQ;AAAA,EACnB,CAAC;AACH;", "names": []}