import crc1 from './crc1.js';
import crc8 from './crc8.js';
import crc81wire from './crc81wire.js';
import crc8dvbs2 from './crc8dvbs2.js';
import crc16 from './crc16.js';
import crc16ccitt from './crc16ccitt.js';
import crc16modbus from './crc16modbus.js';
import crc16xmodem from './crc16xmodem.js';
import crc16kermit from './crc16kermit.js';
import crc24 from './crc24.js';
import crc32 from './crc32.js';
import crc32mpeg2 from './crc32mpeg2.js';
import crcjam from './crcjam.js';
export { crc1 };
export { crc8 };
export { crc81wire };
export { crc8dvbs2 };
export { crc16 };
export { crc16ccitt };
export { crc16modbus };
export { crc16xmodem };
export { crc16kermit };
export { crc24 };
export { crc32 };
export { crc32mpeg2 };
export { crcjam };
declare const _default: {
    crc1: import("./types.js").CRCModule;
    crc8: import("./types.js").CRCModule;
    crc81wire: import("./types.js").CRCModule;
    crc8dvbs2: import("./types.js").CRCModule;
    crc16: import("./types.js").CRCModule;
    crc16ccitt: import("./types.js").CRCModule;
    crc16modbus: import("./types.js").CRCModule;
    crc16xmodem: import("./types.js").CRCModule;
    crc16kermit: import("./types.js").CRCModule;
    crc24: import("./types.js").CRCModule;
    crc32: import("./types.js").CRCModule;
    crc32mpeg2: import("./types.js").CRCModule;
    crcjam: import("./types.js").CRCModule;
};
export default _default;
