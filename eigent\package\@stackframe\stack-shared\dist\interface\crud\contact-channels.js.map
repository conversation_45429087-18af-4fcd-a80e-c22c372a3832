{"version": 3, "sources": ["../../../src/interface/crud/contact-channels.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport { contactChannelIdSchema, contactChannelIsPrimarySchema, contactChannelIsVerifiedSchema, contactChannelTypeSchema, contactChannelUsedForAuthSchema, contactChannelValueSchema, userIdOrMeSchema, userIdSchema, yupMixed, yupObject } from \"../../schema-fields\";\n\nexport const contactChannelsClientReadSchema = yupObject({\n  user_id: userIdSchema.defined(),\n  id: contactChannelIdSchema.defined(),\n  value: contactChannelValueSchema.defined(),\n  type: contactChannelTypeSchema.defined(),\n  used_for_auth: contactChannelUsedForAuthSchema.defined(),\n  is_verified: contactChannelIsVerifiedSchema.defined(),\n  is_primary: contactChannelIsPrimarySchema.defined(),\n}).defined();\n\nexport const contactChannelsCrudClientUpdateSchema = yupObject({\n  value: contactChannelValueSchema.optional(),\n  type: contactChannelTypeSchema.optional(),\n  used_for_auth: contactChannelUsedForAuthSchema.optional(),\n  is_primary: contactChannelIsPrimarySchema.optional(),\n}).defined();\n\nexport const contactChannelsCrudServerUpdateSchema = contactChannelsCrudClientUpdateSchema.concat(yupObject({\n  is_verified: contactChannelIsVerifiedSchema.optional(),\n}));\n\nexport const contactChannelsCrudClientCreateSchema = yupObject({\n  user_id: userIdOrMeSchema.defined(),\n  value: contactChannelValueSchema.defined(),\n  type: contactChannelTypeSchema.defined(),\n  used_for_auth: contactChannelUsedForAuthSchema.defined(),\n  is_primary: contactChannelIsPrimarySchema.optional(),\n}).defined();\n\nexport const contactChannelsCrudServerCreateSchema = contactChannelsCrudClientCreateSchema.concat(yupObject({\n  is_verified: contactChannelIsVerifiedSchema.optional(),\n}));\n\nexport const contactChannelsCrudClientDeleteSchema = yupMixed();\n\nexport const contactChannelsCrud = createCrud({\n  clientReadSchema: contactChannelsClientReadSchema,\n  clientUpdateSchema: contactChannelsCrudClientUpdateSchema,\n  clientCreateSchema: contactChannelsCrudClientCreateSchema,\n  clientDeleteSchema: contactChannelsCrudClientDeleteSchema,\n  serverUpdateSchema: contactChannelsCrudServerUpdateSchema,\n  serverCreateSchema: contactChannelsCrudServerCreateSchema,\n  docs: {\n    clientRead: {\n      summary: \"Get a contact channel\",\n      description: \"Retrieves a specific contact channel by the user ID and the contact channel ID.\",\n      tags: [\"Contact Channels\"],\n    },\n    clientCreate: {\n      summary: \"Create a contact channel\",\n      description: \"Add a new contact channel for a user.\",\n      tags: [\"Contact Channels\"],\n    },\n    clientUpdate: {\n      summary: \"Update a contact channel\",\n      description: \"Updates an existing contact channel. Only the values provided will be updated.\",\n      tags: [\"Contact Channels\"],\n    },\n    clientDelete: {\n      summary: \"Delete a contact channel\",\n      description: \"Removes a contact channel for a given user.\",\n      tags: [\"Contact Channels\"],\n    },\n    clientList: {\n      summary: \"List contact channels\",\n      description: \"Retrieves a list of all contact channels for a user.\",\n      tags: [\"Contact Channels\"],\n    }\n  }\n});\nexport type ContactChannelsCrud = CrudTypeOf<typeof contactChannelsCrud>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAuC;AACvC,2BAAiP;AAE1O,IAAM,sCAAkC,gCAAU;AAAA,EACvD,SAAS,kCAAa,QAAQ;AAAA,EAC9B,IAAI,4CAAuB,QAAQ;AAAA,EACnC,OAAO,+CAA0B,QAAQ;AAAA,EACzC,MAAM,8CAAyB,QAAQ;AAAA,EACvC,eAAe,qDAAgC,QAAQ;AAAA,EACvD,aAAa,oDAA+B,QAAQ;AAAA,EACpD,YAAY,mDAA8B,QAAQ;AACpD,CAAC,EAAE,QAAQ;AAEJ,IAAM,4CAAwC,gCAAU;AAAA,EAC7D,OAAO,+CAA0B,SAAS;AAAA,EAC1C,MAAM,8CAAyB,SAAS;AAAA,EACxC,eAAe,qDAAgC,SAAS;AAAA,EACxD,YAAY,mDAA8B,SAAS;AACrD,CAAC,EAAE,QAAQ;AAEJ,IAAM,wCAAwC,sCAAsC,WAAO,gCAAU;AAAA,EAC1G,aAAa,oDAA+B,SAAS;AACvD,CAAC,CAAC;AAEK,IAAM,4CAAwC,gCAAU;AAAA,EAC7D,SAAS,sCAAiB,QAAQ;AAAA,EAClC,OAAO,+CAA0B,QAAQ;AAAA,EACzC,MAAM,8CAAyB,QAAQ;AAAA,EACvC,eAAe,qDAAgC,QAAQ;AAAA,EACvD,YAAY,mDAA8B,SAAS;AACrD,CAAC,EAAE,QAAQ;AAEJ,IAAM,wCAAwC,sCAAsC,WAAO,gCAAU;AAAA,EAC1G,aAAa,oDAA+B,SAAS;AACvD,CAAC,CAAC;AAEK,IAAM,4CAAwC,+BAAS;AAEvD,IAAM,0BAAsB,wBAAW;AAAA,EAC5C,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,MAAM;AAAA,IACJ,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,kBAAkB;AAAA,IAC3B;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,kBAAkB;AAAA,IAC3B;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,kBAAkB;AAAA,IAC3B;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,kBAAkB;AAAA,IAC3B;AAAA,IACA,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,kBAAkB;AAAA,IAC3B;AAAA,EACF;AACF,CAAC;", "names": []}