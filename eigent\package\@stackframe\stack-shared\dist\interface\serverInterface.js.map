{"version": 3, "sources": ["../../src/interface/serverInterface.ts"], "sourcesContent": ["import { KnownErrors } from \"../known-errors\";\nimport { AccessToken, InternalSession, RefreshToken } from \"../sessions\";\nimport { StackAssertionError } from \"../utils/errors\";\nimport { filterUndefined } from \"../utils/objects\";\nimport { Result } from \"../utils/results\";\nimport { urlString } from \"../utils/urls\";\nimport {\n  ClientInterfaceOptions,\n  StackClientInterface\n} from \"./clientInterface\";\nimport { ContactChannelsCrud } from \"./crud/contact-channels\";\nimport { CurrentUserCrud } from \"./crud/current-user\";\nimport { ConnectedAccountAccessTokenCrud } from \"./crud/oauth\";\nimport { ProjectPermissionsCrud } from \"./crud/project-permissions\";\nimport { SessionsCrud } from \"./crud/sessions\";\nimport { TeamInvitationCrud } from \"./crud/team-invitation\";\nimport { TeamMemberProfilesCrud } from \"./crud/team-member-profiles\";\nimport { TeamMembershipsCrud } from \"./crud/team-memberships\";\nimport { TeamPermissionsCrud } from \"./crud/team-permissions\";\nimport { TeamsCrud } from \"./crud/teams\";\nimport { UsersCrud } from \"./crud/users\";\n\nexport type ServerAuthApplicationOptions = (\n  & ClientInterfaceOptions\n  & (\n    | {\n      readonly secretServerKey: string,\n    }\n    | {\n      readonly projectOwnerSession: InternalSession,\n    }\n  )\n);\n\nexport class StackServerInterface extends StackClientInterface {\n  constructor(public override options: ServerAuthApplicationOptions) {\n    super(options);\n  }\n\n  protected async sendServerRequest(path: string, options: RequestInit, session: InternalSession | null, requestType: \"server\" | \"admin\" = \"server\") {\n    return await this.sendClientRequest(\n      path,\n      {\n        ...options,\n        headers: {\n          \"x-stack-secret-server-key\": \"secretServerKey\" in this.options ? this.options.secretServerKey : \"\",\n          ...options.headers,\n        },\n      },\n      session,\n      requestType,\n    );\n  }\n\n  protected async sendServerRequestAndCatchKnownError<E extends typeof KnownErrors[keyof KnownErrors]>(\n    path: string,\n    requestOptions: RequestInit,\n    tokenStoreOrNull: InternalSession | null,\n    errorsToCatch: readonly E[],\n  ): Promise<Result<\n    Response & {\n      usedTokens: {\n        accessToken: AccessToken,\n        refreshToken: RefreshToken | null,\n      } | null,\n    },\n    InstanceType<E>\n  >> {\n    try {\n      return Result.ok(await this.sendServerRequest(path, requestOptions, tokenStoreOrNull));\n    } catch (e) {\n      for (const errorType of errorsToCatch) {\n        if (errorType.isInstance(e)) {\n          return Result.error(e as InstanceType<E>);\n        }\n      }\n      throw e;\n    }\n  }\n\n  async createServerUser(data: UsersCrud['Server']['Create']): Promise<UsersCrud['Server']['Read']> {\n    const response = await this.sendServerRequest(\n      \"/users\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async getServerUserByToken(session: InternalSession): Promise<CurrentUserCrud['Server']['Read'] | null> {\n    const responseOrError = await this.sendServerRequestAndCatchKnownError(\n      \"/users/me\",\n      {},\n      session,\n      [KnownErrors.CannotGetOwnUserWithoutUser],\n    );\n    if (responseOrError.status === \"error\") {\n      if (KnownErrors.CannotGetOwnUserWithoutUser.isInstance(responseOrError.error)) {\n        return null;\n      } else {\n        throw new StackAssertionError(\"Unexpected uncaught error\", { cause: responseOrError.error });\n      }\n    }\n    const response = responseOrError.data;\n    const user: CurrentUserCrud['Server']['Read'] = await response.json();\n    if (!(user as any)) throw new StackAssertionError(\"User endpoint returned null; this should never happen\");\n    return user;\n  }\n\n  async getServerUserById(userId: string): Promise<Result<UsersCrud['Server']['Read']>> {\n    const responseOrError = await this.sendServerRequestAndCatchKnownError(\n      urlString`/users/${userId}`,\n      {},\n      null,\n      [KnownErrors.UserNotFound],\n    );\n    if (responseOrError.status === \"error\") {\n      return Result.error(responseOrError.error);\n    }\n    const user: UsersCrud['Server']['Read'] = await responseOrError.data.json();\n    return Result.ok(user);\n  }\n\n  async listServerTeamInvitations(options: {\n    teamId: string,\n  }): Promise<TeamInvitationCrud['Server']['Read'][]> {\n    const response = await this.sendServerRequest(\n      urlString`/team-invitations?team_id=${options.teamId}`,\n      {},\n      null,\n    );\n    const result = await response.json() as TeamInvitationCrud['Server']['List'];\n    return result.items;\n  }\n\n  async revokeServerTeamInvitation(invitationId: string, teamId: string) {\n    await this.sendServerRequest(\n      urlString`/team-invitations/${invitationId}?team_id=${teamId}`,\n      { method: \"DELETE\" },\n      null,\n    );\n  }\n\n  async listServerTeamMemberProfiles(\n    options: {\n      teamId: string,\n    },\n  ): Promise<TeamMemberProfilesCrud['Server']['Read'][]> {\n    const response = await this.sendServerRequest(\n      urlString`/team-member-profiles?team_id=${options.teamId}`,\n      {},\n      null,\n    );\n    const result = await response.json() as TeamMemberProfilesCrud['Server']['List'];\n    return result.items;\n  }\n\n  async getServerTeamMemberProfile(\n    options: {\n      teamId: string,\n      userId: string,\n    },\n  ): Promise<TeamMemberProfilesCrud['Client']['Read']> {\n    const response = await this.sendServerRequest(\n      urlString`/team-member-profiles/${options.teamId}/${options.userId}`,\n      {},\n      null,\n    );\n    return await response.json();\n  }\n\n  async listServerTeamPermissions(\n    options: {\n      userId?: string,\n      teamId?: string,\n      recursive: boolean,\n    },\n    session: InternalSession | null,\n  ): Promise<TeamPermissionsCrud['Server']['Read'][]> {\n    const response = await this.sendServerRequest(\n      `/team-permissions?${new URLSearchParams(filterUndefined({\n        user_id: options.userId,\n        team_id: options.teamId,\n        recursive: options.recursive.toString(),\n      }))}`,\n      {},\n      session,\n    );\n    const result = await response.json() as TeamPermissionsCrud['Server']['List'];\n    return result.items;\n  }\n\n  async listServerProjectPermissions(\n    options: {\n      userId?: string,\n      recursive: boolean,\n    },\n    session: InternalSession | null,\n  ): Promise<ProjectPermissionsCrud['Server']['Read'][]> {\n    const response = await this.sendServerRequest(\n      `/project-permissions?${new URLSearchParams(filterUndefined({\n        user_id: options.userId,\n        recursive: options.recursive.toString(),\n      }))}`,\n      {},\n      session,\n    );\n    const result = await response.json() as ProjectPermissionsCrud['Server']['List'];\n    return result.items;\n  }\n\n  async listServerUsers(options: {\n    cursor?: string,\n    limit?: number,\n    orderBy?: 'signedUpAt',\n    desc?: boolean,\n    query?: string,\n  }): Promise<UsersCrud['Server']['List']> {\n    const searchParams = new URLSearchParams(filterUndefined({\n      cursor: options.cursor,\n      limit: options.limit?.toString(),\n      desc: options.desc?.toString(),\n      ...options.orderBy ? {\n        order_by: {\n          signedUpAt: \"signed_up_at\",\n        }[options.orderBy],\n      } : {},\n      ...options.query ? {\n        query: options.query,\n      } : {},\n    }));\n    const response = await this.sendServerRequest(\"/users?\" + searchParams.toString(), {}, null);\n    return await response.json();\n  }\n\n  async listServerTeams(options?: {\n    userId?: string,\n  }): Promise<TeamsCrud['Server']['Read'][]> {\n    const response = await this.sendServerRequest(\n      `/teams?${new URLSearchParams(filterUndefined({\n        user_id: options?.userId,\n      }))}`,\n      {},\n      null\n    );\n    const result = await response.json() as TeamsCrud['Server']['List'];\n    return result.items;\n  }\n\n  async getServerTeam(teamId: string): Promise<TeamsCrud['Server']['Read']> {\n    const response = await this.sendServerRequest(\n      `/teams/${teamId}`,\n      {},\n      null\n    );\n    return await response.json();\n  }\n\n  async listServerTeamUsers(teamId: string): Promise<UsersCrud['Server']['Read'][]> {\n    const response = await this.sendServerRequest(`/users?team_id=${teamId}`, {}, null);\n    const result = await response.json() as UsersCrud['Server']['List'];\n    return result.items;\n  }\n\n  /* when passing a session, the user will be added to the team */\n  async createServerTeam(data: TeamsCrud['Server']['Create']): Promise<TeamsCrud['Server']['Read']> {\n    const response = await this.sendServerRequest(\n      \"/teams\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null\n    );\n    return await response.json();\n  }\n\n  async updateServerTeam(teamId: string, data: TeamsCrud['Server']['Update']): Promise<TeamsCrud['Server']['Read']> {\n    const response = await this.sendServerRequest(\n      urlString`/teams/${teamId}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async deleteServerTeam(teamId: string): Promise<void> {\n    await this.sendServerRequest(\n      urlString`/teams/${teamId}`,\n      { method: \"DELETE\" },\n      null,\n    );\n  }\n\n  async addServerUserToTeam(options: {\n    userId: string,\n    teamId: string,\n  }): Promise<TeamMembershipsCrud['Server']['Read']> {\n    const response = await this.sendServerRequest(\n      urlString`/team-memberships/${options.teamId}/${options.userId}`,\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async removeServerUserFromTeam(options: {\n    userId: string,\n    teamId: string,\n  }) {\n    await this.sendServerRequest(\n      urlString`/team-memberships/${options.teamId}/${options.userId}`,\n      {\n        method: \"DELETE\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      null,\n    );\n  }\n\n  async updateServerUser(userId: string, update: UsersCrud['Server']['Update']): Promise<UsersCrud['Server']['Read']> {\n    const response = await this.sendServerRequest(\n      urlString`/users/${userId}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(update),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async createServerProviderAccessToken(\n    userId: string,\n    provider: string,\n    scope: string,\n  ): Promise<ConnectedAccountAccessTokenCrud['Server']['Read']> {\n    const response = await this.sendServerRequest(\n      urlString`/connected-accounts/${userId}/${provider}/access-token`,\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({ scope }),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async createServerUserSession(userId: string, expiresInMillis: number, isImpersonation: boolean): Promise<{ accessToken: string, refreshToken: string }> {\n    const response = await this.sendServerRequest(\n      \"/auth/sessions\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          user_id: userId,\n          expires_in_millis: expiresInMillis,\n          is_impersonation: isImpersonation,\n        }),\n      },\n      null,\n    );\n    const result = await response.json();\n    return {\n      accessToken: result.access_token,\n      refreshToken: result.refresh_token,\n    };\n  }\n\n  async leaveServerTeam(\n    options: {\n      teamId: string,\n      userId: string,\n    },\n  ) {\n    await this.sendClientRequest(\n      urlString`/team-memberships/${options.teamId}/${options.userId}`,\n      {\n        method: \"DELETE\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      null,\n    );\n  }\n\n  async updateServerTeamMemberProfile(options: {\n    teamId: string,\n    userId: string,\n    profile: TeamMemberProfilesCrud['Server']['Update'],\n  }) {\n    await this.sendServerRequest(\n      urlString`/team-member-profiles/${options.teamId}/${options.userId}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(options.profile),\n      },\n      null,\n    );\n  }\n\n  async grantServerTeamUserPermission(teamId: string, userId: string, permissionId: string) {\n    await this.sendServerRequest(\n      urlString`/team-permissions/${teamId}/${userId}/${permissionId}`,\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      null,\n    );\n  }\n\n  async grantServerProjectPermission(userId: string, permissionId: string) {\n    await this.sendServerRequest(\n      urlString`/project-permissions/${userId}/${permissionId}`,\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      null,\n    );\n  }\n\n  async revokeServerTeamUserPermission(teamId: string, userId: string, permissionId: string) {\n    await this.sendServerRequest(\n      urlString`/team-permissions/${teamId}/${userId}/${permissionId}`,\n      {\n        method: \"DELETE\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      null,\n    );\n  }\n\n  async revokeServerProjectPermission(userId: string, permissionId: string) {\n    await this.sendServerRequest(\n      urlString`/project-permissions/${userId}/${permissionId}`,\n      {\n        method: \"DELETE\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      null,\n    );\n  }\n\n  async deleteServerUser(userId: string) {\n    await this.sendServerRequest(\n      urlString`/users/${userId}`,\n      {\n        method: \"DELETE\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      null,\n    );\n  }\n\n  async createServerContactChannel(\n    data: ContactChannelsCrud['Server']['Create'],\n  ): Promise<ContactChannelsCrud['Server']['Read']> {\n    const response = await this.sendServerRequest(\n      \"/contact-channels\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async updateServerContactChannel(\n    userId: string,\n    contactChannelId: string,\n    data: ContactChannelsCrud['Server']['Update'],\n  ): Promise<ContactChannelsCrud['Server']['Read']> {\n    const response = await this.sendServerRequest(\n      urlString`/contact-channels/${userId}/${contactChannelId}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async deleteServerContactChannel(\n    userId: string,\n    contactChannelId: string,\n  ): Promise<void> {\n    await this.sendServerRequest(\n      urlString`/contact-channels/${userId}/${contactChannelId}`,\n      {\n        method: \"DELETE\",\n      },\n      null,\n    );\n  }\n\n  async listServerContactChannels(\n    userId: string,\n  ): Promise<ContactChannelsCrud['Server']['Read'][]> {\n    const response = await this.sendServerRequest(\n      urlString`/contact-channels?user_id=${userId}`,\n      {\n        method: \"GET\",\n      },\n      null,\n    );\n    const json = await response.json() as ContactChannelsCrud['Server']['List'];\n    return json.items;\n  }\n\n  async sendServerContactChannelVerificationEmail(\n    userId: string,\n    contactChannelId: string,\n    callbackUrl: string,\n  ): Promise<void> {\n    await this.sendServerRequest(\n      urlString`/contact-channels/${userId}/${contactChannelId}/send-verification-code`,\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({ callback_url: callbackUrl }),\n      },\n      null,\n    );\n  }\n\n\n  async listServerSessions(userId: string): Promise<SessionsCrud['Server']['Read'][]> {\n    const response = await this.sendServerRequest(\n      urlString`/auth/sessions?user_id=${userId}`,\n      {\n        method: \"GET\",\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async deleteServerSession(sessionId: string) {\n    await this.sendServerRequest(\n      urlString`/auth/sessions/${sessionId}`,\n      {\n        method: \"DELETE\",\n      },\n      null,\n    );\n  }\n\n\n  async sendServerTeamInvitation(options: {\n    email: string,\n    teamId: string,\n    callbackUrl: string,\n  }): Promise<void> {\n    await this.sendServerRequest(\n      \"/team-invitations/send-code\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          email: options.email,\n          team_id: options.teamId,\n          callback_url: options.callbackUrl,\n        }),\n      },\n      null,\n    );\n  }\n\n  async updatePassword(\n    options: { oldPassword: string, newPassword: string },\n  ): Promise<KnownErrors[\"PasswordConfirmationMismatch\"] | KnownErrors[\"PasswordRequirementsNotMet\"] | undefined> {\n    const res = await this.sendServerRequestAndCatchKnownError(\n      \"/auth/password/update\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          old_password: options.oldPassword,\n          new_password: options.newPassword,\n        }),\n      },\n      null,\n      [KnownErrors.PasswordConfirmationMismatch, KnownErrors.PasswordRequirementsNotMet]\n    );\n\n    if (res.status === \"error\") {\n      return res.error;\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAA4B;AAE5B,oBAAoC;AACpC,qBAAgC;AAChC,qBAAuB;AACvB,kBAA0B;AAC1B,6BAGO;AAyBA,IAAM,uBAAN,cAAmC,4CAAqB;AAAA,EAC7D,YAA4B,SAAuC;AACjE,UAAM,OAAO;AADa;AAAA,EAE5B;AAAA,EAEA,MAAgB,kBAAkB,MAAc,SAAsB,SAAiC,cAAkC,UAAU;AACjJ,WAAO,MAAM,KAAK;AAAA,MAChB;AAAA,MACA;AAAA,QACE,GAAG;AAAA,QACH,SAAS;AAAA,UACP,6BAA6B,qBAAqB,KAAK,UAAU,KAAK,QAAQ,kBAAkB;AAAA,UAChG,GAAG,QAAQ;AAAA,QACb;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAgB,oCACd,MACA,gBACA,kBACA,eASC;AACD,QAAI;AACF,aAAO,sBAAO,GAAG,MAAM,KAAK,kBAAkB,MAAM,gBAAgB,gBAAgB,CAAC;AAAA,IACvF,SAAS,GAAG;AACV,iBAAW,aAAa,eAAe;AACrC,YAAI,UAAU,WAAW,CAAC,GAAG;AAC3B,iBAAO,sBAAO,MAAM,CAAoB;AAAA,QAC1C;AAAA,MACF;AACA,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAM,iBAAiB,MAA2E;AAChG,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,qBAAqB,SAA6E;AACtG,UAAM,kBAAkB,MAAM,KAAK;AAAA,MACjC;AAAA,MACA,CAAC;AAAA,MACD;AAAA,MACA,CAAC,gCAAY,2BAA2B;AAAA,IAC1C;AACA,QAAI,gBAAgB,WAAW,SAAS;AACtC,UAAI,gCAAY,4BAA4B,WAAW,gBAAgB,KAAK,GAAG;AAC7E,eAAO;AAAA,MACT,OAAO;AACL,cAAM,IAAI,kCAAoB,6BAA6B,EAAE,OAAO,gBAAgB,MAAM,CAAC;AAAA,MAC7F;AAAA,IACF;AACA,UAAM,WAAW,gBAAgB;AACjC,UAAM,OAA0C,MAAM,SAAS,KAAK;AACpE,QAAI,CAAE,KAAc,OAAM,IAAI,kCAAoB,uDAAuD;AACzG,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,kBAAkB,QAA8D;AACpF,UAAM,kBAAkB,MAAM,KAAK;AAAA,MACjC,+BAAmB,MAAM;AAAA,MACzB,CAAC;AAAA,MACD;AAAA,MACA,CAAC,gCAAY,YAAY;AAAA,IAC3B;AACA,QAAI,gBAAgB,WAAW,SAAS;AACtC,aAAO,sBAAO,MAAM,gBAAgB,KAAK;AAAA,IAC3C;AACA,UAAM,OAAoC,MAAM,gBAAgB,KAAK,KAAK;AAC1E,WAAO,sBAAO,GAAG,IAAI;AAAA,EACvB;AAAA,EAEA,MAAM,0BAA0B,SAEoB;AAClD,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,kDAAsC,QAAQ,MAAM;AAAA,MACpD,CAAC;AAAA,MACD;AAAA,IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;AAAA,EAChB;AAAA,EAEA,MAAM,2BAA2B,cAAsB,QAAgB;AACrE,UAAM,KAAK;AAAA,MACT,0CAA8B,YAAY,YAAY,MAAM;AAAA,MAC5D,EAAE,QAAQ,SAAS;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,6BACJ,SAGqD;AACrD,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,sDAA0C,QAAQ,MAAM;AAAA,MACxD,CAAC;AAAA,MACD;AAAA,IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;AAAA,EAChB;AAAA,EAEA,MAAM,2BACJ,SAImD;AACnD,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,8CAAkC,QAAQ,MAAM,IAAI,QAAQ,MAAM;AAAA,MAClE,CAAC;AAAA,MACD;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,0BACJ,SAKA,SACkD;AAClD,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,qBAAqB,IAAI,oBAAgB,gCAAgB;AAAA,QACvD,SAAS,QAAQ;AAAA,QACjB,SAAS,QAAQ;AAAA,QACjB,WAAW,QAAQ,UAAU,SAAS;AAAA,MACxC,CAAC,CAAC,CAAC;AAAA,MACH,CAAC;AAAA,MACD;AAAA,IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;AAAA,EAChB;AAAA,EAEA,MAAM,6BACJ,SAIA,SACqD;AACrD,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,wBAAwB,IAAI,oBAAgB,gCAAgB;AAAA,QAC1D,SAAS,QAAQ;AAAA,QACjB,WAAW,QAAQ,UAAU,SAAS;AAAA,MACxC,CAAC,CAAC,CAAC;AAAA,MACH,CAAC;AAAA,MACD;AAAA,IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;AAAA,EAChB;AAAA,EAEA,MAAM,gBAAgB,SAMmB;AACvC,UAAM,eAAe,IAAI,oBAAgB,gCAAgB;AAAA,MACvD,QAAQ,QAAQ;AAAA,MAChB,OAAO,QAAQ,OAAO,SAAS;AAAA,MAC/B,MAAM,QAAQ,MAAM,SAAS;AAAA,MAC7B,GAAG,QAAQ,UAAU;AAAA,QACnB,UAAU;AAAA,UACR,YAAY;AAAA,QACd,EAAE,QAAQ,OAAO;AAAA,MACnB,IAAI,CAAC;AAAA,MACL,GAAG,QAAQ,QAAQ;AAAA,QACjB,OAAO,QAAQ;AAAA,MACjB,IAAI,CAAC;AAAA,IACP,CAAC,CAAC;AACF,UAAM,WAAW,MAAM,KAAK,kBAAkB,YAAY,aAAa,SAAS,GAAG,CAAC,GAAG,IAAI;AAC3F,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,gBAAgB,SAEqB;AACzC,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,UAAU,IAAI,oBAAgB,gCAAgB;AAAA,QAC5C,SAAS,SAAS;AAAA,MACpB,CAAC,CAAC,CAAC;AAAA,MACH,CAAC;AAAA,MACD;AAAA,IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;AAAA,EAChB;AAAA,EAEA,MAAM,cAAc,QAAsD;AACxE,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,UAAU,MAAM;AAAA,MAChB,CAAC;AAAA,MACD;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,oBAAoB,QAAwD;AAChF,UAAM,WAAW,MAAM,KAAK,kBAAkB,kBAAkB,MAAM,IAAI,CAAC,GAAG,IAAI;AAClF,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;AAAA,EAChB;AAAA;AAAA,EAGA,MAAM,iBAAiB,MAA2E;AAChG,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,iBAAiB,QAAgB,MAA2E;AAChH,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,+BAAmB,MAAM;AAAA,MACzB;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,iBAAiB,QAA+B;AACpD,UAAM,KAAK;AAAA,MACT,+BAAmB,MAAM;AAAA,MACzB,EAAE,QAAQ,SAAS;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,oBAAoB,SAGyB;AACjD,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,0CAA8B,QAAQ,MAAM,IAAI,QAAQ,MAAM;AAAA,MAC9D;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,MACzB;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,yBAAyB,SAG5B;AACD,UAAM,KAAK;AAAA,MACT,0CAA8B,QAAQ,MAAM,IAAI,QAAQ,MAAM;AAAA,MAC9D;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,MACzB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,iBAAiB,QAAgB,QAA6E;AAClH,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,+BAAmB,MAAM;AAAA,MACzB;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,MAAM;AAAA,MAC7B;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,gCACJ,QACA,UACA,OAC4D;AAC5D,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,4CAAgC,MAAM,IAAI,QAAQ;AAAA,MAClD;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,EAAE,MAAM,CAAC;AAAA,MAChC;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,wBAAwB,QAAgB,iBAAyB,iBAAkF;AACvJ,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,SAAS;AAAA,UACT,mBAAmB;AAAA,UACnB,kBAAkB;AAAA,QACpB,CAAC;AAAA,MACH;AAAA,MACA;AAAA,IACF;AACA,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO;AAAA,MACL,aAAa,OAAO;AAAA,MACpB,cAAc,OAAO;AAAA,IACvB;AAAA,EACF;AAAA,EAEA,MAAM,gBACJ,SAIA;AACA,UAAM,KAAK;AAAA,MACT,0CAA8B,QAAQ,MAAM,IAAI,QAAQ,MAAM;AAAA,MAC9D;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,MACzB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,8BAA8B,SAIjC;AACD,UAAM,KAAK;AAAA,MACT,8CAAkC,QAAQ,MAAM,IAAI,QAAQ,MAAM;AAAA,MAClE;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,QAAQ,OAAO;AAAA,MACtC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,8BAA8B,QAAgB,QAAgB,cAAsB;AACxF,UAAM,KAAK;AAAA,MACT,0CAA8B,MAAM,IAAI,MAAM,IAAI,YAAY;AAAA,MAC9D;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,MACzB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,6BAA6B,QAAgB,cAAsB;AACvE,UAAM,KAAK;AAAA,MACT,6CAAiC,MAAM,IAAI,YAAY;AAAA,MACvD;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,MACzB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,+BAA+B,QAAgB,QAAgB,cAAsB;AACzF,UAAM,KAAK;AAAA,MACT,0CAA8B,MAAM,IAAI,MAAM,IAAI,YAAY;AAAA,MAC9D;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,MACzB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,8BAA8B,QAAgB,cAAsB;AACxE,UAAM,KAAK;AAAA,MACT,6CAAiC,MAAM,IAAI,YAAY;AAAA,MACvD;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,MACzB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,iBAAiB,QAAgB;AACrC,UAAM,KAAK;AAAA,MACT,+BAAmB,MAAM;AAAA,MACzB;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,MACzB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,2BACJ,MACgD;AAChD,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,2BACJ,QACA,kBACA,MACgD;AAChD,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,0CAA8B,MAAM,IAAI,gBAAgB;AAAA,MACxD;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,2BACJ,QACA,kBACe;AACf,UAAM,KAAK;AAAA,MACT,0CAA8B,MAAM,IAAI,gBAAgB;AAAA,MACxD;AAAA,QACE,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,0BACJ,QACkD;AAClD,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,kDAAsC,MAAM;AAAA,MAC5C;AAAA,QACE,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,IACF;AACA,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,MAAM,0CACJ,QACA,kBACA,aACe;AACf,UAAM,KAAK;AAAA,MACT,0CAA8B,MAAM,IAAI,gBAAgB;AAAA,MACxD;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,EAAE,cAAc,YAAY,CAAC;AAAA,MACpD;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAGA,MAAM,mBAAmB,QAA2D;AAClF,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,+CAAmC,MAAM;AAAA,MACzC;AAAA,QACE,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,oBAAoB,WAAmB;AAC3C,UAAM,KAAK;AAAA,MACT,uCAA2B,SAAS;AAAA,MACpC;AAAA,QACE,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAGA,MAAM,yBAAyB,SAIb;AAChB,UAAM,KAAK;AAAA,MACT;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,OAAO,QAAQ;AAAA,UACf,SAAS,QAAQ;AAAA,UACjB,cAAc,QAAQ;AAAA,QACxB,CAAC;AAAA,MACH;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,eACJ,SAC8G;AAC9G,UAAM,MAAM,MAAM,KAAK;AAAA,MACrB;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,cAAc,QAAQ;AAAA,UACtB,cAAc,QAAQ;AAAA,QACxB,CAAC;AAAA,MACH;AAAA,MACA;AAAA,MACA,CAAC,gCAAY,8BAA8B,gCAAY,0BAA0B;AAAA,IACnF;AAEA,QAAI,IAAI,WAAW,SAAS;AAC1B,aAAO,IAAI;AAAA,IACb;AAAA,EACF;AACF;", "names": []}