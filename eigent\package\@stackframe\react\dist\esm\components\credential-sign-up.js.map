{"version": 3, "sources": ["../../../src/components/credential-sign-up.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { yupResolver } from \"@hookform/resolvers/yup\";\nimport { getPasswordError } from \"@stackframe/stack-shared/dist/helpers/password\";\nimport { passwordSchema, strictEmailSchema, yupObject } from \"@stackframe/stack-shared/dist/schema-fields\";\nimport { runAsynchronously, runAsynchronouslyWithAlert } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { Button, Input, Label, PasswordInput } from \"@stackframe/stack-ui\";\nimport { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport * as yup from \"yup\";\nimport { useStackApp } from \"..\";\nimport { useTranslation } from \"../lib/translations\";\nimport { FormWarningText } from \"./elements/form-warning\";\n\nexport function CredentialSignUp(props: { noPasswordRepeat?: boolean }) {\n  const { t } = useTranslation();\n\n  const schema = yupObject({\n    email: strictEmailSchema(t('Please enter a valid email')).defined().nonEmpty(t('Please enter your email')),\n    password: passwordSchema.defined().nonEmpty(t('Please enter your password')).test({\n      name: 'is-valid-password',\n      test: (value, ctx) => {\n        const error = getPasswordError(value);\n        if (error) {\n          return ctx.createError({ message: error.message });\n        } else {\n          return true;\n        }\n      }\n    }),\n    ...(!props.noPasswordRepeat && {\n      passwordRepeat: passwordSchema.nullable().oneOf([yup.ref('password'), \"\", null], t('Passwords do not match')).nonEmpty(t('Please repeat your password'))\n    })\n  });\n\n  const { register, handleSubmit, setError, formState: { errors }, clearErrors } = useForm({\n    resolver: yupResolver(schema)\n  });\n  const app = useStackApp();\n  const [loading, setLoading] = useState(false);\n\n  const onSubmit = async (data: yup.InferType<typeof schema>) => {\n    setLoading(true);\n    try {\n      const { email, password } = data;\n      const result = await app.signUpWithCredential({ email, password });\n      if (result.status === 'error') {\n        setError('email', { type: 'manual', message: result.error.message });\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const registerPassword = register('password');\n  const registerPasswordRepeat = register('passwordRepeat');\n\n  return (\n    <form\n      className=\"flex flex-col items-stretch stack-scope\"\n      onSubmit={e => runAsynchronouslyWithAlert(handleSubmit(onSubmit)(e))}\n      noValidate\n    >\n      <Label htmlFor=\"email\" className=\"mb-1\">{t('Email')}</Label>\n      <Input id=\"email\" type=\"email\" autoComplete=\"email\" {...register('email')}/>\n      <FormWarningText text={errors.email?.message?.toString()} />\n\n      <Label htmlFor=\"password\" className=\"mt-4 mb-1\">{t('Password')}</Label>\n      <PasswordInput\n        id=\"password\"\n        autoComplete=\"new-password\"\n        {...registerPassword}\n        onChange={(e) => {\n          clearErrors('password');\n          clearErrors('passwordRepeat');\n          runAsynchronously(registerPassword.onChange(e));\n        }}\n      />\n      <FormWarningText text={errors.password?.message?.toString()} />\n      {\n        !props.noPasswordRepeat && (\n          <>\n            <Label htmlFor=\"repeat-password\" className=\"mt-4 mb-1\">{t('Repeat Password')}</Label>\n            <PasswordInput\n              id=\"repeat-password\"\n              {...registerPasswordRepeat}\n              onChange={(e) => {\n              clearErrors('password');\n              clearErrors('passwordRepeat');\n              runAsynchronously(registerPasswordRepeat.onChange(e));\n              }}\n            />\n            <FormWarningText text={errors.passwordRepeat?.message?.toString()} />\n          </>\n        )\n      }\n\n      <Button type=\"submit\" className=\"mt-6\" loading={loading}>\n        {t('Sign Up')}\n      </Button>\n    </form>\n  );\n}\n"], "mappings": ";;;AAOA,SAAS,mBAAmB;AAC5B,SAAS,wBAAwB;AACjC,SAAS,gBAAgB,mBAAmB,iBAAiB;AAC7D,SAAS,mBAAmB,kCAAkC;AAC9D,SAAS,QAAQ,OAAO,OAAO,qBAAqB;AACpD,SAAS,gBAAgB;AACzB,SAAS,eAAe;AACxB,YAAY,SAAS;AACrB,SAAS,mBAAmB;AAC5B,SAAS,sBAAsB;AAC/B,SAAS,uBAAuB;AAmD1B,SAkBI,UAlBJ,KAkBI,YAlBJ;AAjDC,SAAS,iBAAiB,OAAuC;AACtE,QAAM,EAAE,EAAE,IAAI,eAAe;AAE7B,QAAM,SAAS,UAAU;AAAA,IACvB,OAAO,kBAAkB,EAAE,4BAA4B,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,yBAAyB,CAAC;AAAA,IACzG,UAAU,eAAe,QAAQ,EAAE,SAAS,EAAE,4BAA4B,CAAC,EAAE,KAAK;AAAA,MAChF,MAAM;AAAA,MACN,MAAM,CAAC,OAAO,QAAQ;AACpB,cAAM,QAAQ,iBAAiB,KAAK;AACpC,YAAI,OAAO;AACT,iBAAO,IAAI,YAAY,EAAE,SAAS,MAAM,QAAQ,CAAC;AAAA,QACnD,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF,CAAC;AAAA,IACD,GAAI,CAAC,MAAM,oBAAoB;AAAA,MAC7B,gBAAgB,eAAe,SAAS,EAAE,MAAM,CAAK,QAAI,UAAU,GAAG,IAAI,IAAI,GAAG,EAAE,wBAAwB,CAAC,EAAE,SAAS,EAAE,6BAA6B,CAAC;AAAA,IACzJ;AAAA,EACF,CAAC;AAED,QAAM,EAAE,UAAU,cAAc,UAAU,WAAW,EAAE,OAAO,GAAG,YAAY,IAAI,QAAQ;AAAA,IACvF,UAAU,YAAY,MAAM;AAAA,EAC9B,CAAC;AACD,QAAM,MAAM,YAAY;AACxB,QAAM,CAAC,SAAS,UAAU,IAAI,SAAS,KAAK;AAE5C,QAAM,WAAW,OAAO,SAAuC;AAC7D,eAAW,IAAI;AACf,QAAI;AACF,YAAM,EAAE,OAAO,SAAS,IAAI;AAC5B,YAAM,SAAS,MAAM,IAAI,qBAAqB,EAAE,OAAO,SAAS,CAAC;AACjE,UAAI,OAAO,WAAW,SAAS;AAC7B,iBAAS,SAAS,EAAE,MAAM,UAAU,SAAS,OAAO,MAAM,QAAQ,CAAC;AAAA,MACrE;AAAA,IACF,UAAE;AACA,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AAEA,QAAM,mBAAmB,SAAS,UAAU;AAC5C,QAAM,yBAAyB,SAAS,gBAAgB;AAExD,SACE;AAAA,IAAC;AAAA;AAAA,MACC,WAAU;AAAA,MACV,UAAU,OAAK,2BAA2B,aAAa,QAAQ,EAAE,CAAC,CAAC;AAAA,MACnE,YAAU;AAAA,MAEV;AAAA,4BAAC,SAAM,SAAQ,SAAQ,WAAU,QAAQ,YAAE,OAAO,GAAE;AAAA,QACpD,oBAAC,SAAM,IAAG,SAAQ,MAAK,SAAQ,cAAa,SAAS,GAAG,SAAS,OAAO,GAAE;AAAA,QAC1E,oBAAC,mBAAgB,MAAM,OAAO,OAAO,SAAS,SAAS,GAAG;AAAA,QAE1D,oBAAC,SAAM,SAAQ,YAAW,WAAU,aAAa,YAAE,UAAU,GAAE;AAAA,QAC/D;AAAA,UAAC;AAAA;AAAA,YACC,IAAG;AAAA,YACH,cAAa;AAAA,YACZ,GAAG;AAAA,YACJ,UAAU,CAAC,MAAM;AACf,0BAAY,UAAU;AACtB,0BAAY,gBAAgB;AAC5B,gCAAkB,iBAAiB,SAAS,CAAC,CAAC;AAAA,YAChD;AAAA;AAAA,QACF;AAAA,QACA,oBAAC,mBAAgB,MAAM,OAAO,UAAU,SAAS,SAAS,GAAG;AAAA,QAE3D,CAAC,MAAM,oBACL,iCACE;AAAA,8BAAC,SAAM,SAAQ,mBAAkB,WAAU,aAAa,YAAE,iBAAiB,GAAE;AAAA,UAC7E;AAAA,YAAC;AAAA;AAAA,cACC,IAAG;AAAA,cACF,GAAG;AAAA,cACJ,UAAU,CAAC,MAAM;AACjB,4BAAY,UAAU;AACtB,4BAAY,gBAAgB;AAC5B,kCAAkB,uBAAuB,SAAS,CAAC,CAAC;AAAA,cACpD;AAAA;AAAA,UACF;AAAA,UACA,oBAAC,mBAAgB,MAAM,OAAO,gBAAgB,SAAS,SAAS,GAAG;AAAA,WACrE;AAAA,QAIJ,oBAAC,UAAO,MAAK,UAAS,WAAU,QAAO,SACpC,YAAE,SAAS,GACd;AAAA;AAAA;AAAA,EACF;AAEJ;", "names": []}