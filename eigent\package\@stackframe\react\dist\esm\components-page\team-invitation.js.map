{"version": 3, "sources": ["../../../src/components-page/team-invitation.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { KnownErrors } from \"@stackframe/stack-shared\";\nimport { cacheFunction } from \"@stackframe/stack-shared/dist/utils/caches\";\nimport { runAsynchronouslyWithAlert } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { Typography } from \"@stackframe/stack-ui\";\nimport React from \"react\";\nimport { MessageCard, StackClientApp, useStackApp, useUser } from \"..\";\nimport { PredefinedMessageCard } from \"../components/message-cards/predefined-message-card\";\nimport { useTranslation } from \"../lib/translations\";\n\nconst cachedVerifyInvitation = cacheFunction(async (stackApp: StackClientApp<true>, code: string) => {\n  return await stackApp.verifyTeamInvitationCode(code);\n});\n\nconst cachedGetInvitationDetails = cacheFunction(async (stackApp: StackClientApp<true>, code: string) => {\n  return await stackApp.getTeamInvitationDetails(code);\n});\n\nfunction TeamInvitationInner(props: { fullPage?: boolean, searchParams: Record<string, string> }) {\n  const { t } = useTranslation();\n  const stackApp = useStackApp();\n  const [success, setSuccess] = React.useState(false);\n  const [errorMessage, setErrorMessage] = React.useState<string | null>(null);\n  const details = React.use(cachedGetInvitationDetails(stackApp, props.searchParams.code || ''));\n\n  if (errorMessage || details.status === 'error') {\n    return (\n      <PredefinedMessageCard type=\"unknownError\" fullPage={props.fullPage} />\n    );\n  }\n\n  if (success) {\n    return (\n      <MessageCard\n        title={t('Team invitation')}\n        fullPage={props.fullPage}\n        primaryButtonText=\"Go home\"\n        primaryAction={() => stackApp.redirectToHome()}\n      >\n        <Typography>You have successfully joined {details.data.teamDisplayName}</Typography>\n      </MessageCard>\n    );\n  }\n\n\n  return (\n    <MessageCard\n      title={t('Team invitation')}\n      fullPage={props.fullPage}\n      primaryButtonText={t('Join')}\n      primaryAction={() => runAsynchronouslyWithAlert(async () => {\n        const result = await stackApp.acceptTeamInvitation(props.searchParams.code || '');\n        if (result.status === 'error') {\n        setErrorMessage(result.error.message);\n        } else {\n        setSuccess(true);\n        }\n      })}\n      secondaryButtonText={t('Ignore')}\n      secondaryAction={() => stackApp.redirectToHome()}\n    >\n      <Typography>You are invited to join {details.data.teamDisplayName}</Typography>\n    </MessageCard>\n  );\n}\n\nexport function TeamInvitation({ fullPage=false, searchParams }: { fullPage?: boolean, searchParams: Record<string, string> }) {\n  const { t } = useTranslation();\n  const user = useUser();\n  const stackApp = useStackApp();\n\n  const invalidJsx = (\n    <MessageCard title={t('Invalid Team Invitation Link')} fullPage={fullPage}>\n      <Typography>{t('Please double check if you have the correct team invitation link.')}</Typography>\n    </MessageCard>\n  );\n\n  const expiredJsx = (\n    <MessageCard title={t('Expired Team Invitation Link')} fullPage={fullPage}>\n      <Typography>{t('Your team invitation link has expired. Please request a new team invitation link ')}</Typography>\n    </MessageCard>\n  );\n\n  const usedJsx = (\n    <MessageCard title={t('Used Team Invitation Link')} fullPage={fullPage}>\n      <Typography>{t('This team invitation link has already been used.')}</Typography>\n    </MessageCard>\n  );\n\n  const code = searchParams.code;\n  if (!code) {\n    return invalidJsx;\n  }\n\n  if (!user) {\n    return (\n      <MessageCard\n        title={t('Team invitation')}\n        fullPage={fullPage}\n        primaryButtonText={t('Sign in')}\n        primaryAction={() => stackApp.redirectToSignIn()}\n        secondaryButtonText={t('Cancel')}\n        secondaryAction={() => stackApp.redirectToHome()}\n      >\n        <Typography>{t('Sign in or create an account to join the team.')}</Typography>\n      </MessageCard>\n    );\n  }\n\n  const verificationResult = React.use(cachedVerifyInvitation(stackApp, searchParams.code || ''));\n\n  if (verificationResult.status === 'error') {\n    const error = verificationResult.error;\n    if (KnownErrors.VerificationCodeNotFound.isInstance(error)) {\n      return invalidJsx;\n    } else if (KnownErrors.VerificationCodeExpired.isInstance(error)) {\n      return expiredJsx;\n    } else if (KnownErrors.VerificationCodeAlreadyUsed.isInstance(error)) {\n      return usedJsx;\n    } else {\n      throw error;\n    }\n  }\n\n  return <TeamInvitationInner fullPage={fullPage} searchParams={searchParams} />;\n};\n"], "mappings": ";;;AAOA,SAAS,mBAAmB;AAC5B,SAAS,qBAAqB;AAC9B,SAAS,kCAAkC;AAC3C,SAAS,kBAAkB;AAC3B,OAAO,WAAW;AAClB,SAAS,aAA6B,aAAa,eAAe;AAClE,SAAS,6BAA6B;AACtC,SAAS,sBAAsB;AAmBzB,cAYE,YAZF;AAjBN,IAAM,yBAAyB,cAAc,OAAO,UAAgC,SAAiB;AACnG,SAAO,MAAM,SAAS,yBAAyB,IAAI;AACrD,CAAC;AAED,IAAM,6BAA6B,cAAc,OAAO,UAAgC,SAAiB;AACvG,SAAO,MAAM,SAAS,yBAAyB,IAAI;AACrD,CAAC;AAED,SAAS,oBAAoB,OAAqE;AAChG,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,WAAW,YAAY;AAC7B,QAAM,CAAC,SAAS,UAAU,IAAI,MAAM,SAAS,KAAK;AAClD,QAAM,CAAC,cAAc,eAAe,IAAI,MAAM,SAAwB,IAAI;AAC1E,QAAM,UAAU,MAAM,IAAI,2BAA2B,UAAU,MAAM,aAAa,QAAQ,EAAE,CAAC;AAE7F,MAAI,gBAAgB,QAAQ,WAAW,SAAS;AAC9C,WACE,oBAAC,yBAAsB,MAAK,gBAAe,UAAU,MAAM,UAAU;AAAA,EAEzE;AAEA,MAAI,SAAS;AACX,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO,EAAE,iBAAiB;AAAA,QAC1B,UAAU,MAAM;AAAA,QAChB,mBAAkB;AAAA,QAClB,eAAe,MAAM,SAAS,eAAe;AAAA,QAE7C,+BAAC,cAAW;AAAA;AAAA,UAA8B,QAAQ,KAAK;AAAA,WAAgB;AAAA;AAAA,IACzE;AAAA,EAEJ;AAGA,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO,EAAE,iBAAiB;AAAA,MAC1B,UAAU,MAAM;AAAA,MAChB,mBAAmB,EAAE,MAAM;AAAA,MAC3B,eAAe,MAAM,2BAA2B,YAAY;AAC1D,cAAM,SAAS,MAAM,SAAS,qBAAqB,MAAM,aAAa,QAAQ,EAAE;AAChF,YAAI,OAAO,WAAW,SAAS;AAC/B,0BAAgB,OAAO,MAAM,OAAO;AAAA,QACpC,OAAO;AACP,qBAAW,IAAI;AAAA,QACf;AAAA,MACF,CAAC;AAAA,MACD,qBAAqB,EAAE,QAAQ;AAAA,MAC/B,iBAAiB,MAAM,SAAS,eAAe;AAAA,MAE/C,+BAAC,cAAW;AAAA;AAAA,QAAyB,QAAQ,KAAK;AAAA,SAAgB;AAAA;AAAA,EACpE;AAEJ;AAEO,SAAS,eAAe,EAAE,WAAS,OAAO,aAAa,GAAiE;AAC7H,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,OAAO,QAAQ;AACrB,QAAM,WAAW,YAAY;AAE7B,QAAM,aACJ,oBAAC,eAAY,OAAO,EAAE,8BAA8B,GAAG,UACrD,8BAAC,cAAY,YAAE,mEAAmE,GAAE,GACtF;AAGF,QAAM,aACJ,oBAAC,eAAY,OAAO,EAAE,8BAA8B,GAAG,UACrD,8BAAC,cAAY,YAAE,mFAAmF,GAAE,GACtG;AAGF,QAAM,UACJ,oBAAC,eAAY,OAAO,EAAE,2BAA2B,GAAG,UAClD,8BAAC,cAAY,YAAE,kDAAkD,GAAE,GACrE;AAGF,QAAM,OAAO,aAAa;AAC1B,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,MAAM;AACT,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO,EAAE,iBAAiB;AAAA,QAC1B;AAAA,QACA,mBAAmB,EAAE,SAAS;AAAA,QAC9B,eAAe,MAAM,SAAS,iBAAiB;AAAA,QAC/C,qBAAqB,EAAE,QAAQ;AAAA,QAC/B,iBAAiB,MAAM,SAAS,eAAe;AAAA,QAE/C,8BAAC,cAAY,YAAE,gDAAgD,GAAE;AAAA;AAAA,IACnE;AAAA,EAEJ;AAEA,QAAM,qBAAqB,MAAM,IAAI,uBAAuB,UAAU,aAAa,QAAQ,EAAE,CAAC;AAE9F,MAAI,mBAAmB,WAAW,SAAS;AACzC,UAAM,QAAQ,mBAAmB;AACjC,QAAI,YAAY,yBAAyB,WAAW,KAAK,GAAG;AAC1D,aAAO;AAAA,IACT,WAAW,YAAY,wBAAwB,WAAW,KAAK,GAAG;AAChE,aAAO;AAAA,IACT,WAAW,YAAY,4BAA4B,WAAW,KAAK,GAAG;AACpE,aAAO;AAAA,IACT,OAAO;AACL,YAAM;AAAA,IACR;AAAA,EACF;AAEA,SAAO,oBAAC,uBAAoB,UAAoB,cAA4B;AAC9E;", "names": []}