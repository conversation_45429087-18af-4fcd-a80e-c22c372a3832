{"version": 3, "sources": ["../../src/components/api-key-table.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { ActionCell, ActionDialog, BadgeCell, DataTable, DataTableColumnHeader, DataTableFacetedFilter, DateCell, SearchToolbarItem, TextCell, standardFilterFn } from \"@stackframe/stack-ui\";\nimport { ColumnDef, Row, Table } from \"@tanstack/react-table\";\nimport { useMemo, useState } from \"react\";\nimport { ApiKey } from \"../lib/stack-app/api-keys\";\n\ntype ExtendedApiKey = ApiKey & {\n  status: 'valid' | 'expired' | 'revoked',\n};\n\nfunction toolbarRender<TData>(table: Table<TData>) {\n  return (\n    <>\n      <SearchToolbarItem table={table} placeholder=\"Search table\" />\n      <DataTableFacetedFilter\n        column={table.getColumn(\"status\")}\n        title=\"Status\"\n        options={['valid', 'expired', 'revoked'].map((provider) => ({\n          value: provider,\n          label: provider,\n        }))}\n      />\n    </>\n  );\n}\n\nfunction RevokeDialog(props: {\n  apiKey: ExtendedApiKey,\n  open: boolean,\n  onOpenChange: (open: boolean) => void,\n}) {\n  return <ActionDialog\n    open={props.open}\n    onOpenChange={props.onOpenChange}\n    title=\"Revoke API Key\"\n    danger\n    cancelButton\n    okButton={{ label: \"Revoke Key\", onClick: async () => { await props.apiKey.revoke(); } }}\n    confirmText=\"I understand this will unlink all the apps using this API key\"\n  >\n    {`Are you sure you want to revoke API key *****${props.apiKey.value.lastFour}?`}\n  </ActionDialog>;\n}\n\nfunction Actions({ row }: { row: Row<ExtendedApiKey> }) {\n  const [isRevokeModalOpen, setIsRevokeModalOpen] = useState(false);\n  return (\n    <>\n      <RevokeDialog apiKey={row.original} open={isRevokeModalOpen} onOpenChange={setIsRevokeModalOpen} />\n      <ActionCell\n        invisible={row.original.status !== 'valid'}\n        items={[{\n          item: \"Revoke\",\n          danger: true,\n          onClick: () => setIsRevokeModalOpen(true),\n        }]}\n      />\n    </>\n  );\n}\n\nconst columns: ColumnDef<ExtendedApiKey>[] =  [\n  {\n    accessorKey: \"description\",\n    header: ({ column }) => <DataTableColumnHeader column={column} columnTitle=\"Description\" />,\n    cell: ({ row }) => <TextCell size={100}>{row.original.description}</TextCell>,\n  },\n  {\n    accessorKey: \"status\",\n    header: ({ column }) => <DataTableColumnHeader column={column} columnTitle=\"Status\" />,\n    cell: ({ row }) => <BadgeCell badges={[row.original.status]} />,\n    filterFn: standardFilterFn,\n  },\n  {\n    id: \"value\",\n    accessorFn: (row) => row.value.lastFour,\n    header: ({ column }) => <DataTableColumnHeader column={column} columnTitle=\"Client Key\" />,\n    cell: ({ row }) => <TextCell>*******{row.original.value.lastFour}</TextCell>,\n    enableSorting: false,\n  },\n  {\n    accessorKey: \"expiresAt\",\n    header: ({ column }) => <DataTableColumnHeader column={column} columnTitle=\"Expires At\" />,\n    cell: ({ row }) => {\n      if (row.original.status === 'revoked') return <TextCell>-</TextCell>;\n      return row.original.expiresAt ? <DateCell date={row.original.expiresAt} ignoreAfterYears={50} /> : <TextCell>Never</TextCell>;\n    },\n  },\n  {\n    accessorKey: \"createdAt\",\n    header: ({ column }) => <DataTableColumnHeader column={column} columnTitle=\"Created At\" />,\n    cell: ({ row }) => <DateCell date={row.original.createdAt} ignoreAfterYears={50} />\n  },\n  {\n    id: \"actions\",\n    cell: ({ row }) => <Actions row={row} />,\n  },\n];\n\nexport function ApiKeyTable(props: { apiKeys: ApiKey[] }) {\n  const extendedApiKeys = useMemo(() => {\n    const keys = props.apiKeys.map((apiKey) => ({\n      ...apiKey,\n      status: ({ 'valid': 'valid', 'manually-revoked': 'revoked', 'expired': 'expired' } as const)[apiKey.whyInvalid() || 'valid'],\n    } satisfies ExtendedApiKey));\n    // first sort based on status, then by createdAt\n    return keys.sort((a, b) => {\n      if (a.status === b.status) {\n        return a.createdAt < b.createdAt ? 1 : -1;\n      }\n      return a.status === 'valid' ? -1 : 1;\n    });\n  }, [props.apiKeys]);\n\n  return <DataTable\n    data={extendedApiKeys}\n    columns={columns}\n    toolbarRender={toolbarRender}\n    defaultColumnFilters={[]}\n    defaultSorting={[]}\n  />;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,sBAAuK;AAEvK,mBAAkC;AAS9B;AAFJ,SAAS,cAAqB,OAAqB;AACjD,SACE,4EACE;AAAA,gDAAC,qCAAkB,OAAc,aAAY,gBAAe;AAAA,IAC5D;AAAA,MAAC;AAAA;AAAA,QACC,QAAQ,MAAM,UAAU,QAAQ;AAAA,QAChC,OAAM;AAAA,QACN,SAAS,CAAC,SAAS,WAAW,SAAS,EAAE,IAAI,CAAC,cAAc;AAAA,UAC1D,OAAO;AAAA,UACP,OAAO;AAAA,QACT,EAAE;AAAA;AAAA,IACJ;AAAA,KACF;AAEJ;AAEA,SAAS,aAAa,OAInB;AACD,SAAO;AAAA,IAAC;AAAA;AAAA,MACN,MAAM,MAAM;AAAA,MACZ,cAAc,MAAM;AAAA,MACpB,OAAM;AAAA,MACN,QAAM;AAAA,MACN,cAAY;AAAA,MACZ,UAAU,EAAE,OAAO,cAAc,SAAS,YAAY;AAAE,cAAM,MAAM,OAAO,OAAO;AAAA,MAAG,EAAE;AAAA,MACvF,aAAY;AAAA,MAEX,0DAAgD,MAAM,OAAO,MAAM,QAAQ;AAAA;AAAA,EAC9E;AACF;AAEA,SAAS,QAAQ,EAAE,IAAI,GAAiC;AACtD,QAAM,CAAC,mBAAmB,oBAAoB,QAAI,uBAAS,KAAK;AAChE,SACE,4EACE;AAAA,gDAAC,gBAAa,QAAQ,IAAI,UAAU,MAAM,mBAAmB,cAAc,sBAAsB;AAAA,IACjG;AAAA,MAAC;AAAA;AAAA,QACC,WAAW,IAAI,SAAS,WAAW;AAAA,QACnC,OAAO,CAAC;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,SAAS,MAAM,qBAAqB,IAAI;AAAA,QAC1C,CAAC;AAAA;AAAA,IACH;AAAA,KACF;AAEJ;AAEA,IAAM,UAAwC;AAAA,EAC5C;AAAA,IACE,aAAa;AAAA,IACb,QAAQ,CAAC,EAAE,OAAO,MAAM,4CAAC,yCAAsB,QAAgB,aAAY,eAAc;AAAA,IACzF,MAAM,CAAC,EAAE,IAAI,MAAM,4CAAC,4BAAS,MAAM,KAAM,cAAI,SAAS,aAAY;AAAA,EACpE;AAAA,EACA;AAAA,IACE,aAAa;AAAA,IACb,QAAQ,CAAC,EAAE,OAAO,MAAM,4CAAC,yCAAsB,QAAgB,aAAY,UAAS;AAAA,IACpF,MAAM,CAAC,EAAE,IAAI,MAAM,4CAAC,6BAAU,QAAQ,CAAC,IAAI,SAAS,MAAM,GAAG;AAAA,IAC7D,UAAU;AAAA,EACZ;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,YAAY,CAAC,QAAQ,IAAI,MAAM;AAAA,IAC/B,QAAQ,CAAC,EAAE,OAAO,MAAM,4CAAC,yCAAsB,QAAgB,aAAY,cAAa;AAAA,IACxF,MAAM,CAAC,EAAE,IAAI,MAAM,6CAAC,4BAAS;AAAA;AAAA,MAAQ,IAAI,SAAS,MAAM;AAAA,OAAS;AAAA,IACjE,eAAe;AAAA,EACjB;AAAA,EACA;AAAA,IACE,aAAa;AAAA,IACb,QAAQ,CAAC,EAAE,OAAO,MAAM,4CAAC,yCAAsB,QAAgB,aAAY,cAAa;AAAA,IACxF,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,UAAI,IAAI,SAAS,WAAW,UAAW,QAAO,4CAAC,4BAAS,eAAC;AACzD,aAAO,IAAI,SAAS,YAAY,4CAAC,4BAAS,MAAM,IAAI,SAAS,WAAW,kBAAkB,IAAI,IAAK,4CAAC,4BAAS,mBAAK;AAAA,IACpH;AAAA,EACF;AAAA,EACA;AAAA,IACE,aAAa;AAAA,IACb,QAAQ,CAAC,EAAE,OAAO,MAAM,4CAAC,yCAAsB,QAAgB,aAAY,cAAa;AAAA,IACxF,MAAM,CAAC,EAAE,IAAI,MAAM,4CAAC,4BAAS,MAAM,IAAI,SAAS,WAAW,kBAAkB,IAAI;AAAA,EACnF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM,CAAC,EAAE,IAAI,MAAM,4CAAC,WAAQ,KAAU;AAAA,EACxC;AACF;AAEO,SAAS,YAAY,OAA8B;AACxD,QAAM,sBAAkB,sBAAQ,MAAM;AACpC,UAAM,OAAO,MAAM,QAAQ,IAAI,CAAC,YAAY;AAAA,MAC1C,GAAG;AAAA,MACH,QAAS,EAAE,SAAS,SAAS,oBAAoB,WAAW,WAAW,UAAU,EAAY,OAAO,WAAW,KAAK,OAAO;AAAA,IAC7H,EAA2B;AAE3B,WAAO,KAAK,KAAK,CAAC,GAAG,MAAM;AACzB,UAAI,EAAE,WAAW,EAAE,QAAQ;AACzB,eAAO,EAAE,YAAY,EAAE,YAAY,IAAI;AAAA,MACzC;AACA,aAAO,EAAE,WAAW,UAAU,KAAK;AAAA,IACrC,CAAC;AAAA,EACH,GAAG,CAAC,MAAM,OAAO,CAAC;AAElB,SAAO;AAAA,IAAC;AAAA;AAAA,MACN,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA,sBAAsB,CAAC;AAAA,MACvB,gBAAgB,CAAC;AAAA;AAAA,EACnB;AACF;", "names": []}