{"version": 3, "sources": ["../../../src/utils/ips.tsx"], "sourcesContent": ["import ipRegex from \"ip-regex\";\n\nexport type Ipv4Address = `${number}.${number}.${number}.${number}`;\nexport type Ipv6Address = string;\n\nexport function isIpAddress(ip: string): ip is Ipv4Address | Ipv6Address {\n  return ipRegex({ exact: true }).test(ip);\n}\nundefined?.test(\"isIpAddress\", ({ expect }) => {\n  // Test valid IPv4 addresses\n  expect(isIpAddress(\"***********\")).toBe(true);\n  expect(isIpAddress(\"127.0.0.1\")).toBe(true);\n  expect(isIpAddress(\"0.0.0.0\")).toBe(true);\n  expect(isIpAddress(\"***************\")).toBe(true);\n\n  // Test valid IPv6 addresses\n  expect(isIpAddress(\"::1\")).toBe(true);\n  expect(isIpAddress(\"2001:db8::\")).toBe(true);\n  expect(isIpAddress(\"2001:db8:85a3:8d3:1319:8a2e:370:7348\")).toBe(true);\n\n  // Test invalid IP addresses\n  expect(isIpAddress(\"\")).toBe(false);\n  expect(isIpAddress(\"not an ip\")).toBe(false);\n  expect(isIpAddress(\"256.256.256.256\")).toBe(false);\n  expect(isIpAddress(\"192.168.1\")).toBe(false);\n  expect(isIpAddress(\"***********.1\")).toBe(false);\n  expect(isIpAddress(\"2001:db8::xyz\")).toBe(false);\n});\n\nexport function assertIpAddress(ip: string): asserts ip is Ipv4Address | Ipv6Address {\n  if (!isIpAddress(ip)) {\n    throw new Error(`Invalid IP address: ${ip}`);\n  }\n}\nundefined?.test(\"assertIpAddress\", ({ expect }) => {\n  // Test with valid IPv4 address\n  expect(() => assertIpAddress(\"***********\")).not.toThrow();\n\n  // Test with valid IPv6 address\n  expect(() => assertIpAddress(\"::1\")).not.toThrow();\n\n  // Test with invalid IP addresses\n  expect(() => assertIpAddress(\"\")).toThrow(\"Invalid IP address: \");\n  expect(() => assertIpAddress(\"not an ip\")).toThrow(\"Invalid IP address: not an ip\");\n  expect(() => assertIpAddress(\"256.256.256.256\")).toThrow(\"Invalid IP address: 256.256.256.256\");\n  expect(() => assertIpAddress(\"192.168.1\")).toThrow(\"Invalid IP address: 192.168.1\");\n});\n"], "mappings": ";AAAA,OAAO,aAAa;AAKb,SAAS,YAAY,IAA6C;AACvE,SAAO,QAAQ,EAAE,OAAO,KAAK,CAAC,EAAE,KAAK,EAAE;AACzC;AAsBO,SAAS,gBAAgB,IAAqD;AACnF,MAAI,CAAC,YAAY,EAAE,GAAG;AACpB,UAAM,IAAI,MAAM,uBAAuB,EAAE,EAAE;AAAA,EAC7C;AACF;", "names": []}