{"name": "@stackframe/stack-ui", "version": "2.8.12", "main": "./dist/index.js", "types": "./dist/index.d.ts", "sideEffects": false, "files": ["README.md", "dist", "CHANGELOG.md", "LICENSE"], "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "peerDependencies": {"@types/react": ">=18.2.12 || >=19.0.0-rc.0", "@types/react-dom": ">=18.2.12 || >=19.0.0-rc.0", "react": ">=18.2 || >=19.0.0-rc.0", "react-dom": ">=18.2 || >=19.0.0-rc.0", "yup": "^1.4.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}, "yup": {"optional": true}}, "dependencies": {"@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-icons": "^1.3.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@tanstack/react-table": "^8.20.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^3.6.0", "export-to-csv": "^1.4.0", "input-otp": "^1.4.1", "lucide-react": "^0.378.0", "react-day-picker": "^8.10.1", "react-hook-form": "^7.53.1", "react-resizable-panels": "^2.1.6", "tailwind-merge": "^2.5.4", "@stackframe/stack-shared": "2.8.12"}, "devDependencies": {"@types/react": "^18.2.12", "@types/react-dom": "^18.2.12", "react": "^18.2", "react-dom": "^18.2", "rimraf": "^5.0.10"}, "scripts": {"build": "tsc", "typecheck": "tsc --noEmit", "clean": "rimraf dist && rimraf node_modules", "dev": "tsc -w --preserveWatchOutput --declarationMap", "lint": "eslint --ext .tsx,.ts ."}}