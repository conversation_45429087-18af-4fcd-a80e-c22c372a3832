import http.client
import json

# gemini-2.0-flash-exp   gemini-2.0-flash-thinking-exp    gemini-2.0-flash-thinking-exp-1219
model = "claude-3-7-sonnet-20250219"
prompt = '''
你是一位专业的悬疑小说作家,擅长创作3000-5000字的短篇悬疑故事。请注意，你的所有思考和表达输出都是用中文逻辑。请按以下要求创作一篇适合在公众号发表的引人入胜的悬疑短篇故事，直接输出完整的故事内容:
<故事结构>
标题要吸引人且有悬疑感，要能成为爆款标题，有一定的反差，让人看了就想点进来看;
开头300字要抓住读者眼球，例如有悬疑+钩子;
故事要分3-4个自然段落展开;
故事的中间和结尾要有出人意料但细想又符合情理的精彩反转;
全文5000-8000字为宜;
</故事结构>

<内容要求>
核心悬念:{填入核心谜题,如"密室杀人"/"身份之谜"等};
故事背景:{填入具体场景,如"老宅"/"度假村"等};
主要人物:2-3个,需要有各自独特的性格特征，人物的性格和言行都要适合符合他们的设定;
故事基调:悬疑感强但不恐怖血腥;
需要埋下2-3个伏笔为结局服务;
内容需要脑洞大开，角度清奇;
内容要贴近现实生活，故事内容要容易让观众产生画面感，不要出现太多无厘头的抽象内容;
整篇内容的构思和叙述都要合理且条理清晰，不能有明显的逻辑问题;
</内容要求>

<写作风格>
语言要生动形象,多用细节描写;
节奏要紧凑,适时制造紧张感;
对话要自然流畅;
要有代入感和画面感;
符合公众号轻松阅读的风格;
</写作风格>

<注意事项>
避免出现血腥暴力的细节描写;
不要出现令人不适的情节;
悬疑感要贯穿全文;
结局要合理且有新意;
适当运用悬疑小说的技巧如误导、反转等;
</注意事项>
'''
prompt1 = '''
帮我理解以下这段文案，然后生成相关的公众号配图，比例为4:3，输出svg代码格式：
上个月，我参与了一项覆盖8000名职场人的AI应用调研。结果显示：2022年初就开始使用AI工具的"早期尝鲜者"中，有78%已经在各自领域失去了竞争优势。与此同时，那些掌握了新夸克AI系统的用户，职业发展速度平均提升了32%。
'''
conn = http.client.HTTPSConnection("ai98.vip")
payload = json.dumps({
    "model": model,
    "messages": [
        {
            "role": 'user',
            "content": prompt1
        }
    ],
    "temperature": 1,
    "presence_penalty": 1.0,
    "frequency_penalty": 1.0
})
headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': 'Bearer sk-1r4ApCAZjjmjZLUJ9691C2B7946e47669254254c89066a46',
}
conn.request("POST", "/v1/chat/completions", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))

response_data = json.loads(data.decode("utf-8"))
content = response_data['choices'][0]['message']['content']
print(content)




