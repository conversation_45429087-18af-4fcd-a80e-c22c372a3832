{"version": 3, "sources": ["../../src/components-page/magic-link-callback.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { KnownErrors } from \"@stackframe/stack-shared\";\nimport { cacheFunction } from \"@stackframe/stack-shared/dist/utils/caches\";\nimport { throwErr } from \"@stackframe/stack-shared/dist/utils/errors\";\nimport React from \"react\";\nimport { StackClientApp, useStackApp, useUser } from \"..\";\nimport { MessageCard } from \"../components/message-cards/message-card\";\nimport { PredefinedMessageCard } from \"../components/message-cards/predefined-message-card\";\nimport { useTranslation } from \"../lib/translations\";\n\nconst cacheSignInWithMagicLink = cacheFunction(async (stackApp: StackClientApp<true>, code: string) => {\n  return await stackApp.signInWithMagicLink(code);\n});\n\nexport function MagicLinkCallback(props: {\n  searchParams?: Record<string, string>,\n  fullPage?: boolean,\n}) {\n  const { t } = useTranslation();\n  const stackApp = useStackApp();\n  const user = useUser();\n  const [result, setResult] = React.useState<Awaited<ReturnType<typeof stackApp.signInWithMagicLink>> | null>(null);\n\n  if (user) {\n    return <PredefinedMessageCard type='signedIn' fullPage={!!props.fullPage} />;\n  }\n\n  const invalidJsx = (\n    <MessageCard title={t(\"Invalid Magic Link\")} fullPage={!!props.fullPage}>\n      <p>{t(\"Please check if you have the correct link. If you continue to have issues, please contact support.\")}</p>\n    </MessageCard>\n  );\n\n  const expiredJsx = (\n    <MessageCard title={t(\"Expired Magic Link\")} fullPage={!!props.fullPage}>\n      <p>{t(\"Your magic link has expired. Please request a new magic link if you need to sign-in.\")}</p>\n    </MessageCard>\n  );\n\n  const alreadyUsedJsx = (\n    <MessageCard title={t(\"Magic Link Already Used\")} fullPage={!!props.fullPage}>\n      <p>{t(\"The magic link has already been used. The link can only be used once. Please request a new magic link if you need to sign-in again.\")}</p>\n    </MessageCard>\n  );\n\n  if (!props.searchParams?.code) {\n    return invalidJsx;\n  }\n\n  if (!result) {\n    return <MessageCard\n      title={t(\"Do you want to sign in?\")}\n      fullPage={!!props.fullPage}\n      primaryButtonText={t(\"Sign in\")}\n      primaryAction={async () => {\n        const result = await stackApp.signInWithMagicLink(props.searchParams?.code || throwErr(\"No magic link provided\"));\n        setResult(result);\n      }}\n      secondaryButtonText={t(\"Cancel\")}\n      secondaryAction={async () => {\n        await stackApp.redirectToHome();\n      }}\n    />;\n  } else {\n    if (result.status === 'error') {\n      if (KnownErrors.VerificationCodeNotFound.isInstance(result.error)) {\n        return invalidJsx;\n      } else if (KnownErrors.VerificationCodeExpired.isInstance(result.error)) {\n        return expiredJsx;\n      } else if (KnownErrors.VerificationCodeAlreadyUsed.isInstance(result.error)) {\n        return alreadyUsedJsx;\n      } else {\n        throw result.error;\n      }\n    }\n\n    return <MessageCard\n      title={t(\"Signed in successfully!\")}\n      fullPage={!!props.fullPage}\n      primaryButtonText={t(\"Go home\")}\n      primaryAction={async () => {\n        await stackApp.redirectToHome();\n      }}\n    />;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,0BAA4B;AAC5B,oBAA8B;AAC9B,oBAAyB;AACzB,mBAAkB;AAClB,eAAqD;AACrD,0BAA4B;AAC5B,qCAAsC;AACtC,0BAA+B;AAgBpB;AAdX,IAAM,+BAA2B,6BAAc,OAAO,UAAgC,SAAiB;AACrG,SAAO,MAAM,SAAS,oBAAoB,IAAI;AAChD,CAAC;AAEM,SAAS,kBAAkB,OAG/B;AACD,QAAM,EAAE,EAAE,QAAI,oCAAe;AAC7B,QAAM,eAAW,sBAAY;AAC7B,QAAM,WAAO,kBAAQ;AACrB,QAAM,CAAC,QAAQ,SAAS,IAAI,aAAAA,QAAM,SAA0E,IAAI;AAEhH,MAAI,MAAM;AACR,WAAO,4CAAC,wDAAsB,MAAK,YAAW,UAAU,CAAC,CAAC,MAAM,UAAU;AAAA,EAC5E;AAEA,QAAM,aACJ,4CAAC,mCAAY,OAAO,EAAE,oBAAoB,GAAG,UAAU,CAAC,CAAC,MAAM,UAC7D,sDAAC,OAAG,YAAE,oGAAoG,GAAE,GAC9G;AAGF,QAAM,aACJ,4CAAC,mCAAY,OAAO,EAAE,oBAAoB,GAAG,UAAU,CAAC,CAAC,MAAM,UAC7D,sDAAC,OAAG,YAAE,sFAAsF,GAAE,GAChG;AAGF,QAAM,iBACJ,4CAAC,mCAAY,OAAO,EAAE,yBAAyB,GAAG,UAAU,CAAC,CAAC,MAAM,UAClE,sDAAC,OAAG,YAAE,qIAAqI,GAAE,GAC/I;AAGF,MAAI,CAAC,MAAM,cAAc,MAAM;AAC7B,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,MAAC;AAAA;AAAA,QACN,OAAO,EAAE,yBAAyB;AAAA,QAClC,UAAU,CAAC,CAAC,MAAM;AAAA,QAClB,mBAAmB,EAAE,SAAS;AAAA,QAC9B,eAAe,YAAY;AACzB,gBAAMC,UAAS,MAAM,SAAS,oBAAoB,MAAM,cAAc,YAAQ,wBAAS,wBAAwB,CAAC;AAChH,oBAAUA,OAAM;AAAA,QAClB;AAAA,QACA,qBAAqB,EAAE,QAAQ;AAAA,QAC/B,iBAAiB,YAAY;AAC3B,gBAAM,SAAS,eAAe;AAAA,QAChC;AAAA;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,OAAO,WAAW,SAAS;AAC7B,UAAI,gCAAY,yBAAyB,WAAW,OAAO,KAAK,GAAG;AACjE,eAAO;AAAA,MACT,WAAW,gCAAY,wBAAwB,WAAW,OAAO,KAAK,GAAG;AACvE,eAAO;AAAA,MACT,WAAW,gCAAY,4BAA4B,WAAW,OAAO,KAAK,GAAG;AAC3E,eAAO;AAAA,MACT,OAAO;AACL,cAAM,OAAO;AAAA,MACf;AAAA,IACF;AAEA,WAAO;AAAA,MAAC;AAAA;AAAA,QACN,OAAO,EAAE,yBAAyB;AAAA,QAClC,UAAU,CAAC,CAAC,MAAM;AAAA,QAClB,mBAAmB,EAAE,SAAS;AAAA,QAC9B,eAAe,YAAY;AACzB,gBAAM,SAAS,eAAe;AAAA,QAChC;AAAA;AAAA,IACF;AAAA,EACF;AACF;", "names": ["React", "result"]}