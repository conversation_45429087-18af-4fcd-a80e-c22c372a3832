{"version": 3, "sources": ["../../../src/components-page/error-page.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { KnownError, KnownErrors } from \"@stackframe/stack-shared\";\nimport { Typography } from \"@stackframe/stack-ui\";\nimport { useStackApp } from \"..\";\nimport { KnownErrorMessageCard } from \"../components/message-cards/known-error-message-card\";\nimport { MessageCard } from \"../components/message-cards/message-card\";\nimport { PredefinedMessageCard } from \"../components/message-cards/predefined-message-card\";\nimport { useTranslation } from \"../lib/translations\";\n\n\nexport function ErrorPage(props: { fullPage?: boolean, searchParams: Record<string, string> }) {\n  const { t } = useTranslation();\n  const stackApp = useStackApp();\n  const errorCode = props.searchParams.errorCode;\n  const message = props.searchParams.message;\n  const details = props.searchParams.details;\n\n  const unknownErrorCard = <PredefinedMessageCard type='unknownError' fullPage={!!props.fullPage} />;\n\n  if (!errorCode || !message) {\n    return unknownErrorCard;\n  }\n\n  let error;\n  try {\n    const detailJson = details ? JSON.parse(details) : {};\n    error = KnownError.fromJson({ code: errorCode, message, details: detailJson });\n  } catch (e) {\n    return unknownErrorCard;\n  }\n\n  if (KnownErrors.OAuthConnectionAlreadyConnectedToAnotherUser.isInstance(error)) {\n    // TODO: add \"Connect a different account\" button\n    return (\n      <MessageCard\n        title={t(\"Failed to connect account\")}\n        fullPage={!!props.fullPage}\n        primaryButtonText={t(\"Go Home\")}\n        primaryAction={() => stackApp.redirectToHome()}\n      >\n        <Typography>\n          {t(\"This account is already connected to another user. Please connect a different account.\")}\n        </Typography>\n      </MessageCard>\n    );\n  }\n\n  if (KnownErrors.UserAlreadyConnectedToAnotherOAuthConnection.isInstance(error)) {\n    // TODO: add \"Connect again\" button\n    return (\n      <MessageCard\n        title={t(\"Failed to connect account\")}\n        fullPage={!!props.fullPage}\n        primaryButtonText={t(\"Go Home\")}\n        primaryAction={() => stackApp.redirectToHome()}\n      >\n        <Typography>\n          {t(\"The user is already connected to another OAuth account. Did you maybe selected the wrong account on the OAuth provider page?\")}\n        </Typography>\n      </MessageCard>\n    );\n  }\n\n  if (KnownErrors.OAuthProviderAccessDenied.isInstance(error)) {\n    return (\n      <MessageCard\n        title={t(\"OAuth provider access denied\")}\n        fullPage={!!props.fullPage}\n        primaryButtonText={t(\"Sign in again\")}\n        primaryAction={() => stackApp.redirectToSignIn()}\n        secondaryButtonText={t(\"Go Home\")}\n        secondaryAction={() => stackApp.redirectToHome()}\n      >\n        <Typography>\n          {t(\"The sign-in operation has been cancelled. Please try again. [access_denied]\")}\n        </Typography>\n      </MessageCard>\n    );\n  }\n\n  return <KnownErrorMessageCard error={error} fullPage={!!props.fullPage} />;\n}\n"], "mappings": ";;;AAOA,SAAS,YAAY,mBAAmB;AACxC,SAAS,kBAAkB;AAC3B,SAAS,mBAAmB;AAC5B,SAAS,6BAA6B;AACtC,SAAS,mBAAmB;AAC5B,SAAS,6BAA6B;AACtC,SAAS,sBAAsB;AAUJ;AAPpB,SAAS,UAAU,OAAqE;AAC7F,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,WAAW,YAAY;AAC7B,QAAM,YAAY,MAAM,aAAa;AACrC,QAAM,UAAU,MAAM,aAAa;AACnC,QAAM,UAAU,MAAM,aAAa;AAEnC,QAAM,mBAAmB,oBAAC,yBAAsB,MAAK,gBAAe,UAAU,CAAC,CAAC,MAAM,UAAU;AAEhG,MAAI,CAAC,aAAa,CAAC,SAAS;AAC1B,WAAO;AAAA,EACT;AAEA,MAAI;AACJ,MAAI;AACF,UAAM,aAAa,UAAU,KAAK,MAAM,OAAO,IAAI,CAAC;AACpD,YAAQ,WAAW,SAAS,EAAE,MAAM,WAAW,SAAS,SAAS,WAAW,CAAC;AAAA,EAC/E,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AAEA,MAAI,YAAY,6CAA6C,WAAW,KAAK,GAAG;AAE9E,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO,EAAE,2BAA2B;AAAA,QACpC,UAAU,CAAC,CAAC,MAAM;AAAA,QAClB,mBAAmB,EAAE,SAAS;AAAA,QAC9B,eAAe,MAAM,SAAS,eAAe;AAAA,QAE7C,8BAAC,cACE,YAAE,wFAAwF,GAC7F;AAAA;AAAA,IACF;AAAA,EAEJ;AAEA,MAAI,YAAY,6CAA6C,WAAW,KAAK,GAAG;AAE9E,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO,EAAE,2BAA2B;AAAA,QACpC,UAAU,CAAC,CAAC,MAAM;AAAA,QAClB,mBAAmB,EAAE,SAAS;AAAA,QAC9B,eAAe,MAAM,SAAS,eAAe;AAAA,QAE7C,8BAAC,cACE,YAAE,8HAA8H,GACnI;AAAA;AAAA,IACF;AAAA,EAEJ;AAEA,MAAI,YAAY,0BAA0B,WAAW,KAAK,GAAG;AAC3D,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO,EAAE,8BAA8B;AAAA,QACvC,UAAU,CAAC,CAAC,MAAM;AAAA,QAClB,mBAAmB,EAAE,eAAe;AAAA,QACpC,eAAe,MAAM,SAAS,iBAAiB;AAAA,QAC/C,qBAAqB,EAAE,SAAS;AAAA,QAChC,iBAAiB,MAAM,SAAS,eAAe;AAAA,QAE/C,8BAAC,cACE,YAAE,6EAA6E,GAClF;AAAA;AAAA,IACF;AAAA,EAEJ;AAEA,SAAO,oBAAC,yBAAsB,OAAc,UAAU,CAAC,CAAC,MAAM,UAAU;AAC1E;", "names": []}