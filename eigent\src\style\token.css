@layer base {
  :root {
    /* Red */
    --colors-red-50: #fef2f2;
    --colors-red-100: #ffe2e2;
    --colors-red-200: #ffc9c9;
    --colors-red-300: #ffa2a2;
    --colors-red-400: #ff6467;
    --colors-red-500: #fb2c36;
    --colors-red-700: #c10007;
    --colors-red-800: #9f0712;
    --colors-red-900: #82181a;
    --colors-red-950: #460809;
    --colors-red-default: #e7000b;
    /* Yellow */
    --colors-yellow-50: #fefce8;
    --colors-yellow-100: #fef9c2;
    --colors-yellow-200: #fff085;
    --colors-yellow-300: #ffdf20;
    --colors-yellow-400: #fdc700;
    --colors-yellow-500: #f0b100;
    --colors-yellow-700: #a65f00;
    --colors-yellow-800: #894b00;
    --colors-yellow-900: #733e0a;
    --colors-yellow-950: #432004;
    --colors-yellow-default: #d08700;
    /* Green */
    --colors-green-50: #f0fdf4;
    --colors-green-100: #dcfce7;
    --colors-green-200: #b9f8cf;
    --colors-green-300: #7bf1a8;
    --colors-green-400: #05df72;
    --colors-green-500: #00c950;
    --colors-green-700: #008236;
    --colors-green-800: #016630;
    --colors-green-900: #0d542b;
    --colors-green-950: #032e15;
    --colors-green-default: #00a63e;
    /* Indigo */
    --colors-indigo-50: #eef2ff;
    --colors-indigo-100: #e0e7ff;
    --colors-indigo-200: #c6d2ff;
    --colors-indigo-300: #a3b3ff;
    --colors-indigo-400: #7c86ff;
    --colors-indigo-500: #615fff;
    --colors-indigo-700: #432dd7;
    --colors-indigo-800: #372aac;
    --colors-indigo-900: #312c85;
    --colors-indigo-950: #1e1a4d;
    --colors-indigo-default: #4f39f6;
    /* Blue */
    --colors-blue-50: #eff6ff;
    --colors-blue-100: #dbeafe;
    --colors-blue-200: #bedbff;
    --colors-blue-300: #8ec5ff;
    --colors-blue-400: #51a2ff;
    --colors-blue-500: #2b7fff;
    --colors-blue-700: #1447e6;
    --colors-blue-800: #193cb8;
    --colors-blue-900: #1c398e;
    --colors-blue-950: #162456;
    --colors-blue-default: #155dfc;
    /* Amber */
    --colors-amber-50: #fffbeb;
    --colors-amber-100: #fef3c6;
    --colors-amber-200: #fee685;
    --colors-amber-300: #ffd230;
    --colors-amber-400: #ffb900;
    --colors-amber-500: #fe9a00;
    --colors-amber-700: #bb4d00;
    --colors-amber-800: #973c00;
    --colors-amber-900: #7b3306;
    --colors-amber-950: #461901;
    --colors-amber-default: #e17100;
    /* Emerald */
    --colors-emerald-50: #ecfdf5;
    --colors-emerald-100: #d0fae5;
    --colors-emerald-200: #a4f4cf;
    --colors-emerald-300: #5ee9b5;
    --colors-emerald-400: #00d492;
    --colors-emerald-500: #00bc7d;
    --colors-emerald-700: #007a55;
    --colors-emerald-800: #006045;
    --colors-emerald-900: #004f3b;
    --colors-emerald-950: #002c22;
    --colors-emerald-default: #009966;
    /* Purple */
    --colors-purple-50: #faf5ff;
    --colors-purple-100: #f3e8ff;
    --colors-purple-200: #e9d5ff;
    --colors-purple-300: #d8b4fe;
    --colors-purple-400: #c084fc;
    --colors-purple-500: #a855f7;
    --colors-purple-700: #7e22ce;
    --colors-purple-800: #6b21a8;
    --colors-purple-900: #581c87;
    --colors-purple-default: #9333ea;
    /* Orange */
    --colors-orange-50: #fff7ed;
    --colors-orange-100: #ffedd4;
    --colors-orange-200: #ffd6a8;
    --colors-orange-300: #ffb86a;
    --colors-orange-400: #ff8904;
    --colors-orange-500: #ff6900;
    --colors-orange-700: #ca3500;
    --colors-orange-800: #9f2d00;
    --colors-orange-900: #7e2a0c;
    --colors-orange-950: #441306;
    --colors-orange-default: #f54900;
    /* Sky */
    --colors-sky-50: #f0f9ff;
    --colors-sky-100: #dff2fe;
    --colors-sky-200: #b8e6fe;
    --colors-sky-300: #74d4ff;
    --colors-sky-400: #00bcff;
    --colors-sky-500: #00a6f4;
    --colors-sky-700: #0069a8;
    --colors-sky-800: #00598a;
    --colors-sky-900: #024a70;
    --colors-sky-950: #052f4a;
    --colors-sky-default: #0084d1;
    /* Fuchsia */
    --colors-fuchsia-50: #fdf4ff;
    --colors-fuchsia-100: #fae8ff;
    --colors-fuchsia-200: #f6cfff;
    --colors-fuchsia-300: #f4a8ff;
    --colors-fuchsia-400: #ed6aff;
    --colors-fuchsia-500: #e12afb;
    --colors-fuchsia-700: #a800b7;
    --colors-fuchsia-800: #8a0194;
    --colors-fuchsia-900: #721378;
    --colors-fuchsia-950: #4b004f;
    --colors-fuchsia-default: #c800de;
    /* Black */
    --colors-black-0: #00000000;
    --colors-black-10: #0000001a;
    --colors-black-30: #0000004d;
    --colors-black-50: #00000080;
    --colors-black-80: #000000cc;
    --colors-black-100: #000000;
    /* Primary */
    --colors-primary-1: #f5f5f5;
    --colors-primary-2: #eeeeee;
    --colors-primary-3: #cccccc;
    --colors-primary-4: #aaaaaa;
    --colors-primary-5: #888888;
    --colors-primary-6: #666666;
    --colors-primary-7: #444444;
    --colors-primary-8: #333333;
    --colors-primary-10: #111111;
    --colors-primary-11: #000000;
    --colors-primary-default: #222222;
    /* Off-White */
    --colors-off-white-0: #f5f5f500;
    --colors-off-white-10: #f5f5f51a;
    --colors-off-white-30: #f5f5f54d;
    --colors-off-white-50: #f5f5f580;
    --colors-off-white-80: #f5f5f5cc;
    --colors-off-white-100: #f5f5f5;
    /* White */
    --colors-white-0: #ffffff00;
    --colors-white-10: #ffffff1a;
    --colors-white-30: #ffffff4d;
    --colors-white-50: #ffffff80;
    --colors-white-80: #ffffffcc;
    --colors-white-100: #ffffff;
    /* Off-Black */
    --colors-off-black-0: #1d1c1b00;
    --colors-off-black-10: #1d1c1b1a;
    --colors-off-black-30: #1d1c1b4d;
    --colors-off-black-50: #1d1c1b80;
    --colors-off-black-80: #1d1c1bcc;
    --colors-off-black-100: #1d1c1b;
    /* Gradient */
    --colors-gradient: #ffffff;

    /* Component Tokens */
    /* Input Component */
    --input-bg-default: var(--fill-default);
    --input-border-default: var(--border-action);
    --input-border-hover: var(--border-action-hover);
    --input-border-focus: var(--border-information);
    --input-text-default: var(--text-disabled);
    --input-text-focus: var(--text-action);
    --input-label-default: var(--text-label);
    --input-border-success: var(--border-success);
    --input-border-cuation: var(--border-cuation);
    --input-border-warning: var(--border-warning);

    /* Popup Component */
    --popup-surface: var(--surface-primary);
    --popup-bg: var(--fill-default);
    --popup-border: var(--border-secondary);

    /* Menu Tabs Component */
    --menutabs-fill-default: var(--fill-fill-transparent);
    --menutabs-fill-hover: var(--fill-fill-tertiary-hover);
    --menutabs-fill-active: var(--fill-fill-tertiary);
    --menutabs-fill-disabled: var(--fill-fill-tertiary-disabled);
    --menutabs-border-disabled: var(--fill-fill-tertiary-disabled);
    --menutabs-border-active: var(--fill-fill-tertiary-active);
    --menutabs-border-hover: var(--fill-fill-tertiary-hover);
    --menutabs-border-default: var(--fill-fill-tertiary);
    --menutabs-text-active: var(--text-action);
    --menutabs-text-disabled: var(--text-disabled);
    --menutabs-text-hover: var(--text-action-hover);
    --menutabs-text-default: var(--text-disabled);
    --menutabs-icon-hover: var(--icon-action-hover);
    --menutabs-icon-default: var(--icon-disabled);
    --menutabs-icon-disabled: var(--icon-disabled);
    --menutabs-icon-active: var(--icon-action);
    --menutabs-bg-default: var(--fill-default);

    /* Progress Component */
    --progress-fill-default: var(--fill-fill-success);
    --progress-bg: var(--fill-default);
    --progress-fill-complete: var(--fill-fill-success-active);
    --progress-fill-past: var(--fill-fill-primary);
    --progress-fill-new: var(--fill-fill-warning);

    /* Button Primary Component */
    --button-primary-fill-default: var(--fill-fill-primary);
    --button-primary-fill-hover: var(--fill-fill-primary-hover);
    --button-primary-fill-active: var(--fill-fill-primary-active);
    --button-primary-fill-disabled: var(--fill-fill-primary-disabled);
    --button-primary-icon-hover: var(--text-on-hover);
    --button-primary-icon-default: var(--text-on-action);
    --button-primary-text-disabled: var(--text-on-disabled);
    --button-primary-text-active: var(--text-on-action);
    --button-primary-text-hover: var(--text-on-hover);
    --button-primary-text-default: var(--text-on-action);
    --button-primary-icon-disabled: var(--text-on-disabled);
    --button-primary-icon-active: var(--text-on-action);

    /* Button Secondary Component */
    --button-secondary-fill-disabled: var(--fill-fill-secondary-disabled);
    --button-secondary-fill-active: var(--fill-fill-secondary-active);
    --button-secondary-fill-hover: var(--fill-fill-secondary-hover);
    --button-secondary-fill-default: var(--fill-fill-secondary);
    --button-secondary-text-default: var(--text-on-action);
    --button-secondary-text-hover: var(--text-on-hover);
    --button-secondary-text-active: var(--text-on-action);
    --button-secondary-text-disabled: var(--text-on-disabled);
    --button-secondary-icon-disabled: var(--icon-on-disabled);
    --button-secondary-icon-active: var(--icon-on-action);
    --button-secondary-icon-hover: var(--icon-on-hover);
    --button-secondary-icon-default: var(--icon-on-action);

    /* Button Transparent Component */
    --button-transparent-fill-disabled: var(--fill-fill-transparent-disabled);
    --button-transparent-fill-active: var(--fill-fill-transparent-active);
    --button-transparent-fill-hover: var(--fill-fill-transparent-hover);
    --button-transparent-fill-default: var(--fill-fill-transparent);
    --button-transparent-icon-default: var(--icon-action);
    --button-transparent-text-disabled: var(--text-disabled);
    --button-transparent-text-default: var(--text-action);
    --button-transparent-text-active: var(--text-action);
    --button-transparent-icon-hover: var(--icon-action-hover);
    --button-transparent-text-hover: var(--text-action-hover);
    --button-transparent-icon-disabled: var(--icon-disabled);
    --button-transparent-icon-active: var(--icon-action);

    /* Button Tertiary Component */
    --button-tertiery-fill-hover: var(--fill-fill-tertiary-hover);
    --button-tertiery-fill-default: var(--fill-fill-tertiary);
    --button-tertiery-fill-disabled: var(--fill-fill-tertiary-disabled);
    --button-tertiery-fill-active: var(--fill-fill-tertiary-active);
    --button-tertiery-icon-hover: var(--icon-action-hover);
    --button-tertiery-icon-default: var(--icon-action);
    --button-tertiery-text-disabled: var(--text-disabled);
    --button-tertiery-text-active: var(--text-action);
    --button-tertiery-text-hover: var(--text-action-hover);
    --button-tertiery-text-default: var(--text-action);
    --button-tertiery-icon-disabled: var(--icon-disabled);
    --button-tertiery-icon-active: var(--icon-action);
    --button-tertiery-icon-hover-2: var(--icon-on-hover);
    --button-tertiery-icon-default-2: var(--icon-on-action);
    --button-tertiery-text-disabled-2: var(--text-on-disabled);
    --button-tertiery-text-active-2: var(--text-on-action);
    --button-tertiery-text-hover-2: var(--text-on-hover);
    --button-tertiery-text-default-2: var(--text-on-action);
    --button-tertiery-icon-disabled-2: var(--icon-on-disabled);
    --button-tertiery-icon-active-2: var(--icon-on-action);

    /* Button State Colors */
    --button-fill-success: var(--fill-fill-success);
    --button-fill-cuation: var(--fill-fill-cuation);
    --button-fill-warning: var(--fill-fill-warning);
    --button-fill-success-foreground: var(--text-on-action);
    --button-fill-cuation-foreground: var(--text-on-action);
    --button-fill-warning-foreground: var(--text-on-action);
    --button-fill-information: var(--fill-fill-information);
    --button-fill-information-foreground: var(--text-on-action);

    /* Badge Component */
    --badge-running-surface: var(--surface-success);
    --badge-running-surface-foreground: var(--text-success);
    --badge-paused-surface-foreground: var(--text-warning);
    --badge-paused-surface: var(--surface-warning);
    --badge-error-surface-foreground: var(--text-cuation);
    --badge-error-surface: var(--surface-cuation);
    --badge-complete-surface-foreground: var(--text-body);
    --badge-complete-surface: var(--surface-primary);
    --badge-splitting-surface-foreground: var(--text-information);
    --badge-splitting-surface: var(--surface-information);

    /* Switch Component */
    --switch-off-fill-track-fill: var(--fill-fill-primary);
    --switch-off-fill-track-border: var(--border-primary);
    --switch-off-fill-thumb-border: var(--border-primary);
    --switch-off-fill-thumb-fill: var(--fill-default);
    --switch-on-fill-thumb-border: var(--border-success);
    --switch-on-fill-thumb-fill: var(--fill-default);
    --switch-on-fill-track-border: var(--border-success);
    --switch-on-fill-track-fill: var(--fill-fill-success);
    --switch-disabled-fill-thumb-border: var(--border-disabled);
    --switch-disabled-fill-track-border: var(--border-disabled);
    --switch-disabled-fill-thumb-fill: var(--fill-default);
    --switch-disabled-fill-track-fill: var(--fill-fill-primary-disabled);
    /* Pill Component */
    --pill-bg: var(--fill-default);
    --pill-surface: var(--fill-fill-primary);
    --pill-border: var(--border-primary);

    /* Menu Button Component */
    --menubutton-fill-default: var(--fill-fill-transparent);
    --menubutton-fill-hover: var(--fill-fill-transparent-hover);
    --menubutton-fill-active: var(--fill-default);
    --menubutton-border-active: var(--border-primary);
    --menubutton-border-default: var(--border-transparent);
    --menubutton-border-hover: var(--border-disabled);
    --menubutton-disabled: 1.25rem;
    /* Dropdown Component */
    --dropdown-bg: var(--fill-default);
    --dropdown-border: var(--border-secondary);
    --dropdown-item-bg-default: var(--fill-fill-transparent);
    --dropdown-item-bg-hover: var(--fill-fill-tertiary-hover);
    --dropdown-item-bg-active: var(--fill-fill-tertiary);

    /* Search Component */
    --search-bg: var(--fill-default);
    --search-border-hover: var(--border-secondary);
    --search-border-default: var(--border-disabled);
    --search-default: 3.125rem;

    /* Tag Component */
    --tag-surface: var(--button-tertiery-fill-default);
    --tag-fill-browser: var(--fill-browser);
    --tag-fill-developer: var(--fill-developer);
    --tag-fill-document: var(--fill-document);
    --tag-fill-multimodal: var(--fill-multimodal);
    --tag-fill-socialmedia: var(--fill-socialmedia);
    --tag-fill-info: var(--surface-information);
    --tag-text-info: var(--text-information);
    --tag-surface-hover: var(--button-tertiery-fill-hover);
    --tag-fill-success: var(--surface-success);
    --tag-text-success: var(--text-success);

    /* Message Component */
    --message-fill-default: var(--surface-tirtery);
    --message-fill-hover: var(--surface-secondary);
    --message-fill-active: var(--surface-primary);
    --message-border-default: var(--border-disabled);
    --message-border-focus: var(--border-focus);

    /* Task Component */
    --task-surface: var(--surface-tirtery);
    --task-border-default: var(--border-disabled);
    --task-border-focus: var(--border-focus);
    --task-fill-default: var(--fill-fill-tertiary);
    --task-fill-hover: var(--fill-fill-tertiary-hover);
    --task-fill-success: var(--surface-success);
    --task-fill-warning: var(--surface-warning);
    --task-fill-error: var(--surface-cuation);
    --task-border-focus-success: var(--border-success);
    --task-border-focus-warning: var(--border-warning);
    --task-border-focus-error: var(--border-cuation);

    /* Worker Component */
    --worker-surface-primary: var(--surface-tirtery);
    --worker-border-default: var(--border-disabled);
    --worker-border-focus: var(--border-focus);
    --worker-surface-secondary: var(--surface-disabled);

    /* Mask Component */
    --mask-default: var(--bg-secondary);
    --mask-dark: var(--bg-dark-secondary);

    /* Code Component */
    --code-bg: var(--bg-dark-default);
    --code-foreground: var(--text-on-action);
    /* Shadow Tokens */

    /* Perfect Shadow */
    --shadow-perfect:
      0 8px 20px -2px #1d21291a,
      0 32px 48px -12px #1d21291f,
      0 96px 120px -12px #414a5c0f,
      0 108px 72px -16px #414a5c14,
      0 32px 64px -8px #7199bd1f,
      0 8px 10px 0 #7199bd1f;

    /* Button Shadow */
    --shadow-button:
      inset 0 1px 0 0 #ffffff54,
      0 3px 4px -1px #00000040,
      0 0 0 1px #d4d4d440;

  }

  .root,
  [data-theme="transparent"] {
    --text-heading: var(--colors-primary-10);
    --text-body: var(--colors-primary-default);
    --text-label: var(--colors-primary-7);
    --text-action: var(--colors-primary-default);
    --text-action-hover: var(--colors-primary-10);
    --text-disabled: var(--colors-primary-3);
    --text-information: var(--colors-blue-default);
    --text-success: var(--colors-green-default);
    --text-warning: var(--colors-yellow-default);
    --text-cuation: var(--colors-red-default);
    --text-on-action: var(--colors-primary-1);
    --text-on-disabled: var(--colors-off-white-50);
    --text-document: var(--colors-amber-default);
    --text-socialmedia: var(--colors-purple-default);
    --text-browser: var(--colors-sky-default);
    --text-developer: var(--colors-emerald-default);
    --text-multimodal: var(--colors-fuchsia-default);
    --text-on-hover: var(--colors-primary-2);
    --surface-primary: var(--colors-off-white-80);
    --surface-secondary: var(--colors-off-white-50);
    --surface-success: var(--colors-green-50);
    --surface-information: var(--colors-blue-50);
    --surface-warning: var(--colors-yellow-50);
    --surface-cuation: var(--colors-red-50);
    --surface-action: var(--colors-primary-2);
    --surface-action-hover: var(--colors-primary-1);
    --surface-disabled: var(--colors-off-white-30);
    --surface-tirtery: var(--colors-white-100);
    --surface-card: var(--colors-off-white-30);
    --surface-card-hover: var(--colors-off-white-80);
    --surface-card-focus: var(--colors-white-100);
    --surface-card-default: 1.25rem;
    --border-primary: var(--colors-primary-4);
    --border-secondary: var(--colors-primary-3);
    --border-information: var(--colors-blue-default);
    --border-success: var(--colors-green-default);
    --border-warning: var(--colors-yellow-default);
    --border-cuation: var(--colors-red-default);
    --border-focus: var(--colors-primary-5);
    --border-action: var(--colors-primary-3);
    --border-action-hover: var(--colors-primary-4);
    --border-disabled: var(--colors-primary-2);
    --border-developer: var(--colors-emerald-default);
    --border-browser: var(--colors-sky-default);
    --border-socialmedia: var(--colors-purple-default);
    --border-multimodal: var(--colors-fuchsia-default);
    --border-document: var(--colors-amber-default);
    --border-transparent: var(--colors-white-0);
    --icon-primary: var(--colors-primary-default);
    --icon-action: var(--colors-primary-default);
    --icon-disabled: var(--colors-primary-3);
    --icon-information: var(--colors-blue-default);
    --icon-success: var(--colors-green-default);
    --icon-warning: var(--colors-yellow-default);
    --icon-cuation: var(--colors-red-default);
    --icon-action-hover: var(--colors-primary-10);
    --icon-multimodal: var(--colors-fuchsia-default);
    --icon-socialmedia: var(--colors-purple-default);
    --icon-document: var(--colors-amber-default);
    --icon-browser: var(--colors-sky-default);
    --icon-developer: var(--colors-emerald-default);
    --icon-on-disabled: var(--colors-off-white-50);
    --icon-on-hover: var(--colors-primary-2);
    --icon-on-action: var(--colors-primary-1);
    --icon-secondary: var(--colors-primary-5);
    --developer: var(--colors-emerald-default);
    --browser: var(--colors-sky-default);
    --document: var(--colors-amber-default);
    --multimodal: var(--colors-fuchsia-default);
    --socialmedia: var(--colors-purple-default);
    --fill-default: var(--colors-white-100);
    --fill-fill-primary: var(--colors-primary-default);
    --fill-fill-primary-hover: var(--colors-primary-10);
    --fill-fill-primary-active: var(--colors-primary-11);
    --fill-fill-primary-disabled: var(--colors-primary-5);
    --fill-fill-tertiary: var(--colors-primary-1);
    --fill-fill-transparent: var(--colors-white-0);
    --fill-fill-transparent-hover: var(--colors-primary-1);
    --fill-fill-tertiary-hover: var(--colors-primary-2);
    --fill-fill-tertiary-active: var(--colors-primary-3);
    --fill-fill-tertiary-disabled: var(--colors-off-white-50);
    --fill-fill-transparent-active: var(--colors-primary-2);
    --fill-fill-transparent-disabled: var(--colors-white-0);
    --fill-fill-secondary-disabled: var(--colors-primary-4);
    --fill-fill-secondary-active: var(--colors-primary-7);
    --fill-fill-secondary-hover: var(--colors-primary-6);
    --fill-fill-secondary: var(--colors-primary-5);
    --fill-fill-success: var(--colors-green-default);
    --fill-fill-success-hover: var(--colors-green-700);
    --fill-fill-success-active: var(--colors-green-800);
    --fill-fill-success-disable: var(--colors-green-400);
    --fill-fill-warning: var(--colors-yellow-default);
    --fill-fill-cuation: var(--colors-red-default);
    --fill-socialmedia: var(--colors-purple-100);
    --fill-document: var(--colors-amber-100);
    --fill-browser: var(--colors-sky-100);
    --fill-multimodal: var(--colors-fuchsia-100);
    --fill-developer: var(--colors-emerald-100);
    --fill-scrollbar-dark: var(--colors-primary-3);
    --fill-scrollbar-light: var(--colors-primary-1);
    --fill-skeloten-default: var(--colors-primary-2);
    --fill-fill-information: var(--colors-blue-default);
    --bg-page: var(--colors-off-white-80);
    --bg-primary: var(--colors-off-white-50);
    --bg-secondary: var(--colors-off-white-30);
    --bg-tertiary: var(--colors-off-white-10);
    --bg-dark: var(--colors-off-black-80);
    --bg-dark-primary: var(--colors-off-black-50);
    --bg-dark-secondary: var(--colors-off-black-30);
    --bg-dark-tertiary: var(--colors-off-black-10);
    --bg-dark-default: var(--colors-off-black-100);
    --bg-page-default: var(--colors-off-white-100);
  }

  .root,
  [data-theme="light"] {
    --text-heading: var(--colors-primary-10);
    --text-body: var(--colors-primary-default);
    --text-label: var(--colors-primary-7);
    --text-action: var(--colors-primary-default);
    --text-action-hover: var(--colors-primary-10);
    --text-disabled: var(--colors-primary-3);
    --text-information: var(--colors-blue-default);
    --text-success: var(--colors-green-default);
    --text-warning: var(--colors-yellow-default);
    --text-cuation: var(--colors-red-default);
    --text-on-action: var(--colors-primary-1);
    --text-on-disabled: var(--colors-primary-1);
    --text-document: var(--colors-amber-default);
    --text-socialmedia: var(--colors-purple-default);
    --text-browser: var(--colors-sky-default);
    --text-developer: var(--colors-emerald-default);
    --text-multimodal: var(--colors-fuchsia-default);
    --text-on-hover: var(--colors-primary-2);
    --surface-primary: var(--colors-off-white-100);
    --surface-secondary: var(--colors-primary-2);
    --surface-success: var(--colors-green-50);
    --surface-information: var(--colors-blue-50);
    --surface-warning: var(--colors-yellow-50);
    --surface-cuation: var(--colors-red-50);
    --surface-action: var(--colors-primary-2);
    --surface-action-hover: var(--colors-primary-1);
    --surface-disabled: var(--colors-off-white-30);
    --surface-tirtery: var(--colors-white-100);
    --surface-card: var(--colors-off-white-30);
    --surface-card-hover: var(--colors-off-white-80);
    --surface-card-focus: var(--colors-white-100);
    --surface-card-default: 1.25rem;
    --border-primary: var(--colors-primary-4);
    --border-secondary: var(--colors-primary-3);
    --border-information: var(--colors-blue-default);
    --border-success: var(--colors-green-default);
    --border-warning: var(--colors-yellow-default);
    --border-cuation: var(--colors-red-default);
    --border-focus: var(--colors-primary-5);
    --border-action: var(--colors-primary-3);
    --border-action-hover: var(--colors-primary-4);
    --border-disabled: var(--colors-primary-2);
    --border-developer: var(--colors-emerald-default);
    --border-browser: var(--colors-sky-default);
    --border-socialmedia: var(--colors-purple-default);
    --border-multimodal: var(--colors-fuchsia-default);
    --border-document: var(--colors-amber-default);
    --border-transparent: var(--colors-white-0);
    --icon-primary: var(--colors-primary-default);
    --icon-action: var(--colors-primary-default);
    --icon-disabled: var(--colors-primary-3);
    --icon-information: var(--colors-blue-default);
    --icon-success: var(--colors-green-default);
    --icon-warning: var(--colors-yellow-default);
    --icon-cuation: var(--colors-red-default);
    --icon-action-hover: var(--colors-primary-10);
    --icon-multimodal: var(--colors-fuchsia-default);
    --icon-socialmedia: var(--colors-purple-default);
    --icon-document: var(--colors-amber-default);
    --icon-browser: var(--colors-sky-default);
    --icon-developer: var(--colors-emerald-default);
    --icon-on-disabled: var(--colors-off-white-50);
    --icon-on-hover: var(--colors-primary-2);
    --icon-on-action: var(--colors-primary-1);
    --icon-secondary: var(--colors-primary-5);
    --developer: var(--colors-emerald-default);
    --browser: var(--colors-sky-default);
    --document: var(--colors-amber-default);
    --multimodal: var(--colors-fuchsia-default);
    --socialmedia: var(--colors-purple-default);
    --fill-default: var(--colors-white-100);
    --fill-fill-primary: var(--colors-primary-default);
    --fill-fill-primary-hover: var(--colors-primary-10);
    --fill-fill-primary-active: var(--colors-primary-11);
    --fill-fill-primary-disabled: var(--colors-primary-5);
    --fill-fill-tertiary: var(--colors-primary-1);
    --fill-fill-transparent: var(--colors-white-0);
    --fill-fill-transparent-hover: var(--colors-primary-1);
    --fill-fill-tertiary-hover: var(--colors-primary-2);
    --fill-fill-tertiary-active: var(--colors-primary-3);
    --fill-fill-tertiary-disabled: var(--colors-primary-2);
    --fill-fill-transparent-active: var(--colors-primary-2);
    --fill-fill-transparent-disabled: var(--colors-white-0);
    --fill-fill-secondary-disabled: var(--colors-primary-4);
    --fill-fill-secondary-active: var(--colors-primary-7);
    --fill-fill-secondary-hover: var(--colors-primary-6);
    --fill-fill-secondary: var(--colors-primary-5);
    --fill-fill-success: var(--colors-green-default);
    --fill-fill-success-hover: var(--colors-green-700);
    --fill-fill-success-active: var(--colors-green-800);
    --fill-fill-success-disable: var(--colors-green-400);
    --fill-fill-warning: var(--colors-yellow-default);
    --fill-fill-cuation: var(--colors-red-default);
    --fill-socialmedia: var(--colors-purple-100);
    --fill-document: var(--colors-amber-100);
    --fill-browser: var(--colors-sky-100);
    --fill-multimodal: var(--colors-fuchsia-100);
    --fill-developer: var(--colors-emerald-100);
    --fill-scrollbar-dark: var(--colors-primary-3);
    --fill-scrollbar-light: var(--colors-primary-1);
    --fill-skeloten-default: var(--colors-primary-2);
    --fill-fill-information: var(--colors-blue-default);
    --bg-page: var(--colors-off-white-100);
    --bg-primary: var(--colors-primary-1);
    --bg-secondary: var(--colors-primary-2);
    --bg-tertiary: var(--colors-primary-3);
    --bg-dark: var(--colors-off-black-80);
    --bg-dark-primary: var(--colors-off-black-50);
    --bg-dark-secondary: var(--colors-off-black-30);
    --bg-dark-tertiary: var(--colors-off-black-10);
    --bg-dark-default: var(--colors-off-black-100);
    --bg-page-default: var(--colors-off-white-100);
  }

}