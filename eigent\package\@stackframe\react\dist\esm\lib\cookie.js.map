{"version": 3, "sources": ["../../../src/lib/cookie.ts", "../../../../../node_modules/.pnpm/oauth4webapi@2.10.4/node_modules/oauth4webapi/build/index.js"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { isBrowserLike } from '@stackframe/stack-shared/dist/utils/env';\nimport { StackAssertionError } from '@stackframe/stack-shared/dist/utils/errors';\nimport Cookies from \"js-cookie\";\nimport { calculatePKCECodeChallenge, generateRandomCodeVerifier, generateRandomState } from \"oauth4webapi\";\n\ntype SetCookieOptions = { maxAge?: number, noOpIfServerComponent?: boolean };\ntype DeleteCookieOptions = { noOpIfServerComponent?: boolean };\n\nfunction ensureClient() {\n  if (!isBrowserLike()) {\n    throw new Error(\"cookieClient functions can only be called in a browser environment, yet window is undefined\");\n  }\n}\n\nexport type CookieHelper = {\n  get: (name: string) => string | null,\n  set: (name: string, value: string, options: SetCookieOptions) => void,\n  setOrDelete: (name: string, value: string | null, options: SetCookieOptions & DeleteCookieOptions) => void,\n  delete: (name: string, options: DeleteCookieOptions) => void,\n};\n\nconst placeholderCookieHelperIdentity = { \"placeholder cookie helper identity\": true };\nexport async function createPlaceholderCookieHelper(): Promise<CookieHelper> {\n  function throwError(): never {\n    throw new StackAssertionError(\"Throwing cookie helper is just a placeholder. This should never be called\");\n  }\n  return {\n    get: throwError,\n    set: throwError,\n    setOrDelete: throwError,\n    delete: throwError,\n  };\n}\n\nexport async function createCookieHelper(): Promise<CookieHelper> {\n  if (isBrowserLike()) {\n    return createBrowserCookieHelper();\n  } else {\n    return await createPlaceholderCookieHelper();\n  }\n}\n\nexport function createBrowserCookieHelper(): CookieHelper {\n  return {\n    get: getCookieClient,\n    set: setCookieClient,\n    setOrDelete: setOrDeleteCookieClient,\n    delete: deleteCookieClient,\n  };\n}\n\nfunction handleCookieError(e: unknown, options: DeleteCookieOptions | SetCookieOptions) {\n  if (e instanceof Error && e.message.includes(\"Cookies can only be modified in\")) {\n    if (options.noOpIfServerComponent) {\n      // ignore\n    } else {\n      throw new StackAssertionError(\"Attempted to set cookie in server component. Pass { noOpIfServerComponent: true } in the options of Stack's cookie functions if this is intentional and you want to ignore this error. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options\");\n    }\n  } else {\n    throw e;\n  }\n}\n\n\nexport function getCookieClient(name: string): string | null {\n  ensureClient();\n  // set a helper cookie, see comment in `NextCookieHelper.set` above\n  Cookies.set(\"stack-is-https\", \"true\", { secure: true });\n  return Cookies.get(name) ?? null;\n}\n\nexport async function getCookie(name: string): Promise<string | null> {\n  const cookieHelper = await createCookieHelper();\n  return cookieHelper.get(name);\n}\n\nexport function setOrDeleteCookieClient(name: string, value: string | null, options: SetCookieOptions & DeleteCookieOptions = {}) {\n  ensureClient();\n  if (value === null) {\n    deleteCookieClient(name, options);\n  } else {\n    setCookieClient(name, value, options);\n  }\n}\n\nexport async function setOrDeleteCookie(name: string, value: string | null, options: SetCookieOptions & DeleteCookieOptions = {}) {\n  const cookieHelper = await createCookieHelper();\n  cookieHelper.setOrDelete(name, value, options);\n}\n\nexport function deleteCookieClient(name: string, options: DeleteCookieOptions = {}) {\n  ensureClient();\n  Cookies.remove(name);\n}\n\nexport async function deleteCookie(name: string, options: DeleteCookieOptions = {}) {\n  const cookieHelper = await createCookieHelper();\n  cookieHelper.delete(name, options);\n}\n\nexport function setCookieClient(name: string, value: string, options: SetCookieOptions = {}) {\n  ensureClient();\n  Cookies.set(name, value, {\n    expires: options.maxAge === undefined ? undefined : new Date(Date.now() + (options.maxAge) * 1000),\n  });\n}\n\nexport async function setCookie(name: string, value: string, options: SetCookieOptions = {}) {\n  const cookieHelper = await createCookieHelper();\n  cookieHelper.set(name, value, options);\n}\n\nexport async function saveVerifierAndState() {\n  const codeVerifier = generateRandomCodeVerifier();\n  const codeChallenge = await calculatePKCECodeChallenge(codeVerifier);\n  const state = generateRandomState();\n\n  await setCookie(\"stack-oauth-outer-\" + state, codeVerifier, { maxAge: 60 * 60 });\n\n  return {\n    codeChallenge,\n    state,\n  };\n}\n\nexport function consumeVerifierAndStateCookie(state: string) {\n  ensureClient();\n  const cookieName = \"stack-oauth-outer-\" + state;\n  const codeVerifier = getCookieClient(cookieName);\n  if (!codeVerifier) {\n    return null;\n  }\n  deleteCookieClient(cookieName);\n  return {\n    codeVerifier,\n  };\n}\n", "let USER_AGENT;\nif (typeof navigator === 'undefined' || !navigator.userAgent?.startsWith?.('Mozilla/5.0 ')) {\n    const NAME = 'oauth4webapi';\n    const VERSION = 'v2.10.4';\n    USER_AGENT = `${NAME}/${VERSION}`;\n}\nfunction looseInstanceOf(input, expected) {\n    if (input == null) {\n        return false;\n    }\n    try {\n        return (input instanceof expected ||\n            Object.getPrototypeOf(input)[Symbol.toStringTag] === expected.prototype[Symbol.toStringTag]);\n    }\n    catch {\n        return false;\n    }\n}\nexport const clockSkew = Symbol();\nexport const clockTolerance = Symbol();\nexport const customFetch = Symbol();\nexport const useMtlsAlias = Symbol();\nconst encoder = new TextEncoder();\nconst decoder = new TextDecoder();\nfunction buf(input) {\n    if (typeof input === 'string') {\n        return encoder.encode(input);\n    }\n    return decoder.decode(input);\n}\nconst CHUNK_SIZE = 0x8000;\nfunction encodeBase64Url(input) {\n    if (input instanceof ArrayBuffer) {\n        input = new Uint8Array(input);\n    }\n    const arr = [];\n    for (let i = 0; i < input.byteLength; i += CHUNK_SIZE) {\n        arr.push(String.fromCharCode.apply(null, input.subarray(i, i + CHUNK_SIZE)));\n    }\n    return btoa(arr.join('')).replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\nfunction decodeBase64Url(input) {\n    try {\n        const binary = atob(input.replace(/-/g, '+').replace(/_/g, '/').replace(/\\s/g, ''));\n        const bytes = new Uint8Array(binary.length);\n        for (let i = 0; i < binary.length; i++) {\n            bytes[i] = binary.charCodeAt(i);\n        }\n        return bytes;\n    }\n    catch (cause) {\n        throw new OPE('The input to be decoded is not correctly encoded.', { cause });\n    }\n}\nfunction b64u(input) {\n    if (typeof input === 'string') {\n        return decodeBase64Url(input);\n    }\n    return encodeBase64Url(input);\n}\nclass LRU {\n    constructor(maxSize) {\n        this.cache = new Map();\n        this._cache = new Map();\n        this.maxSize = maxSize;\n    }\n    get(key) {\n        let v = this.cache.get(key);\n        if (v) {\n            return v;\n        }\n        if ((v = this._cache.get(key))) {\n            this.update(key, v);\n            return v;\n        }\n        return undefined;\n    }\n    has(key) {\n        return this.cache.has(key) || this._cache.has(key);\n    }\n    set(key, value) {\n        if (this.cache.has(key)) {\n            this.cache.set(key, value);\n        }\n        else {\n            this.update(key, value);\n        }\n        return this;\n    }\n    delete(key) {\n        if (this.cache.has(key)) {\n            return this.cache.delete(key);\n        }\n        if (this._cache.has(key)) {\n            return this._cache.delete(key);\n        }\n        return false;\n    }\n    update(key, value) {\n        this.cache.set(key, value);\n        if (this.cache.size >= this.maxSize) {\n            this._cache = this.cache;\n            this.cache = new Map();\n        }\n    }\n}\nexport class UnsupportedOperationError extends Error {\n    constructor(message) {\n        super(message ?? 'operation not supported');\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nexport class OperationProcessingError extends Error {\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nconst OPE = OperationProcessingError;\nconst dpopNonces = new LRU(100);\nfunction isCryptoKey(key) {\n    return key instanceof CryptoKey;\n}\nfunction isPrivateKey(key) {\n    return isCryptoKey(key) && key.type === 'private';\n}\nfunction isPublicKey(key) {\n    return isCryptoKey(key) && key.type === 'public';\n}\nconst SUPPORTED_JWS_ALGS = [\n    'PS256',\n    'ES256',\n    'RS256',\n    'PS384',\n    'ES384',\n    'RS384',\n    'PS512',\n    'ES512',\n    'RS512',\n    'EdDSA',\n];\nfunction processDpopNonce(response) {\n    try {\n        const nonce = response.headers.get('dpop-nonce');\n        if (nonce) {\n            dpopNonces.set(new URL(response.url).origin, nonce);\n        }\n    }\n    catch { }\n    return response;\n}\nfunction normalizeTyp(value) {\n    return value.toLowerCase().replace(/^application\\//, '');\n}\nfunction isJsonObject(input) {\n    if (input === null || typeof input !== 'object' || Array.isArray(input)) {\n        return false;\n    }\n    return true;\n}\nfunction prepareHeaders(input) {\n    if (looseInstanceOf(input, Headers)) {\n        input = Object.fromEntries(input.entries());\n    }\n    const headers = new Headers(input);\n    if (USER_AGENT && !headers.has('user-agent')) {\n        headers.set('user-agent', USER_AGENT);\n    }\n    if (headers.has('authorization')) {\n        throw new TypeError('\"options.headers\" must not include the \"authorization\" header name');\n    }\n    if (headers.has('dpop')) {\n        throw new TypeError('\"options.headers\" must not include the \"dpop\" header name');\n    }\n    return headers;\n}\nfunction signal(value) {\n    if (typeof value === 'function') {\n        value = value();\n    }\n    if (!(value instanceof AbortSignal)) {\n        throw new TypeError('\"options.signal\" must return or be an instance of AbortSignal');\n    }\n    return value;\n}\nexport async function discoveryRequest(issuerIdentifier, options) {\n    if (!(issuerIdentifier instanceof URL)) {\n        throw new TypeError('\"issuerIdentifier\" must be an instance of URL');\n    }\n    if (issuerIdentifier.protocol !== 'https:' && issuerIdentifier.protocol !== 'http:') {\n        throw new TypeError('\"issuer.protocol\" must be \"https:\" or \"http:\"');\n    }\n    const url = new URL(issuerIdentifier.href);\n    switch (options?.algorithm) {\n        case undefined:\n        case 'oidc':\n            url.pathname = `${url.pathname}/.well-known/openid-configuration`.replace('//', '/');\n            break;\n        case 'oauth2':\n            if (url.pathname === '/') {\n                url.pathname = '.well-known/oauth-authorization-server';\n            }\n            else {\n                url.pathname = `.well-known/oauth-authorization-server/${url.pathname}`.replace('//', '/');\n            }\n            break;\n        default:\n            throw new TypeError('\"options.algorithm\" must be \"oidc\" (default), or \"oauth2\"');\n    }\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    return (options?.[customFetch] || fetch)(url.href, {\n        headers: Object.fromEntries(headers.entries()),\n        method: 'GET',\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : null,\n    }).then(processDpopNonce);\n}\nfunction validateString(input) {\n    return typeof input === 'string' && input.length !== 0;\n}\nexport async function processDiscoveryResponse(expectedIssuerIdentifier, response) {\n    if (!(expectedIssuerIdentifier instanceof URL)) {\n        throw new TypeError('\"expectedIssuer\" must be an instance of URL');\n    }\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        throw new OPE('\"response\" is not a conform Authorization Server Metadata response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.issuer)) {\n        throw new OPE('\"response\" body \"issuer\" property must be a non-empty string');\n    }\n    if (new URL(json.issuer).href !== expectedIssuerIdentifier.href) {\n        throw new OPE('\"response\" body \"issuer\" does not match \"expectedIssuer\"');\n    }\n    return json;\n}\nfunction randomBytes() {\n    return b64u(crypto.getRandomValues(new Uint8Array(32)));\n}\nexport function generateRandomCodeVerifier() {\n    return randomBytes();\n}\nexport function generateRandomState() {\n    return randomBytes();\n}\nexport function generateRandomNonce() {\n    return randomBytes();\n}\nexport async function calculatePKCECodeChallenge(codeVerifier) {\n    if (!validateString(codeVerifier)) {\n        throw new TypeError('\"codeVerifier\" must be a non-empty string');\n    }\n    return b64u(await crypto.subtle.digest('SHA-256', buf(codeVerifier)));\n}\nfunction getKeyAndKid(input) {\n    if (input instanceof CryptoKey) {\n        return { key: input };\n    }\n    if (!(input?.key instanceof CryptoKey)) {\n        return {};\n    }\n    if (input.kid !== undefined && !validateString(input.kid)) {\n        throw new TypeError('\"kid\" must be a non-empty string');\n    }\n    return { key: input.key, kid: input.kid };\n}\nfunction formUrlEncode(token) {\n    return encodeURIComponent(token).replace(/%20/g, '+');\n}\nfunction clientSecretBasic(clientId, clientSecret) {\n    const username = formUrlEncode(clientId);\n    const password = formUrlEncode(clientSecret);\n    const credentials = btoa(`${username}:${password}`);\n    return `Basic ${credentials}`;\n}\nfunction psAlg(key) {\n    switch (key.algorithm.hash.name) {\n        case 'SHA-256':\n            return 'PS256';\n        case 'SHA-384':\n            return 'PS384';\n        case 'SHA-512':\n            return 'PS512';\n        default:\n            throw new UnsupportedOperationError('unsupported RsaHashedKeyAlgorithm hash name');\n    }\n}\nfunction rsAlg(key) {\n    switch (key.algorithm.hash.name) {\n        case 'SHA-256':\n            return 'RS256';\n        case 'SHA-384':\n            return 'RS384';\n        case 'SHA-512':\n            return 'RS512';\n        default:\n            throw new UnsupportedOperationError('unsupported RsaHashedKeyAlgorithm hash name');\n    }\n}\nfunction esAlg(key) {\n    switch (key.algorithm.namedCurve) {\n        case 'P-256':\n            return 'ES256';\n        case 'P-384':\n            return 'ES384';\n        case 'P-521':\n            return 'ES512';\n        default:\n            throw new UnsupportedOperationError('unsupported EcKeyAlgorithm namedCurve');\n    }\n}\nfunction keyToJws(key) {\n    switch (key.algorithm.name) {\n        case 'RSA-PSS':\n            return psAlg(key);\n        case 'RSASSA-PKCS1-v1_5':\n            return rsAlg(key);\n        case 'ECDSA':\n            return esAlg(key);\n        case 'Ed25519':\n        case 'Ed448':\n            return 'EdDSA';\n        default:\n            throw new UnsupportedOperationError('unsupported CryptoKey algorithm name');\n    }\n}\nfunction getClockSkew(client) {\n    const skew = client?.[clockSkew];\n    return typeof skew === 'number' && Number.isFinite(skew) ? skew : 0;\n}\nfunction getClockTolerance(client) {\n    const tolerance = client?.[clockTolerance];\n    return typeof tolerance === 'number' && Number.isFinite(tolerance) && Math.sign(tolerance) !== -1\n        ? tolerance\n        : 30;\n}\nfunction epochTime() {\n    return Math.floor(Date.now() / 1000);\n}\nfunction clientAssertion(as, client) {\n    const now = epochTime() + getClockSkew(client);\n    return {\n        jti: randomBytes(),\n        aud: [as.issuer, as.token_endpoint],\n        exp: now + 60,\n        iat: now,\n        nbf: now,\n        iss: client.client_id,\n        sub: client.client_id,\n    };\n}\nasync function privateKeyJwt(as, client, key, kid) {\n    return jwt({\n        alg: keyToJws(key),\n        kid,\n    }, clientAssertion(as, client), key);\n}\nfunction assertAs(as) {\n    if (typeof as !== 'object' || as === null) {\n        throw new TypeError('\"as\" must be an object');\n    }\n    if (!validateString(as.issuer)) {\n        throw new TypeError('\"as.issuer\" property must be a non-empty string');\n    }\n    return true;\n}\nfunction assertClient(client) {\n    if (typeof client !== 'object' || client === null) {\n        throw new TypeError('\"client\" must be an object');\n    }\n    if (!validateString(client.client_id)) {\n        throw new TypeError('\"client.client_id\" property must be a non-empty string');\n    }\n    return true;\n}\nfunction assertClientSecret(clientSecret) {\n    if (!validateString(clientSecret)) {\n        throw new TypeError('\"client.client_secret\" property must be a non-empty string');\n    }\n    return clientSecret;\n}\nfunction assertNoClientPrivateKey(clientAuthMethod, clientPrivateKey) {\n    if (clientPrivateKey !== undefined) {\n        throw new TypeError(`\"options.clientPrivateKey\" property must not be provided when ${clientAuthMethod} client authentication method is used.`);\n    }\n}\nfunction assertNoClientSecret(clientAuthMethod, clientSecret) {\n    if (clientSecret !== undefined) {\n        throw new TypeError(`\"client.client_secret\" property must not be provided when ${clientAuthMethod} client authentication method is used.`);\n    }\n}\nasync function clientAuthentication(as, client, body, headers, clientPrivateKey) {\n    body.delete('client_secret');\n    body.delete('client_assertion_type');\n    body.delete('client_assertion');\n    switch (client.token_endpoint_auth_method) {\n        case undefined:\n        case 'client_secret_basic': {\n            assertNoClientPrivateKey('client_secret_basic', clientPrivateKey);\n            headers.set('authorization', clientSecretBasic(client.client_id, assertClientSecret(client.client_secret)));\n            break;\n        }\n        case 'client_secret_post': {\n            assertNoClientPrivateKey('client_secret_post', clientPrivateKey);\n            body.set('client_id', client.client_id);\n            body.set('client_secret', assertClientSecret(client.client_secret));\n            break;\n        }\n        case 'private_key_jwt': {\n            assertNoClientSecret('private_key_jwt', client.client_secret);\n            if (clientPrivateKey === undefined) {\n                throw new TypeError('\"options.clientPrivateKey\" must be provided when \"client.token_endpoint_auth_method\" is \"private_key_jwt\"');\n            }\n            const { key, kid } = getKeyAndKid(clientPrivateKey);\n            if (!isPrivateKey(key)) {\n                throw new TypeError('\"options.clientPrivateKey.key\" must be a private CryptoKey');\n            }\n            body.set('client_id', client.client_id);\n            body.set('client_assertion_type', 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer');\n            body.set('client_assertion', await privateKeyJwt(as, client, key, kid));\n            break;\n        }\n        case 'tls_client_auth':\n        case 'self_signed_tls_client_auth':\n        case 'none': {\n            assertNoClientSecret(client.token_endpoint_auth_method, client.client_secret);\n            assertNoClientPrivateKey(client.token_endpoint_auth_method, clientPrivateKey);\n            body.set('client_id', client.client_id);\n            break;\n        }\n        default:\n            throw new UnsupportedOperationError('unsupported client token_endpoint_auth_method');\n    }\n}\nasync function jwt(header, claimsSet, key) {\n    if (!key.usages.includes('sign')) {\n        throw new TypeError('CryptoKey instances used for signing assertions must include \"sign\" in their \"usages\"');\n    }\n    const input = `${b64u(buf(JSON.stringify(header)))}.${b64u(buf(JSON.stringify(claimsSet)))}`;\n    const signature = b64u(await crypto.subtle.sign(keyToSubtle(key), key, buf(input)));\n    return `${input}.${signature}`;\n}\nexport async function issueRequestObject(as, client, parameters, privateKey) {\n    assertAs(as);\n    assertClient(client);\n    parameters = new URLSearchParams(parameters);\n    const { key, kid } = getKeyAndKid(privateKey);\n    if (!isPrivateKey(key)) {\n        throw new TypeError('\"privateKey.key\" must be a private CryptoKey');\n    }\n    parameters.set('client_id', client.client_id);\n    const now = epochTime() + getClockSkew(client);\n    const claims = {\n        ...Object.fromEntries(parameters.entries()),\n        jti: randomBytes(),\n        aud: as.issuer,\n        exp: now + 60,\n        iat: now,\n        nbf: now,\n        iss: client.client_id,\n    };\n    let resource;\n    if (parameters.has('resource') &&\n        (resource = parameters.getAll('resource')) &&\n        resource.length > 1) {\n        claims.resource = resource;\n    }\n    {\n        let value = parameters.get('max_age');\n        if (value !== null) {\n            claims.max_age = parseInt(value, 10);\n            if (!Number.isFinite(claims.max_age)) {\n                throw new OPE('\"max_age\" parameter must be a number');\n            }\n        }\n    }\n    {\n        let value = parameters.get('claims');\n        if (value !== null) {\n            try {\n                claims.claims = JSON.parse(value);\n            }\n            catch (cause) {\n                throw new OPE('failed to parse the \"claims\" parameter as JSON', { cause });\n            }\n            if (!isJsonObject(claims.claims)) {\n                throw new OPE('\"claims\" parameter must be a JSON with a top level object');\n            }\n        }\n    }\n    {\n        let value = parameters.get('authorization_details');\n        if (value !== null) {\n            try {\n                claims.authorization_details = JSON.parse(value);\n            }\n            catch (cause) {\n                throw new OPE('failed to parse the \"authorization_details\" parameter as JSON', { cause });\n            }\n            if (!Array.isArray(claims.authorization_details)) {\n                throw new OPE('\"authorization_details\" parameter must be a JSON with a top level array');\n            }\n        }\n    }\n    return jwt({\n        alg: keyToJws(key),\n        typ: 'oauth-authz-req+jwt',\n        kid,\n    }, claims, key);\n}\nasync function dpopProofJwt(headers, options, url, htm, clockSkew, accessToken) {\n    const { privateKey, publicKey, nonce = dpopNonces.get(url.origin) } = options;\n    if (!isPrivateKey(privateKey)) {\n        throw new TypeError('\"DPoP.privateKey\" must be a private CryptoKey');\n    }\n    if (!isPublicKey(publicKey)) {\n        throw new TypeError('\"DPoP.publicKey\" must be a public CryptoKey');\n    }\n    if (nonce !== undefined && !validateString(nonce)) {\n        throw new TypeError('\"DPoP.nonce\" must be a non-empty string or undefined');\n    }\n    if (!publicKey.extractable) {\n        throw new TypeError('\"DPoP.publicKey.extractable\" must be true');\n    }\n    const now = epochTime() + clockSkew;\n    const proof = await jwt({\n        alg: keyToJws(privateKey),\n        typ: 'dpop+jwt',\n        jwk: await publicJwk(publicKey),\n    }, {\n        iat: now,\n        jti: randomBytes(),\n        htm,\n        nonce,\n        htu: `${url.origin}${url.pathname}`,\n        ath: accessToken ? b64u(await crypto.subtle.digest('SHA-256', buf(accessToken))) : undefined,\n    }, privateKey);\n    headers.set('dpop', proof);\n}\nlet jwkCache;\nasync function getSetPublicJwkCache(key) {\n    const { kty, e, n, x, y, crv } = await crypto.subtle.exportKey('jwk', key);\n    const jwk = { kty, e, n, x, y, crv };\n    jwkCache.set(key, jwk);\n    return jwk;\n}\nasync function publicJwk(key) {\n    jwkCache || (jwkCache = new WeakMap());\n    return jwkCache.get(key) || getSetPublicJwkCache(key);\n}\nfunction validateEndpoint(value, endpoint, options) {\n    if (typeof value !== 'string') {\n        if (options?.[useMtlsAlias]) {\n            throw new TypeError(`\"as.mtls_endpoint_aliases.${endpoint}\" must be a string`);\n        }\n        throw new TypeError(`\"as.${endpoint}\" must be a string`);\n    }\n    return new URL(value);\n}\nfunction resolveEndpoint(as, endpoint, options) {\n    if (options?.[useMtlsAlias] && as.mtls_endpoint_aliases && endpoint in as.mtls_endpoint_aliases) {\n        return validateEndpoint(as.mtls_endpoint_aliases[endpoint], endpoint, options);\n    }\n    return validateEndpoint(as[endpoint], endpoint);\n}\nexport async function pushedAuthorizationRequest(as, client, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'pushed_authorization_request_endpoint', options);\n    const body = new URLSearchParams(parameters);\n    body.set('client_id', client.client_id);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    if (options?.DPoP !== undefined) {\n        await dpopProofJwt(headers, options.DPoP, url, 'POST', getClockSkew(client));\n    }\n    return authenticatedRequest(as, client, 'POST', url, body, headers, options);\n}\nexport function isOAuth2Error(input) {\n    const value = input;\n    if (typeof value !== 'object' || Array.isArray(value) || value === null) {\n        return false;\n    }\n    return value.error !== undefined;\n}\nfunction unquote(value) {\n    if (value.length >= 2 && value[0] === '\"' && value[value.length - 1] === '\"') {\n        return value.slice(1, -1);\n    }\n    return value;\n}\nconst SPLIT_REGEXP = /((?:,|, )?[0-9a-zA-Z!#$%&'*+-.^_`|~]+=)/;\nconst SCHEMES_REGEXP = /(?:^|, ?)([0-9a-zA-Z!#$%&'*+\\-.^_`|~]+)(?=$|[ ,])/g;\nfunction wwwAuth(scheme, params) {\n    const arr = params.split(SPLIT_REGEXP).slice(1);\n    if (!arr.length) {\n        return { scheme: scheme.toLowerCase(), parameters: {} };\n    }\n    arr[arr.length - 1] = arr[arr.length - 1].replace(/,$/, '');\n    const parameters = {};\n    for (let i = 1; i < arr.length; i += 2) {\n        const idx = i;\n        if (arr[idx][0] === '\"') {\n            while (arr[idx].slice(-1) !== '\"' && ++i < arr.length) {\n                arr[idx] += arr[i];\n            }\n        }\n        const key = arr[idx - 1].replace(/^(?:, ?)|=$/g, '').toLowerCase();\n        parameters[key] = unquote(arr[idx]);\n    }\n    return {\n        scheme: scheme.toLowerCase(),\n        parameters,\n    };\n}\nexport function parseWwwAuthenticateChallenges(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    const header = response.headers.get('www-authenticate');\n    if (header === null) {\n        return undefined;\n    }\n    const result = [];\n    for (const { 1: scheme, index } of header.matchAll(SCHEMES_REGEXP)) {\n        result.push([scheme, index]);\n    }\n    if (!result.length) {\n        return undefined;\n    }\n    const challenges = result.map(([scheme, indexOf], i, others) => {\n        const next = others[i + 1];\n        let parameters;\n        if (next) {\n            parameters = header.slice(indexOf, next[1]);\n        }\n        else {\n            parameters = header.slice(indexOf);\n        }\n        return wwwAuth(scheme, parameters);\n    });\n    return challenges;\n}\nexport async function processPushedAuthorizationResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 201) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Pushed Authorization Request Endpoint response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.request_uri)) {\n        throw new OPE('\"response\" body \"request_uri\" property must be a non-empty string');\n    }\n    if (typeof json.expires_in !== 'number' || json.expires_in <= 0) {\n        throw new OPE('\"response\" body \"expires_in\" property must be a positive number');\n    }\n    return json;\n}\nexport async function protectedResourceRequest(accessToken, method, url, headers, body, options) {\n    if (!validateString(accessToken)) {\n        throw new TypeError('\"accessToken\" must be a non-empty string');\n    }\n    if (!(url instanceof URL)) {\n        throw new TypeError('\"url\" must be an instance of URL');\n    }\n    headers = prepareHeaders(headers);\n    if (options?.DPoP === undefined) {\n        headers.set('authorization', `Bearer ${accessToken}`);\n    }\n    else {\n        await dpopProofJwt(headers, options.DPoP, url, 'GET', getClockSkew({ [clockSkew]: options?.[clockSkew] }), accessToken);\n        headers.set('authorization', `DPoP ${accessToken}`);\n    }\n    return (options?.[customFetch] || fetch)(url.href, {\n        body,\n        headers: Object.fromEntries(headers.entries()),\n        method,\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : null,\n    }).then(processDpopNonce);\n}\nexport async function userInfoRequest(as, client, accessToken, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'userinfo_endpoint', options);\n    const headers = prepareHeaders(options?.headers);\n    if (client.userinfo_signed_response_alg) {\n        headers.set('accept', 'application/jwt');\n    }\n    else {\n        headers.set('accept', 'application/json');\n        headers.append('accept', 'application/jwt');\n    }\n    return protectedResourceRequest(accessToken, 'GET', url, headers, null, {\n        ...options,\n        [clockSkew]: getClockSkew(client),\n    });\n}\nlet jwksCache;\nasync function getPublicSigKeyFromIssuerJwksUri(as, options, header) {\n    const { alg, kid } = header;\n    checkSupportedJwsAlg(alg);\n    let jwks;\n    let age;\n    jwksCache || (jwksCache = new WeakMap());\n    if (jwksCache.has(as)) {\n        ;\n        ({ jwks, age } = jwksCache.get(as));\n        if (age >= 300) {\n            jwksCache.delete(as);\n            return getPublicSigKeyFromIssuerJwksUri(as, options, header);\n        }\n    }\n    else {\n        jwks = await jwksRequest(as, options).then(processJwksResponse);\n        age = 0;\n        jwksCache.set(as, {\n            jwks,\n            iat: epochTime(),\n            get age() {\n                return epochTime() - this.iat;\n            },\n        });\n    }\n    let kty;\n    switch (alg.slice(0, 2)) {\n        case 'RS':\n        case 'PS':\n            kty = 'RSA';\n            break;\n        case 'ES':\n            kty = 'EC';\n            break;\n        case 'Ed':\n            kty = 'OKP';\n            break;\n        default:\n            throw new UnsupportedOperationError();\n    }\n    const candidates = jwks.keys.filter((jwk) => {\n        if (jwk.kty !== kty) {\n            return false;\n        }\n        if (kid !== undefined && kid !== jwk.kid) {\n            return false;\n        }\n        if (jwk.alg !== undefined && alg !== jwk.alg) {\n            return false;\n        }\n        if (jwk.use !== undefined && jwk.use !== 'sig') {\n            return false;\n        }\n        if (jwk.key_ops?.includes('verify') === false) {\n            return false;\n        }\n        switch (true) {\n            case alg === 'ES256' && jwk.crv !== 'P-256':\n            case alg === 'ES384' && jwk.crv !== 'P-384':\n            case alg === 'ES512' && jwk.crv !== 'P-521':\n            case alg === 'EdDSA' && !(jwk.crv === 'Ed25519' || jwk.crv === 'Ed448'):\n                return false;\n        }\n        return true;\n    });\n    const { 0: jwk, length } = candidates;\n    if (!length) {\n        if (age >= 60) {\n            jwksCache.delete(as);\n            return getPublicSigKeyFromIssuerJwksUri(as, options, header);\n        }\n        throw new OPE('error when selecting a JWT verification key, no applicable keys found');\n    }\n    if (length !== 1) {\n        throw new OPE('error when selecting a JWT verification key, multiple applicable keys found, a \"kid\" JWT Header Parameter is required');\n    }\n    const key = await importJwk(alg, jwk);\n    if (key.type !== 'public') {\n        throw new OPE('jwks_uri must only contain public keys');\n    }\n    return key;\n}\nexport const skipSubjectCheck = Symbol();\nfunction getContentType(response) {\n    return response.headers.get('content-type')?.split(';')[0];\n}\nexport async function processUserInfoResponse(as, client, expectedSubject, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        throw new OPE('\"response\" is not a conform UserInfo Endpoint response');\n    }\n    let json;\n    if (getContentType(response) === 'application/jwt') {\n        assertReadableResponse(response);\n        const { claims } = await validateJwt(await response.text(), checkSigningAlgorithm.bind(undefined, client.userinfo_signed_response_alg, as.userinfo_signing_alg_values_supported), noSignatureCheck, getClockSkew(client), getClockTolerance(client))\n            .then(validateOptionalAudience.bind(undefined, client.client_id))\n            .then(validateOptionalIssuer.bind(undefined, as.issuer));\n        json = claims;\n    }\n    else {\n        if (client.userinfo_signed_response_alg) {\n            throw new OPE('JWT UserInfo Response expected');\n        }\n        assertReadableResponse(response);\n        try {\n            json = await response.json();\n        }\n        catch (cause) {\n            throw new OPE('failed to parse \"response\" body as JSON', { cause });\n        }\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.sub)) {\n        throw new OPE('\"response\" body \"sub\" property must be a non-empty string');\n    }\n    switch (expectedSubject) {\n        case skipSubjectCheck:\n            break;\n        default:\n            if (!validateString(expectedSubject)) {\n                throw new OPE('\"expectedSubject\" must be a non-empty string');\n            }\n            if (json.sub !== expectedSubject) {\n                throw new OPE('unexpected \"response\" body \"sub\" value');\n            }\n    }\n    return json;\n}\nasync function authenticatedRequest(as, client, method, url, body, headers, options) {\n    await clientAuthentication(as, client, body, headers, options?.clientPrivateKey);\n    headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n    return (options?.[customFetch] || fetch)(url.href, {\n        body,\n        headers: Object.fromEntries(headers.entries()),\n        method,\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : null,\n    }).then(processDpopNonce);\n}\nasync function tokenEndpointRequest(as, client, grantType, parameters, options) {\n    const url = resolveEndpoint(as, 'token_endpoint', options);\n    parameters.set('grant_type', grantType);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    if (options?.DPoP !== undefined) {\n        await dpopProofJwt(headers, options.DPoP, url, 'POST', getClockSkew(client));\n    }\n    return authenticatedRequest(as, client, 'POST', url, parameters, headers, options);\n}\nexport async function refreshTokenGrantRequest(as, client, refreshToken, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!validateString(refreshToken)) {\n        throw new TypeError('\"refreshToken\" must be a non-empty string');\n    }\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('refresh_token', refreshToken);\n    return tokenEndpointRequest(as, client, 'refresh_token', parameters, options);\n}\nconst idTokenClaims = new WeakMap();\nexport function getValidatedIdTokenClaims(ref) {\n    if (!ref.id_token) {\n        return undefined;\n    }\n    const claims = idTokenClaims.get(ref);\n    if (!claims) {\n        throw new TypeError('\"ref\" was already garbage collected or did not resolve from the proper sources');\n    }\n    return claims;\n}\nasync function processGenericAccessTokenResponse(as, client, response, ignoreIdToken = false, ignoreRefreshToken = false) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Token Endpoint response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.access_token)) {\n        throw new OPE('\"response\" body \"access_token\" property must be a non-empty string');\n    }\n    if (!validateString(json.token_type)) {\n        throw new OPE('\"response\" body \"token_type\" property must be a non-empty string');\n    }\n    json.token_type = json.token_type.toLowerCase();\n    if (json.token_type !== 'dpop' && json.token_type !== 'bearer') {\n        throw new UnsupportedOperationError('unsupported `token_type` value');\n    }\n    if (json.expires_in !== undefined &&\n        (typeof json.expires_in !== 'number' || json.expires_in <= 0)) {\n        throw new OPE('\"response\" body \"expires_in\" property must be a positive number');\n    }\n    if (!ignoreRefreshToken &&\n        json.refresh_token !== undefined &&\n        !validateString(json.refresh_token)) {\n        throw new OPE('\"response\" body \"refresh_token\" property must be a non-empty string');\n    }\n    if (json.scope !== undefined && typeof json.scope !== 'string') {\n        throw new OPE('\"response\" body \"scope\" property must be a string');\n    }\n    if (!ignoreIdToken) {\n        if (json.id_token !== undefined && !validateString(json.id_token)) {\n            throw new OPE('\"response\" body \"id_token\" property must be a non-empty string');\n        }\n        if (json.id_token) {\n            const { claims } = await validateJwt(json.id_token, checkSigningAlgorithm.bind(undefined, client.id_token_signed_response_alg, as.id_token_signing_alg_values_supported), noSignatureCheck, getClockSkew(client), getClockTolerance(client))\n                .then(validatePresence.bind(undefined, ['aud', 'exp', 'iat', 'iss', 'sub']))\n                .then(validateIssuer.bind(undefined, as.issuer))\n                .then(validateAudience.bind(undefined, client.client_id));\n            if (Array.isArray(claims.aud) && claims.aud.length !== 1 && claims.azp !== client.client_id) {\n                throw new OPE('unexpected ID Token \"azp\" (authorized party) claim value');\n            }\n            if (client.require_auth_time && typeof claims.auth_time !== 'number') {\n                throw new OPE('unexpected ID Token \"auth_time\" (authentication time) claim value');\n            }\n            idTokenClaims.set(json, claims);\n        }\n    }\n    return json;\n}\nexport async function processRefreshTokenResponse(as, client, response) {\n    return processGenericAccessTokenResponse(as, client, response);\n}\nfunction validateOptionalAudience(expected, result) {\n    if (result.claims.aud !== undefined) {\n        return validateAudience(expected, result);\n    }\n    return result;\n}\nfunction validateAudience(expected, result) {\n    if (Array.isArray(result.claims.aud)) {\n        if (!result.claims.aud.includes(expected)) {\n            throw new OPE('unexpected JWT \"aud\" (audience) claim value');\n        }\n    }\n    else if (result.claims.aud !== expected) {\n        throw new OPE('unexpected JWT \"aud\" (audience) claim value');\n    }\n    return result;\n}\nfunction validateOptionalIssuer(expected, result) {\n    if (result.claims.iss !== undefined) {\n        return validateIssuer(expected, result);\n    }\n    return result;\n}\nfunction validateIssuer(expected, result) {\n    if (result.claims.iss !== expected) {\n        throw new OPE('unexpected JWT \"iss\" (issuer) claim value');\n    }\n    return result;\n}\nconst branded = new WeakSet();\nfunction brand(searchParams) {\n    branded.add(searchParams);\n    return searchParams;\n}\nexport async function authorizationCodeGrantRequest(as, client, callbackParameters, redirectUri, codeVerifier, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!branded.has(callbackParameters)) {\n        throw new TypeError('\"callbackParameters\" must be an instance of URLSearchParams obtained from \"validateAuthResponse()\", or \"validateJwtAuthResponse()');\n    }\n    if (!validateString(redirectUri)) {\n        throw new TypeError('\"redirectUri\" must be a non-empty string');\n    }\n    if (!validateString(codeVerifier)) {\n        throw new TypeError('\"codeVerifier\" must be a non-empty string');\n    }\n    const code = getURLSearchParameter(callbackParameters, 'code');\n    if (!code) {\n        throw new OPE('no authorization code in \"callbackParameters\"');\n    }\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('redirect_uri', redirectUri);\n    parameters.set('code_verifier', codeVerifier);\n    parameters.set('code', code);\n    return tokenEndpointRequest(as, client, 'authorization_code', parameters, options);\n}\nconst jwtClaimNames = {\n    aud: 'audience',\n    c_hash: 'code hash',\n    client_id: 'client id',\n    exp: 'expiration time',\n    iat: 'issued at',\n    iss: 'issuer',\n    jti: 'jwt id',\n    nonce: 'nonce',\n    s_hash: 'state hash',\n    sub: 'subject',\n    ath: 'access token hash',\n    htm: 'http method',\n    htu: 'http uri',\n    cnf: 'confirmation',\n};\nfunction validatePresence(required, result) {\n    for (const claim of required) {\n        if (result.claims[claim] === undefined) {\n            throw new OPE(`JWT \"${claim}\" (${jwtClaimNames[claim]}) claim missing`);\n        }\n    }\n    return result;\n}\nexport const expectNoNonce = Symbol();\nexport const skipAuthTimeCheck = Symbol();\nexport async function processAuthorizationCodeOpenIDResponse(as, client, response, expectedNonce, maxAge) {\n    const result = await processGenericAccessTokenResponse(as, client, response);\n    if (isOAuth2Error(result)) {\n        return result;\n    }\n    if (!validateString(result.id_token)) {\n        throw new OPE('\"response\" body \"id_token\" property must be a non-empty string');\n    }\n    maxAge ?? (maxAge = client.default_max_age ?? skipAuthTimeCheck);\n    const claims = getValidatedIdTokenClaims(result);\n    if ((client.require_auth_time || maxAge !== skipAuthTimeCheck) &&\n        claims.auth_time === undefined) {\n        throw new OPE('ID Token \"auth_time\" (authentication time) claim missing');\n    }\n    if (maxAge !== skipAuthTimeCheck) {\n        if (typeof maxAge !== 'number' || maxAge < 0) {\n            throw new TypeError('\"options.max_age\" must be a non-negative number');\n        }\n        const now = epochTime() + getClockSkew(client);\n        const tolerance = getClockTolerance(client);\n        if (claims.auth_time + maxAge < now - tolerance) {\n            throw new OPE('too much time has elapsed since the last End-User authentication');\n        }\n    }\n    switch (expectedNonce) {\n        case undefined:\n        case expectNoNonce:\n            if (claims.nonce !== undefined) {\n                throw new OPE('unexpected ID Token \"nonce\" claim value');\n            }\n            break;\n        default:\n            if (!validateString(expectedNonce)) {\n                throw new TypeError('\"expectedNonce\" must be a non-empty string');\n            }\n            if (claims.nonce === undefined) {\n                throw new OPE('ID Token \"nonce\" claim missing');\n            }\n            if (claims.nonce !== expectedNonce) {\n                throw new OPE('unexpected ID Token \"nonce\" claim value');\n            }\n    }\n    return result;\n}\nexport async function processAuthorizationCodeOAuth2Response(as, client, response) {\n    const result = await processGenericAccessTokenResponse(as, client, response, true);\n    if (isOAuth2Error(result)) {\n        return result;\n    }\n    if (result.id_token !== undefined) {\n        if (typeof result.id_token === 'string' && result.id_token.length) {\n            throw new OPE('Unexpected ID Token returned, use processAuthorizationCodeOpenIDResponse() for OpenID Connect callback processing');\n        }\n        delete result.id_token;\n    }\n    return result;\n}\nfunction checkJwtType(expected, result) {\n    if (typeof result.header.typ !== 'string' || normalizeTyp(result.header.typ) !== expected) {\n        throw new OPE('unexpected JWT \"typ\" header parameter value');\n    }\n    return result;\n}\nexport async function clientCredentialsGrantRequest(as, client, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    return tokenEndpointRequest(as, client, 'client_credentials', new URLSearchParams(parameters), options);\n}\nexport async function processClientCredentialsResponse(as, client, response) {\n    const result = await processGenericAccessTokenResponse(as, client, response, true, true);\n    if (isOAuth2Error(result)) {\n        return result;\n    }\n    return result;\n}\nexport async function revocationRequest(as, client, token, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!validateString(token)) {\n        throw new TypeError('\"token\" must be a non-empty string');\n    }\n    const url = resolveEndpoint(as, 'revocation_endpoint', options);\n    const body = new URLSearchParams(options?.additionalParameters);\n    body.set('token', token);\n    const headers = prepareHeaders(options?.headers);\n    headers.delete('accept');\n    return authenticatedRequest(as, client, 'POST', url, body, headers, options);\n}\nexport async function processRevocationResponse(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Revocation Endpoint response');\n    }\n    return undefined;\n}\nfunction assertReadableResponse(response) {\n    if (response.bodyUsed) {\n        throw new TypeError('\"response\" body has been used already');\n    }\n}\nexport async function introspectionRequest(as, client, token, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!validateString(token)) {\n        throw new TypeError('\"token\" must be a non-empty string');\n    }\n    const url = resolveEndpoint(as, 'introspection_endpoint', options);\n    const body = new URLSearchParams(options?.additionalParameters);\n    body.set('token', token);\n    const headers = prepareHeaders(options?.headers);\n    if (options?.requestJwtResponse ?? client.introspection_signed_response_alg) {\n        headers.set('accept', 'application/token-introspection+jwt');\n    }\n    else {\n        headers.set('accept', 'application/json');\n    }\n    return authenticatedRequest(as, client, 'POST', url, body, headers, options);\n}\nexport async function processIntrospectionResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Introspection Endpoint response');\n    }\n    let json;\n    if (getContentType(response) === 'application/token-introspection+jwt') {\n        assertReadableResponse(response);\n        const { claims } = await validateJwt(await response.text(), checkSigningAlgorithm.bind(undefined, client.introspection_signed_response_alg, as.introspection_signing_alg_values_supported), noSignatureCheck, getClockSkew(client), getClockTolerance(client))\n            .then(checkJwtType.bind(undefined, 'token-introspection+jwt'))\n            .then(validatePresence.bind(undefined, ['aud', 'iat', 'iss']))\n            .then(validateIssuer.bind(undefined, as.issuer))\n            .then(validateAudience.bind(undefined, client.client_id));\n        json = claims.token_introspection;\n        if (!isJsonObject(json)) {\n            throw new OPE('JWT \"token_introspection\" claim must be a JSON object');\n        }\n    }\n    else {\n        assertReadableResponse(response);\n        try {\n            json = await response.json();\n        }\n        catch (cause) {\n            throw new OPE('failed to parse \"response\" body as JSON', { cause });\n        }\n        if (!isJsonObject(json)) {\n            throw new OPE('\"response\" body must be a top level object');\n        }\n    }\n    if (typeof json.active !== 'boolean') {\n        throw new OPE('\"response\" body \"active\" property must be a boolean');\n    }\n    return json;\n}\nasync function jwksRequest(as, options) {\n    assertAs(as);\n    const url = resolveEndpoint(as, 'jwks_uri');\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    headers.append('accept', 'application/jwk-set+json');\n    return (options?.[customFetch] || fetch)(url.href, {\n        headers: Object.fromEntries(headers.entries()),\n        method: 'GET',\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : null,\n    }).then(processDpopNonce);\n}\nasync function processJwksResponse(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        throw new OPE('\"response\" is not a conform JSON Web Key Set response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!Array.isArray(json.keys)) {\n        throw new OPE('\"response\" body \"keys\" property must be an array');\n    }\n    if (!Array.prototype.every.call(json.keys, isJsonObject)) {\n        throw new OPE('\"response\" body \"keys\" property members must be JWK formatted objects');\n    }\n    return json;\n}\nasync function handleOAuthBodyError(response) {\n    if (response.status > 399 && response.status < 500) {\n        assertReadableResponse(response);\n        try {\n            const json = await response.json();\n            if (isJsonObject(json) && typeof json.error === 'string' && json.error.length) {\n                if (json.error_description !== undefined && typeof json.error_description !== 'string') {\n                    delete json.error_description;\n                }\n                if (json.error_uri !== undefined && typeof json.error_uri !== 'string') {\n                    delete json.error_uri;\n                }\n                if (json.algs !== undefined && typeof json.algs !== 'string') {\n                    delete json.algs;\n                }\n                if (json.scope !== undefined && typeof json.scope !== 'string') {\n                    delete json.scope;\n                }\n                return json;\n            }\n        }\n        catch { }\n    }\n    return undefined;\n}\nfunction checkSupportedJwsAlg(alg) {\n    if (!SUPPORTED_JWS_ALGS.includes(alg)) {\n        throw new UnsupportedOperationError('unsupported JWS \"alg\" identifier');\n    }\n    return alg;\n}\nfunction checkRsaKeyAlgorithm(algorithm) {\n    if (typeof algorithm.modulusLength !== 'number' || algorithm.modulusLength < 2048) {\n        throw new OPE(`${algorithm.name} modulusLength must be at least 2048 bits`);\n    }\n}\nfunction ecdsaHashName(namedCurve) {\n    switch (namedCurve) {\n        case 'P-256':\n            return 'SHA-256';\n        case 'P-384':\n            return 'SHA-384';\n        case 'P-521':\n            return 'SHA-512';\n        default:\n            throw new UnsupportedOperationError();\n    }\n}\nfunction keyToSubtle(key) {\n    switch (key.algorithm.name) {\n        case 'ECDSA':\n            return {\n                name: key.algorithm.name,\n                hash: ecdsaHashName(key.algorithm.namedCurve),\n            };\n        case 'RSA-PSS': {\n            checkRsaKeyAlgorithm(key.algorithm);\n            switch (key.algorithm.hash.name) {\n                case 'SHA-256':\n                case 'SHA-384':\n                case 'SHA-512':\n                    return {\n                        name: key.algorithm.name,\n                        saltLength: parseInt(key.algorithm.hash.name.slice(-3), 10) >> 3,\n                    };\n                default:\n                    throw new UnsupportedOperationError();\n            }\n        }\n        case 'RSASSA-PKCS1-v1_5':\n            checkRsaKeyAlgorithm(key.algorithm);\n            return key.algorithm.name;\n        case 'Ed448':\n        case 'Ed25519':\n            return key.algorithm.name;\n    }\n    throw new UnsupportedOperationError();\n}\nconst noSignatureCheck = Symbol();\nasync function validateJwt(jws, checkAlg, getKey, clockSkew, clockTolerance) {\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature, length } = jws.split('.');\n    if (length === 5) {\n        throw new UnsupportedOperationError('JWE structure JWTs are not supported');\n    }\n    if (length !== 3) {\n        throw new OPE('Invalid JWT');\n    }\n    let header;\n    try {\n        header = JSON.parse(buf(b64u(protectedHeader)));\n    }\n    catch (cause) {\n        throw new OPE('failed to parse JWT Header body as base64url encoded JSON', { cause });\n    }\n    if (!isJsonObject(header)) {\n        throw new OPE('JWT Header must be a top level object');\n    }\n    checkAlg(header);\n    if (header.crit !== undefined) {\n        throw new OPE('unexpected JWT \"crit\" header parameter');\n    }\n    const signature = b64u(encodedSignature);\n    let key;\n    if (getKey !== noSignatureCheck) {\n        key = await getKey(header);\n        const input = `${protectedHeader}.${payload}`;\n        const verified = await crypto.subtle.verify(keyToSubtle(key), key, signature, buf(input));\n        if (!verified) {\n            throw new OPE('JWT signature verification failed');\n        }\n    }\n    let claims;\n    try {\n        claims = JSON.parse(buf(b64u(payload)));\n    }\n    catch (cause) {\n        throw new OPE('failed to parse JWT Payload body as base64url encoded JSON', { cause });\n    }\n    if (!isJsonObject(claims)) {\n        throw new OPE('JWT Payload must be a top level object');\n    }\n    const now = epochTime() + clockSkew;\n    if (claims.exp !== undefined) {\n        if (typeof claims.exp !== 'number') {\n            throw new OPE('unexpected JWT \"exp\" (expiration time) claim type');\n        }\n        if (claims.exp <= now - clockTolerance) {\n            throw new OPE('unexpected JWT \"exp\" (expiration time) claim value, timestamp is <= now()');\n        }\n    }\n    if (claims.iat !== undefined) {\n        if (typeof claims.iat !== 'number') {\n            throw new OPE('unexpected JWT \"iat\" (issued at) claim type');\n        }\n    }\n    if (claims.iss !== undefined) {\n        if (typeof claims.iss !== 'string') {\n            throw new OPE('unexpected JWT \"iss\" (issuer) claim type');\n        }\n    }\n    if (claims.nbf !== undefined) {\n        if (typeof claims.nbf !== 'number') {\n            throw new OPE('unexpected JWT \"nbf\" (not before) claim type');\n        }\n        if (claims.nbf > now + clockTolerance) {\n            throw new OPE('unexpected JWT \"nbf\" (not before) claim value, timestamp is > now()');\n        }\n    }\n    if (claims.aud !== undefined) {\n        if (typeof claims.aud !== 'string' && !Array.isArray(claims.aud)) {\n            throw new OPE('unexpected JWT \"aud\" (audience) claim type');\n        }\n    }\n    return { header, claims, signature, key };\n}\nexport async function validateJwtAuthResponse(as, client, parameters, expectedState, options) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        parameters = parameters.searchParams;\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw new TypeError('\"parameters\" must be an instance of URLSearchParams, or URL');\n    }\n    const response = getURLSearchParameter(parameters, 'response');\n    if (!response) {\n        throw new OPE('\"parameters\" does not contain a JARM response');\n    }\n    if (typeof as.jwks_uri !== 'string') {\n        throw new TypeError('\"as.jwks_uri\" must be a string');\n    }\n    const { claims } = await validateJwt(response, checkSigningAlgorithm.bind(undefined, client.authorization_signed_response_alg, as.authorization_signing_alg_values_supported), getPublicSigKeyFromIssuerJwksUri.bind(undefined, as, options), getClockSkew(client), getClockTolerance(client))\n        .then(validatePresence.bind(undefined, ['aud', 'exp', 'iss']))\n        .then(validateIssuer.bind(undefined, as.issuer))\n        .then(validateAudience.bind(undefined, client.client_id));\n    const result = new URLSearchParams();\n    for (const [key, value] of Object.entries(claims)) {\n        if (typeof value === 'string' && key !== 'aud') {\n            result.set(key, value);\n        }\n    }\n    return validateAuthResponse(as, client, result, expectedState);\n}\nasync function idTokenHash(alg, data, key) {\n    let algorithm;\n    switch (alg) {\n        case 'RS256':\n        case 'PS256':\n        case 'ES256':\n            algorithm = 'SHA-256';\n            break;\n        case 'RS384':\n        case 'PS384':\n        case 'ES384':\n            algorithm = 'SHA-384';\n            break;\n        case 'RS512':\n        case 'PS512':\n        case 'ES512':\n            algorithm = 'SHA-512';\n            break;\n        case 'EdDSA':\n            if (key.algorithm.name === 'Ed25519') {\n                algorithm = 'SHA-512';\n                break;\n            }\n            throw new UnsupportedOperationError();\n        default:\n            throw new UnsupportedOperationError();\n    }\n    const digest = await crypto.subtle.digest(algorithm, buf(data));\n    return b64u(digest.slice(0, digest.byteLength / 2));\n}\nasync function idTokenHashMatches(data, actual, alg, key) {\n    const expected = await idTokenHash(alg, data, key);\n    return actual === expected;\n}\nexport async function validateDetachedSignatureResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        if (!parameters.hash.length) {\n            throw new TypeError('\"parameters\" as an instance of URL must contain a hash (fragment) with the Authorization Response parameters');\n        }\n        parameters = new URLSearchParams(parameters.hash.slice(1));\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw new TypeError('\"parameters\" must be an instance of URLSearchParams');\n    }\n    parameters = new URLSearchParams(parameters);\n    const id_token = getURLSearchParameter(parameters, 'id_token');\n    parameters.delete('id_token');\n    switch (expectedState) {\n        case undefined:\n        case expectNoState:\n            break;\n        default:\n            if (!validateString(expectedState)) {\n                throw new TypeError('\"expectedState\" must be a non-empty string');\n            }\n    }\n    const result = validateAuthResponse({\n        ...as,\n        authorization_response_iss_parameter_supported: false,\n    }, client, parameters, expectedState);\n    if (isOAuth2Error(result)) {\n        return result;\n    }\n    if (!id_token) {\n        throw new OPE('\"parameters\" does not contain an ID Token');\n    }\n    const code = getURLSearchParameter(parameters, 'code');\n    if (!code) {\n        throw new OPE('\"parameters\" does not contain an Authorization Code');\n    }\n    if (typeof as.jwks_uri !== 'string') {\n        throw new TypeError('\"as.jwks_uri\" must be a string');\n    }\n    const requiredClaims = [\n        'aud',\n        'exp',\n        'iat',\n        'iss',\n        'sub',\n        'nonce',\n        'c_hash',\n    ];\n    if (typeof expectedState === 'string') {\n        requiredClaims.push('s_hash');\n    }\n    const { claims, header, key } = await validateJwt(id_token, checkSigningAlgorithm.bind(undefined, client.id_token_signed_response_alg, as.id_token_signing_alg_values_supported), getPublicSigKeyFromIssuerJwksUri.bind(undefined, as, options), getClockSkew(client), getClockTolerance(client))\n        .then(validatePresence.bind(undefined, requiredClaims))\n        .then(validateIssuer.bind(undefined, as.issuer))\n        .then(validateAudience.bind(undefined, client.client_id));\n    const clockSkew = getClockSkew(client);\n    const now = epochTime() + clockSkew;\n    if (claims.iat < now - 3600) {\n        throw new OPE('unexpected JWT \"iat\" (issued at) claim value, it is too far in the past');\n    }\n    if (typeof claims.c_hash !== 'string' ||\n        (await idTokenHashMatches(code, claims.c_hash, header.alg, key)) !== true) {\n        throw new OPE('invalid ID Token \"c_hash\" (code hash) claim value');\n    }\n    if (claims.s_hash !== undefined && typeof expectedState !== 'string') {\n        throw new OPE('could not verify ID Token \"s_hash\" (state hash) claim value');\n    }\n    if (typeof expectedState === 'string' &&\n        (typeof claims.s_hash !== 'string' ||\n            (await idTokenHashMatches(expectedState, claims.s_hash, header.alg, key)) !== true)) {\n        throw new OPE('invalid ID Token \"s_hash\" (state hash) claim value');\n    }\n    if (client.require_auth_time !== undefined && typeof claims.auth_time !== 'number') {\n        throw new OPE('unexpected ID Token \"auth_time\" (authentication time) claim value');\n    }\n    maxAge ?? (maxAge = client.default_max_age ?? skipAuthTimeCheck);\n    if ((client.require_auth_time || maxAge !== skipAuthTimeCheck) &&\n        claims.auth_time === undefined) {\n        throw new OPE('ID Token \"auth_time\" (authentication time) claim missing');\n    }\n    if (maxAge !== skipAuthTimeCheck) {\n        if (typeof maxAge !== 'number' || maxAge < 0) {\n            throw new TypeError('\"options.max_age\" must be a non-negative number');\n        }\n        const now = epochTime() + getClockSkew(client);\n        const tolerance = getClockTolerance(client);\n        if (claims.auth_time + maxAge < now - tolerance) {\n            throw new OPE('too much time has elapsed since the last End-User authentication');\n        }\n    }\n    if (!validateString(expectedNonce)) {\n        throw new TypeError('\"expectedNonce\" must be a non-empty string');\n    }\n    if (claims.nonce !== expectedNonce) {\n        throw new OPE('unexpected ID Token \"nonce\" claim value');\n    }\n    if (Array.isArray(claims.aud) && claims.aud.length !== 1 && claims.azp !== client.client_id) {\n        throw new OPE('unexpected ID Token \"azp\" (authorized party) claim value');\n    }\n    return result;\n}\nfunction checkSigningAlgorithm(client, issuer, header) {\n    if (client !== undefined) {\n        if (header.alg !== client) {\n            throw new OPE('unexpected JWT \"alg\" header parameter');\n        }\n        return;\n    }\n    if (Array.isArray(issuer)) {\n        if (!issuer.includes(header.alg)) {\n            throw new OPE('unexpected JWT \"alg\" header parameter');\n        }\n        return;\n    }\n    if (header.alg !== 'RS256') {\n        throw new OPE('unexpected JWT \"alg\" header parameter');\n    }\n}\nfunction getURLSearchParameter(parameters, name) {\n    const { 0: value, length } = parameters.getAll(name);\n    if (length > 1) {\n        throw new OPE(`\"${name}\" parameter must be provided only once`);\n    }\n    return value;\n}\nexport const skipStateCheck = Symbol();\nexport const expectNoState = Symbol();\nexport function validateAuthResponse(as, client, parameters, expectedState) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        parameters = parameters.searchParams;\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw new TypeError('\"parameters\" must be an instance of URLSearchParams, or URL');\n    }\n    if (getURLSearchParameter(parameters, 'response')) {\n        throw new OPE('\"parameters\" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()');\n    }\n    const iss = getURLSearchParameter(parameters, 'iss');\n    const state = getURLSearchParameter(parameters, 'state');\n    if (!iss && as.authorization_response_iss_parameter_supported) {\n        throw new OPE('response parameter \"iss\" (issuer) missing');\n    }\n    if (iss && iss !== as.issuer) {\n        throw new OPE('unexpected \"iss\" (issuer) response parameter value');\n    }\n    switch (expectedState) {\n        case undefined:\n        case expectNoState:\n            if (state !== undefined) {\n                throw new OPE('unexpected \"state\" response parameter encountered');\n            }\n            break;\n        case skipStateCheck:\n            break;\n        default:\n            if (!validateString(expectedState)) {\n                throw new OPE('\"expectedState\" must be a non-empty string');\n            }\n            if (state === undefined) {\n                throw new OPE('response parameter \"state\" missing');\n            }\n            if (state !== expectedState) {\n                throw new OPE('unexpected \"state\" response parameter value');\n            }\n    }\n    const error = getURLSearchParameter(parameters, 'error');\n    if (error) {\n        return {\n            error,\n            error_description: getURLSearchParameter(parameters, 'error_description'),\n            error_uri: getURLSearchParameter(parameters, 'error_uri'),\n        };\n    }\n    const id_token = getURLSearchParameter(parameters, 'id_token');\n    const token = getURLSearchParameter(parameters, 'token');\n    if (id_token !== undefined || token !== undefined) {\n        throw new UnsupportedOperationError('implicit and hybrid flows are not supported');\n    }\n    return brand(new URLSearchParams(parameters));\n}\nfunction algToSubtle(alg, crv) {\n    switch (alg) {\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            return { name: 'RSA-PSS', hash: `SHA-${alg.slice(-3)}` };\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            return { name: 'RSASSA-PKCS1-v1_5', hash: `SHA-${alg.slice(-3)}` };\n        case 'ES256':\n        case 'ES384':\n            return { name: 'ECDSA', namedCurve: `P-${alg.slice(-3)}` };\n        case 'ES512':\n            return { name: 'ECDSA', namedCurve: 'P-521' };\n        case 'EdDSA': {\n            switch (crv) {\n                case 'Ed25519':\n                case 'Ed448':\n                    return crv;\n                default:\n                    throw new UnsupportedOperationError();\n            }\n        }\n        default:\n            throw new UnsupportedOperationError();\n    }\n}\nasync function importJwk(alg, jwk) {\n    const { ext, key_ops, use, ...key } = jwk;\n    return crypto.subtle.importKey('jwk', key, algToSubtle(alg, jwk.crv), true, ['verify']);\n}\nexport async function deviceAuthorizationRequest(as, client, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'device_authorization_endpoint', options);\n    const body = new URLSearchParams(parameters);\n    body.set('client_id', client.client_id);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    return authenticatedRequest(as, client, 'POST', url, body, headers, options);\n}\nexport async function processDeviceAuthorizationResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw new TypeError('\"response\" must be an instance of Response');\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            return err;\n        }\n        throw new OPE('\"response\" is not a conform Device Authorization Endpoint response');\n    }\n    assertReadableResponse(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw new OPE('failed to parse \"response\" body as JSON', { cause });\n    }\n    if (!isJsonObject(json)) {\n        throw new OPE('\"response\" body must be a top level object');\n    }\n    if (!validateString(json.device_code)) {\n        throw new OPE('\"response\" body \"device_code\" property must be a non-empty string');\n    }\n    if (!validateString(json.user_code)) {\n        throw new OPE('\"response\" body \"user_code\" property must be a non-empty string');\n    }\n    if (!validateString(json.verification_uri)) {\n        throw new OPE('\"response\" body \"verification_uri\" property must be a non-empty string');\n    }\n    if (typeof json.expires_in !== 'number' || json.expires_in <= 0) {\n        throw new OPE('\"response\" body \"expires_in\" property must be a positive number');\n    }\n    if (json.verification_uri_complete !== undefined &&\n        !validateString(json.verification_uri_complete)) {\n        throw new OPE('\"response\" body \"verification_uri_complete\" property must be a non-empty string');\n    }\n    if (json.interval !== undefined && (typeof json.interval !== 'number' || json.interval <= 0)) {\n        throw new OPE('\"response\" body \"interval\" property must be a positive number');\n    }\n    return json;\n}\nexport async function deviceCodeGrantRequest(as, client, deviceCode, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!validateString(deviceCode)) {\n        throw new TypeError('\"deviceCode\" must be a non-empty string');\n    }\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('device_code', deviceCode);\n    return tokenEndpointRequest(as, client, 'urn:ietf:params:oauth:grant-type:device_code', parameters, options);\n}\nexport async function processDeviceCodeResponse(as, client, response) {\n    return processGenericAccessTokenResponse(as, client, response);\n}\nexport async function generateKeyPair(alg, options) {\n    if (!validateString(alg)) {\n        throw new TypeError('\"alg\" must be a non-empty string');\n    }\n    const algorithm = algToSubtle(alg, alg === 'EdDSA' ? options?.crv ?? 'Ed25519' : undefined);\n    if (alg.startsWith('PS') || alg.startsWith('RS')) {\n        Object.assign(algorithm, {\n            modulusLength: options?.modulusLength ?? 2048,\n            publicExponent: new Uint8Array([0x01, 0x00, 0x01]),\n        });\n    }\n    return (crypto.subtle.generateKey(algorithm, options?.extractable ?? false, ['sign', 'verify']));\n}\nfunction normalizeHtu(htu) {\n    const url = new URL(htu);\n    url.search = '';\n    url.hash = '';\n    return url.href;\n}\nasync function validateDPoP(as, request, accessToken, accessTokenClaims, options) {\n    const header = request.headers.get('dpop');\n    if (header === null) {\n        throw new OPE('operation indicated DPoP use but the request has no DPoP HTTP Header');\n    }\n    if (request.headers.get('authorization')?.toLowerCase().startsWith('dpop ') === false) {\n        throw new OPE(`operation indicated DPoP use but the request's Authorization HTTP Header scheme is not DPoP`);\n    }\n    if (typeof accessTokenClaims.cnf?.jkt !== 'string') {\n        throw new OPE('operation indicated DPoP use but the JWT Access Token has no jkt confirmation claim');\n    }\n    const clockSkew = getClockSkew(options);\n    const proof = await validateJwt(header, checkSigningAlgorithm.bind(undefined, undefined, as?.dpop_signing_alg_values_supported || SUPPORTED_JWS_ALGS), async ({ jwk, alg }) => {\n        if (!jwk) {\n            throw new OPE('DPoP Proof is missing the jwk header parameter');\n        }\n        const key = await importJwk(alg, jwk);\n        if (key.type !== 'public') {\n            throw new OPE('DPoP Proof jwk header parameter must contain a public key');\n        }\n        return key;\n    }, clockSkew, getClockTolerance(options))\n        .then(checkJwtType.bind(undefined, 'dpop+jwt'))\n        .then(validatePresence.bind(undefined, ['iat', 'jti', 'ath', 'htm', 'htu']));\n    const now = epochTime() + clockSkew;\n    const diff = Math.abs(now - proof.claims.iat);\n    if (diff > 300) {\n        throw new OPE('DPoP Proof iat is not recent enough');\n    }\n    if (proof.claims.htm !== request.method) {\n        throw new OPE('DPoP Proof htm mismatch');\n    }\n    if (typeof proof.claims.htu !== 'string' ||\n        normalizeHtu(proof.claims.htu) !== normalizeHtu(request.url)) {\n        throw new OPE('DPoP Proof htu mismatch');\n    }\n    {\n        const expected = b64u(await crypto.subtle.digest('SHA-256', encoder.encode(accessToken)));\n        if (proof.claims.ath !== expected) {\n            throw new OPE('DPoP Proof ath mismatch');\n        }\n    }\n    {\n        let components;\n        switch (proof.header.jwk.kty) {\n            case 'EC':\n                components = {\n                    crv: proof.header.jwk.crv,\n                    kty: proof.header.jwk.kty,\n                    x: proof.header.jwk.x,\n                    y: proof.header.jwk.y,\n                };\n                break;\n            case 'OKP':\n                components = {\n                    crv: proof.header.jwk.crv,\n                    kty: proof.header.jwk.kty,\n                    x: proof.header.jwk.x,\n                };\n                break;\n            case 'RSA':\n                components = {\n                    e: proof.header.jwk.e,\n                    kty: proof.header.jwk.kty,\n                    n: proof.header.jwk.n,\n                };\n                break;\n            default:\n                throw new UnsupportedOperationError();\n        }\n        const expected = b64u(await crypto.subtle.digest('SHA-256', encoder.encode(JSON.stringify(components))));\n        if (accessTokenClaims.cnf.jkt !== expected) {\n            throw new OPE('JWT Access Token confirmation mismatch');\n        }\n    }\n}\nexport async function validateJwtAccessToken(as, request, expectedAudience, options) {\n    assertAs(as);\n    if (!looseInstanceOf(request, Request)) {\n        throw new TypeError('\"request\" must be an instance of Request');\n    }\n    if (!validateString(expectedAudience)) {\n        throw new OPE('\"expectedAudience\" must be a non-empty string');\n    }\n    const authorization = request.headers.get('authorization');\n    if (authorization === null) {\n        throw new OPE('\"request\" is missing an Authorization HTTP Header');\n    }\n    let { 0: scheme, 1: accessToken, length } = authorization.split(' ');\n    scheme = scheme.toLowerCase();\n    switch (scheme) {\n        case 'dpop':\n        case 'bearer':\n            break;\n        default:\n            throw new UnsupportedOperationError('unsupported Authorization HTTP Header scheme');\n    }\n    if (length !== 2) {\n        throw new OPE('invalid Authorization HTTP Header format');\n    }\n    const requiredClaims = [\n        'iss',\n        'exp',\n        'aud',\n        'sub',\n        'iat',\n        'jti',\n        'client_id',\n    ];\n    if (options?.requireDPoP || scheme === 'dpop' || request.headers.has('dpop')) {\n        requiredClaims.push('cnf');\n    }\n    const { claims } = await validateJwt(accessToken, checkSigningAlgorithm.bind(undefined, undefined, SUPPORTED_JWS_ALGS), getPublicSigKeyFromIssuerJwksUri.bind(undefined, as, options), getClockSkew(options), getClockTolerance(options))\n        .then(checkJwtType.bind(undefined, 'at+jwt'))\n        .then(validatePresence.bind(undefined, requiredClaims))\n        .then(validateIssuer.bind(undefined, as.issuer))\n        .then(validateAudience.bind(undefined, expectedAudience));\n    for (const claim of ['client_id', 'jti', 'sub']) {\n        if (typeof claims[claim] !== 'string') {\n            throw new OPE(`unexpected JWT \"${claim}\" claim type`);\n        }\n    }\n    if ('cnf' in claims) {\n        if (!isJsonObject(claims.cnf)) {\n            throw new OPE('unexpected JWT \"cnf\" (confirmation) claim value');\n        }\n        const { 0: cnf, length } = Object.keys(claims.cnf);\n        if (length) {\n            if (length !== 1) {\n                throw new UnsupportedOperationError('multiple confirmation claims are not supported');\n            }\n            if (cnf !== 'jkt') {\n                throw new UnsupportedOperationError('unsupported JWT Confirmation method');\n            }\n        }\n    }\n    if (options?.requireDPoP ||\n        scheme === 'dpop' ||\n        claims.cnf?.jkt !== undefined ||\n        request.headers.has('dpop')) {\n        await validateDPoP(as, request, accessToken, claims, options);\n    }\n    return claims;\n}\nexport const experimentalCustomFetch = customFetch;\nexport const experimental_customFetch = customFetch;\nexport const experimentalUseMtlsAlias = useMtlsAlias;\nexport const experimental_useMtlsAlias = useMtlsAlias;\nexport const experimental_validateDetachedSignatureResponse = validateDetachedSignatureResponse;\nexport const experimental_validateJwtAccessToken = validateJwtAccessToken;\n"], "mappings": ";AAIA,SAAS,qBAAqB;AAC9B,SAAS,2BAA2B;AACpC,OAAO,aAAa;;;ACNpB,IAAI;AACJ,IAAI,OAAO,cAAc,eAAe,CAAC,UAAU,WAAW,aAAa,cAAc,GAAG;AACxF,QAAM,OAAO;AACb,QAAM,UAAU;AAChB,eAAa,GAAG,IAAI,IAAI,OAAO;AACnC;AAaO,IAAM,YAAY,OAAO;AACzB,IAAM,iBAAiB,OAAO;AAC9B,IAAM,cAAc,OAAO;AAC3B,IAAM,eAAe,OAAO;AACnC,IAAM,UAAU,IAAI,YAAY;AAChC,IAAM,UAAU,IAAI,YAAY;AAChC,SAAS,IAAI,OAAO;AAChB,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO,QAAQ,OAAO,KAAK;AAAA,EAC/B;AACA,SAAO,QAAQ,OAAO,KAAK;AAC/B;AACA,IAAM,aAAa;AACnB,SAAS,gBAAgB,OAAO;AAC5B,MAAI,iBAAiB,aAAa;AAC9B,YAAQ,IAAI,WAAW,KAAK;AAAA,EAChC;AACA,QAAM,MAAM,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,YAAY,KAAK,YAAY;AACnD,QAAI,KAAK,OAAO,aAAa,MAAM,MAAM,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,CAAC;AAAA,EAC/E;AACA,SAAO,KAAK,IAAI,KAAK,EAAE,CAAC,EAAE,QAAQ,MAAM,EAAE,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG;AACtF;AACA,SAAS,gBAAgB,OAAO;AAC5B,MAAI;AACA,UAAM,SAAS,KAAK,MAAM,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG,EAAE,QAAQ,OAAO,EAAE,CAAC;AAClF,UAAM,QAAQ,IAAI,WAAW,OAAO,MAAM;AAC1C,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,YAAM,CAAC,IAAI,OAAO,WAAW,CAAC;AAAA,IAClC;AACA,WAAO;AAAA,EACX,SACO,OAAO;AACV,UAAM,IAAI,IAAI,qDAAqD,EAAE,MAAM,CAAC;AAAA,EAChF;AACJ;AACA,SAAS,KAAK,OAAO;AACjB,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO,gBAAgB,KAAK;AAAA,EAChC;AACA,SAAO,gBAAgB,KAAK;AAChC;AACA,IAAM,MAAN,MAAU;AAAA,EACN,YAAY,SAAS;AACjB,SAAK,QAAQ,oBAAI,IAAI;AACrB,SAAK,SAAS,oBAAI,IAAI;AACtB,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,IAAI,KAAK;AACL,QAAI,IAAI,KAAK,MAAM,IAAI,GAAG;AAC1B,QAAI,GAAG;AACH,aAAO;AAAA,IACX;AACA,QAAK,IAAI,KAAK,OAAO,IAAI,GAAG,GAAI;AAC5B,WAAK,OAAO,KAAK,CAAC;AAClB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,IAAI,KAAK;AACL,WAAO,KAAK,MAAM,IAAI,GAAG,KAAK,KAAK,OAAO,IAAI,GAAG;AAAA,EACrD;AAAA,EACA,IAAI,KAAK,OAAO;AACZ,QAAI,KAAK,MAAM,IAAI,GAAG,GAAG;AACrB,WAAK,MAAM,IAAI,KAAK,KAAK;AAAA,IAC7B,OACK;AACD,WAAK,OAAO,KAAK,KAAK;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,KAAK;AACR,QAAI,KAAK,MAAM,IAAI,GAAG,GAAG;AACrB,aAAO,KAAK,MAAM,OAAO,GAAG;AAAA,IAChC;AACA,QAAI,KAAK,OAAO,IAAI,GAAG,GAAG;AACtB,aAAO,KAAK,OAAO,OAAO,GAAG;AAAA,IACjC;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,KAAK,OAAO;AACf,SAAK,MAAM,IAAI,KAAK,KAAK;AACzB,QAAI,KAAK,MAAM,QAAQ,KAAK,SAAS;AACjC,WAAK,SAAS,KAAK;AACnB,WAAK,QAAQ,oBAAI,IAAI;AAAA,IACzB;AAAA,EACJ;AACJ;AAQO,IAAM,2BAAN,cAAuC,MAAM;AAAA,EAChD,YAAY,SAAS,SAAS;AAC1B,UAAM,SAAS,OAAO;AACtB,SAAK,OAAO,KAAK,YAAY;AAC7B,UAAM,oBAAoB,MAAM,KAAK,WAAW;AAAA,EACpD;AACJ;AACA,IAAM,MAAM;AACZ,IAAM,aAAa,IAAI,IAAI,GAAG;AAmG9B,SAAS,eAAe,OAAO;AAC3B,SAAO,OAAO,UAAU,YAAY,MAAM,WAAW;AACzD;AA8BA,SAAS,cAAc;AACnB,SAAO,KAAK,OAAO,gBAAgB,IAAI,WAAW,EAAE,CAAC,CAAC;AAC1D;AACO,SAAS,6BAA6B;AACzC,SAAO,YAAY;AACvB;AACO,SAAS,sBAAsB;AAClC,SAAO,YAAY;AACvB;AAIA,eAAsB,2BAA2B,cAAc;AAC3D,MAAI,CAAC,eAAe,YAAY,GAAG;AAC/B,UAAM,IAAI,UAAU,2CAA2C;AAAA,EACnE;AACA,SAAO,KAAK,MAAM,OAAO,OAAO,OAAO,WAAW,IAAI,YAAY,CAAC,CAAC;AACxE;AAiiBO,IAAM,mBAAmB,OAAO;AAmPhC,IAAM,gBAAgB,OAAO;AAC7B,IAAM,oBAAoB,OAAO;AA6RxC,IAAM,mBAAmB,OAAO;AA0QzB,IAAM,iBAAiB,OAAO;AAC9B,IAAM,gBAAgB,OAAO;;;AD9jDpC,SAAS,eAAe;AACtB,MAAI,CAAC,cAAc,GAAG;AACpB,UAAM,IAAI,MAAM,6FAA6F;AAAA,EAC/G;AACF;AAUA,eAAsB,gCAAuD;AAC3E,WAAS,aAAoB;AAC3B,UAAM,IAAI,oBAAoB,2EAA2E;AAAA,EAC3G;AACA,SAAO;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AACF;AAEA,eAAsB,qBAA4C;AAChE,MAAI,cAAc,GAAG;AACnB,WAAO,0BAA0B;AAAA,EACnC,OAAO;AACL,WAAO,MAAM,8BAA8B;AAAA,EAC7C;AACF;AAEO,SAAS,4BAA0C;AACxD,SAAO;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AACF;AAeO,SAAS,gBAAgB,MAA6B;AAC3D,eAAa;AAEb,UAAQ,IAAI,kBAAkB,QAAQ,EAAE,QAAQ,KAAK,CAAC;AACtD,SAAO,QAAQ,IAAI,IAAI,KAAK;AAC9B;AAEA,eAAsB,UAAU,MAAsC;AACpE,QAAM,eAAe,MAAM,mBAAmB;AAC9C,SAAO,aAAa,IAAI,IAAI;AAC9B;AAEO,SAAS,wBAAwB,MAAc,OAAsB,UAAkD,CAAC,GAAG;AAChI,eAAa;AACb,MAAI,UAAU,MAAM;AAClB,uBAAmB,MAAM,OAAO;AAAA,EAClC,OAAO;AACL,oBAAgB,MAAM,OAAO,OAAO;AAAA,EACtC;AACF;AAEA,eAAsB,kBAAkB,MAAc,OAAsB,UAAkD,CAAC,GAAG;AAChI,QAAM,eAAe,MAAM,mBAAmB;AAC9C,eAAa,YAAY,MAAM,OAAO,OAAO;AAC/C;AAEO,SAAS,mBAAmB,MAAc,UAA+B,CAAC,GAAG;AAClF,eAAa;AACb,UAAQ,OAAO,IAAI;AACrB;AAEA,eAAsB,aAAa,MAAc,UAA+B,CAAC,GAAG;AAClF,QAAM,eAAe,MAAM,mBAAmB;AAC9C,eAAa,OAAO,MAAM,OAAO;AACnC;AAEO,SAAS,gBAAgB,MAAc,OAAe,UAA4B,CAAC,GAAG;AAC3F,eAAa;AACb,UAAQ,IAAI,MAAM,OAAO;AAAA,IACvB,SAAS,QAAQ,WAAW,SAAY,SAAY,IAAI,KAAK,KAAK,IAAI,IAAK,QAAQ,SAAU,GAAI;AAAA,EACnG,CAAC;AACH;AAEA,eAAsB,UAAU,MAAc,OAAe,UAA4B,CAAC,GAAG;AAC3F,QAAM,eAAe,MAAM,mBAAmB;AAC9C,eAAa,IAAI,MAAM,OAAO,OAAO;AACvC;AAEA,eAAsB,uBAAuB;AAC3C,QAAM,eAAe,2BAA2B;AAChD,QAAM,gBAAgB,MAAM,2BAA2B,YAAY;AACnE,QAAM,QAAQ,oBAAoB;AAElC,QAAM,UAAU,uBAAuB,OAAO,cAAc,EAAE,QAAQ,KAAK,GAAG,CAAC;AAE/E,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,8BAA8B,OAAe;AAC3D,eAAa;AACb,QAAM,aAAa,uBAAuB;AAC1C,QAAM,eAAe,gBAAgB,UAAU;AAC/C,MAAI,CAAC,cAAc;AACjB,WAAO;AAAA,EACT;AACA,qBAAmB,UAAU;AAC7B,SAAO;AAAA,IACL;AAAA,EACF;AACF;", "names": []}