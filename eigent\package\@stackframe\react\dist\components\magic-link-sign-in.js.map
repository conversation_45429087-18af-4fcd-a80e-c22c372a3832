{"version": 3, "sources": ["../../src/components/magic-link-sign-in.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { yupResolver } from \"@hookform/resolvers/yup\";\nimport { KnownErrors } from \"@stackframe/stack-shared\";\nimport { strictEmailSchema, yupObject } from \"@stackframe/stack-shared/dist/schema-fields\";\nimport { runAsynchronouslyWithAlert } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { Button, Input, InputOTP, InputOTPGroup, InputOTPSlot, Label, Typography } from \"@stackframe/stack-ui\";\nimport { useEffect, useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport * as yup from \"yup\";\nimport { useStackApp } from \"..\";\nimport { useTranslation } from \"../lib/translations\";\nimport { FormWarningText } from \"./elements/form-warning\";\n\nfunction OTP(props: {\n  onBack: () => void,\n  nonce: string,\n}) {\n  const { t } = useTranslation();\n  const [otp, setOtp] = useState<string>('');\n  const [submitting, setSubmitting] = useState<boolean>(false);\n  const stackApp = useStackApp();\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (otp.length === 6 && !submitting) {\n      setSubmitting(true);\n      stackApp.signInWithMagicLink(otp + props.nonce)\n        .then(result => {\n          if (result.status === 'error') {\n            if (KnownErrors.VerificationCodeError.isInstance(result.error)) {\n              setError(t(\"Invalid code\"));\n            } else if (KnownErrors.InvalidTotpCode.isInstance(result.error)) {\n              setError(t(\"Invalid TOTP code\"));\n            } else {\n              throw result.error;\n            }\n          }\n        })\n        .catch(e => console.error(e))\n        .finally(() => {\n          setSubmitting(false);\n          setOtp('');\n        });\n    }\n    if (otp.length !== 0 && otp.length !== 6) {\n      setError(null);\n    }\n  }, [otp, submitting]);\n\n  return (\n    <div className=\"flex flex-col items-stretch stack-scope\">\n      <form className='w-full flex flex-col items-center mb-2'>\n        <Typography className='mb-2' >{t('Enter the code from your email')}</Typography>\n        <InputOTP\n          maxLength={6}\n          type=\"text\"\n          inputMode=\"text\"\n          pattern={\"^[a-zA-Z0-9]+$\"}\n          value={otp}\n          onChange={value => setOtp(value.toUpperCase())}\n          disabled={submitting}\n        >\n          <InputOTPGroup>\n            {[0, 1, 2, 3, 4, 5].map((index) => (\n              <InputOTPSlot key={index} index={index} size='lg' />\n            ))}\n          </InputOTPGroup>\n        </InputOTP>\n        {error && <FormWarningText text={error} />}\n      </form>\n      <Button variant='link' onClick={props.onBack} className='underline'>{t('Cancel')}</Button>\n    </div>\n  );\n}\n\nexport function MagicLinkSignIn() {\n  const { t } = useTranslation();\n  const app = useStackApp();\n  const [loading, setLoading] = useState(false);\n  const [nonce, setNonce] = useState<string | null>(null);\n\n  const schema = yupObject({\n    email: strictEmailSchema(t('Please enter a valid email')).defined().nonEmpty(t('Please enter your email'))\n  });\n\n  const { register, handleSubmit, setError, formState: { errors } } = useForm({\n    resolver: yupResolver(schema)\n  });\n\n  const onSubmit = async (data: yup.InferType<typeof schema>) => {\n    setLoading(true);\n    try {\n      const { email } = data;\n      const result = await app.sendMagicLinkEmail(email);\n      if (result.status === 'error') {\n        setError('email', { type: 'manual', message: result.error.message });\n        return;\n      } else {\n        setNonce(result.data.nonce);\n      }\n    } catch (e) {\n      if (KnownErrors.SignUpNotEnabled.isInstance(e)) {\n        setError('email', { type: 'manual', message: t('New account registration is not allowed') });\n      } else {\n        throw e;\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (nonce) {\n    return <OTP nonce={nonce} onBack={() => setNonce(null)} />;\n  } else {\n    return (\n      <form\n        className=\"flex flex-col items-stretch stack-scope\"\n        onSubmit={e => runAsynchronouslyWithAlert(handleSubmit(onSubmit)(e))}\n        noValidate\n      >\n        <Label htmlFor=\"email\" className=\"mb-1\">{t('Email')}</Label>\n        <Input\n          id=\"email\"\n          type=\"email\"\n          autoComplete=\"email\"\n          {...register('email')}\n        />\n        <FormWarningText text={errors.email?.message?.toString()} />\n\n        <Button type=\"submit\" className=\"mt-6\" loading={loading}>\n          {t('Send email')}\n        </Button>\n      </form>\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,iBAA4B;AAC5B,0BAA4B;AAC5B,2BAA6C;AAC7C,sBAA2C;AAC3C,sBAAwF;AACxF,mBAAoC;AACpC,6BAAwB;AAExB,eAA4B;AAC5B,0BAA+B;AAC/B,0BAAgC;AAwC1B;AAtCN,SAAS,IAAI,OAGV;AACD,QAAM,EAAE,EAAE,QAAI,oCAAe;AAC7B,QAAM,CAAC,KAAK,MAAM,QAAI,uBAAiB,EAAE;AACzC,QAAM,CAAC,YAAY,aAAa,QAAI,uBAAkB,KAAK;AAC3D,QAAM,eAAW,sBAAY;AAC7B,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAAwB,IAAI;AAEtD,8BAAU,MAAM;AACd,QAAI,IAAI,WAAW,KAAK,CAAC,YAAY;AACnC,oBAAc,IAAI;AAClB,eAAS,oBAAoB,MAAM,MAAM,KAAK,EAC3C,KAAK,YAAU;AACd,YAAI,OAAO,WAAW,SAAS;AAC7B,cAAI,gCAAY,sBAAsB,WAAW,OAAO,KAAK,GAAG;AAC9D,qBAAS,EAAE,cAAc,CAAC;AAAA,UAC5B,WAAW,gCAAY,gBAAgB,WAAW,OAAO,KAAK,GAAG;AAC/D,qBAAS,EAAE,mBAAmB,CAAC;AAAA,UACjC,OAAO;AACL,kBAAM,OAAO;AAAA,UACf;AAAA,QACF;AAAA,MACF,CAAC,EACA,MAAM,OAAK,QAAQ,MAAM,CAAC,CAAC,EAC3B,QAAQ,MAAM;AACb,sBAAc,KAAK;AACnB,eAAO,EAAE;AAAA,MACX,CAAC;AAAA,IACL;AACA,QAAI,IAAI,WAAW,KAAK,IAAI,WAAW,GAAG;AACxC,eAAS,IAAI;AAAA,IACf;AAAA,EACF,GAAG,CAAC,KAAK,UAAU,CAAC;AAEpB,SACE,6CAAC,SAAI,WAAU,2CACb;AAAA,iDAAC,UAAK,WAAU,0CACd;AAAA,kDAAC,8BAAW,WAAU,QAAS,YAAE,gCAAgC,GAAE;AAAA,MACnE;AAAA,QAAC;AAAA;AAAA,UACC,WAAW;AAAA,UACX,MAAK;AAAA,UACL,WAAU;AAAA,UACV,SAAS;AAAA,UACT,OAAO;AAAA,UACP,UAAU,WAAS,OAAO,MAAM,YAAY,CAAC;AAAA,UAC7C,UAAU;AAAA,UAEV,sDAAC,iCACE,WAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,UACvB,4CAAC,gCAAyB,OAAc,MAAK,QAA1B,KAA+B,CACnD,GACH;AAAA;AAAA,MACF;AAAA,MACC,SAAS,4CAAC,uCAAgB,MAAM,OAAO;AAAA,OAC1C;AAAA,IACA,4CAAC,0BAAO,SAAQ,QAAO,SAAS,MAAM,QAAQ,WAAU,aAAa,YAAE,QAAQ,GAAE;AAAA,KACnF;AAEJ;AAEO,SAAS,kBAAkB;AAChC,QAAM,EAAE,EAAE,QAAI,oCAAe;AAC7B,QAAM,UAAM,sBAAY;AACxB,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAS,KAAK;AAC5C,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAAwB,IAAI;AAEtD,QAAM,aAAS,gCAAU;AAAA,IACvB,WAAO,wCAAkB,EAAE,4BAA4B,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,yBAAyB,CAAC;AAAA,EAC3G,CAAC;AAED,QAAM,EAAE,UAAU,cAAc,UAAU,WAAW,EAAE,OAAO,EAAE,QAAI,gCAAQ;AAAA,IAC1E,cAAU,wBAAY,MAAM;AAAA,EAC9B,CAAC;AAED,QAAM,WAAW,OAAO,SAAuC;AAC7D,eAAW,IAAI;AACf,QAAI;AACF,YAAM,EAAE,MAAM,IAAI;AAClB,YAAM,SAAS,MAAM,IAAI,mBAAmB,KAAK;AACjD,UAAI,OAAO,WAAW,SAAS;AAC7B,iBAAS,SAAS,EAAE,MAAM,UAAU,SAAS,OAAO,MAAM,QAAQ,CAAC;AACnE;AAAA,MACF,OAAO;AACL,iBAAS,OAAO,KAAK,KAAK;AAAA,MAC5B;AAAA,IACF,SAAS,GAAG;AACV,UAAI,gCAAY,iBAAiB,WAAW,CAAC,GAAG;AAC9C,iBAAS,SAAS,EAAE,MAAM,UAAU,SAAS,EAAE,yCAAyC,EAAE,CAAC;AAAA,MAC7F,OAAO;AACL,cAAM;AAAA,MACR;AAAA,IACF,UAAE;AACA,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AAEA,MAAI,OAAO;AACT,WAAO,4CAAC,OAAI,OAAc,QAAQ,MAAM,SAAS,IAAI,GAAG;AAAA,EAC1D,OAAO;AACL,WACE;AAAA,MAAC;AAAA;AAAA,QACC,WAAU;AAAA,QACV,UAAU,WAAK,4CAA2B,aAAa,QAAQ,EAAE,CAAC,CAAC;AAAA,QACnE,YAAU;AAAA,QAEV;AAAA,sDAAC,yBAAM,SAAQ,SAAQ,WAAU,QAAQ,YAAE,OAAO,GAAE;AAAA,UACpD;AAAA,YAAC;AAAA;AAAA,cACC,IAAG;AAAA,cACH,MAAK;AAAA,cACL,cAAa;AAAA,cACZ,GAAG,SAAS,OAAO;AAAA;AAAA,UACtB;AAAA,UACA,4CAAC,uCAAgB,MAAM,OAAO,OAAO,SAAS,SAAS,GAAG;AAAA,UAE1D,4CAAC,0BAAO,MAAK,UAAS,WAAU,QAAO,SACpC,YAAE,YAAY,GACjB;AAAA;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;", "names": []}