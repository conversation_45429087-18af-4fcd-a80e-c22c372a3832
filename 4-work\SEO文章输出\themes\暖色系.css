/* Warm Reader-Friendly Theme */
body {
  font-family: 'Georgia', serif;
  line-height: 1.7;
  color: #444;
  max-width: 750px;
  margin: 0 auto;
  padding: 2rem;
  background-color: #fffcf7;
}

h1 {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  font-size: 2.4rem;
  font-weight: 700;
  color: #2c2c2c;
  margin-bottom: 1.5rem;
  line-height: 1.3;
}

h2 {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  font-size: 1.8rem;
  font-weight: 600;
  color: #3a3a3a;
  margin-top: 2.5rem;
  margin-bottom: 1rem;
  padding-bottom: 0.3rem;
  border-bottom: 1px solid #e0d6c9;
}

h3 {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  font-size: 1.4rem;
  font-weight: 600;
  color: #4a4a4a;
  margin-top: 2rem;
  margin-bottom: 0.75rem;
}

p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

a {
  color: #9b6a36;
  text-decoration: none;
  border-bottom: 1px solid rgba(155, 106, 54, 0.2);
  transition: border-color 0.2s;
}

a:hover {
  border-bottom-color: #9b6a36;
}

blockquote {
  border-left: 3px solid #d4c5b0;
  padding: 0.5rem 1.5rem;
  margin: 1.5rem 0;
  background-color: #f9f5f0;
  color: #5a5a5a;
}

code {
  background: #f5f1e8;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 0.9rem;
  color: #8a6534;
}

ul, ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
}

li {
  margin-bottom: 0.75rem;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 2rem auto;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
}

th, td {
  padding: 0.75rem;
  border: 1px solid #e0d6c9;
  text-align: left;
}

th {
  background-color: #f9f5f0;
  font-weight: 600;
}

@media (max-width: 768px) {
  body {
    padding: 1rem;
    font-size: 0.95rem;
  }
  
  h1 {
    font-size: 2rem;
  }
  
  h2 {
    font-size: 1.5rem;
  }
  
  h3 {
    font-size: 1.25rem;
  }
}