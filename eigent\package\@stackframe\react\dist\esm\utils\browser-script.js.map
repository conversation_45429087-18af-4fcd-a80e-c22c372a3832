{"version": 3, "sources": ["../../../src/utils/browser-script.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n// Note that this script can not import anything from outside as it will be converted to a string and executed in the browser.\n\nimport { SsrScript } from \"../components/elements/ssr-layout-effect\";\n\nconst script = () => {\n  const attributes = ['data-joy-color-scheme', 'data-mui-color-scheme', 'data-theme', 'data-color-scheme', 'class'];\n\n  const getColorMode = (value: string) => {\n    if (value.includes('dark')) {\n      return 'dark';\n    }\n    if (value.includes('light')) {\n      return 'light';\n    }\n    return null;\n  };\n\n  const setTheme = (mode: 'dark' | 'light') => {\n    let el = document.getElementById(`--stack-theme-mode`);\n    if (!el) {\n      el = document.createElement(\"style\");\n      el.id = `--stack-theme-mode`;\n      el.innerHTML = `/* This tag is used by Stack Auth to set the theme in the browser without causing a hydration error (since React ignores additional tags in the <head>). We later use the \\`html:has(head > [data-stack-theme=XYZ])\\` selector to apply styles based on the theme. */`;\n      document.head.appendChild(el);\n    }\n    el.setAttribute(\"data-stack-theme\", mode);\n  };\n\n  const colorToRGB = (color: string): [number, number, number] | null => {\n    // Create a temporary element to use for color conversion\n    const temp = document.createElement('div');\n    temp.style.color = color;\n    document.body.appendChild(temp);\n\n    // Get the computed style\n    const computedColor = getComputedStyle(temp).color;\n    document.body.removeChild(temp);\n\n    // Parse the RGB values\n    const match = computedColor.match(/^rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)$/);\n    if (match) {\n      return [parseInt(match[1]), parseInt(match[2]), parseInt(match[3])];\n    }\n\n    return null;\n  };\n\n  const rgbToLuma = (rgb: [number, number, number]) => {\n    return (rgb[0] * 299 + rgb[1] * 587 + rgb[2] * 114) / 1000;\n  };\n\n  const copyFromColorScheme = () => {\n    const colorScheme = getComputedStyle(document.documentElement).getPropertyValue('color-scheme');\n\n    if (colorScheme) {\n      const mode = getColorMode(colorScheme);\n      if (mode) {\n        setTheme(mode);\n        return true;\n      }\n    }\n    return false;\n  };\n\n  const copyFromVariables = () => {\n    let backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--background');\n    if (backgroundColor) {\n      // shadcn by default uses the hsl values with the format \"123 45% 6.5%\"\n      if (/^\\d+\\s\\d+%\\s\\d+(\\.\\d+)?%$/.test(backgroundColor)) {\n        backgroundColor = `hsl(${backgroundColor})`;\n      }\n\n      // convert backgroundColor to luma and check if it's dark\n      const rgb = colorToRGB(backgroundColor);\n      if (rgb) {\n        const luma = rgbToLuma(rgb);\n        if (luma < 128) {\n          setTheme('dark');\n        } else {\n          setTheme('light');\n        }\n        return true;\n      }\n    }\n    return false;\n  };\n\n  const copyFromAttributes = () => {\n    for (const attributeName of attributes) {\n      const colorTheme = document.documentElement.getAttribute(attributeName);\n      if (colorTheme) {\n        const mode = getColorMode(colorTheme);\n        if (mode) {\n          setTheme(mode);\n          return true;\n        }\n      }\n    }\n    return false;\n  };\n\n  const observer = new MutationObserver((mutations) => {\n    mutations.forEach((mutation) => {\n      if (copyFromColorScheme()) {\n        return;\n      }\n      if (mutation.attributeName && attributes.includes(mutation.attributeName) && copyFromAttributes()) {\n        return;\n      }\n      if (copyFromVariables()) {\n        return;\n      }\n    });\n  });\n\n  observer.observe(document.documentElement, {\n    attributes: true,\n    attributeFilter: attributes,\n  });\n\n  // Initial check on page load\n  if (!copyFromColorScheme()) {\n    if (!copyFromAttributes()) {\n      copyFromVariables();\n    }\n  }\n};\n\nexport function BrowserScript(props : { nonce?: string }) {\n  return <SsrScript nonce={props.nonce} script={`(${script.toString()})()`}/>;\n}\n"], "mappings": ";AAMA,SAAS,iBAAiB;AA+HjB;AA7HT,IAAM,SAAS,MAAM;AACnB,QAAM,aAAa,CAAC,yBAAyB,yBAAyB,cAAc,qBAAqB,OAAO;AAEhH,QAAM,eAAe,CAAC,UAAkB;AACtC,QAAI,MAAM,SAAS,MAAM,GAAG;AAC1B,aAAO;AAAA,IACT;AACA,QAAI,MAAM,SAAS,OAAO,GAAG;AAC3B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAEA,QAAM,WAAW,CAAC,SAA2B;AAC3C,QAAI,KAAK,SAAS,eAAe,oBAAoB;AACrD,QAAI,CAAC,IAAI;AACP,WAAK,SAAS,cAAc,OAAO;AACnC,SAAG,KAAK;AACR,SAAG,YAAY;AACf,eAAS,KAAK,YAAY,EAAE;AAAA,IAC9B;AACA,OAAG,aAAa,oBAAoB,IAAI;AAAA,EAC1C;AAEA,QAAM,aAAa,CAAC,UAAmD;AAErE,UAAM,OAAO,SAAS,cAAc,KAAK;AACzC,SAAK,MAAM,QAAQ;AACnB,aAAS,KAAK,YAAY,IAAI;AAG9B,UAAM,gBAAgB,iBAAiB,IAAI,EAAE;AAC7C,aAAS,KAAK,YAAY,IAAI;AAG9B,UAAM,QAAQ,cAAc,MAAM,kCAAkC;AACpE,QAAI,OAAO;AACT,aAAO,CAAC,SAAS,MAAM,CAAC,CAAC,GAAG,SAAS,MAAM,CAAC,CAAC,GAAG,SAAS,MAAM,CAAC,CAAC,CAAC;AAAA,IACpE;AAEA,WAAO;AAAA,EACT;AAEA,QAAM,YAAY,CAAC,QAAkC;AACnD,YAAQ,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,OAAO;AAAA,EACxD;AAEA,QAAM,sBAAsB,MAAM;AAChC,UAAM,cAAc,iBAAiB,SAAS,eAAe,EAAE,iBAAiB,cAAc;AAE9F,QAAI,aAAa;AACf,YAAM,OAAO,aAAa,WAAW;AACrC,UAAI,MAAM;AACR,iBAAS,IAAI;AACb,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,QAAM,oBAAoB,MAAM;AAC9B,QAAI,kBAAkB,iBAAiB,SAAS,eAAe,EAAE,iBAAiB,cAAc;AAChG,QAAI,iBAAiB;AAEnB,UAAI,4BAA4B,KAAK,eAAe,GAAG;AACrD,0BAAkB,OAAO,eAAe;AAAA,MAC1C;AAGA,YAAM,MAAM,WAAW,eAAe;AACtC,UAAI,KAAK;AACP,cAAM,OAAO,UAAU,GAAG;AAC1B,YAAI,OAAO,KAAK;AACd,mBAAS,MAAM;AAAA,QACjB,OAAO;AACL,mBAAS,OAAO;AAAA,QAClB;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,QAAM,qBAAqB,MAAM;AAC/B,eAAW,iBAAiB,YAAY;AACtC,YAAM,aAAa,SAAS,gBAAgB,aAAa,aAAa;AACtE,UAAI,YAAY;AACd,cAAM,OAAO,aAAa,UAAU;AACpC,YAAI,MAAM;AACR,mBAAS,IAAI;AACb,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,QAAM,WAAW,IAAI,iBAAiB,CAAC,cAAc;AACnD,cAAU,QAAQ,CAAC,aAAa;AAC9B,UAAI,oBAAoB,GAAG;AACzB;AAAA,MACF;AACA,UAAI,SAAS,iBAAiB,WAAW,SAAS,SAAS,aAAa,KAAK,mBAAmB,GAAG;AACjG;AAAA,MACF;AACA,UAAI,kBAAkB,GAAG;AACvB;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,WAAS,QAAQ,SAAS,iBAAiB;AAAA,IACzC,YAAY;AAAA,IACZ,iBAAiB;AAAA,EACnB,CAAC;AAGD,MAAI,CAAC,oBAAoB,GAAG;AAC1B,QAAI,CAAC,mBAAmB,GAAG;AACzB,wBAAkB;AAAA,IACpB;AAAA,EACF;AACF;AAEO,SAAS,cAAc,OAA4B;AACxD,SAAO,oBAAC,aAAU,OAAO,MAAM,OAAO,QAAQ,IAAI,OAAO,SAAS,CAAC,OAAM;AAC3E;", "names": []}