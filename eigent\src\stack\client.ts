// Stack Auth is temporarily disabled for development
// To enable Stack Auth, uncomment the code below and configure environment variables

// import { StackClientApp } from "@stackframe/react";
// import { useNavigate } from "react-router-dom";

// Check if Stack Auth environment variables are configured
// const hasStackAuthConfig = import.meta.env.VITE_STACK_PROJECT_ID && import.meta.env.VITE_STACK_PUBLISHABLE_CLIENT_KEY;

// Only initialize Stack Auth if environment variables are provided
// export const stackClientApp = hasStackAuthConfig ? new StackClientApp({
// 	projectId: import.meta.env.VITE_STACK_PROJECT_ID,
// 	publishableClientKey: import.meta.env.VITE_STACK_PUBLISHABLE_CLIENT_KEY,
// 	tokenStore: "cookie",
// 	redirectMethod: {
// 		useNavigate,
// 	},
// 	urls: {
// 		oauthCallback: (import.meta.env.DEV ? import.meta.env.VITE_PROXY_URL : import.meta.env.VITE_BASE_URL) + "/api/redirect/callback",
// 	},
// }) : null;

// Temporarily disable Stack Auth
export const stackClientApp = null;
