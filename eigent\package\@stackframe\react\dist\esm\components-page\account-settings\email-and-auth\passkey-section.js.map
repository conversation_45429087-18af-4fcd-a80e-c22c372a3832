{"version": 3, "sources": ["../../../../../src/components-page/account-settings/email-and-auth/passkey-section.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { Button, Typography } from \"@stackframe/stack-ui\";\nimport { useState } from \"react\";\nimport { useStackApp } from \"../../..\";\nimport { useUser } from \"../../../lib/hooks\";\nimport { useTranslation } from \"../../../lib/translations\";\nimport { Section } from \"../section\";\n\nexport function PasskeySection() {\n  const { t } = useTranslation();\n  const user = useUser({ or: \"throw\" });\n  const stackApp = useStackApp();\n  const project = stackApp.useProject();\n  const contactChannels = user.useContactChannels();\n\n\n  // passkey is enabled if there is a passkey\n  const hasPasskey = user.passkeyAuthEnabled;\n\n  const isLastAuth = user.passkeyAuthEnabled && !user.hasPassword && user.oauthProviders.length === 0 && !user.otpAuthEnabled;\n  const [showConfirmationModal, setShowConfirmationModal] = useState(false);\n\n  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n  const hasValidEmail = contactChannels.filter(x => x.type === 'email' && x.isVerified && x.usedForAuth).length > 0;\n\n  if (!project.config.passkeyEnabled) {\n    return null;\n  }\n\n  const handleDeletePasskey = async () => {\n    await user.update({ passkeyAuthEnabled: false });\n    setShowConfirmationModal(false);\n  };\n\n\n  const handleAddNewPasskey = async () => {\n    await user.registerPasskey();\n  };\n\n  return (\n    <>\n      <Section title={t(\"Passkey\")} description={hasPasskey ? t(\"Passkey registered\") : t(\"Register a passkey\")}>\n        <div className='flex md:justify-end gap-2'>\n          {!hasValidEmail && (\n            <Typography variant='secondary' type='label'>{t(\"To enable Passkey sign-in, please add a verified sign-in email.\")}</Typography>\n          )}\n          {hasValidEmail && hasPasskey && isLastAuth && (\n            <Typography variant='secondary' type='label'>{t(\"Passkey sign-in is enabled and cannot be disabled as it is currently the only sign-in method\")}</Typography>\n          )}\n          {!hasPasskey && hasValidEmail && (\n            <div>\n              <Button onClick={handleAddNewPasskey} variant='secondary'>{t(\"Add new passkey\")}</Button>\n            </div>\n          )}\n          {hasValidEmail && hasPasskey && !isLastAuth && !showConfirmationModal && (\n            <Button\n              variant='secondary'\n              onClick={() => setShowConfirmationModal(true)}\n            >\n              {t(\"Delete Passkey\")}\n            </Button>\n          )}\n          {hasValidEmail && hasPasskey && !isLastAuth && showConfirmationModal && (\n            <div className='flex flex-col gap-2'>\n              <Typography variant='destructive'>\n                {t(\"Are you sure you want to disable Passkey sign-in? You will not be able to sign in with your passkey anymore.\")}\n              </Typography>\n              <div className='flex gap-2'>\n                <Button\n                  variant='destructive'\n                  onClick={handleDeletePasskey}\n                >\n                  {t(\"Disable\")}\n                </Button>\n                <Button\n                  variant='secondary'\n                  onClick={() => setShowConfirmationModal(false)}\n                >\n                  {t(\"Cancel\")}\n                </Button>\n              </div>\n            </div>\n          )}\n        </div>\n      </Section>\n\n\n    </>\n\n  );\n}\n"], "mappings": ";AAIA,SAAS,QAAQ,kBAAkB;AACnC,SAAS,gBAAgB;AACzB,SAAS,mBAAmB;AAC5B,SAAS,eAAe;AACxB,SAAS,sBAAsB;AAC/B,SAAS,eAAe;AAkCpB,mBAIQ,KAuBE,YA3BV;AAhCG,SAAS,iBAAiB;AAC/B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,OAAO,QAAQ,EAAE,IAAI,QAAQ,CAAC;AACpC,QAAM,WAAW,YAAY;AAC7B,QAAM,UAAU,SAAS,WAAW;AACpC,QAAM,kBAAkB,KAAK,mBAAmB;AAIhD,QAAM,aAAa,KAAK;AAExB,QAAM,aAAa,KAAK,sBAAsB,CAAC,KAAK,eAAe,KAAK,eAAe,WAAW,KAAK,CAAC,KAAK;AAC7G,QAAM,CAAC,uBAAuB,wBAAwB,IAAI,SAAS,KAAK;AAGxE,QAAM,gBAAgB,gBAAgB,OAAO,OAAK,EAAE,SAAS,WAAW,EAAE,cAAc,EAAE,WAAW,EAAE,SAAS;AAEhH,MAAI,CAAC,QAAQ,OAAO,gBAAgB;AAClC,WAAO;AAAA,EACT;AAEA,QAAM,sBAAsB,YAAY;AACtC,UAAM,KAAK,OAAO,EAAE,oBAAoB,MAAM,CAAC;AAC/C,6BAAyB,KAAK;AAAA,EAChC;AAGA,QAAM,sBAAsB,YAAY;AACtC,UAAM,KAAK,gBAAgB;AAAA,EAC7B;AAEA,SACE,gCACE,8BAAC,WAAQ,OAAO,EAAE,SAAS,GAAG,aAAa,aAAa,EAAE,oBAAoB,IAAI,EAAE,oBAAoB,GACtG,+BAAC,SAAI,WAAU,6BACZ;AAAA,KAAC,iBACA,oBAAC,cAAW,SAAQ,aAAY,MAAK,SAAS,YAAE,iEAAiE,GAAE;AAAA,IAEpH,iBAAiB,cAAc,cAC9B,oBAAC,cAAW,SAAQ,aAAY,MAAK,SAAS,YAAE,8FAA8F,GAAE;AAAA,IAEjJ,CAAC,cAAc,iBACd,oBAAC,SACC,8BAAC,UAAO,SAAS,qBAAqB,SAAQ,aAAa,YAAE,iBAAiB,GAAE,GAClF;AAAA,IAED,iBAAiB,cAAc,CAAC,cAAc,CAAC,yBAC9C;AAAA,MAAC;AAAA;AAAA,QACC,SAAQ;AAAA,QACR,SAAS,MAAM,yBAAyB,IAAI;AAAA,QAE3C,YAAE,gBAAgB;AAAA;AAAA,IACrB;AAAA,IAED,iBAAiB,cAAc,CAAC,cAAc,yBAC7C,qBAAC,SAAI,WAAU,uBACb;AAAA,0BAAC,cAAW,SAAQ,eACjB,YAAE,8GAA8G,GACnH;AAAA,MACA,qBAAC,SAAI,WAAU,cACb;AAAA;AAAA,UAAC;AAAA;AAAA,YACC,SAAQ;AAAA,YACR,SAAS;AAAA,YAER,YAAE,SAAS;AAAA;AAAA,QACd;AAAA,QACA;AAAA,UAAC;AAAA;AAAA,YACC,SAAQ;AAAA,YACR,SAAS,MAAM,yBAAyB,KAAK;AAAA,YAE5C,YAAE,QAAQ;AAAA;AAAA,QACb;AAAA,SACF;AAAA,OACF;AAAA,KAEJ,GACF,GAGF;AAGJ;", "names": []}