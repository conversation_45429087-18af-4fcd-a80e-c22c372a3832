{"version": 3, "sources": ["../../../src/components-page/cli-auth-confirm.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { Typography } from \"@stackframe/stack-ui\";\nimport { useState } from \"react\";\nimport { stackAppInternalsSymbol, useStackApp } from \"..\";\nimport { MessageCard } from \"../components/message-cards/message-card\";\nimport { useTranslation } from \"../lib/translations\";\n\nexport function CliAuthConfirmation({ fullPage = true }: { fullPage?: boolean }) {\n  const { t } = useTranslation();\n  const app = useStackApp();\n  const [authorizing, setAuthorizing] = useState(false);\n  const [success, setSuccess] = useState(false);\n  const [error, setError] = useState<Error | null>(null);\n\n  const user = app.useUser({ or: \"redirect\" });\n\n  const handleAuthorize = async () => {\n    if (authorizing) return;\n\n    setAuthorizing(true);\n    try {\n      // Get login code from URL query parameters\n      const urlParams = new URLSearchParams(window.location.search);\n      const loginCode = urlParams.get(\"login_code\");\n\n      if (!loginCode) {\n        throw new Error(\"Missing login code in URL parameters\");\n      }\n      const refreshToken = (await user.currentSession.getTokens()).refreshToken;\n      if (!refreshToken) {\n        throw new Error(\"You must be logged in to authorize CLI access\");\n      }\n\n      // Use the internal API to send the CLI login request\n      const result = await (app as any)[stackAppInternalsSymbol].sendRequest(\"/auth/cli/complete\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          login_code: loginCode,\n          refresh_token: (await user.currentSession.getTokens()).refreshToken\n        })\n      });\n\n      if (!result.ok) {\n        throw new Error(`Authorization failed: ${result.status} ${await result.text()}`);\n      }\n\n      setSuccess(true);\n    } catch (err) {\n      setError(err as Error);\n    } finally {\n      setAuthorizing(false);\n    }\n  };\n\n  if (success) {\n    return (\n      <MessageCard\n        title={t(\"CLI Authorization Successful\")}\n        fullPage={fullPage}\n        primaryButtonText={t(\"Close\")}\n        primaryAction={() => window.close()}\n      >\n        <Typography>\n          {t(\"The CLI application has been authorized successfully. You can now close this window and return to the command line.\")}\n        </Typography>\n      </MessageCard>\n    );\n  }\n\n  if (error) {\n    return (\n      <MessageCard\n        title={t(\"Authorization Failed\")}\n        fullPage={fullPage}\n        primaryButtonText={t(\"Try Again\")}\n        primaryAction={() => setError(null)}\n        secondaryButtonText={t(\"Cancel\")}\n        secondaryAction={() => window.close()}\n      >\n        <Typography className=\"text-red-600\">\n          {t(\"Failed to authorize the CLI application:\")}\n        </Typography>\n        <Typography className=\"text-red-600\">\n          {error.message}\n        </Typography>\n      </MessageCard>\n    );\n  }\n\n  return (\n    <MessageCard\n      title={t(\"Authorize CLI Application\")}\n      fullPage={fullPage}\n      primaryButtonText={authorizing ? t(\"Authorizing...\") : t(\"Authorize\")}\n      primaryAction={handleAuthorize}\n      secondaryButtonText={t(\"Cancel\")}\n      secondaryAction={() => window.close()}\n    >\n      <Typography>\n        {t(\"A command line application is requesting access to your account. Click the button below to authorize it.\")}\n      </Typography>\n      <Typography variant=\"destructive\">\n        {t(\"WARNING: Make sure you trust the command line application, as it will gain access to your account. If you did not initiate this request, you can close this page and ignore it. We will never send you this link via email or any other means.\")}\n      </Typography>\n    </MessageCard>\n  );\n}\n"], "mappings": ";;;AAOA,SAAS,kBAAkB;AAC3B,SAAS,gBAAgB;AACzB,SAAS,yBAAyB,mBAAmB;AACrD,SAAS,mBAAmB;AAC5B,SAAS,sBAAsB;AA4DvB,cASF,YATE;AA1DD,SAAS,oBAAoB,EAAE,WAAW,KAAK,GAA2B;AAC/E,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,MAAM,YAAY;AACxB,QAAM,CAAC,aAAa,cAAc,IAAI,SAAS,KAAK;AACpD,QAAM,CAAC,SAAS,UAAU,IAAI,SAAS,KAAK;AAC5C,QAAM,CAAC,OAAO,QAAQ,IAAI,SAAuB,IAAI;AAErD,QAAM,OAAO,IAAI,QAAQ,EAAE,IAAI,WAAW,CAAC;AAE3C,QAAM,kBAAkB,YAAY;AAClC,QAAI,YAAa;AAEjB,mBAAe,IAAI;AACnB,QAAI;AAEF,YAAM,YAAY,IAAI,gBAAgB,OAAO,SAAS,MAAM;AAC5D,YAAM,YAAY,UAAU,IAAI,YAAY;AAE5C,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,sCAAsC;AAAA,MACxD;AACA,YAAM,gBAAgB,MAAM,KAAK,eAAe,UAAU,GAAG;AAC7D,UAAI,CAAC,cAAc;AACjB,cAAM,IAAI,MAAM,+CAA+C;AAAA,MACjE;AAGA,YAAM,SAAS,MAAO,IAAY,uBAAuB,EAAE,YAAY,sBAAsB;AAAA,QAC3F,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,YAAY;AAAA,UACZ,gBAAgB,MAAM,KAAK,eAAe,UAAU,GAAG;AAAA,QACzD,CAAC;AAAA,MACH,CAAC;AAED,UAAI,CAAC,OAAO,IAAI;AACd,cAAM,IAAI,MAAM,yBAAyB,OAAO,MAAM,IAAI,MAAM,OAAO,KAAK,CAAC,EAAE;AAAA,MACjF;AAEA,iBAAW,IAAI;AAAA,IACjB,SAAS,KAAK;AACZ,eAAS,GAAY;AAAA,IACvB,UAAE;AACA,qBAAe,KAAK;AAAA,IACtB;AAAA,EACF;AAEA,MAAI,SAAS;AACX,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO,EAAE,8BAA8B;AAAA,QACvC;AAAA,QACA,mBAAmB,EAAE,OAAO;AAAA,QAC5B,eAAe,MAAM,OAAO,MAAM;AAAA,QAElC,8BAAC,cACE,YAAE,qHAAqH,GAC1H;AAAA;AAAA,IACF;AAAA,EAEJ;AAEA,MAAI,OAAO;AACT,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO,EAAE,sBAAsB;AAAA,QAC/B;AAAA,QACA,mBAAmB,EAAE,WAAW;AAAA,QAChC,eAAe,MAAM,SAAS,IAAI;AAAA,QAClC,qBAAqB,EAAE,QAAQ;AAAA,QAC/B,iBAAiB,MAAM,OAAO,MAAM;AAAA,QAEpC;AAAA,8BAAC,cAAW,WAAU,gBACnB,YAAE,0CAA0C,GAC/C;AAAA,UACA,oBAAC,cAAW,WAAU,gBACnB,gBAAM,SACT;AAAA;AAAA;AAAA,IACF;AAAA,EAEJ;AAEA,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO,EAAE,2BAA2B;AAAA,MACpC;AAAA,MACA,mBAAmB,cAAc,EAAE,gBAAgB,IAAI,EAAE,WAAW;AAAA,MACpE,eAAe;AAAA,MACf,qBAAqB,EAAE,QAAQ;AAAA,MAC/B,iBAAiB,MAAM,OAAO,MAAM;AAAA,MAEpC;AAAA,4BAAC,cACE,YAAE,0GAA0G,GAC/G;AAAA,QACA,oBAAC,cAAW,SAAQ,eACjB,YAAE,gPAAgP,GACrP;AAAA;AAAA;AAAA,EACF;AAEJ;", "names": []}