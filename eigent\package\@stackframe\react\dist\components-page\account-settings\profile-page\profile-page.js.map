{"version": 3, "sources": ["../../../../src/components-page/account-settings/profile-page/profile-page.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { ProfileImageEditor } from \"../../../components/profile-image-editor\";\nimport { useUser } from \"../../../lib/hooks\";\nimport { useTranslation } from \"../../../lib/translations\";\nimport { EditableText } from \"../editable-text\";\nimport { PageLayout } from \"../page-layout\";\nimport { Section } from \"../section\";\n\nexport function ProfilePage() {\n  const { t } = useTranslation();\n  const user = useUser({ or: 'redirect' });\n\n  return (\n    <PageLayout>\n      <Section\n        title={t(\"User name\")}\n        description={t(\"This is a display name and is not used for authentication\")}\n      >\n        <EditableText\n          value={user.displayName || ''}\n          onSave={async (newDisplayName) => {\n            await user.update({ displayName: newDisplayName });\n          }}/>\n      </Section>\n\n      <Section\n        title={t(\"Profile image\")}\n        description={t(\"Upload your own image as your avatar\")}\n      >\n        <ProfileImageEditor\n          user={user}\n          onProfileImageUrlChange={async (profileImageUrl) => {\n            await user.update({ profileImageUrl });\n          }}\n        />\n      </Section>\n    </PageLayout>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,kCAAmC;AACnC,mBAAwB;AACxB,0BAA+B;AAC/B,2BAA6B;AAC7B,yBAA2B;AAC3B,qBAAwB;AAOpB;AALG,SAAS,cAAc;AAC5B,QAAM,EAAE,EAAE,QAAI,oCAAe;AAC7B,QAAM,WAAO,sBAAQ,EAAE,IAAI,WAAW,CAAC;AAEvC,SACE,6CAAC,iCACC;AAAA;AAAA,MAAC;AAAA;AAAA,QACC,OAAO,EAAE,WAAW;AAAA,QACpB,aAAa,EAAE,2DAA2D;AAAA,QAE1E;AAAA,UAAC;AAAA;AAAA,YACC,OAAO,KAAK,eAAe;AAAA,YAC3B,QAAQ,OAAO,mBAAmB;AAChC,oBAAM,KAAK,OAAO,EAAE,aAAa,eAAe,CAAC;AAAA,YACnD;AAAA;AAAA,QAAE;AAAA;AAAA,IACN;AAAA,IAEA;AAAA,MAAC;AAAA;AAAA,QACC,OAAO,EAAE,eAAe;AAAA,QACxB,aAAa,EAAE,sCAAsC;AAAA,QAErD;AAAA,UAAC;AAAA;AAAA,YACC;AAAA,YACA,yBAAyB,OAAO,oBAAoB;AAClD,oBAAM,KAAK,OAAO,EAAE,gBAAgB,CAAC;AAAA,YACvC;AAAA;AAAA,QACF;AAAA;AAAA,IACF;AAAA,KACF;AAEJ;", "names": []}