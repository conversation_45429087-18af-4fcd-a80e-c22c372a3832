{"version": 3, "sources": ["../../../../src/components-page/account-settings/page-layout.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nexport function PageLayout(props: { children: React.ReactNode }) {\n  return (\n    <div className='flex flex-col gap-6'>\n      {props.children}\n    </div>\n  );\n}\n"], "mappings": ";AAMI;AAFG,SAAS,WAAW,OAAsC;AAC/D,SACE,oBAAC,SAAI,WAAU,uBACZ,gBAAM,UACT;AAEJ;", "names": []}