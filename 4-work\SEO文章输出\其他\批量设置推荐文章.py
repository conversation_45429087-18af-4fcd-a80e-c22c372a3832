import requests

for i in range(1, 200):
    # 改推荐文章
    url = 'https://fkhd2024.jz.fkw.com/en/ajax/news_h.jsp?cmd=setWafCk_batchSetNews&_TOKEN=8b68a1df853486f1591c021acfe29e31'
    headers = {
        'origin': 'https://fkhd2024.jz.fkw.com',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36',
        'cookie': '_cliid=XLaqFy3qhYu6YaLu; first_ta=207; _ta=207; _tp=-; _newUnion=0; _kw=0; _vid_url=https%3A%2F%2Fwww.fkw.com%2F; _s_pro=www.fkw.com%2F; _c_pro=www.fkw.com%2F; reg_sid=0; innerFlag=1; _faiHeDistictId=64adc12a52cb6381; Hm_lvt_26126ee052ae4ad30a36da928aff7654=**********; Hm_lpvt_26126ee052ae4ad30a36da928aff7654=**********; HMACCOUNT=01C3A9F3D2449B78; _pykey_=e355ec7f-e0aa-52a6-a78d-9a79f8856f0f; loginReferer=https://www.fkw.com/; loginComeForm=fkjz; wxRegBiz=none; grayUrl=; loginCacct=fkhd2024; loginCaid=********; loginSacct=boss; loginUseSacct=0; _FSESSIONID=3KyKP1ECcYGZZGUC; loginSign=; _jzmFirstLogin=false; loginTimeInMills=*************; _hasClosePlatinumAd_=false; _hasClosePlatinum_=false; _hasCloseFlyerAd_=false; faiscoAd=true; _hasCloseHdGG_=false; _whereToPortal_=login; _readCouponStr=init(effective_9_4910.0_9_4910.0); _readAllOrderTab=0; _new_reg_home=5; adImg_module_********=0_7_1729612800000; _isFirstLoginPc=false; _isFirstLoginPc_7=false'
    }
    data = {
        'fieldKey': 'recommendNews',
        'value': '{"ns":false,"ids":[],"groupIds":[44],"t":1}',
        'idList': f'[{i}]',
    }
    res = requests.post(url, headers=headers, data=data)
    if res.status_code == 200:
        print(f"id:{i}  ", res.json())
        tree = res.json()
    else:
        print("修改失败")

    # 改自定义地址
    data1 = {
        'fieldKey': 'cusUrlAddressV2',
        'value': '{"cut":"d","p":"","d":"marketing","md":"","mp":"","ocu":true,"icu":true}',
        'idList': f'[{i}]',
    }
    res = requests.post(url, headers=headers, data=data1)
    if res.status_code == 200:
        print(f"id:{i}  ", res.json())
        tree = res.json()
    else:
        print("修改失败")