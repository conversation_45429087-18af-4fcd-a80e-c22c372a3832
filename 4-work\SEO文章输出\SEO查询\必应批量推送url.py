import requests
import json
from urllib.parse import urlparse
import time


def read_urls_from_file(file_path):
    """
    从文本文件中读取URL列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            urls = [line.strip() for line in file if line.strip()]
        return urls
    except Exception as e:
        print(f"读取文件出错: {str(e)}")
        return []


def get_site_url(url):
    """
    从完整URL中提取站点URL
    """
    parsed = urlparse(url)
    return f"{parsed.scheme}://{parsed.netloc}"


def submit_urls_to_bing(api_key, site_url, url_batch):
    """
    向Bing提交一批URL
    """
    endpoint = f"https://ssl.bing.com/webmaster/api.svc/json/SubmitUrlbatch?apikey={api_key}"

    payload = {
        "siteUrl": site_url,
        "urlList": url_batch
    }

    headers = {
        "Content-Type": "application/json; charset=utf-8"
    }

    try:
        print(f"\n正在提交第 {len(url_batch)} 个URL...")

        response = requests.post(
            endpoint,
            headers=headers,
            json=payload
        )

        print(f"状态码: {response.status_code}")
        try:
            print(f"响应内容: {response.json()}")
        except:
            print(f"响应内容: {response.text}")

        response.raise_for_status()
        return True

    except requests.exceptions.RequestException as e:
        print(f"提交错误: {e}")
        return False


def batch_submit_urls(api_key, urls, batch_size=500):
    """
    分批提交URL列表
    """
    site_url = get_site_url(urls[0])
    total_urls = len(urls)
    successful_submissions = 0

    # 分批处理URL
    for i in range(0, total_urls, batch_size):
        batch = urls[i:i + batch_size]
        batch_num = (i // batch_size) + 1
        total_batches = (total_urls + batch_size - 1) // batch_size

        print(f"\n处理第 {batch_num}/{total_batches} 批 (共 {len(batch)} 个URL)")

        if submit_urls_to_bing(api_key, site_url, batch):
            successful_submissions += len(batch)

        # 批次之间暂停3秒，避免请求过于频繁
        if i + batch_size < total_urls:
            print("等待3秒后继续下一批...")
            time.sleep(3)

    return successful_submissions


def main():
    # 配置参数
    api_key = "fcc945e3c29d400685c170097c9f4a05"  # 你的API密钥
    urls_file = "E:/urls.txt"

    # 读取URL列表
    urls = read_urls_from_file(urls_file)

    if not urls:
        print("未能读取到URL，请检查文件内容")
        return

    print(f"从文件中读取到 {len(urls)} 个URL")
    print("第一个URL:", urls[0])
    print("最后一个URL:", urls[-1])

    # 确认继续
    print("\n请确认以下信息:")
    print(f"1. API密钥: {api_key}")
    print(f"2. 网站域名: {get_site_url(urls[0])}")
    print(f"3. URL总数: {len(urls)}")
    print(f"4. 将分 {(len(urls) + 499) // 500} 批提交，每批最多500个URL")

    # 分批提交URL
    successful_count = batch_submit_urls(api_key, urls)

    # 显示最终结果
    print("\n提交完成!")
    print(f"成功提交: {successful_count}/{len(urls)} 个URL")


if __name__ == "__main__":
    main()