{"version": 3, "sources": ["../../src/components-page/sign-out.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { cacheFunction } from \"@stackframe/stack-shared/dist/utils/caches\";\nimport React from \"react\";\nimport { CurrentUser, useUser } from \"..\";\nimport { PredefinedMessageCard } from \"../components/message-cards/predefined-message-card\";\n\nconst cacheSignOut = cacheFunction(async (user: CurrentUser) => {\n  return await user.signOut();\n});\n\nexport function SignOut(props: { fullPage?: boolean }) {\n  const user = useUser();\n\n  if (user) {\n    React.use(cacheSignOut(user));\n  }\n\n  return <PredefinedMessageCard type='signedOut' fullPage={props.fullPage} />;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,oBAA8B;AAC9B,mBAAkB;AAClB,eAAqC;AACrC,qCAAsC;AAa7B;AAXT,IAAM,mBAAe,6BAAc,OAAO,SAAsB;AAC9D,SAAO,MAAM,KAAK,QAAQ;AAC5B,CAAC;AAEM,SAAS,QAAQ,OAA+B;AACrD,QAAM,WAAO,kBAAQ;AAErB,MAAI,MAAM;AACR,iBAAAA,QAAM,IAAI,aAAa,IAAI,CAAC;AAAA,EAC9B;AAEA,SAAO,4CAAC,wDAAsB,MAAK,aAAY,UAAU,MAAM,UAAU;AAC3E;", "names": ["React"]}