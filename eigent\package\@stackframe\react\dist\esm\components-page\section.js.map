{"version": 3, "sources": ["../../../src/components-page/section.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { Separator, Typography } from \"@stackframe/stack-ui\";\n\n\nfunction Section(props: { title: string, description?: string, children: React.ReactNode }) {\n  return (\n    <>\n      <Separator />\n      <div className='flex flex-col sm:flex-row gap-2'>\n        <div className='sm:flex-1 flex flex-col justify-center'>\n          <Typography className='font-medium'>\n            {props.title}\n          </Typography>\n          {props.description && <Typography variant='secondary' type='footnote'>\n            {props.description}\n          </Typography>}\n        </div>\n        <div className='sm:flex-1 sm:items-end flex flex-col gap-2 '>\n          {props.children}\n        </div>\n      </div>\n    </>\n  );\n}\n"], "mappings": ";AAIA,SAAS,WAAW,kBAAkB;AAKlC,mBACE,KAEE,YAHJ;", "names": []}