/* 创意杂志风格 - 突出特点：大胆排版、鲜明色彩、杂志风格 */
body {
  font-family: 'Montserrat', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  max-width: 820px;
  margin: 0 auto;
  padding: 2rem;
  background-color: #fff;
}

h1 {
  font-size: 3.2rem;
  font-weight: 900;
  color: #333;
  margin: 1rem 0 2rem;
  line-height: 1.1;
  text-transform: uppercase;
  letter-spacing: -0.03em;
  position: relative;
  padding-bottom: 1rem;
  overflow: hidden;
}

h1::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 8px;
  background: linear-gradient(to right, #ff6b6b, #feca57, #1dd1a1, #00d2d3, #54a0ff);
}

h2 {
  font-size: 2.4rem;
  font-weight: 800;
  color: #333;
  margin-top: 3.5rem;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  letter-spacing: -0.02em;
  text-transform: uppercase;
  display: inline-block;
  position: relative;
  padding: 0 15px;
}

h2 .content {
  position: relative;
  z-index: 2;
}

h2::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 10px;
  width: 100%;
  height: 12px;
  background-color: #feca57;
  z-index: 1;
  transform: skewX(-15deg);
}

h3 {
  font-size: 1.6rem;
  font-weight: 700;
  color: #333;
  margin-top: 2.5rem;
  margin-bottom: 1rem;
  letter-spacing: -0.01em;
  position: relative;
  display: inline-block;
}

h3 .content {
  position: relative;
  padding-left: 18px;
  padding-right: 5px;
}

h3 .content::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 10px;
  height: 10px;
  background-color: #ff6b6b;
  border-radius: 50%;
}

p {
  margin-bottom: 1.7rem;
  font-size: 1.05rem;
  line-height: 1.8;
}

a {
  color: #ff6b6b;
  text-decoration: none;
  font-weight: 600;
  position: relative;
  transition: all 0.3s;
  display: inline-block;
}

a:hover {
  color: #ff5252;
}

a::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #ff6b6b;
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s;
}

a:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

blockquote {
  margin: 2.5rem 0;
  padding: 1.5rem 2rem;
  background-color: #f9f9f9;
  border-left: 8px solid #ff6b6b;
  font-size: 1.1rem;
  position: relative;
  box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.05);
}

blockquote::before {
  content: """;
  position: absolute;
  top: 0;
  left: 10px;
  font-size: 5rem;
  color: rgba(255, 107, 107, 0.1);
  font-family: 'Georgia', serif;
}

code {
  background: #f7f7f7;
  font-family: 'Fira Code', 'Menlo', monospace;
  font-size: 0.9em;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  color: #ff6b6b;
  border: 1px solid #eee;
}

ul, ol {
  margin: 1.7rem 0;
  padding-left: 1.5rem