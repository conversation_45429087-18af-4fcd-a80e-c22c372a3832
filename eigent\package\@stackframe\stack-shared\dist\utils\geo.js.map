{"version": 3, "sources": ["../../src/utils/geo.tsx"], "sourcesContent": ["\nimport * as yup from \"yup\";\nimport { yupNumber, yupObject, yupString } from \"../schema-fields\";\n\nexport const geoInfoSchema = yupObject({\n  ip: yupString().defined(),\n  countryCode: yupString().nullable(),\n  regionCode: yupString().nullable(),\n  cityName: yupString().nullable(),\n  latitude: yupNumber().nullable(),\n  longitude: yupNumber().nullable(),\n  tzIdentifier: yupString().nullable(),\n});\n\nexport type GeoInfo = yup.InferType<typeof geoInfoSchema>;\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,2BAAgD;AAEzC,IAAM,oBAAgB,gCAAU;AAAA,EACrC,QAAI,gCAAU,EAAE,QAAQ;AAAA,EACxB,iBAAa,gCAAU,EAAE,SAAS;AAAA,EAClC,gBAAY,gCAAU,EAAE,SAAS;AAAA,EACjC,cAAU,gCAAU,EAAE,SAAS;AAAA,EAC/B,cAAU,gCAAU,EAAE,SAAS;AAAA,EAC/B,eAAW,gCAAU,EAAE,SAAS;AAAA,EAChC,kBAAc,gCAAU,EAAE,SAAS;AACrC,CAAC;", "names": []}