{"version": 3, "sources": ["../../../src/components/message-cards/message-card.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport React from \"react\";\nimport { MaybeFullPage } from \"../elements/maybe-full-page\";\nimport { Button, Typography } from \"@stackframe/stack-ui\";\n\nexport function MessageCard(\n  { fullPage=false, ...props }:\n  {\n    children?: React.ReactNode,\n    title: string,\n    fullPage?: boolean,\n    primaryButtonText?: string,\n    primaryAction?: () => Promise<void> | void,\n    secondaryButtonText?: string,\n    secondaryAction?: () => Promise<void> | void,\n  }\n) {\n  return (\n    <MaybeFullPage fullPage={fullPage}>\n      <div className=\"text-center stack-scope flex flex-col gap-4\" style={{ maxWidth: '380px', flexBasis: '380px', padding: fullPage ? '1rem' : 0 }}>\n        <Typography type='h3'>{props.title}</Typography>\n        {props.children}\n        {(props.primaryButtonText || props.secondaryButtonText) && (\n          <div className=\"flex justify-center gap-4 my-5\">\n            {props.secondaryButtonText && (\n              <Button variant=\"secondary\" onClick={props.secondaryAction}>\n                {props.secondaryButtonText}\n              </Button>\n            )}\n            {props.primaryButtonText && (\n              <Button onClick={props.primaryAction}>\n                {props.primaryButtonText}\n              </Button>\n            )}\n          </div>\n        )}\n      </div>\n    </MaybeFullPage>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,6BAA8B;AAC9B,sBAAmC;AAiB3B;AAfD,SAAS,YACd,EAAE,WAAS,OAAO,GAAG,MAAM,GAU3B;AACA,SACE,4CAAC,wCAAc,UACb,uDAAC,SAAI,WAAU,+CAA8C,OAAO,EAAE,UAAU,SAAS,WAAW,SAAS,SAAS,WAAW,SAAS,EAAE,GAC1I;AAAA,gDAAC,8BAAW,MAAK,MAAM,gBAAM,OAAM;AAAA,IAClC,MAAM;AAAA,KACL,MAAM,qBAAqB,MAAM,wBACjC,6CAAC,SAAI,WAAU,kCACZ;AAAA,YAAM,uBACL,4CAAC,0BAAO,SAAQ,aAAY,SAAS,MAAM,iBACxC,gBAAM,qBACT;AAAA,MAED,MAAM,qBACL,4CAAC,0BAAO,SAAS,MAAM,eACpB,gBAAM,mBACT;AAAA,OAEJ;AAAA,KAEJ,GACF;AAEJ;", "names": []}