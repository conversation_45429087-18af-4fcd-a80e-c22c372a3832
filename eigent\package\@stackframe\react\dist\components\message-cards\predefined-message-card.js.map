{"version": 3, "sources": ["../../../src/components/message-cards/predefined-message-card.tsx"], "sourcesContent": ["\"use client\";\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { Typography } from \"@stackframe/stack-ui\";\nimport { useStackApp } from \"../..\";\nimport { useTranslation } from \"../../lib/translations\";\nimport { MessageCard } from \"./message-card\";\n\nexport function PredefinedMessageCard({\n  type,\n  fullPage=false,\n}: {\n  type: 'signedIn' | 'signedOut' | 'emailSent' | 'passwordReset' | 'unknownError' | 'signUpDisabled',\n  fullPage?: boolean,\n}) {\n  const stackApp = useStackApp();\n  const { t } = useTranslation();\n\n  let title: string;\n  let message: string | null = null;\n  let primaryButton: string | null = null;\n  let secondaryButton: string | null = null;\n  let primaryAction: (() => Promise<void> | void) | null = null;\n  let secondaryAction: (() => Promise<void> | void) | null = null;\n\n  switch (type) {\n    case 'signedIn': {\n      title = t(\"You are already signed in\");\n      primaryAction = () => stackApp.redirectToHome();\n      secondaryAction = () => stackApp.redirectToSignOut();\n      primaryButton = t(\"Go home\");\n      secondaryButton = t(\"Sign out\");\n      break;\n    }\n    case 'signedOut': {\n      title = t(\"You are not currently signed in.\");\n      primaryAction = () => stackApp.redirectToSignIn();\n      primaryButton = t(\"Sign in\");\n      break;\n    }\n    case 'signUpDisabled': {\n      title = t(\"Sign up for new users is not enabled at the moment.\");\n      primaryAction = () => stackApp.redirectToHome();\n      secondaryAction = () => stackApp.redirectToSignIn();\n      primaryButton = t(\"Go home\");\n      secondaryButton = t(\"Sign in\");\n      break;\n    }\n    case 'emailSent': {\n      title = t(\"Email sent!\");\n      message = t(\"If the user with this e-mail address exists, an e-mail was sent to your inbox. Make sure to check your spam folder.\");\n      primaryAction = () => stackApp.redirectToHome();\n      primaryButton = t(\"Go home\");\n      break;\n    }\n    case 'passwordReset': {\n      title = t(\"Password reset successfully!\");\n      message = t(\"Your password has been reset. You can now sign in with your new password.\");\n      primaryAction = () => stackApp.redirectToSignIn({ noRedirectBack: true });\n      primaryButton = t(\"Sign in\");\n      break;\n    }\n    case 'unknownError': {\n      title = t(\"An unknown error occurred\");\n      message = t(\"Please try again and if the problem persists, contact support.\");\n      primaryAction = () => stackApp.redirectToHome();\n      primaryButton = t(\"Go home\");\n      break;\n    }\n  }\n\n  return (\n    <MessageCard\n      title={title}\n      fullPage={fullPage}\n      primaryButtonText={primaryButton}\n      primaryAction={primaryAction}\n      secondaryButtonText={secondaryButton || undefined}\n      secondaryAction={secondaryAction || undefined}\n    >\n      {message && <Typography>{message}</Typography>}\n    </MessageCard>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,sBAA2B;AAC3B,eAA4B;AAC5B,0BAA+B;AAC/B,0BAA4B;AA0EV;AAxEX,SAAS,sBAAsB;AAAA,EACpC;AAAA,EACA,WAAS;AACX,GAGG;AACD,QAAM,eAAW,sBAAY;AAC7B,QAAM,EAAE,EAAE,QAAI,oCAAe;AAE7B,MAAI;AACJ,MAAI,UAAyB;AAC7B,MAAI,gBAA+B;AACnC,MAAI,kBAAiC;AACrC,MAAI,gBAAqD;AACzD,MAAI,kBAAuD;AAE3D,UAAQ,MAAM;AAAA,IACZ,KAAK,YAAY;AACf,cAAQ,EAAE,2BAA2B;AACrC,sBAAgB,MAAM,SAAS,eAAe;AAC9C,wBAAkB,MAAM,SAAS,kBAAkB;AACnD,sBAAgB,EAAE,SAAS;AAC3B,wBAAkB,EAAE,UAAU;AAC9B;AAAA,IACF;AAAA,IACA,KAAK,aAAa;AAChB,cAAQ,EAAE,kCAAkC;AAC5C,sBAAgB,MAAM,SAAS,iBAAiB;AAChD,sBAAgB,EAAE,SAAS;AAC3B;AAAA,IACF;AAAA,IACA,KAAK,kBAAkB;AACrB,cAAQ,EAAE,qDAAqD;AAC/D,sBAAgB,MAAM,SAAS,eAAe;AAC9C,wBAAkB,MAAM,SAAS,iBAAiB;AAClD,sBAAgB,EAAE,SAAS;AAC3B,wBAAkB,EAAE,SAAS;AAC7B;AAAA,IACF;AAAA,IACA,KAAK,aAAa;AAChB,cAAQ,EAAE,aAAa;AACvB,gBAAU,EAAE,qHAAqH;AACjI,sBAAgB,MAAM,SAAS,eAAe;AAC9C,sBAAgB,EAAE,SAAS;AAC3B;AAAA,IACF;AAAA,IACA,KAAK,iBAAiB;AACpB,cAAQ,EAAE,8BAA8B;AACxC,gBAAU,EAAE,2EAA2E;AACvF,sBAAgB,MAAM,SAAS,iBAAiB,EAAE,gBAAgB,KAAK,CAAC;AACxE,sBAAgB,EAAE,SAAS;AAC3B;AAAA,IACF;AAAA,IACA,KAAK,gBAAgB;AACnB,cAAQ,EAAE,2BAA2B;AACrC,gBAAU,EAAE,gEAAgE;AAC5E,sBAAgB,MAAM,SAAS,eAAe;AAC9C,sBAAgB,EAAE,SAAS;AAC3B;AAAA,IACF;AAAA,EACF;AAEA,SACE;AAAA,IAAC;AAAA;AAAA,MACC;AAAA,MACA;AAAA,MACA,mBAAmB;AAAA,MACnB;AAAA,MACA,qBAAqB,mBAAmB;AAAA,MACxC,iBAAiB,mBAAmB;AAAA,MAEnC,qBAAW,4CAAC,8BAAY,mBAAQ;AAAA;AAAA,EACnC;AAEJ;", "names": []}