{"version": 3, "sources": ["../../../../../src/components-page/account-settings/email-and-auth/email-and-auth-page.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { PageLayout } from \"../page-layout\";\nimport { EmailsSection } from \"./emails-section\";\nimport { MfaSection } from \"./mfa-section\";\nimport { OtpSection } from \"./otp-section\";\nimport { PasskeySection } from \"./passkey-section\";\nimport { PasswordSection } from \"./password-section\";\n\nexport function EmailsAndAuthPage() {\n  return (\n    <PageLayout>\n      <EmailsSection/>\n      <PasswordSection />\n      <PasskeySection />\n      <OtpSection />\n      <MfaSection />\n    </PageLayout>\n  );\n}\n"], "mappings": ";AAIA,SAAS,kBAAkB;AAC3B,SAAS,qBAAqB;AAC9B,SAAS,kBAAkB;AAC3B,SAAS,kBAAkB;AAC3B,SAAS,sBAAsB;AAC/B,SAAS,uBAAuB;AAI5B,SACE,KADF;AAFG,SAAS,oBAAoB;AAClC,SACE,qBAAC,cACC;AAAA,wBAAC,iBAAa;AAAA,IACd,oBAAC,mBAAgB;AAAA,IACjB,oBAAC,kBAAe;AAAA,IAChB,oBAAC,cAAW;AAAA,IACZ,oBAAC,cAAW;AAAA,KACd;AAEJ;", "names": []}