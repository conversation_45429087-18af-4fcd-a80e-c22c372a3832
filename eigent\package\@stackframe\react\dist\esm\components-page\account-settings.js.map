{"version": 3, "sources": ["../../../src/components-page/account-settings.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { Skeleton, Typography } from '@stackframe/stack-ui';\nimport { icons } from 'lucide-react';\nimport React, { Suspense } from \"react\";\nimport { useStackApp, useUser } from '..';\nimport { MaybeFullPage } from \"../components/elements/maybe-full-page\";\nimport { SidebarLayout } from '../components/elements/sidebar-layout';\nimport { TeamIcon } from '../components/team-icon';\nimport { useTranslation } from \"../lib/translations\";\nimport { ActiveSessionsPage } from \"./account-settings/active-sessions/active-sessions-page\";\nimport { ApiKeysPage } from \"./account-settings/api-keys/api-keys-page\";\nimport { EmailsAndAuthPage } from './account-settings/email-and-auth/email-and-auth-page';\nimport { ProfilePage } from \"./account-settings/profile-page/profile-page\";\nimport { SettingsPage } from './account-settings/settings/settings-page';\nimport { TeamCreationPage } from './account-settings/teams/team-creation-page';\nimport { TeamPage } from './account-settings/teams/team-page';\n\nconst Icon = ({ name }: { name: keyof typeof icons }) => {\n  const LucideIcon = icons[name];\n  return <LucideIcon className=\"mr-2 h-4 w-4\"/>;\n};\n\nexport function AccountSettings(props: {\n  fullPage?: boolean,\n  extraItems?: ({\n    title: string,\n    content: React.ReactNode,\n    id: string,\n  } & ({\n    icon?: React.ReactNode,\n  } | {\n    iconName?: keyof typeof icons,\n  }))[],\n}) {\n  const { t } = useTranslation();\n  const user = useUser({ or: 'redirect' });\n  const teams = user.useTeams();\n  const stackApp = useStackApp();\n  const project = stackApp.useProject();\n\n  return (\n    <MaybeFullPage fullPage={!!props.fullPage}>\n      <div className=\"self-stretch flex-grow w-full\">\n        <SidebarLayout\n          items={([\n            {\n              title: t('My Profile'),\n              type: 'item',\n              id: 'profile',\n              icon: <Icon name=\"Contact\"/>,\n              content: <ProfilePage/>,\n            },\n            {\n              title: t('Emails & Auth'),\n              type: 'item',\n              id: 'auth',\n              icon: <Icon name=\"ShieldCheck\"/>,\n              content: <Suspense fallback={<EmailsAndAuthPageSkeleton/>}>\n                <EmailsAndAuthPage/>\n              </Suspense>,\n            },\n            {\n              title: t('Active Sessions'),\n              type: 'item',\n              id: 'sessions',\n              icon: <Icon name=\"Monitor\"/>,\n              content: <Suspense fallback={<ActiveSessionsPageSkeleton/>}>\n                <ActiveSessionsPage/>\n              </Suspense>,\n            },\n            ...(project.config.allowUserApiKeys ? [{\n              title: t('API Keys'),\n              type: 'item',\n              id: 'api-keys',\n              icon: <Icon name=\"Key\" />,\n              content: <Suspense fallback={<ApiKeysPageSkeleton/>}>\n                <ApiKeysPage />\n              </Suspense>,\n            }] as const : []),\n            {\n              title: t('Settings'),\n              type: 'item',\n              id: 'settings',\n              icon: <Icon name=\"Settings\"/>,\n              content: <SettingsPage/>,\n            },\n            ...(props.extraItems?.map(item => ({\n              title: item.title,\n              type: 'item',\n              id: item.id,\n              icon: (() => {\n                const iconName = (item as any).iconName as keyof typeof icons | undefined;\n                if (iconName) {\n                  return <Icon name={iconName}/>;\n                } else if ((item as any).icon) {\n                  return (item as any).icon;\n                }\n                return null;\n              })(),\n              content: item.content,\n            } as const)) || []),\n            ...(teams.length > 0 || project.config.clientTeamCreationEnabled) ? [{\n              title: t('Teams'),\n              type: 'divider',\n            }] as const : [],\n            ...teams.map(team => ({\n              title: <div className='flex gap-2 items-center w-full'>\n                <TeamIcon team={team}/>\n                <Typography className=\"max-w-[320px] md:w-[90%] truncate\">{team.displayName}</Typography>\n              </div>,\n              type: 'item',\n              id: `team-${team.id}`,\n              content: <Suspense fallback={<TeamPageSkeleton/>}>\n                <TeamPage team={team}/>\n              </Suspense>,\n            } as const)),\n            ...project.config.clientTeamCreationEnabled ? [{\n              title: t('Create a team'),\n              icon: <Icon name=\"CirclePlus\"/>,\n              type: 'item',\n              id: 'team-creation',\n              content: <Suspense fallback={<TeamCreationSkeleton/>}>\n                <TeamCreationPage />\n              </Suspense>,\n            }] as const : [],\n          ] as const).filter((p) => p.type === 'divider' || (p as any).content )}\n          title={t(\"Account Settings\")}\n        />\n      </div>\n    </MaybeFullPage>\n  );\n}\n\nfunction PageLayout(props: { children: React.ReactNode }) {\n  return (\n    <div className='flex flex-col gap-6'>\n      {props.children}\n    </div>\n  );\n}\n\nfunction EmailsAndAuthPageSkeleton() {\n  return <PageLayout>\n    <Skeleton className=\"h-9 w-full mt-1\"/>\n    <Skeleton className=\"h-9 w-full mt-1\"/>\n    <Skeleton className=\"h-9 w-full mt-1\"/>\n    <Skeleton className=\"h-9 w-full mt-1\"/>\n  </PageLayout>;\n}\n\nfunction ActiveSessionsPageSkeleton() {\n  return <PageLayout>\n    <Skeleton className=\"h-6 w-48 mb-2\"/>\n    <Skeleton className=\"h-4 w-full mb-4\"/>\n    <Skeleton className=\"h-[200px] w-full mt-1 rounded-md\"/>\n  </PageLayout>;\n}\n\nfunction ApiKeysPageSkeleton() {\n  return <PageLayout>\n    <Skeleton className=\"h-9 w-full mt-1\"/>\n    <Skeleton className=\"h-[200px] w-full mt-1 rounded-md\"/>\n  </PageLayout>;\n}\n\nfunction TeamPageSkeleton() {\n  return <PageLayout>\n    <Skeleton className=\"h-9 w-full mt-1\"/>\n    <Skeleton className=\"h-9 w-full mt-1\"/>\n    <Skeleton className=\"h-9 w-full mt-1\"/>\n    <Skeleton className=\"h-[200px] w-full mt-1 rounded-md\"/>\n  </PageLayout>;\n}\n\nfunction TeamCreationSkeleton() {\n  return <PageLayout>\n    <Skeleton className=\"h-9 w-full mt-1\"/>\n    <Skeleton className=\"h-9 w-full mt-1\"/>\n  </PageLayout>;\n}\n"], "mappings": ";;;AAOA,SAAS,UAAU,kBAAkB;AACrC,SAAS,aAAa;AACtB,SAAgB,gBAAgB;AAChC,SAAS,aAAa,eAAe;AACrC,SAAS,qBAAqB;AAC9B,SAAS,qBAAqB;AAC9B,SAAS,gBAAgB;AACzB,SAAS,sBAAsB;AAC/B,SAAS,0BAA0B;AACnC,SAAS,mBAAmB;AAC5B,SAAS,yBAAyB;AAClC,SAAS,mBAAmB;AAC5B,SAAS,oBAAoB;AAC7B,SAAS,wBAAwB;AACjC,SAAS,gBAAgB;AAIhB,cAuFY,YAvFZ;AAFT,IAAM,OAAO,CAAC,EAAE,KAAK,MAAoC;AACvD,QAAM,aAAa,MAAM,IAAI;AAC7B,SAAO,oBAAC,cAAW,WAAU,gBAAc;AAC7C;AAEO,SAAS,gBAAgB,OAW7B;AACD,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,OAAO,QAAQ,EAAE,IAAI,WAAW,CAAC;AACvC,QAAM,QAAQ,KAAK,SAAS;AAC5B,QAAM,WAAW,YAAY;AAC7B,QAAM,UAAU,SAAS,WAAW;AAEpC,SACE,oBAAC,iBAAc,UAAU,CAAC,CAAC,MAAM,UAC/B,8BAAC,SAAI,WAAU,iCACb;AAAA,IAAC;AAAA;AAAA,MACC,OAAQ;AAAA,QACN;AAAA,UACE,OAAO,EAAE,YAAY;AAAA,UACrB,MAAM;AAAA,UACN,IAAI;AAAA,UACJ,MAAM,oBAAC,QAAK,MAAK,WAAS;AAAA,UAC1B,SAAS,oBAAC,eAAW;AAAA,QACvB;AAAA,QACA;AAAA,UACE,OAAO,EAAE,eAAe;AAAA,UACxB,MAAM;AAAA,UACN,IAAI;AAAA,UACJ,MAAM,oBAAC,QAAK,MAAK,eAAa;AAAA,UAC9B,SAAS,oBAAC,YAAS,UAAU,oBAAC,6BAAyB,GACrD,8BAAC,qBAAiB,GACpB;AAAA,QACF;AAAA,QACA;AAAA,UACE,OAAO,EAAE,iBAAiB;AAAA,UAC1B,MAAM;AAAA,UACN,IAAI;AAAA,UACJ,MAAM,oBAAC,QAAK,MAAK,WAAS;AAAA,UAC1B,SAAS,oBAAC,YAAS,UAAU,oBAAC,8BAA0B,GACtD,8BAAC,sBAAkB,GACrB;AAAA,QACF;AAAA,QACA,GAAI,QAAQ,OAAO,mBAAmB,CAAC;AAAA,UACrC,OAAO,EAAE,UAAU;AAAA,UACnB,MAAM;AAAA,UACN,IAAI;AAAA,UACJ,MAAM,oBAAC,QAAK,MAAK,OAAM;AAAA,UACvB,SAAS,oBAAC,YAAS,UAAU,oBAAC,uBAAmB,GAC/C,8BAAC,eAAY,GACf;AAAA,QACF,CAAC,IAAa,CAAC;AAAA,QACf;AAAA,UACE,OAAO,EAAE,UAAU;AAAA,UACnB,MAAM;AAAA,UACN,IAAI;AAAA,UACJ,MAAM,oBAAC,QAAK,MAAK,YAAU;AAAA,UAC3B,SAAS,oBAAC,gBAAY;AAAA,QACxB;AAAA,QACA,GAAI,MAAM,YAAY,IAAI,WAAS;AAAA,UACjC,OAAO,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,IAAI,KAAK;AAAA,UACT,OAAO,MAAM;AACX,kBAAM,WAAY,KAAa;AAC/B,gBAAI,UAAU;AACZ,qBAAO,oBAAC,QAAK,MAAM,UAAS;AAAA,YAC9B,WAAY,KAAa,MAAM;AAC7B,qBAAQ,KAAa;AAAA,YACvB;AACA,mBAAO;AAAA,UACT,GAAG;AAAA,UACH,SAAS,KAAK;AAAA,QAChB,EAAW,KAAK,CAAC;AAAA,QACjB,GAAI,MAAM,SAAS,KAAK,QAAQ,OAAO,4BAA6B,CAAC;AAAA,UACnE,OAAO,EAAE,OAAO;AAAA,UAChB,MAAM;AAAA,QACR,CAAC,IAAa,CAAC;AAAA,QACf,GAAG,MAAM,IAAI,WAAS;AAAA,UACpB,OAAO,qBAAC,SAAI,WAAU,kCACpB;AAAA,gCAAC,YAAS,MAAW;AAAA,YACrB,oBAAC,cAAW,WAAU,qCAAqC,eAAK,aAAY;AAAA,aAC9E;AAAA,UACA,MAAM;AAAA,UACN,IAAI,QAAQ,KAAK,EAAE;AAAA,UACnB,SAAS,oBAAC,YAAS,UAAU,oBAAC,oBAAgB,GAC5C,8BAAC,YAAS,MAAW,GACvB;AAAA,QACF,EAAW;AAAA,QACX,GAAG,QAAQ,OAAO,4BAA4B,CAAC;AAAA,UAC7C,OAAO,EAAE,eAAe;AAAA,UACxB,MAAM,oBAAC,QAAK,MAAK,cAAY;AAAA,UAC7B,MAAM;AAAA,UACN,IAAI;AAAA,UACJ,SAAS,oBAAC,YAAS,UAAU,oBAAC,wBAAoB,GAChD,8BAAC,oBAAiB,GACpB;AAAA,QACF,CAAC,IAAa,CAAC;AAAA,MACjB,EAAY,OAAO,CAAC,MAAM,EAAE,SAAS,aAAc,EAAU,OAAQ;AAAA,MACrE,OAAO,EAAE,kBAAkB;AAAA;AAAA,EAC7B,GACF,GACF;AAEJ;AAEA,SAAS,WAAW,OAAsC;AACxD,SACE,oBAAC,SAAI,WAAU,uBACZ,gBAAM,UACT;AAEJ;AAEA,SAAS,4BAA4B;AACnC,SAAO,qBAAC,cACN;AAAA,wBAAC,YAAS,WAAU,mBAAiB;AAAA,IACrC,oBAAC,YAAS,WAAU,mBAAiB;AAAA,IACrC,oBAAC,YAAS,WAAU,mBAAiB;AAAA,IACrC,oBAAC,YAAS,WAAU,mBAAiB;AAAA,KACvC;AACF;AAEA,SAAS,6BAA6B;AACpC,SAAO,qBAAC,cACN;AAAA,wBAAC,YAAS,WAAU,iBAAe;AAAA,IACnC,oBAAC,YAAS,WAAU,mBAAiB;AAAA,IACrC,oBAAC,YAAS,WAAU,oCAAkC;AAAA,KACxD;AACF;AAEA,SAAS,sBAAsB;AAC7B,SAAO,qBAAC,cACN;AAAA,wBAAC,YAAS,WAAU,mBAAiB;AAAA,IACrC,oBAAC,YAAS,WAAU,oCAAkC;AAAA,KACxD;AACF;AAEA,SAAS,mBAAmB;AAC1B,SAAO,qBAAC,cACN;AAAA,wBAAC,YAAS,WAAU,mBAAiB;AAAA,IACrC,oBAAC,YAAS,WAAU,mBAAiB;AAAA,IACrC,oBAAC,YAAS,WAAU,mBAAiB;AAAA,IACrC,oBAAC,YAAS,WAAU,oCAAkC;AAAA,KACxD;AACF;AAEA,SAAS,uBAAuB;AAC9B,SAAO,qBAAC,cACN;AAAA,wBAAC,YAAS,WAAU,mBAAiB;AAAA,IACrC,oBAAC,YAAS,WAAU,mBAAiB;AAAA,KACvC;AACF;", "names": []}