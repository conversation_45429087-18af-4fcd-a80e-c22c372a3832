{"version": 3, "sources": ["../../../src/providers/stack-provider.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport React, { Suspense } from 'react';\nimport { StackAdminApp, StackClientApp, StackServerApp, stackAppInternalsSymbol } from '../lib/stack-app';\nimport { StackProviderClient, UserSetter } from './stack-provider-client';\nimport { TranslationProvider } from './translation-provider';\n\nfunction UserFetcher(props: { app: StackClientApp<true> }) {\n  const userPromise = props.app.getUser({ or: \"anonymous-if-exists\" }).then((user) => user?.toClientJson() ?? null);\n  return <UserSetter userJsonPromise={userPromise} />;\n}\n\nfunction ReactStackProvider({\n  children,\n  app,\n  lang,\n  translationOverrides,\n}: {\n  lang?: React.ComponentProps<typeof TranslationProvider>['lang'],\n  /**\n   * A mapping of English translations to translated equivalents.\n   *\n   * These will take priority over the translations from the language specified in the `lang` property. Note that the\n   * keys are case-sensitive.\n   */\n  translationOverrides?: Record<string, string>,\n  children: React.ReactNode,\n  // list all three types of apps even though server and admin are subclasses of client so it's clear that you can pass any\n  app: StackClientApp<true>,\n}) {\n  return (\n    <StackProviderClient app={app as any} serialized={false}>\n      <Suspense fallback={null}>\n        <UserFetcher app={app} />\n      </Suspense>\n      <TranslationProvider lang={lang} translationOverrides={translationOverrides}>\n        {children}\n      </TranslationProvider>\n    </StackProviderClient>\n  );\n}\n\nexport default ReactStackProvider;\n"], "mappings": ";AAIA,SAAgB,gBAAgB;AAEhC,SAAS,qBAAqB,kBAAkB;AAChD,SAAS,2BAA2B;AAI3B,cAsBL,YAtBK;AAFT,SAAS,YAAY,OAAsC;AACzD,QAAM,cAAc,MAAM,IAAI,QAAQ,EAAE,IAAI,sBAAsB,CAAC,EAAE,KAAK,CAAC,SAAS,MAAM,aAAa,KAAK,IAAI;AAChH,SAAO,oBAAC,cAAW,iBAAiB,aAAa;AACnD;AAEA,SAAS,mBAAmB;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAYG;AACD,SACE,qBAAC,uBAAoB,KAAiB,YAAY,OAChD;AAAA,wBAAC,YAAS,UAAU,MAClB,8BAAC,eAAY,KAAU,GACzB;AAAA,IACA,oBAAC,uBAAoB,MAAY,sBAC9B,UACH;AAAA,KACF;AAEJ;AAEA,IAAO,yBAAQ;", "names": []}