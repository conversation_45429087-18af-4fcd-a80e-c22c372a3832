{"version": 3, "sources": ["../../src/utils/crypto.tsx"], "sourcesContent": ["import { encodeBase32 } from \"./bytes\";\nimport { StackAssertionError } from \"./errors\";\nimport { globalVar } from \"./globals\";\n\nexport function generateRandomValues(array: Uint8Array): typeof array {\n  if (!globalVar.crypto) {\n    throw new StackAssertionError(\"Crypto API is not available in this environment. Are you using an old browser?\");\n  }\n  if (!globalVar.crypto.getRandomValues) {\n    throw new StackAssertionError(\"crypto.getRandomValues is not available in this environment. Are you using an old browser?\");\n  }\n  return globalVar.crypto.getRandomValues(array);\n}\n\n/**\n * Generates a secure alphanumeric string using the system's cryptographically secure\n * random number generator.\n */\nexport function generateSecureRandomString(minBitsOfEntropy: number = 224) {\n  const base32CharactersCount = Math.ceil(minBitsOfEntropy / 5);\n  const bytesCount = Math.ceil(base32CharactersCount * 5 / 8);\n  const randomBytes = generateRandomValues(new Uint8Array(bytesCount));\n  const str = encodeBase32(randomBytes);\n  return str.slice(str.length - base32CharactersCount).toLowerCase();\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAA6B;AAC7B,oBAAoC;AACpC,qBAA0B;AAEnB,SAAS,qBAAqB,OAAiC;AACpE,MAAI,CAAC,yBAAU,QAAQ;AACrB,UAAM,IAAI,kCAAoB,gFAAgF;AAAA,EAChH;AACA,MAAI,CAAC,yBAAU,OAAO,iBAAiB;AACrC,UAAM,IAAI,kCAAoB,4FAA4F;AAAA,EAC5H;AACA,SAAO,yBAAU,OAAO,gBAAgB,KAAK;AAC/C;AAMO,SAAS,2BAA2B,mBAA2B,KAAK;AACzE,QAAM,wBAAwB,KAAK,KAAK,mBAAmB,CAAC;AAC5D,QAAM,aAAa,KAAK,KAAK,wBAAwB,IAAI,CAAC;AAC1D,QAAM,cAAc,qBAAqB,IAAI,WAAW,UAAU,CAAC;AACnE,QAAM,UAAM,2BAAa,WAAW;AACpC,SAAO,IAAI,MAAM,IAAI,SAAS,qBAAqB,EAAE,YAAY;AACnE;", "names": []}