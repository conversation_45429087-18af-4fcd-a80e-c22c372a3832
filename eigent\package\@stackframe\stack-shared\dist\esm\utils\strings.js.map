{"version": 3, "sources": ["../../../src/utils/strings.tsx"], "sourcesContent": ["import { findLastIndex, unique } from \"./arrays\";\nimport { StackAssertionError } from \"./errors\";\nimport { filterUndefined } from \"./objects\";\n\nexport function typedToLowercase<S extends string>(s: S): Lowercase<S> {\n  if (typeof s !== \"string\") throw new StackAssertionError(\"Expected a string for typedToLowercase\", { s });\n  return s.toLowerCase() as Lowercase<S>;\n}\nundefined?.test(\"typedToLowercase\", ({ expect }) => {\n  expect(typedToLowercase(\"\")).toBe(\"\");\n  expect(typedToLowercase(\"HELLO\")).toBe(\"hello\");\n  expect(typedToLowercase(\"Hello World\")).toBe(\"hello world\");\n  expect(typedToLowercase(\"hello\")).toBe(\"hello\");\n  expect(typedToLowercase(\"123\")).toBe(\"123\");\n  expect(typedToLowercase(\"MIXED123case\")).toBe(\"mixed123case\");\n  expect(typedToLowercase(\"Special@Chars!\")).toBe(\"special@chars!\");\n  expect(() => typedToLowercase(123 as any)).toThrow(\"Expected a string for typedToLowercase\");\n});\n\nexport function typedToUppercase<S extends string>(s: S): Uppercase<S> {\n  if (typeof s !== \"string\") throw new StackAssertionError(\"Expected a string for typedToUppercase\", { s });\n  return s.toUpperCase() as Uppercase<S>;\n}\nundefined?.test(\"typedToUppercase\", ({ expect }) => {\n  expect(typedToUppercase(\"\")).toBe(\"\");\n  expect(typedToUppercase(\"hello\")).toBe(\"HELLO\");\n  expect(typedToUppercase(\"Hello World\")).toBe(\"HELLO WORLD\");\n  expect(typedToUppercase(\"HELLO\")).toBe(\"HELLO\");\n  expect(typedToUppercase(\"123\")).toBe(\"123\");\n  expect(typedToUppercase(\"mixed123Case\")).toBe(\"MIXED123CASE\");\n  expect(typedToUppercase(\"special@chars!\")).toBe(\"SPECIAL@CHARS!\");\n  expect(() => typedToUppercase(123 as any)).toThrow(\"Expected a string for typedToUppercase\");\n});\n\nexport function typedCapitalize<S extends string>(s: S): Capitalize<S> {\n  return s.charAt(0).toUpperCase() + s.slice(1) as Capitalize<S>;\n}\nundefined?.test(\"typedCapitalize\", ({ expect }) => {\n  expect(typedCapitalize(\"\")).toBe(\"\");\n  expect(typedCapitalize(\"hello\")).toBe(\"Hello\");\n  expect(typedCapitalize(\"hello world\")).toBe(\"Hello world\");\n  expect(typedCapitalize(\"HELLO\")).toBe(\"HELLO\");\n  expect(typedCapitalize(\"123test\")).toBe(\"123test\");\n  expect(typedCapitalize(\"already Capitalized\")).toBe(\"Already Capitalized\");\n  expect(typedCapitalize(\"h\")).toBe(\"H\");\n});\n\n/**\n * Compares two strings in a way that is not dependent on the current locale.\n */\nexport function stringCompare(a: string, b: string): number {\n  if (typeof a !== \"string\" || typeof b !== \"string\") throw new StackAssertionError(`Expected two strings for stringCompare, found ${typeof a} and ${typeof b}`, { a, b });\n  const cmp = (a: string, b: string) => a < b ? -1 : a > b ? 1 : 0;\n  return cmp(a.toUpperCase(), b.toUpperCase()) || cmp(b, a);\n}\nundefined?.test(\"stringCompare\", ({ expect }) => {\n  // Equal strings\n  expect(stringCompare(\"a\", \"a\")).toBe(0);\n  expect(stringCompare(\"\", \"\")).toBe(0);\n\n  // Case comparison - note that this function is NOT case-insensitive\n  // It compares uppercase versions first, then original strings\n  expect(stringCompare(\"a\", \"A\")).toBe(-1); // lowercase comes after uppercase\n  expect(stringCompare(\"A\", \"a\")).toBe(1);  // uppercase comes before lowercase\n  expect(stringCompare(\"abc\", \"ABC\")).toBe(-1);\n  expect(stringCompare(\"ABC\", \"abc\")).toBe(1);\n\n  // Different strings\n  expect(stringCompare(\"a\", \"b\")).toBe(-1);\n  expect(stringCompare(\"b\", \"a\")).toBe(1);\n\n  // Strings with different lengths\n  expect(stringCompare(\"abc\", \"abcd\")).toBe(-1);\n  expect(stringCompare(\"abcd\", \"abc\")).toBe(1);\n\n  // Strings with numbers\n  expect(stringCompare(\"a1\", \"a2\")).toBe(-1);\n  expect(stringCompare(\"a10\", \"a2\")).toBe(-1);\n\n  // Strings with special characters\n  expect(stringCompare(\"a\", \"a!\")).toBe(-1);\n  expect(stringCompare(\"a!\", \"a\")).toBe(1);\n});\n\n/**\n * Returns all whitespace character at the start of the string.\n *\n * Uses the same definition for whitespace as `String.prototype.trim()`.\n */\nexport function getWhitespacePrefix(s: string): string {\n  return s.substring(0, s.length - s.trimStart().length);\n}\nundefined?.test(\"getWhitespacePrefix\", ({ expect }) => {\n  expect(getWhitespacePrefix(\"\")).toBe(\"\");\n  expect(getWhitespacePrefix(\"hello\")).toBe(\"\");\n  expect(getWhitespacePrefix(\" hello\")).toBe(\" \");\n  expect(getWhitespacePrefix(\"  hello\")).toBe(\"  \");\n  expect(getWhitespacePrefix(\"\\thello\")).toBe(\"\\t\");\n  expect(getWhitespacePrefix(\"\\n hello\")).toBe(\"\\n \");\n  expect(getWhitespacePrefix(\"   \")).toBe(\"   \");\n  expect(getWhitespacePrefix(\" \\t\\n\\r\")).toBe(\" \\t\\n\\r\");\n});\n\n/**\n * Returns all whitespace character at the end of the string.\n *\n * Uses the same definition for whitespace as `String.prototype.trim()`.\n */\nexport function getWhitespaceSuffix(s: string): string {\n  return s.substring(s.trimEnd().length);\n}\nundefined?.test(\"getWhitespaceSuffix\", ({ expect }) => {\n  expect(getWhitespaceSuffix(\"\")).toBe(\"\");\n  expect(getWhitespaceSuffix(\"hello\")).toBe(\"\");\n  expect(getWhitespaceSuffix(\"hello \")).toBe(\" \");\n  expect(getWhitespaceSuffix(\"hello  \")).toBe(\"  \");\n  expect(getWhitespaceSuffix(\"hello\\t\")).toBe(\"\\t\");\n  expect(getWhitespaceSuffix(\"hello \\n\")).toBe(\" \\n\");\n  expect(getWhitespaceSuffix(\"   \")).toBe(\"   \");\n  expect(getWhitespaceSuffix(\" \\t\\n\\r\")).toBe(\" \\t\\n\\r\");\n});\n\n/**\n * Returns a string with all empty or whitespace-only lines at the start removed.\n *\n * Uses the same definition for whitespace as `String.prototype.trim()`.\n */\nexport function trimEmptyLinesStart(s: string): string {\n  const lines = s.split(\"\\n\");\n  const firstNonEmptyLineIndex = lines.findIndex((line) => line.trim() !== \"\");\n  // If all lines are empty or whitespace-only, return an empty string\n  if (firstNonEmptyLineIndex === -1) return \"\";\n  return lines.slice(firstNonEmptyLineIndex).join(\"\\n\");\n}\nundefined?.test(\"trimEmptyLinesStart\", ({ expect }) => {\n  expect(trimEmptyLinesStart(\"\")).toBe(\"\");\n  expect(trimEmptyLinesStart(\"hello\")).toBe(\"hello\");\n  expect(trimEmptyLinesStart(\"\\nhello\")).toBe(\"hello\");\n  expect(trimEmptyLinesStart(\"\\n\\nhello\")).toBe(\"hello\");\n  expect(trimEmptyLinesStart(\"  \\n\\t\\nhello\")).toBe(\"hello\");\n  expect(trimEmptyLinesStart(\"\\n\\nhello\\nworld\")).toBe(\"hello\\nworld\");\n  expect(trimEmptyLinesStart(\"hello\\n\\nworld\")).toBe(\"hello\\n\\nworld\");\n  expect(trimEmptyLinesStart(\"hello\\nworld\\n\")).toBe(\"hello\\nworld\\n\");\n  expect(trimEmptyLinesStart(\"\\n  \\n\\nhello\\n  \\nworld\")).toBe(\"hello\\n  \\nworld\");\n  // Edge case: all lines are empty\n  expect(trimEmptyLinesStart(\"\\n\\n  \\n\\t\")).toBe(\"\");\n});\n\n/**\n * Returns a string with all empty or whitespace-only lines at the end removed.\n *\n * Uses the same definition for whitespace as `String.prototype.trim()`.\n */\nexport function trimEmptyLinesEnd(s: string): string {\n  const lines = s.split(\"\\n\");\n  const lastNonEmptyLineIndex = findLastIndex(lines, (line) => line.trim() !== \"\");\n  return lines.slice(0, lastNonEmptyLineIndex + 1).join(\"\\n\");\n}\nundefined?.test(\"trimEmptyLinesEnd\", ({ expect }) => {\n  expect(trimEmptyLinesEnd(\"\")).toBe(\"\");\n  expect(trimEmptyLinesEnd(\"hello\")).toBe(\"hello\");\n  expect(trimEmptyLinesEnd(\"hello\\n\")).toBe(\"hello\");\n  expect(trimEmptyLinesEnd(\"hello\\n\\n\")).toBe(\"hello\");\n  expect(trimEmptyLinesEnd(\"hello\\n  \\n\\t\")).toBe(\"hello\");\n  expect(trimEmptyLinesEnd(\"hello\\nworld\\n\\n\")).toBe(\"hello\\nworld\");\n  expect(trimEmptyLinesEnd(\"hello\\n\\nworld\")).toBe(\"hello\\n\\nworld\");\n  expect(trimEmptyLinesEnd(\"\\nhello\\nworld\")).toBe(\"\\nhello\\nworld\");\n  expect(trimEmptyLinesEnd(\"hello\\n  \\nworld\\n\\n  \")).toBe(\"hello\\n  \\nworld\");\n  // Edge case: all lines are empty\n  expect(trimEmptyLinesEnd(\"\\n\\n  \\n\\t\")).toBe(\"\");\n});\n\n/**\n * Returns a string with all empty or whitespace-only lines trimmed at the start and end.\n *\n * Uses the same definition for whitespace as `String.prototype.trim()`.\n */\nexport function trimLines(s: string): string {\n  return trimEmptyLinesEnd(trimEmptyLinesStart(s));\n}\nundefined?.test(\"trimLines\", ({ expect }) => {\n  expect(trimLines(\"\")).toBe(\"\");\n  expect(trimLines(\" \")).toBe(\"\");\n  expect(trimLines(\" \\n \")).toBe(\"\");\n  expect(trimLines(\" abc \")).toBe(\" abc \");\n  expect(trimLines(\"\\n  \\nLine1\\nLine2\\n \\n\")).toBe(\"Line1\\nLine2\");\n  expect(trimLines(\"Line1\\n   \\nLine2\")).toBe(\"Line1\\n   \\nLine2\");\n  expect(trimLines(\" \\n    \\n\\t\")).toBe(\"\");\n  expect(trimLines(\"   Hello World\")).toBe(\"   Hello World\");\n  expect(trimLines(\"\\n\")).toBe(\"\");\n  expect(trimLines(\"\\t \\n\\t\\tLine1 \\n \\nLine2\\t\\t\\n\\t  \")).toBe(\"\\t\\tLine1 \\n \\nLine2\\t\\t\");\n});\n\n\n/**\n * A template literal tag that returns the same string as the template literal without a tag.\n *\n * Useful for implementing your own template literal tags.\n */\nexport function templateIdentity(strings: TemplateStringsArray | readonly string[], ...values: string[]): string {\n  if (values.length !== strings.length - 1) throw new StackAssertionError(\"Invalid number of values; must be one less than strings\", { strings, values });\n\n  return strings.reduce((result, str, i) => result + str + (values[i] ?? ''), '');\n}\nundefined?.test(\"templateIdentity\", ({ expect }) => {\n  expect(templateIdentity`Hello World`).toBe(\"Hello World\");\n  expect(templateIdentity`${\"Hello\"}`).toBe(\"Hello\");\n  const greeting = \"Hello\";\n  const subject = \"World\";\n  expect(templateIdentity`${greeting}, ${subject}!`).toBe(\"Hello, World!\");\n  expect(templateIdentity`${\"A\"}${\"B\"}${\"C\"}`).toBe(\"ABC\");\n  expect(templateIdentity`Start${\"\"}Middle${\"\"}End`).toBe(\"StartMiddleEnd\");\n  expect(templateIdentity``).toBe(\"\");\n  expect(templateIdentity`Line1\nLine2`).toBe(\"Line1\\nLine2\");\n  expect(templateIdentity([\"a \", \" scientific \", \"gun\"], \"certain\", \"rail\")).toBe(\"a certain scientific railgun\");\n  expect(templateIdentity([\"only one part\"])).toBe(\"only one part\");\n  expect(() => templateIdentity([\"a \", \"b\", \"c\"], \"only one\")).toThrow(\"Invalid number of values\");\n  expect(() => templateIdentity([\"a\", \"b\"], \"x\", \"y\")).toThrow(\"Invalid number of values\");\n});\n\n\nexport function deindent(code: string): string;\nexport function deindent(strings: TemplateStringsArray | readonly string[], ...values: any[]): string;\nexport function deindent(strings: string | readonly string[], ...values: any[]): string {\n  if (typeof strings === \"string\") return deindent([strings]);\n  return templateIdentity(...deindentTemplate(strings, ...values));\n}\n\nexport function deindentTemplate(strings: TemplateStringsArray | readonly string[], ...values: any[]): [string[], ...string[]] {\n  if (values.length !== strings.length - 1) throw new StackAssertionError(\"Invalid number of values; must be one less than strings\", { strings, values });\n\n  const trimmedStrings = [...strings];\n  trimmedStrings[0] = trimEmptyLinesStart(trimmedStrings[0] + \"+\").slice(0, -1);\n  trimmedStrings[trimmedStrings.length - 1] = trimEmptyLinesEnd(\"+\" + trimmedStrings[trimmedStrings.length - 1]).slice(1);\n\n  const indentation = trimmedStrings\n    .join(\"${SOME_VALUE}\")\n    .split(\"\\n\")\n    .filter((line) => line.trim() !== \"\")\n    .map((line) => getWhitespacePrefix(line).length)\n    .reduce((min, current) => Math.min(min, current), Infinity);\n\n  const deindentedStrings = trimmedStrings\n    .map((string, stringIndex) => {\n      return string\n        .split(\"\\n\")\n        .map((line, lineIndex) => stringIndex !== 0 && lineIndex === 0 ? line : line.substring(indentation))\n        .join(\"\\n\");\n    });\n\n  const indentedValues = values.map((value, i) => {\n    const firstLineIndentation = getWhitespacePrefix(deindentedStrings[i].split(\"\\n\").at(-1)!);\n    return `${value}`.replaceAll(\"\\n\", `\\n${firstLineIndentation}`);\n  });\n\n  return [deindentedStrings, ...indentedValues];\n}\nundefined?.test(\"deindent\", ({ expect }) => {\n  // Test with string input\n  expect(deindent(\"  hello\")).toBe(\"hello\");\n  expect(deindent(\"  hello\\n  world\")).toBe(\"hello\\nworld\");\n  expect(deindent(\"  hello\\n    world\")).toBe(\"hello\\n  world\");\n  expect(deindent(\"\\n  hello\\n  world\\n\")).toBe(\"hello\\nworld\");\n\n  // Test with empty input\n  expect(deindent(\"\")).toBe(\"\");\n\n  // Test with template literal\n  expect(deindent`\n    hello\n    world\n  `).toBe(\"hello\\nworld\");\n\n  expect(deindent`\n    hello\n      world\n  `).toBe(\"hello\\n  world\");\n\n  // Test with values\n  const value = \"test\";\n  expect(deindent`\n    hello ${value}\n    world\n  `).toBe(`hello ${value}\\nworld`);\n\n  // Test with multiline values\n  expect(deindent`\n    hello\n      to ${\"line1\\n  line2\"}\n    world\n  `).toBe(`hello\\n  to line1\\n    line2\\nworld`);\n\n  // Leading whitespace values\n  expect(deindent`\n    ${\"  \"}A\n    ${\"  \"}B\n    ${\"  \"}C\n  `).toBe(`  A\\n  B\\n  C`);\n\n  // Trailing whitespaces (note: there are two whitespaces each after A and after C)\n  expect(deindent`\n    A  \n    B  ${\"  \"}\n    C  \n  `).toBe(`A  \\nB    \\nC  `);\n\n  // Test with mixed indentation\n  expect(deindent`\n    hello\n      world\n        !\n  `).toBe(\"hello\\n  world\\n    !\");\n\n  // Test error cases\n  expect(() => deindent([\"a\", \"b\", \"c\"], \"too\", \"many\", \"values\")).toThrow(\"Invalid number of values\");\n});\n\nexport function extractScopes(scope: string, removeDuplicates=true): string[] {\n  // TODO what is this for? can we move this into the OAuth code in the backend?\n  const trimmedString = scope.trim();\n  const scopesArray = trimmedString.split(/\\s+/);\n  const filtered = scopesArray.filter(scope => scope.length > 0);\n  return removeDuplicates ? [...new Set(filtered)] : filtered;\n}\nundefined?.test(\"extractScopes\", ({ expect }) => {\n  // Test with empty string\n  expect(extractScopes(\"\")).toEqual([]);\n\n  // Test with single scope\n  expect(extractScopes(\"read\")).toEqual([\"read\"]);\n\n  // Test with multiple scopes\n  expect(extractScopes(\"read write\")).toEqual([\"read\", \"write\"]);\n\n  // Test with extra whitespace\n  expect(extractScopes(\"  read  write  \")).toEqual([\"read\", \"write\"]);\n\n  // Test with newlines and tabs\n  expect(extractScopes(\"read\\nwrite\\tdelete\")).toEqual([\"read\", \"write\", \"delete\"]);\n\n  // Test with duplicates (default behavior)\n  expect(extractScopes(\"read write read\")).toEqual([\"read\", \"write\"]);\n\n  // Test with duplicates (explicitly set to remove)\n  expect(extractScopes(\"read write read\", true)).toEqual([\"read\", \"write\"]);\n\n  // Test with duplicates (explicitly set to keep)\n  expect(extractScopes(\"read write read\", false)).toEqual([\"read\", \"write\", \"read\"]);\n});\n\nexport function mergeScopeStrings(...scopes: string[]): string {\n  // TODO what is this for? can we move this into the OAuth code in the backend?\n  const allScope = scopes.map((s) => extractScopes(s)).flat().join(\" \");\n  return extractScopes(allScope).join(\" \");\n}\nundefined?.test(\"mergeScopeStrings\", ({ expect }) => {\n  // Test with empty input\n  expect(mergeScopeStrings()).toBe(\"\");\n\n  // Test with single scope string\n  expect(mergeScopeStrings(\"read write\")).toBe(\"read write\");\n\n  // Test with multiple scope strings\n  expect(mergeScopeStrings(\"read\", \"write\")).toBe(\"read write\");\n\n  // Test with overlapping scopes\n  expect(mergeScopeStrings(\"read write\", \"write delete\")).toBe(\"read write delete\");\n\n  // Test with extra whitespace\n  expect(mergeScopeStrings(\"  read  write  \", \"  delete  \")).toBe(\"read write delete\");\n\n  // Test with duplicates across strings\n  expect(mergeScopeStrings(\"read write\", \"write delete\", \"read\")).toBe(\"read write delete\");\n\n  // Test with empty strings\n  expect(mergeScopeStrings(\"read write\", \"\", \"delete\")).toBe(\"read write delete\");\n});\n\nexport function escapeTemplateLiteral(s: string): string {\n  return s.replaceAll(\"`\", \"\\\\`\").replaceAll(\"\\\\\", \"\\\\\\\\\").replaceAll(\"$\", \"\\\\$\");\n}\nundefined?.test(\"escapeTemplateLiteral\", ({ expect }) => {\n  // Test with empty string\n  expect(escapeTemplateLiteral(\"\")).toBe(\"\");\n\n  // Test with normal string (no special characters)\n  expect(escapeTemplateLiteral(\"hello world\")).toBe(\"hello world\");\n\n  // Test with backtick\n  const input1 = \"hello `world`\";\n  const output1 = escapeTemplateLiteral(input1);\n  // Verify backticks are escaped\n  expect(output1.includes(\"\\\\`\")).toBe(true);\n  expect(output1).not.toBe(input1);\n\n  // Test with backslash\n  const input2 = \"hello \\\\world\";\n  const output2 = escapeTemplateLiteral(input2);\n  // Verify backslashes are escaped\n  expect(output2.includes(\"\\\\\\\\\")).toBe(true);\n  expect(output2).not.toBe(input2);\n\n  // Test with dollar sign\n  const input3 = \"hello $world\";\n  const output3 = escapeTemplateLiteral(input3);\n  // Verify dollar signs are escaped\n  expect(output3.includes(\"\\\\$\")).toBe(true);\n  expect(output3).not.toBe(input3);\n\n  // Test with multiple special characters\n  const input4 = \"`hello` $world\\\\\";\n  const output4 = escapeTemplateLiteral(input4);\n  // Verify all special characters are escaped\n  expect(output4.includes(\"\\\\`\")).toBe(true);\n  expect(output4.includes(\"\\\\$\")).toBe(true);\n  expect(output4.includes(\"\\\\\\\\\")).toBe(true);\n  expect(output4).not.toBe(input4);\n\n  // Test with already escaped characters\n  const input5 = \"\\\\`hello\\\\`\";\n  const output5 = escapeTemplateLiteral(input5);\n  // Verify already escaped characters are properly escaped\n  expect(output5).not.toBe(input5);\n});\n\n/**\n * Some classes have different constructor names in different environments (eg. `Headers` is sometimes called `_Headers`,\n * so we create an object of overrides to handle these cases.\n */\nconst nicifiableClassNameOverrides = new Map(Object.entries({\n  Headers,\n} as Record<string, unknown>).map(([k, v]) => [v, k]));\nexport type Nicifiable = {\n  getNicifiableKeys?(): PropertyKey[],\n  getNicifiedObjectExtraLines?(): string[],\n};\nexport type NicifyOptions = {\n  maxDepth: number,\n  currentIndent: string,\n  lineIndent: string,\n  multiline: boolean,\n  refs: Map<unknown, string>,\n  path: string,\n  parent: null | {\n    options: NicifyOptions,\n    value: unknown,\n  },\n  keyInParent: PropertyKey | null,\n  hideFields: PropertyKey[],\n  overrides: (...args: Parameters<typeof nicify>) => string | null,\n};\nexport function nicify(\n  value: unknown,\n  options: Partial<NicifyOptions> = {},\n): string {\n  const fullOptions: NicifyOptions = {\n    maxDepth: 5,\n    currentIndent: \"\",\n    lineIndent: \"  \",\n    multiline: true,\n    refs: new Map(),\n    path: \"value\",\n    parent: null,\n    overrides: () => null,\n    keyInParent: null,\n    hideFields: [],\n    ...filterUndefined(options),\n  };\n  const {\n    maxDepth,\n    currentIndent,\n    lineIndent,\n    multiline,\n    refs,\n    path,\n    overrides,\n    hideFields,\n  } = fullOptions;\n  const nl = `\\n${currentIndent}`;\n\n  const overrideResult = overrides(value, options);\n  if (overrideResult !== null) return overrideResult;\n\n  if ([\"function\", \"object\", \"symbol\"].includes(typeof value) && value !== null) {\n    if (refs.has(value)) {\n      return `Ref<${refs.get(value)}>`;\n    }\n    refs.set(value, path);\n  }\n\n  const newOptions: NicifyOptions = {\n    maxDepth: maxDepth - 1,\n    currentIndent,\n    lineIndent,\n    multiline,\n    refs,\n    path: path + \"->[unknown property]\",\n    overrides,\n    parent: { value, options: fullOptions },\n    keyInParent: null,\n    hideFields: [],\n  };\n  const nestedNicify = (newValue: unknown, newPath: string, keyInParent: PropertyKey | null, options: Partial<NicifyOptions> = {}) => {\n    return nicify(newValue, {\n      ...newOptions,\n      path: newPath,\n      currentIndent: currentIndent + lineIndent,\n      keyInParent,\n      ...options,\n    });\n  };\n\n  switch (typeof value) {\n    case \"boolean\": case \"number\": {\n      return JSON.stringify(value);\n    }\n    case \"string\": {\n      const isDeindentable = (v: string) => deindent(v) === v && v.includes(\"\\n\");\n      const wrapInDeindent = (v: string) => deindent`\n        deindent\\`\n        ${currentIndent + lineIndent}${escapeTemplateLiteral(v).replaceAll(\"\\n\", nl + lineIndent)}\n        ${currentIndent}\\`\n      `;\n      if (isDeindentable(value)) {\n        return wrapInDeindent(value);\n      } else if (value.endsWith(\"\\n\") && isDeindentable(value.slice(0, -1))) {\n        return wrapInDeindent(value.slice(0, -1)) + ' + \"\\\\n\"';\n      } else {\n        return JSON.stringify(value);\n      }\n    }\n    case \"undefined\": {\n      return \"undefined\";\n    }\n    case \"symbol\": {\n      return value.toString();\n    }\n    case \"bigint\": {\n      return `${value}n`;\n    }\n    case \"function\": {\n      if (value.name) return `function ${value.name}(...) { ... }`;\n      return `(...) => { ... }`;\n    }\n    case \"object\": {\n      if (value === null) return \"null\";\n      if (Array.isArray(value)) {\n        const extraLines = getNicifiedObjectExtraLines(value);\n        const resValueLength = value.length + extraLines.length;\n        if (maxDepth <= 0 && resValueLength === 0) return \"[...]\";\n        const resValues = value.map((v, i) => nestedNicify(v, `${path}[${i}]`, i));\n        resValues.push(...extraLines);\n        if (resValues.length !== resValueLength) throw new StackAssertionError(\"nicify of object: resValues.length !== resValueLength\", { value, resValues, resValueLength });\n        const shouldIndent = resValues.length > 4 || resValues.some(x => (resValues.length > 1 && x.length > 4) || x.includes(\"\\n\"));\n        if (shouldIndent) {\n          return `[${nl}${resValues.map(x => `${lineIndent}${x},${nl}`).join(\"\")}]`;\n        } else {\n          return `[${resValues.join(\", \")}]`;\n        }\n      }\n      if (value instanceof URL) {\n        return `URL(${nestedNicify(value.toString(), `${path}.toString()`, null)})`;\n      }\n      if (ArrayBuffer.isView(value)) {\n        return `${value.constructor.name}([${value.toString()}])`;\n      }\n      if (value instanceof Error) {\n        let stack = value.stack ?? \"\";\n        const toString = value.toString();\n        if (!stack.startsWith(toString)) stack = `${toString}\\n${stack}`;  // some browsers don't include the error message in the stack, some do\n        stack = stack.trimEnd();\n        stack = stack.replace(/\\n\\s+/g, `\\n${lineIndent}${lineIndent}`);\n        stack = stack.replace(\"\\n\", `\\n${lineIndent}Stack:\\n`);\n        if (Object.keys(value).length > 0) {\n          stack += `\\n${lineIndent}Extra properties: ${nestedNicify(Object.fromEntries(Object.entries(value)), path, null)}`;\n        }\n        if (value.cause) {\n          stack += `\\n${lineIndent}Cause:\\n${lineIndent}${lineIndent}${nestedNicify(value.cause, path, null, { currentIndent: currentIndent + lineIndent + lineIndent })}`;\n        }\n        stack = stack.replaceAll(\"\\n\", `\\n${currentIndent}`);\n        return stack;\n      }\n\n      const constructorName = [null, Object.prototype].includes(Object.getPrototypeOf(value)) ? null : (nicifiableClassNameOverrides.get(value.constructor) ?? value.constructor.name);\n      const constructorString = constructorName ? `${constructorName} ` : \"\";\n\n      const entries = getNicifiableEntries(value).filter(([k]) => !hideFields.includes(k));\n      const extraLines = [\n        ...getNicifiedObjectExtraLines(value),\n        ...hideFields.length > 0 ? [`<some fields may have been hidden>`] : [],\n      ];\n      const resValueLength = entries.length + extraLines.length;\n      if (resValueLength === 0) return `${constructorString}{}`;\n      if (maxDepth <= 0) return `${constructorString}{ ... }`;\n      const resValues = entries.map(([k, v], keyIndex) => {\n        const keyNicified = nestedNicify(k, `Object.keys(${path})[${keyIndex}]`, null);\n        const keyInObjectLiteral = typeof k === \"string\" ? nicifyPropertyString(k) : `[${keyNicified}]`;\n        if (typeof v === \"function\" && v.name === k) {\n          return `${keyInObjectLiteral}(...): { ... }`;\n        } else {\n          return `${keyInObjectLiteral}: ${nestedNicify(v, `${path}[${keyNicified}]`, k)}`;\n        }\n      });\n      resValues.push(...extraLines);\n      if (resValues.length !== resValueLength) throw new StackAssertionError(\"nicify of object: resValues.length !== resValueLength\", { value, resValues, resValueLength });\n      const shouldIndent = resValues.length > 1 || resValues.some(x => x.includes(\"\\n\"));\n\n      if (resValues.length === 0) return `${constructorString}{}`;\n      if (shouldIndent) {\n        return `${constructorString}{${nl}${resValues.map(x => `${lineIndent}${x},${nl}`).join(\"\")}}`;\n      } else {\n        return `${constructorString}{ ${resValues.join(\", \")} }`;\n      }\n    }\n    default: {\n      return `${typeof value}<${value}>`;\n    }\n  }\n}\n\nexport function replaceAll(input: string, searchValue: string, replaceValue: string): string {\n  if (searchValue === \"\") throw new StackAssertionError(\"replaceAll: searchValue is empty\");\n  return input.split(searchValue).join(replaceValue);\n}\nundefined?.test(\"replaceAll\", ({ expect }) => {\n  expect(replaceAll(\"hello world\", \"o\", \"x\")).toBe(\"hellx wxrld\");\n  expect(replaceAll(\"aaa\", \"a\", \"b\")).toBe(\"bbb\");\n  expect(replaceAll(\"\", \"a\", \"b\")).toBe(\"\");\n  expect(replaceAll(\"abc\", \"b\", \"\")).toBe(\"ac\");\n  expect(replaceAll(\"test.test.test\", \".\", \"_\")).toBe(\"test_test_test\");\n  expect(replaceAll(\"a.b*c\", \".\", \"x\")).toBe(\"axb*c\");\n  expect(replaceAll(\"a*b*c\", \"*\", \"x\")).toBe(\"axbxc\");\n  expect(replaceAll(\"hello hello\", \"hello\", \"hi\")).toBe(\"hi hi\");\n});\n\nfunction nicifyPropertyString(str: string) {\n  return JSON.stringify(str);\n}\nundefined?.test(\"nicifyPropertyString\", ({ expect }) => {\n  // Test valid identifiers\n  expect(nicifyPropertyString(\"validName\")).toBe('\"validName\"');\n  expect(nicifyPropertyString(\"_validName\")).toBe('\"_validName\"');\n  expect(nicifyPropertyString(\"valid123Name\")).toBe('\"valid123Name\"');\n\n  // Test invalid identifiers\n  expect(nicifyPropertyString(\"123invalid\")).toBe('\"123invalid\"');\n  expect(nicifyPropertyString(\"invalid-name\")).toBe('\"invalid-name\"');\n  expect(nicifyPropertyString(\"invalid space\")).toBe('\"invalid space\"');\n  expect(nicifyPropertyString(\"$invalid\")).toBe('\"$invalid\"');\n  expect(nicifyPropertyString(\"\")).toBe('\"\"');\n\n  // Test with special characters\n  expect(nicifyPropertyString(\"property!\")).toBe('\"property!\"');\n  expect(nicifyPropertyString(\"property.name\")).toBe('\"property.name\"');\n\n  // Test with escaped characters\n  expect(nicifyPropertyString(\"\\\\\")).toBe('\"\\\\\\\\\"');\n  expect(nicifyPropertyString('\"')).toBe('\"\\\\\"\"');\n});\n\nfunction getNicifiableKeys(value: Nicifiable | object) {\n  const overridden = (\"getNicifiableKeys\" in value ? value.getNicifiableKeys?.bind(value) : null)?.();\n  if (overridden != null) return overridden;\n  const keys = Object.keys(value).sort();\n  return unique(keys);\n}\nundefined?.test(\"getNicifiableKeys\", ({ expect }) => {\n  // Test regular object\n  expect(getNicifiableKeys({ b: 1, a: 2, c: 3 })).toEqual([\"a\", \"b\", \"c\"]);\n\n  // Test empty object\n  expect(getNicifiableKeys({})).toEqual([]);\n\n  // Test object with custom getNicifiableKeys\n  const customObject = {\n    a: 1,\n    b: 2,\n    getNicifiableKeys() {\n      return [\"customKey1\", \"customKey2\"];\n    }\n  };\n  expect(getNicifiableKeys(customObject)).toEqual([\"customKey1\", \"customKey2\"]);\n});\n\nfunction getNicifiableEntries(value: Nicifiable | object): [PropertyKey, unknown][] {\n  const recordLikes = [Headers];\n  function isRecordLike(value: unknown): value is InstanceType<typeof recordLikes[number]> {\n    return recordLikes.some(x => value instanceof x);\n  }\n\n  if (isRecordLike(value)) {\n    return [...value.entries()].sort(([a], [b]) => stringCompare(`${a}`, `${b}`));\n  }\n  const keys = getNicifiableKeys(value);\n  return keys.map((k) => [k, value[k as never]] as [PropertyKey, unknown]);\n}\n\nfunction getNicifiedObjectExtraLines(value: Nicifiable | object) {\n  return (\"getNicifiedObjectExtraLines\" in value ? value.getNicifiedObjectExtraLines : null)?.() ?? [];\n}\n"], "mappings": ";AAAA,SAAS,eAAe,cAAc;AACtC,SAAS,2BAA2B;AACpC,SAAS,uBAAuB;AAEzB,SAAS,iBAAmC,GAAoB;AACrE,MAAI,OAAO,MAAM,SAAU,OAAM,IAAI,oBAAoB,0CAA0C,EAAE,EAAE,CAAC;AACxG,SAAO,EAAE,YAAY;AACvB;AAYO,SAAS,iBAAmC,GAAoB;AACrE,MAAI,OAAO,MAAM,SAAU,OAAM,IAAI,oBAAoB,0CAA0C,EAAE,EAAE,CAAC;AACxG,SAAO,EAAE,YAAY;AACvB;AAYO,SAAS,gBAAkC,GAAqB;AACrE,SAAO,EAAE,OAAO,CAAC,EAAE,YAAY,IAAI,EAAE,MAAM,CAAC;AAC9C;AAcO,SAAS,cAAc,GAAW,GAAmB;AAC1D,MAAI,OAAO,MAAM,YAAY,OAAO,MAAM,SAAU,OAAM,IAAI,oBAAoB,iDAAiD,OAAO,CAAC,QAAQ,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AACvK,QAAM,MAAM,CAACA,IAAWC,OAAcD,KAAIC,KAAI,KAAKD,KAAIC,KAAI,IAAI;AAC/D,SAAO,IAAI,EAAE,YAAY,GAAG,EAAE,YAAY,CAAC,KAAK,IAAI,GAAG,CAAC;AAC1D;AAmCO,SAAS,oBAAoB,GAAmB;AACrD,SAAO,EAAE,UAAU,GAAG,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM;AACvD;AAiBO,SAAS,oBAAoB,GAAmB;AACrD,SAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM;AACvC;AAiBO,SAAS,oBAAoB,GAAmB;AACrD,QAAM,QAAQ,EAAE,MAAM,IAAI;AAC1B,QAAM,yBAAyB,MAAM,UAAU,CAAC,SAAS,KAAK,KAAK,MAAM,EAAE;AAE3E,MAAI,2BAA2B,GAAI,QAAO;AAC1C,SAAO,MAAM,MAAM,sBAAsB,EAAE,KAAK,IAAI;AACtD;AAoBO,SAAS,kBAAkB,GAAmB;AACnD,QAAM,QAAQ,EAAE,MAAM,IAAI;AAC1B,QAAM,wBAAwB,cAAc,OAAO,CAAC,SAAS,KAAK,KAAK,MAAM,EAAE;AAC/E,SAAO,MAAM,MAAM,GAAG,wBAAwB,CAAC,EAAE,KAAK,IAAI;AAC5D;AAoBO,SAAS,UAAU,GAAmB;AAC3C,SAAO,kBAAkB,oBAAoB,CAAC,CAAC;AACjD;AAoBO,SAAS,iBAAiB,YAAsD,QAA0B;AAC/G,MAAI,OAAO,WAAW,QAAQ,SAAS,EAAG,OAAM,IAAI,oBAAoB,2DAA2D,EAAE,SAAS,OAAO,CAAC;AAEtJ,SAAO,QAAQ,OAAO,CAAC,QAAQ,KAAK,MAAM,SAAS,OAAO,OAAO,CAAC,KAAK,KAAK,EAAE;AAChF;AAqBO,SAAS,SAAS,YAAwC,QAAuB;AACtF,MAAI,OAAO,YAAY,SAAU,QAAO,SAAS,CAAC,OAAO,CAAC;AAC1D,SAAO,iBAAiB,GAAG,iBAAiB,SAAS,GAAG,MAAM,CAAC;AACjE;AAEO,SAAS,iBAAiB,YAAsD,QAAwC;AAC7H,MAAI,OAAO,WAAW,QAAQ,SAAS,EAAG,OAAM,IAAI,oBAAoB,2DAA2D,EAAE,SAAS,OAAO,CAAC;AAEtJ,QAAM,iBAAiB,CAAC,GAAG,OAAO;AAClC,iBAAe,CAAC,IAAI,oBAAoB,eAAe,CAAC,IAAI,GAAG,EAAE,MAAM,GAAG,EAAE;AAC5E,iBAAe,eAAe,SAAS,CAAC,IAAI,kBAAkB,MAAM,eAAe,eAAe,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC;AAEtH,QAAM,cAAc,eACjB,KAAK,eAAe,EACpB,MAAM,IAAI,EACV,OAAO,CAAC,SAAS,KAAK,KAAK,MAAM,EAAE,EACnC,IAAI,CAAC,SAAS,oBAAoB,IAAI,EAAE,MAAM,EAC9C,OAAO,CAAC,KAAK,YAAY,KAAK,IAAI,KAAK,OAAO,GAAG,QAAQ;AAE5D,QAAM,oBAAoB,eACvB,IAAI,CAAC,QAAQ,gBAAgB;AAC5B,WAAO,OACJ,MAAM,IAAI,EACV,IAAI,CAAC,MAAM,cAAc,gBAAgB,KAAK,cAAc,IAAI,OAAO,KAAK,UAAU,WAAW,CAAC,EAClG,KAAK,IAAI;AAAA,EACd,CAAC;AAEH,QAAM,iBAAiB,OAAO,IAAI,CAAC,OAAO,MAAM;AAC9C,UAAM,uBAAuB,oBAAoB,kBAAkB,CAAC,EAAE,MAAM,IAAI,EAAE,GAAG,EAAE,CAAE;AACzF,WAAO,GAAG,KAAK,GAAG,WAAW,MAAM;AAAA,EAAK,oBAAoB,EAAE;AAAA,EAChE,CAAC;AAED,SAAO,CAAC,mBAAmB,GAAG,cAAc;AAC9C;AA6DO,SAAS,cAAc,OAAe,mBAAiB,MAAgB;AAE5E,QAAM,gBAAgB,MAAM,KAAK;AACjC,QAAM,cAAc,cAAc,MAAM,KAAK;AAC7C,QAAM,WAAW,YAAY,OAAO,CAAAC,WAASA,OAAM,SAAS,CAAC;AAC7D,SAAO,mBAAmB,CAAC,GAAG,IAAI,IAAI,QAAQ,CAAC,IAAI;AACrD;AA2BO,SAAS,qBAAqB,QAA0B;AAE7D,QAAM,WAAW,OAAO,IAAI,CAAC,MAAM,cAAc,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,GAAG;AACpE,SAAO,cAAc,QAAQ,EAAE,KAAK,GAAG;AACzC;AAwBO,SAAS,sBAAsB,GAAmB;AACvD,SAAO,EAAE,WAAW,KAAK,KAAK,EAAE,WAAW,MAAM,MAAM,EAAE,WAAW,KAAK,KAAK;AAChF;AAiDA,IAAM,+BAA+B,IAAI,IAAI,OAAO,QAAQ;AAAA,EAC1D;AACF,CAA4B,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAoB9C,SAAS,OACd,OACA,UAAkC,CAAC,GAC3B;AACR,QAAM,cAA6B;AAAA,IACjC,UAAU;AAAA,IACV,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,MAAM,oBAAI,IAAI;AAAA,IACd,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW,MAAM;AAAA,IACjB,aAAa;AAAA,IACb,YAAY,CAAC;AAAA,IACb,GAAG,gBAAgB,OAAO;AAAA,EAC5B;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,KAAK;AAAA,EAAK,aAAa;AAE7B,QAAM,iBAAiB,UAAU,OAAO,OAAO;AAC/C,MAAI,mBAAmB,KAAM,QAAO;AAEpC,MAAI,CAAC,YAAY,UAAU,QAAQ,EAAE,SAAS,OAAO,KAAK,KAAK,UAAU,MAAM;AAC7E,QAAI,KAAK,IAAI,KAAK,GAAG;AACnB,aAAO,OAAO,KAAK,IAAI,KAAK,CAAC;AAAA,IAC/B;AACA,SAAK,IAAI,OAAO,IAAI;AAAA,EACtB;AAEA,QAAM,aAA4B;AAAA,IAChC,UAAU,WAAW;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM,OAAO;AAAA,IACb;AAAA,IACA,QAAQ,EAAE,OAAO,SAAS,YAAY;AAAA,IACtC,aAAa;AAAA,IACb,YAAY,CAAC;AAAA,EACf;AACA,QAAM,eAAe,CAAC,UAAmB,SAAiB,aAAiCC,WAAkC,CAAC,MAAM;AAClI,WAAO,OAAO,UAAU;AAAA,MACtB,GAAG;AAAA,MACH,MAAM;AAAA,MACN,eAAe,gBAAgB;AAAA,MAC/B;AAAA,MACA,GAAGA;AAAA,IACL,CAAC;AAAA,EACH;AAEA,UAAQ,OAAO,OAAO;AAAA,IACpB,KAAK;AAAA,IAAW,KAAK,UAAU;AAC7B,aAAO,KAAK,UAAU,KAAK;AAAA,IAC7B;AAAA,IACA,KAAK,UAAU;AACb,YAAM,iBAAiB,CAAC,MAAc,SAAS,CAAC,MAAM,KAAK,EAAE,SAAS,IAAI;AAC1E,YAAM,iBAAiB,CAAC,MAAc;AAAA;AAAA,UAElC,gBAAgB,UAAU,GAAG,sBAAsB,CAAC,EAAE,WAAW,MAAM,KAAK,UAAU,CAAC;AAAA,UACvF,aAAa;AAAA;AAEjB,UAAI,eAAe,KAAK,GAAG;AACzB,eAAO,eAAe,KAAK;AAAA,MAC7B,WAAW,MAAM,SAAS,IAAI,KAAK,eAAe,MAAM,MAAM,GAAG,EAAE,CAAC,GAAG;AACrE,eAAO,eAAe,MAAM,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,MAC9C,OAAO;AACL,eAAO,KAAK,UAAU,KAAK;AAAA,MAC7B;AAAA,IACF;AAAA,IACA,KAAK,aAAa;AAChB,aAAO;AAAA,IACT;AAAA,IACA,KAAK,UAAU;AACb,aAAO,MAAM,SAAS;AAAA,IACxB;AAAA,IACA,KAAK,UAAU;AACb,aAAO,GAAG,KAAK;AAAA,IACjB;AAAA,IACA,KAAK,YAAY;AACf,UAAI,MAAM,KAAM,QAAO,YAAY,MAAM,IAAI;AAC7C,aAAO;AAAA,IACT;AAAA,IACA,KAAK,UAAU;AACb,UAAI,UAAU,KAAM,QAAO;AAC3B,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,cAAMC,cAAa,4BAA4B,KAAK;AACpD,cAAMC,kBAAiB,MAAM,SAASD,YAAW;AACjD,YAAI,YAAY,KAAKC,oBAAmB,EAAG,QAAO;AAClD,cAAMC,aAAY,MAAM,IAAI,CAAC,GAAG,MAAM,aAAa,GAAG,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;AACzE,QAAAA,WAAU,KAAK,GAAGF,WAAU;AAC5B,YAAIE,WAAU,WAAWD,gBAAgB,OAAM,IAAI,oBAAoB,yDAAyD,EAAE,OAAO,WAAAC,YAAW,gBAAAD,gBAAe,CAAC;AACpK,cAAME,gBAAeD,WAAU,SAAS,KAAKA,WAAU,KAAK,OAAMA,WAAU,SAAS,KAAK,EAAE,SAAS,KAAM,EAAE,SAAS,IAAI,CAAC;AAC3H,YAAIC,eAAc;AAChB,iBAAO,IAAI,EAAE,GAAGD,WAAU,IAAI,OAAK,GAAG,UAAU,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC;AAAA,QACxE,OAAO;AACL,iBAAO,IAAIA,WAAU,KAAK,IAAI,CAAC;AAAA,QACjC;AAAA,MACF;AACA,UAAI,iBAAiB,KAAK;AACxB,eAAO,OAAO,aAAa,MAAM,SAAS,GAAG,GAAG,IAAI,eAAe,IAAI,CAAC;AAAA,MAC1E;AACA,UAAI,YAAY,OAAO,KAAK,GAAG;AAC7B,eAAO,GAAG,MAAM,YAAY,IAAI,KAAK,MAAM,SAAS,CAAC;AAAA,MACvD;AACA,UAAI,iBAAiB,OAAO;AAC1B,YAAI,QAAQ,MAAM,SAAS;AAC3B,cAAM,WAAW,MAAM,SAAS;AAChC,YAAI,CAAC,MAAM,WAAW,QAAQ,EAAG,SAAQ,GAAG,QAAQ;AAAA,EAAK,KAAK;AAC9D,gBAAQ,MAAM,QAAQ;AACtB,gBAAQ,MAAM,QAAQ,UAAU;AAAA,EAAK,UAAU,GAAG,UAAU,EAAE;AAC9D,gBAAQ,MAAM,QAAQ,MAAM;AAAA,EAAK,UAAU;AAAA,CAAU;AACrD,YAAI,OAAO,KAAK,KAAK,EAAE,SAAS,GAAG;AACjC,mBAAS;AAAA,EAAK,UAAU,qBAAqB,aAAa,OAAO,YAAY,OAAO,QAAQ,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AAAA,QAClH;AACA,YAAI,MAAM,OAAO;AACf,mBAAS;AAAA,EAAK,UAAU;AAAA,EAAW,UAAU,GAAG,UAAU,GAAG,aAAa,MAAM,OAAO,MAAM,MAAM,EAAE,eAAe,gBAAgB,aAAa,WAAW,CAAC,CAAC;AAAA,QAChK;AACA,gBAAQ,MAAM,WAAW,MAAM;AAAA,EAAK,aAAa,EAAE;AACnD,eAAO;AAAA,MACT;AAEA,YAAM,kBAAkB,CAAC,MAAM,OAAO,SAAS,EAAE,SAAS,OAAO,eAAe,KAAK,CAAC,IAAI,OAAQ,6BAA6B,IAAI,MAAM,WAAW,KAAK,MAAM,YAAY;AAC3K,YAAM,oBAAoB,kBAAkB,GAAG,eAAe,MAAM;AAEpE,YAAM,UAAU,qBAAqB,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,SAAS,CAAC,CAAC;AACnF,YAAM,aAAa;AAAA,QACjB,GAAG,4BAA4B,KAAK;AAAA,QACpC,GAAG,WAAW,SAAS,IAAI,CAAC,oCAAoC,IAAI,CAAC;AAAA,MACvE;AACA,YAAM,iBAAiB,QAAQ,SAAS,WAAW;AACnD,UAAI,mBAAmB,EAAG,QAAO,GAAG,iBAAiB;AACrD,UAAI,YAAY,EAAG,QAAO,GAAG,iBAAiB;AAC9C,YAAM,YAAY,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa;AAClD,cAAM,cAAc,aAAa,GAAG,eAAe,IAAI,KAAK,QAAQ,KAAK,IAAI;AAC7E,cAAM,qBAAqB,OAAO,MAAM,WAAW,qBAAqB,CAAC,IAAI,IAAI,WAAW;AAC5F,YAAI,OAAO,MAAM,cAAc,EAAE,SAAS,GAAG;AAC3C,iBAAO,GAAG,kBAAkB;AAAA,QAC9B,OAAO;AACL,iBAAO,GAAG,kBAAkB,KAAK,aAAa,GAAG,GAAG,IAAI,IAAI,WAAW,KAAK,CAAC,CAAC;AAAA,QAChF;AAAA,MACF,CAAC;AACD,gBAAU,KAAK,GAAG,UAAU;AAC5B,UAAI,UAAU,WAAW,eAAgB,OAAM,IAAI,oBAAoB,yDAAyD,EAAE,OAAO,WAAW,eAAe,CAAC;AACpK,YAAM,eAAe,UAAU,SAAS,KAAK,UAAU,KAAK,OAAK,EAAE,SAAS,IAAI,CAAC;AAEjF,UAAI,UAAU,WAAW,EAAG,QAAO,GAAG,iBAAiB;AACvD,UAAI,cAAc;AAChB,eAAO,GAAG,iBAAiB,IAAI,EAAE,GAAG,UAAU,IAAI,OAAK,GAAG,UAAU,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC;AAAA,MAC5F,OAAO;AACL,eAAO,GAAG,iBAAiB,KAAK,UAAU,KAAK,IAAI,CAAC;AAAA,MACtD;AAAA,IACF;AAAA,IACA,SAAS;AACP,aAAO,GAAG,OAAO,KAAK,IAAI,KAAK;AAAA,IACjC;AAAA,EACF;AACF;AAEO,SAAS,WAAW,OAAe,aAAqB,cAA8B;AAC3F,MAAI,gBAAgB,GAAI,OAAM,IAAI,oBAAoB,kCAAkC;AACxF,SAAO,MAAM,MAAM,WAAW,EAAE,KAAK,YAAY;AACnD;AAYA,SAAS,qBAAqB,KAAa;AACzC,SAAO,KAAK,UAAU,GAAG;AAC3B;AAuBA,SAAS,kBAAkB,OAA4B;AACrD,QAAM,cAAc,uBAAuB,QAAQ,MAAM,mBAAmB,KAAK,KAAK,IAAI,QAAQ;AAClG,MAAI,cAAc,KAAM,QAAO;AAC/B,QAAM,OAAO,OAAO,KAAK,KAAK,EAAE,KAAK;AACrC,SAAO,OAAO,IAAI;AACpB;AAmBA,SAAS,qBAAqB,OAAsD;AAClF,QAAM,cAAc,CAAC,OAAO;AAC5B,WAAS,aAAaE,QAAmE;AACvF,WAAO,YAAY,KAAK,OAAKA,kBAAiB,CAAC;AAAA,EACjD;AAEA,MAAI,aAAa,KAAK,GAAG;AACvB,WAAO,CAAC,GAAG,MAAM,QAAQ,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,cAAc,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;AAAA,EAC9E;AACA,QAAM,OAAO,kBAAkB,KAAK;AACpC,SAAO,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAU,CAAC,CAA2B;AACzE;AAEA,SAAS,4BAA4B,OAA4B;AAC/D,UAAQ,iCAAiC,QAAQ,MAAM,8BAA8B,QAAQ,KAAK,CAAC;AACrG;", "names": ["a", "b", "scope", "options", "extraLines", "res<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "res<PERSON><PERSON><PERSON>", "shouldIndent", "value"]}