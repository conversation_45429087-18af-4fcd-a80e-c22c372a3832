{"version": 3, "sources": ["../../../src/interface/crud/current-user.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport { yupObject } from \"../../schema-fields\";\nimport { teamsCrudClientReadSchema } from \"./teams\";\nimport { usersCrudServerDeleteSchema, usersCrudServerReadSchema, usersCrudServerUpdateSchema } from \"./users\";\n\nconst clientUpdateSchema = usersCrudServerUpdateSchema.pick([\n  \"display_name\",\n  \"profile_image_url\",\n  \"client_metadata\",\n  \"selected_team_id\",\n  \"totp_secret_base64\",\n  \"otp_auth_enabled\",\n  \"passkey_auth_enabled\",\n]).defined();\n\nconst serverUpdateSchema = usersCrudServerUpdateSchema;\n\nconst clientReadSchema = usersCrudServerReadSchema.pick([\n  \"id\",\n  \"primary_email\",\n  \"primary_email_verified\",\n  \"display_name\",\n  \"client_metadata\",\n  \"client_read_only_metadata\",\n  \"profile_image_url\",\n  \"signed_up_at_millis\",\n  \"has_password\",\n  \"auth_with_email\",\n  \"oauth_providers\",\n  \"selected_team_id\",\n  \"requires_totp_mfa\",\n  \"otp_auth_enabled\",\n  \"passkey_auth_enabled\",\n  \"is_anonymous\",\n]).concat(yupObject({\n  selected_team: teamsCrudClientReadSchema.nullable().defined(),\n})).defined();\n\nconst serverReadSchema = usersCrudServerReadSchema.defined();\n\nconst clientDeleteSchema = usersCrudServerDeleteSchema;\n\nexport const currentUserCrud = createCrud({\n  clientReadSchema,\n  serverReadSchema,\n  clientUpdateSchema,\n  serverUpdateSchema,\n  clientDeleteSchema,\n  docs: {\n    clientRead: {\n      summary: 'Get current user',\n      description: 'Gets the currently authenticated user.',\n      tags: ['Users'],\n    },\n    clientUpdate: {\n      summary: 'Update current user',\n      description: 'Updates the currently authenticated user. Only the values provided will be updated.',\n      tags: ['Users'],\n    },\n    clientDelete: {\n      summary: 'Delete current user',\n      description: 'Deletes the currently authenticated user. Use this with caution.',\n      tags: ['Users'],\n    },\n  },\n});\nexport type CurrentUserCrud = CrudTypeOf<typeof currentUserCrud>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAuC;AACvC,2BAA0B;AAC1B,mBAA0C;AAC1C,mBAAoG;AAEpG,IAAM,qBAAqB,yCAA4B,KAAK;AAAA,EAC1D;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC,EAAE,QAAQ;AAEX,IAAM,qBAAqB;AAE3B,IAAM,mBAAmB,uCAA0B,KAAK;AAAA,EACtD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC,EAAE,WAAO,gCAAU;AAAA,EAClB,eAAe,uCAA0B,SAAS,EAAE,QAAQ;AAC9D,CAAC,CAAC,EAAE,QAAQ;AAEZ,IAAM,mBAAmB,uCAA0B,QAAQ;AAE3D,IAAM,qBAAqB;AAEpB,IAAM,sBAAkB,wBAAW;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,IACJ,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,EACF;AACF,CAAC;", "names": []}