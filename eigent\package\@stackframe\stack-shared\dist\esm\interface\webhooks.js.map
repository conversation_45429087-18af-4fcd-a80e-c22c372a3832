{"version": 3, "sources": ["../../../src/interface/webhooks.ts"], "sourcesContent": ["import * as yup from \"yup\";\nimport { teamMembershipCreatedWebhookEvent, teamMembershipDeletedWebhookEvent } from \"./crud/team-memberships\";\nimport { teamPermissionCreatedWebhookEvent, teamPermissionDeletedWebhookEvent } from \"./crud/team-permissions\";\nimport { teamCreatedWebhookEvent, teamDeletedWebhookEvent, teamUpdatedWebhookEvent } from \"./crud/teams\";\nimport { userCreatedWebhookEvent, userDeletedWebhookEvent, userUpdatedWebhookEvent } from \"./crud/users\";\n\nexport type WebhookEvent<S extends yup.Schema> = {\n  type: string,\n  schema: S,\n  metadata: {\n    summary: string,\n    description: string,\n    tags?: string[],\n  },\n};\n\nexport const webhookEvents = [\n  userCreatedWebhookEvent,\n  userUpdatedWebhookEvent,\n  userDeletedWebhookEvent,\n  teamCreatedWebhookEvent,\n  teamUpdatedWebhookEvent,\n  teamDeletedWebhookEvent,\n  teamMembershipCreatedWebhookEvent,\n  teamMembershipDeletedWebhookEvent,\n  teamPermissionCreatedWebhookEvent,\n  teamPermissionDeletedWebhookEvent,\n] as const;\n"], "mappings": ";AACA,SAAS,mCAAmC,yCAAyC;AACrF,SAAS,mCAAmC,yCAAyC;AACrF,SAAS,yBAAyB,yBAAyB,+BAA+B;AAC1F,SAAS,yBAAyB,yBAAyB,+BAA+B;AAYnF,IAAM,gBAAgB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}