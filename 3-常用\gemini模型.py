import os
from google import genai

# --- 配置 ---
# 1. 设置代理环境变量
# Clash Verge 默认端口通常是 7890，请根据您的实际配置调整
proxy_url = "http://127.0.0.1:7897"
os.environ['HTTP_PROXY'] = proxy_url
os.environ['HTTPS_PROXY'] = proxy_url  # Gemini API 通常通过 HTTPS 访问

# 2. 读取 API Key
try:
    with open("常用/key.txt", "r", encoding="utf-8") as f:
        # 使用 strip() 移除可能存在的前后空格或换行符
        key = f.read().strip()
    if not key:
        raise ValueError("API key 文件为空或只包含空白字符。")
except FileNotFoundError:
    print("错误：找不到 key.txt 文件。请确保该文件存在于脚本所在的目录。")
    exit(1)
except Exception as e:
    print(f"读取 API key 时出错: {e}")
    exit(1)

# --- API 调用 ---
try:
    # 3. 创建 Client
    # Client 会自动从环境变量中读取代理设置
    client = genai.Client(api_key=key)

    print("正在连接 Gemini API 并生成内容...") # 添加提示信息

    # 4. 流式生成内容
    # content = input("请输入提示词：")
    # print(content)
    response = client.models.generate_content_stream(
        model="gemini-2.5-pro",
        # contents=[content]
        contents=['''
介绍下你的能力
'''])

    # 5. 打印响应块
    print("-------------------------")
    output_generated = False
    for chunk in response:
        print(chunk.text, end="", flush=True) # 使用 flush=True 确保立即输出
        output_generated = True

    if not output_generated:
        print("[没有收到任何内容块]") # 如果循环未执行，则给出提示

    print("\n\n内容生成完毕。") # 结束提示

except Exception as e:
    print(f"\n执行过程中发生错误: {e}")
    # 如果需要更详细的调试信息，可以取消下面两行的注释
    # import traceback
    # traceback.print_exc()