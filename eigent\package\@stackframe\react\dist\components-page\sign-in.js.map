{"version": 3, "sources": ["../../src/components-page/sign-in.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { AuthPage } from \"./auth-page\";\n\nexport function SignIn(props: {\n  fullPage?: boolean,\n  automaticRedirect?: boolean,\n  extraInfo?: React.ReactNode,\n  firstTab?: 'magic-link' | 'password',\n}) {\n  return (\n    <AuthPage\n      fullPage={!!props.fullPage}\n      type=\"sign-in\"\n      automaticRedirect={!!props.automaticRedirect}\n      extraInfo={props.extraInfo}\n      firstTab={props.firstTab}\n    />\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,uBAAyB;AASrB;AAPG,SAAS,OAAO,OAKpB;AACD,SACE;AAAA,IAAC;AAAA;AAAA,MACC,UAAU,CAAC,CAAC,MAAM;AAAA,MAClB,MAAK;AAAA,MACL,mBAAmB,CAAC,CAAC,MAAM;AAAA,MAC3B,WAAW,MAAM;AAAA,MACjB,UAAU,MAAM;AAAA;AAAA,EAClB;AAEJ;", "names": []}