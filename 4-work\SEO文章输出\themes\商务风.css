/* Professional Business Theme */
body {
  font-family: 'Roboto', 'Segoe UI', Tahoma, sans-serif;
  line-height: 1.7;
  color: #333;
  max-width: 850px;
  margin: 0 auto;
  padding: 2rem;
  background-color: #fff;
}

h1 {
  font-size: 2.6rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2a2a2a;
  margin-top: 2.5rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #f0f0f0;
}

h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #3a3a3a;
  margin-top: 2rem;
  margin-bottom: 0.75rem;
}

p {
  margin-bottom: 1.5rem;
  font-size: 1.05rem;
}

a {
  color: #0056b3;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s;
}

a:hover {
  border-bottom-color: #0056b3;
}

blockquote {
  border-left: 4px solid #0056b3;
  padding: 0.5rem 1.5rem;
  margin: 1.5rem 0;
  background-color: #f8f9fa;
  color: #495057;
}

code {
  background: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Consolas', 'Liberation Mono', monospace;
  font-size: 0.9rem;
  color: #212529;
}

ul, ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
}

li {
  margin-bottom: 0.75rem;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 2rem auto;
  border: 1px solid #eee;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  border: 1px solid #dee2e6;
}

th, td {
  padding: 0.75rem;
  border: 1px solid #dee2e6;
  text-align: left;
}

th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #212529;
}

@media (max-width: 768px) {
  body {
    padding: 1rem;
  }
  
  h1 {
    font-size: 2.2rem;
  }
  
  h2 {
    font-size: 1.6rem;
  }
  
  h3 {
    font-size: 1.3rem;
  }
}