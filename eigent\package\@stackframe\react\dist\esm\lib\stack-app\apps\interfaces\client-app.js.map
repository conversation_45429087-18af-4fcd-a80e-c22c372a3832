{"version": 3, "sources": ["../../../../../../src/lib/stack-app/apps/interfaces/client-app.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { KnownErrors } from \"@stackframe/stack-shared\";\nimport { CurrentUserCrud } from \"@stackframe/stack-shared/dist/interface/crud/current-user\";\nimport { Result } from \"@stackframe/stack-shared/dist/utils/results\";\nimport { AsyncStoreProperty, GetUserOptions, HandlerUrls, OAuthScopesOnSignIn, RedirectMethod, RedirectToOptions, TokenStoreInit, stackAppInternalsSymbol } from \"../../common\";\nimport { Project } from \"../../projects\";\nimport { ProjectCurrentUser } from \"../../users\";\nimport { _StackClientAppImpl } from \"../implementations\";\n\n\nexport type StackClientAppConstructorOptions<HasTokenStore extends boolean, ProjectId extends string> = {\n  baseUrl?: string | { browser: string, server: string },\n  extraRequestHeaders?: Record<string, string>,\n  projectId?: ProjectId,\n  publishableClientKey?: string,\n  urls?: Partial<HandlerUrls>,\n  oauthScopesOnSignIn?: Partial<OAuthScopesOnSignIn>,\n  tokenStore: TokenStoreInit<HasTokenStore>,\n  redirectMethod?: RedirectMethod,\n\n  /**\n   * By default, the Stack app will automatically prefetch some data from Stack's server when this app is first\n   * constructed. This improves the performance of your app, but will create network requests that are unnecessary if\n   * the app is never used or disposed of immediately. To disable this behavior, set this option to true.\n   */\n  noAutomaticPrefetch?: boolean,\n};\n\n\nexport type StackClientAppJson<HasTokenStore extends boolean, ProjectId extends string> = StackClientAppConstructorOptions<HasTokenStore, ProjectId> & {\n  uniqueIdentifier: string,\n  // note: if you add more fields here, make sure to ensure the checkString in the constructor has/doesn't have them\n};\n\nexport type StackClientApp<HasTokenStore extends boolean = boolean, ProjectId extends string = string> = (\n  & {\n    readonly projectId: ProjectId,\n\n    readonly urls: Readonly<HandlerUrls>,\n\n    signInWithOAuth(provider: string): Promise<void>,\n    signInWithCredential(options: { email: string, password: string, noRedirect?: boolean }): Promise<Result<undefined, KnownErrors[\"EmailPasswordMismatch\"] | KnownErrors[\"InvalidTotpCode\"]>>,\n    signUpWithCredential(options: { email: string, password: string, noRedirect?: boolean, verificationCallbackUrl?: string }): Promise<Result<undefined, KnownErrors[\"UserWithEmailAlreadyExists\"] | KnownErrors[\"PasswordRequirementsNotMet\"]>>,\n    signInWithPasskey(): Promise<Result<undefined, KnownErrors[\"PasskeyAuthenticationFailed\"]| KnownErrors[\"InvalidTotpCode\"] | KnownErrors[\"PasskeyWebAuthnError\"]>>,\n    callOAuthCallback(): Promise<boolean>,\n    promptCliLogin(options: { appUrl: string, expiresInMillis?: number }): Promise<Result<string, KnownErrors[\"CliAuthError\"] | KnownErrors[\"CliAuthExpiredError\"] | KnownErrors[\"CliAuthUsedError\"]>>,\n    sendForgotPasswordEmail(email: string, options?: { callbackUrl?: string }): Promise<Result<undefined, KnownErrors[\"UserNotFound\"]>>,\n    sendMagicLinkEmail(email: string, options?: { callbackUrl?: string }): Promise<Result<{ nonce: string }, KnownErrors[\"RedirectUrlNotWhitelisted\"]>>,\n    resetPassword(options: { code: string, password: string }): Promise<Result<undefined, KnownErrors[\"VerificationCodeError\"]>>,\n    verifyPasswordResetCode(code: string): Promise<Result<undefined, KnownErrors[\"VerificationCodeError\"]>>,\n    verifyTeamInvitationCode(code: string): Promise<Result<undefined, KnownErrors[\"VerificationCodeError\"]>>,\n    acceptTeamInvitation(code: string): Promise<Result<undefined, KnownErrors[\"VerificationCodeError\"]>>,\n    getTeamInvitationDetails(code: string): Promise<Result<{ teamDisplayName: string }, KnownErrors[\"VerificationCodeError\"]>>,\n    verifyEmail(code: string): Promise<Result<undefined, KnownErrors[\"VerificationCodeError\"]>>,\n    signInWithMagicLink(code: string, options?: { noRedirect?: boolean }): Promise<Result<undefined, KnownErrors[\"VerificationCodeError\"] | KnownErrors[\"InvalidTotpCode\"]>>,\n\n    redirectToOAuthCallback(): Promise<void>,\n\n    useUser(options: GetUserOptions<HasTokenStore> & { or: 'redirect' }): ProjectCurrentUser<ProjectId>,\n    useUser(options: GetUserOptions<HasTokenStore> & { or: 'throw' }): ProjectCurrentUser<ProjectId>,\n    useUser(options: GetUserOptions<HasTokenStore> & { or: 'anonymous' }): ProjectCurrentUser<ProjectId>,\n    useUser(options?: GetUserOptions<HasTokenStore>): ProjectCurrentUser<ProjectId> | null,\n\n    getUser(options: GetUserOptions<HasTokenStore> & { or: 'redirect' }): Promise<ProjectCurrentUser<ProjectId>>,\n    getUser(options: GetUserOptions<HasTokenStore> & { or: 'throw' }): Promise<ProjectCurrentUser<ProjectId>>,\n    getUser(options: GetUserOptions<HasTokenStore> & { or: 'anonymous' }): Promise<ProjectCurrentUser<ProjectId>>,\n    getUser(options?: GetUserOptions<HasTokenStore>): Promise<ProjectCurrentUser<ProjectId> | null>,\n\n    useNavigate(): (to: string) => void, // THIS_LINE_PLATFORM react-like\n\n    [stackAppInternalsSymbol]: {\n      toClientJson(): StackClientAppJson<HasTokenStore, ProjectId>,\n      setCurrentUser(userJsonPromise: Promise<CurrentUserCrud['Client']['Read'] | null>): void,\n    },\n  }\n  & AsyncStoreProperty<\"project\", [], Project, false>\n  & { [K in `redirectTo${Capitalize<keyof Omit<HandlerUrls, 'handler' | 'oauthCallback'>>}`]: (options?: RedirectToOptions) => Promise<void> }\n);\nexport type StackClientAppConstructor = {\n  new <\n    TokenStoreType extends string,\n    HasTokenStore extends (TokenStoreType extends {} ? true : boolean),\n    ProjectId extends string\n  >(options: StackClientAppConstructorOptions<HasTokenStore, ProjectId>): StackClientApp<HasTokenStore, ProjectId>,\n  new (options: StackClientAppConstructorOptions<boolean, string>): StackClientApp<boolean, string>,\n\n  [stackAppInternalsSymbol]: {\n    fromClientJson<HasTokenStore extends boolean, ProjectId extends string>(\n      json: StackClientAppJson<HasTokenStore, ProjectId>\n    ): StackClientApp<HasTokenStore, ProjectId>,\n  },\n};\nexport const StackClientApp: StackClientAppConstructor = _StackClientAppImpl;\n"], "mappings": ";AAUA,SAAS,2BAA2B;AAqF7B,IAAM,iBAA4C;", "names": []}