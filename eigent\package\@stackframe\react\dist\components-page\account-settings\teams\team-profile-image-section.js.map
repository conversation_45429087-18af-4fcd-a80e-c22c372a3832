{"version": 3, "sources": ["../../../../src/components-page/account-settings/teams/team-profile-image-section.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { Team } from \"../../..\";\nimport { ProfileImageEditor } from \"../../../components/profile-image-editor\";\nimport { useUser } from \"../../../lib/hooks\";\nimport { useTranslation } from \"../../../lib/translations\";\nimport { Section } from \"../section\";\n\nexport function TeamProfileImageSection(props: { team: Team }) {\n  const { t } = useTranslation();\n  const user = useUser({ or: 'redirect' });\n  const updateTeamPermission = user.usePermission(props.team, '$update_team');\n\n  if (!updateTeamPermission) {\n    return null;\n  }\n\n  return (\n    <Section\n      title={t(\"Team profile image\")}\n      description={t(\"Upload an image for your team\")}\n    >\n      <ProfileImageEditor\n        user={props.team}\n        onProfileImageUrlChange={async (profileImageUrl) => {\n          await props.team.update({ profileImageUrl });\n        }}\n      />\n    </Section>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,kCAAmC;AACnC,mBAAwB;AACxB,0BAA+B;AAC/B,qBAAwB;AAgBlB;AAdC,SAAS,wBAAwB,OAAuB;AAC7D,QAAM,EAAE,EAAE,QAAI,oCAAe;AAC7B,QAAM,WAAO,sBAAQ,EAAE,IAAI,WAAW,CAAC;AACvC,QAAM,uBAAuB,KAAK,cAAc,MAAM,MAAM,cAAc;AAE1E,MAAI,CAAC,sBAAsB;AACzB,WAAO;AAAA,EACT;AAEA,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,+BAA+B;AAAA,MAE9C;AAAA,QAAC;AAAA;AAAA,UACC,MAAM,MAAM;AAAA,UACZ,yBAAyB,OAAO,oBAAoB;AAClD,kBAAM,MAAM,KAAK,OAAO,EAAE,gBAAgB,CAAC;AAAA,UAC7C;AAAA;AAAA,MACF;AAAA;AAAA,EACF;AAEJ;", "names": []}