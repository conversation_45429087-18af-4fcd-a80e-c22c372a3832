{"version": 3, "sources": ["../../../src/components/credential-sign-in.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { yupResolver } from \"@hookform/resolvers/yup\";\nimport { passwordSchema, strictEmailSchema, yupObject } from \"@stackframe/stack-shared/dist/schema-fields\";\nimport { runAsynchronouslyWithAlert } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { Button, Input, Label, PasswordInput } from \"@stackframe/stack-ui\";\nimport { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport * as yup from \"yup\";\nimport { useStackApp } from \"..\";\nimport { useTranslation } from \"../lib/translations\";\nimport { FormWarningText } from \"./elements/form-warning\";\nimport { StyledLink } from \"./link\";\n\nexport function CredentialSignIn() {\n  const { t } = useTranslation();\n\n  const schema = yupObject({\n    email: strictEmailSchema(t('Please enter a valid email')).defined().nonEmpty(t('Please enter your email')),\n    password: passwordSchema.defined().nonEmpty(t('Please enter your password'))\n  });\n\n  const { register, handleSubmit, setError, formState: { errors } } = useForm({\n    resolver: yupResolver(schema)\n  });\n  const app = useStackApp();\n  const [loading, setLoading] = useState(false);\n\n  const onSubmit = async (data: yup.InferType<typeof schema>) => {\n    setLoading(true);\n\n    try {\n      const { email, password } = data;\n      const result = await app.signInWithCredential({\n        email,\n        password,\n      });\n      if (result.status === 'error') {\n        setError('email', { type: 'manual', message: result.error.message });\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <form\n      className=\"flex flex-col items-stretch stack-scope\"\n      onSubmit={e => runAsynchronouslyWithAlert(handleSubmit(onSubmit)(e))}\n      noValidate\n    >\n      <Label htmlFor=\"email\" className=\"mb-1\">{t('Email')}</Label>\n      <Input\n        id=\"email\"\n        type=\"email\"\n        autoComplete=\"email\"\n        {...register('email')}\n      />\n      <FormWarningText text={errors.email?.message?.toString()} />\n\n      <Label htmlFor=\"password\" className=\"mt-4 mb-1\">{t('Password')}</Label>\n      <PasswordInput\n        id=\"password\"\n        autoComplete=\"current-password\"\n        {...register('password')}\n      />\n      <FormWarningText text={errors.password?.message?.toString()} />\n\n      <StyledLink href={app.urls.forgotPassword} className=\"mt-1 text-sm\">\n        {t('Forgot password?')}\n      </StyledLink>\n\n      <Button type=\"submit\" className=\"mt-6\" loading={loading}>\n        {t('Sign In')}\n      </Button>\n    </form>\n  );\n}\n"], "mappings": ";;;AAOA,SAAS,mBAAmB;AAC5B,SAAS,gBAAgB,mBAAmB,iBAAiB;AAC7D,SAAS,kCAAkC;AAC3C,SAAS,QAAQ,OAAO,OAAO,qBAAqB;AACpD,SAAS,gBAAgB;AACzB,SAAS,eAAe;AAExB,SAAS,mBAAmB;AAC5B,SAAS,sBAAsB;AAC/B,SAAS,uBAAuB;AAChC,SAAS,kBAAkB;AAkCvB,SAKE,KALF;AAhCG,SAAS,mBAAmB;AACjC,QAAM,EAAE,EAAE,IAAI,eAAe;AAE7B,QAAM,SAAS,UAAU;AAAA,IACvB,OAAO,kBAAkB,EAAE,4BAA4B,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,yBAAyB,CAAC;AAAA,IACzG,UAAU,eAAe,QAAQ,EAAE,SAAS,EAAE,4BAA4B,CAAC;AAAA,EAC7E,CAAC;AAED,QAAM,EAAE,UAAU,cAAc,UAAU,WAAW,EAAE,OAAO,EAAE,IAAI,QAAQ;AAAA,IAC1E,UAAU,YAAY,MAAM;AAAA,EAC9B,CAAC;AACD,QAAM,MAAM,YAAY;AACxB,QAAM,CAAC,SAAS,UAAU,IAAI,SAAS,KAAK;AAE5C,QAAM,WAAW,OAAO,SAAuC;AAC7D,eAAW,IAAI;AAEf,QAAI;AACF,YAAM,EAAE,OAAO,SAAS,IAAI;AAC5B,YAAM,SAAS,MAAM,IAAI,qBAAqB;AAAA,QAC5C;AAAA,QACA;AAAA,MACF,CAAC;AACD,UAAI,OAAO,WAAW,SAAS;AAC7B,iBAAS,SAAS,EAAE,MAAM,UAAU,SAAS,OAAO,MAAM,QAAQ,CAAC;AAAA,MACrE;AAAA,IACF,UAAE;AACA,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AAEA,SACE;AAAA,IAAC;AAAA;AAAA,MACC,WAAU;AAAA,MACV,UAAU,OAAK,2BAA2B,aAAa,QAAQ,EAAE,CAAC,CAAC;AAAA,MACnE,YAAU;AAAA,MAEV;AAAA,4BAAC,SAAM,SAAQ,SAAQ,WAAU,QAAQ,YAAE,OAAO,GAAE;AAAA,QACpD;AAAA,UAAC;AAAA;AAAA,YACC,IAAG;AAAA,YACH,MAAK;AAAA,YACL,cAAa;AAAA,YACZ,GAAG,SAAS,OAAO;AAAA;AAAA,QACtB;AAAA,QACA,oBAAC,mBAAgB,MAAM,OAAO,OAAO,SAAS,SAAS,GAAG;AAAA,QAE1D,oBAAC,SAAM,SAAQ,YAAW,WAAU,aAAa,YAAE,UAAU,GAAE;AAAA,QAC/D;AAAA,UAAC;AAAA;AAAA,YACC,IAAG;AAAA,YACH,cAAa;AAAA,YACZ,GAAG,SAAS,UAAU;AAAA;AAAA,QACzB;AAAA,QACA,oBAAC,mBAAgB,MAAM,OAAO,UAAU,SAAS,SAAS,GAAG;AAAA,QAE7D,oBAAC,cAAW,MAAM,IAAI,KAAK,gBAAgB,WAAU,gBAClD,YAAE,kBAAkB,GACvB;AAAA,QAEA,oBAAC,UAAO,MAAK,UAAS,WAAU,QAAO,SACpC,YAAE,SAAS,GACd;AAAA;AAAA;AAAA,EACF;AAEJ;", "names": []}