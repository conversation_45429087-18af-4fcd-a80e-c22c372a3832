{"version": 3, "sources": ["../../../../src/components-page/account-settings/section.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { Separator, Typography } from \"@stackframe/stack-ui\";\n\nexport function Section(props: { title: string, description?: string, children: React.ReactNode }) {\n  return (\n    <>\n      <Separator />\n      <div className='flex flex-col sm:flex-row gap-2'>\n        <div className='sm:flex-1 flex flex-col justify-center'>\n          <Typography className='font-medium'>\n            {props.title}\n          </Typography>\n          {props.description && <Typography variant='secondary' type='footnote'>\n            {props.description}\n          </Typography>}\n        </div>\n        <div className='sm:flex-1 sm:items-end flex flex-col gap-2 '>\n          {props.children}\n        </div>\n      </div>\n    </>\n  );\n}\n"], "mappings": ";AAIA,SAAS,WAAW,kBAAkB;AAIlC,mBACE,KAEE,YAHJ;AAFG,SAAS,QAAQ,OAA2E;AACjG,SACE,iCACE;AAAA,wBAAC,aAAU;AAAA,IACX,qBAAC,SAAI,WAAU,mCACb;AAAA,2BAAC,SAAI,WAAU,0CACb;AAAA,4BAAC,cAAW,WAAU,eACnB,gBAAM,OACT;AAAA,QACC,MAAM,eAAe,oBAAC,cAAW,SAAQ,aAAY,MAAK,YACxD,gBAAM,aACT;AAAA,SACF;AAAA,MACA,oBAAC,SAAI,WAAU,+CACZ,gBAAM,UACT;AAAA,OACF;AAAA,KACF;AAEJ;", "names": []}