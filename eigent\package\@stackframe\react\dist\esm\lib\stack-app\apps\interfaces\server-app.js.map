{"version": 3, "sources": ["../../../../../../src/lib/stack-app/apps/interfaces/server-app.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { AsyncStoreProperty, GetUserOptions } from \"../../common\";\nimport { ServerListUsersOptions, ServerTeam, ServerTeamCreateOptions } from \"../../teams\";\nimport { ProjectCurrentServerUser, ServerUser, ServerUserCreateOptions } from \"../../users\";\nimport { _StackServerAppImpl } from \"../implementations\";\nimport { StackClientApp, StackClientAppConstructorOptions } from \"./client-app\";\n\n\nexport type StackServerAppConstructorOptions<HasTokenStore extends boolean, ProjectId extends string> = StackClientAppConstructorOptions<HasTokenStore, ProjectId> & {\n  secretServerKey?: string,\n};\n\nexport type StackServerApp<HasTokenStore extends boolean = boolean, ProjectId extends string = string> = (\n  & {\n    createTeam(data: ServerTeamCreateOptions): Promise<ServerTeam>,\n    /**\n     * @deprecated use `getUser()` instead\n     */\n    getServerUser(): Promise<ProjectCurrentServerUser<ProjectId> | null>,\n\n    createUser(options: ServerUserCreateOptions): Promise<ServerUser>,\n\n    useUser(options: GetUserOptions<HasTokenStore> & { or: 'redirect' }): ProjectCurrentServerUser<ProjectId>,\n    useUser(options: GetUserOptions<HasTokenStore> & { or: 'throw' }): ProjectCurrentServerUser<ProjectId>,\n    useUser(options: GetUserOptions<HasTokenStore> & { or: 'anonymous' }): ProjectCurrentServerUser<ProjectId>,\n    useUser(options?: GetUserOptions<HasTokenStore>): ProjectCurrentServerUser<ProjectId> | null,\n    useUser(id: string): ServerUser | null,\n    useUser(options: { apiKey: string }): ServerUser | null,\n\n    getUser(options: GetUserOptions<HasTokenStore> & { or: 'redirect' }): Promise<ProjectCurrentServerUser<ProjectId>>,\n    getUser(options: GetUserOptions<HasTokenStore> & { or: 'throw' }): Promise<ProjectCurrentServerUser<ProjectId>>,\n    getUser(options: GetUserOptions<HasTokenStore> & { or: 'anonymous' }): Promise<ProjectCurrentServerUser<ProjectId>>,\n    getUser(options?: GetUserOptions<HasTokenStore>): Promise<ProjectCurrentServerUser<ProjectId> | null>,\n    getUser(id: string): Promise<ServerUser | null>,\n    getUser(options: { apiKey: string }): Promise<ServerUser | null>,\n\n    useTeam(id: string): ServerTeam | null,\n    useTeam(options: { apiKey: string }): ServerTeam | null,\n\n    getTeam(id: string): Promise<ServerTeam | null>,\n    getTeam(options: { apiKey: string }): Promise<ServerTeam | null>,\n\n\n    useUsers(options?: ServerListUsersOptions): ServerUser[] & { nextCursor: string | null }, // THIS_LINE_PLATFORM react-like\n    listUsers(options?: ServerListUsersOptions): Promise<ServerUser[] & { nextCursor: string | null }>,\n  }\n  & AsyncStoreProperty<\"user\", [id: string], ServerUser | null, false>\n  & Omit<AsyncStoreProperty<\"users\", [], ServerUser[], true>, \"listUsers\" | \"useUsers\">\n  & AsyncStoreProperty<\"teams\", [], ServerTeam[], true>\n  & StackClientApp<HasTokenStore, ProjectId>\n);\nexport type StackServerAppConstructor = {\n  new <\n    TokenStoreType extends string,\n    HasTokenStore extends (TokenStoreType extends {} ? true : boolean),\n    ProjectId extends string\n  >(options: StackServerAppConstructorOptions<HasTokenStore, ProjectId>): StackServerApp<HasTokenStore, ProjectId>,\n  new (options: StackServerAppConstructorOptions<boolean, string>): StackServerApp<boolean, string>,\n};\nexport const StackServerApp: StackServerAppConstructor = _StackServerAppImpl;\n"], "mappings": ";AAOA,SAAS,2BAA2B;AAuD7B,IAAM,iBAA4C;", "names": []}