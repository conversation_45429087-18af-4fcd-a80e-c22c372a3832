{"version": 3, "sources": ["../../../../src/interface/crud/internal-api-keys.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport { yupBoolean, yupMixed, yupNumber, yupObject, yupString } from \"../../schema-fields\";\n\nconst baseInternalApiKeysReadSchema = yupObject({\n  id: yupString().defined(),\n  description: yupString().defined(),\n  expires_at_millis: yupNumber().defined(),\n  manually_revoked_at_millis: yupNumber().optional(),\n  created_at_millis: yupNumber().defined(),\n});\n\n// Used for the result of the create endpoint\nexport const internalApiKeysCreateInputSchema = yupObject({\n  description: yupString().defined(),\n  expires_at_millis: yupNumber().defined(),\n  has_publishable_client_key: yupBoolean().defined(),\n  has_secret_server_key: yupBoolean().defined(),\n  has_super_secret_admin_key: yupBoolean().defined(),\n});\n\nexport const internalApiKeysCreateOutputSchema = baseInternalApiKeysReadSchema.concat(yupObject({\n  publishable_client_key: yupString().optional(),\n  secret_server_key: yupString().optional(),\n  super_secret_admin_key: yupString().optional(),\n}).defined());\n\n// Used for list, read and update endpoints after the initial creation\nexport const internalApiKeysCrudAdminObfuscatedReadSchema = baseInternalApiKeysReadSchema.concat(yupObject({\n  publishable_client_key: yupObject({\n    last_four: yupString().defined(),\n  }).optional(),\n  secret_server_key: yupObject({\n    last_four: yupString().defined(),\n  }).optional(),\n  super_secret_admin_key: yupObject({\n    last_four: yupString().defined(),\n  }).optional(),\n}));\n\nexport const internalApiKeysCrudAdminUpdateSchema = yupObject({\n  description: yupString().optional(),\n  revoked: yupBoolean().oneOf([true]).optional(),\n}).defined();\n\nexport const internalApiKeysCrudAdminDeleteSchema = yupMixed();\n\nexport const internalApiKeysCrud = createCrud({\n  adminReadSchema: internalApiKeysCrudAdminObfuscatedReadSchema,\n  adminUpdateSchema: internalApiKeysCrudAdminUpdateSchema,\n  adminDeleteSchema: internalApiKeysCrudAdminDeleteSchema,\n  docs: {\n    adminList: {\n      hidden: true,\n    },\n    adminRead: {\n      hidden: true,\n    },\n    adminCreate: {\n      hidden: true,\n    },\n    adminUpdate: {\n      hidden: true,\n    },\n    adminDelete: {\n      hidden: true,\n    },\n  },\n});\nexport type InternalApiKeysCrud = CrudTypeOf<typeof internalApiKeysCrud>;\n"], "mappings": ";AAAA,SAAqB,kBAAkB;AACvC,SAAS,YAAY,UAAU,WAAW,WAAW,iBAAiB;AAEtE,IAAM,gCAAgC,UAAU;AAAA,EAC9C,IAAI,UAAU,EAAE,QAAQ;AAAA,EACxB,aAAa,UAAU,EAAE,QAAQ;AAAA,EACjC,mBAAmB,UAAU,EAAE,QAAQ;AAAA,EACvC,4BAA4B,UAAU,EAAE,SAAS;AAAA,EACjD,mBAAmB,UAAU,EAAE,QAAQ;AACzC,CAAC;AAGM,IAAM,mCAAmC,UAAU;AAAA,EACxD,aAAa,UAAU,EAAE,QAAQ;AAAA,EACjC,mBAAmB,UAAU,EAAE,QAAQ;AAAA,EACvC,4BAA4B,WAAW,EAAE,QAAQ;AAAA,EACjD,uBAAuB,WAAW,EAAE,QAAQ;AAAA,EAC5C,4BAA4B,WAAW,EAAE,QAAQ;AACnD,CAAC;AAEM,IAAM,oCAAoC,8BAA8B,OAAO,UAAU;AAAA,EAC9F,wBAAwB,UAAU,EAAE,SAAS;AAAA,EAC7C,mBAAmB,UAAU,EAAE,SAAS;AAAA,EACxC,wBAAwB,UAAU,EAAE,SAAS;AAC/C,CAAC,EAAE,QAAQ,CAAC;AAGL,IAAM,+CAA+C,8BAA8B,OAAO,UAAU;AAAA,EACzG,wBAAwB,UAAU;AAAA,IAChC,WAAW,UAAU,EAAE,QAAQ;AAAA,EACjC,CAAC,EAAE,SAAS;AAAA,EACZ,mBAAmB,UAAU;AAAA,IAC3B,WAAW,UAAU,EAAE,QAAQ;AAAA,EACjC,CAAC,EAAE,SAAS;AAAA,EACZ,wBAAwB,UAAU;AAAA,IAChC,WAAW,UAAU,EAAE,QAAQ;AAAA,EACjC,CAAC,EAAE,SAAS;AACd,CAAC,CAAC;AAEK,IAAM,uCAAuC,UAAU;AAAA,EAC5D,aAAa,UAAU,EAAE,SAAS;AAAA,EAClC,SAAS,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS;AAC/C,CAAC,EAAE,QAAQ;AAEJ,IAAM,uCAAuC,SAAS;AAEtD,IAAM,sBAAsB,WAAW;AAAA,EAC5C,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,MAAM;AAAA,IACJ,WAAW;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,IACA,aAAa;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,IACA,aAAa;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,IACA,aAAa;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,EACF;AACF,CAAC;", "names": []}