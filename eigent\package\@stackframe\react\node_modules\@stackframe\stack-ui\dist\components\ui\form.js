'use client';
import { jsx as _jsx } from "react/jsx-runtime";
import { Slot } from "@radix-ui/react-slot";
import React from "react";
import { forwardRefIfNeeded } from "@stackframe/stack-shared/dist/utils/react";
import { Controller, FormProvider, useFormContext, } from "react-hook-form";
import { StackAssertionError } from "@stackframe/stack-shared/dist/utils/errors";
import { cn } from "../../lib/utils";
import { SpanLabel } from "./label";
const Form = FormProvider;
const FormFieldContext = React.createContext(null);
const FormField = ({ ...props }) => {
    return (_jsx(FormFieldContext.Provider, { value: { name: props.name }, children: _jsx(Controller, { ...props }) }));
};
const useFormField = () => {
    const fieldContext = React.useContext(FormFieldContext);
    const itemContext = React.useContext(FormItemContext);
    const { getFieldState, formState } = useFormContext();
    if (!fieldContext) {
        throw new StackAssertionError("useFormField should be used within <FormField>");
    }
    const fieldState = getFieldState(fieldContext.name, formState);
    const { id } = itemContext;
    const formItemId = React.useId();
    return {
        id,
        name: fieldContext.name,
        formItemId: `${formItemId}-form-item`,
        formDescriptionId: `${id}-form-item-description`,
        formMessageId: `${id}-form-item-message`,
        ...fieldState,
    };
};
const FormItemContext = React.createContext({});
const FormItem = forwardRefIfNeeded(({ className, ...props }, ref) => {
    const id = React.useId();
    return (_jsx(FormItemContext.Provider, { value: { id }, children: _jsx("div", { ref: ref, className: cn("space-y-2", className), ...props }) }));
});
FormItem.displayName = "FormItem";
const FormLabel = forwardRefIfNeeded(({ className, ...props }, ref) => {
    const { error } = useFormField();
    return (_jsx(SpanLabel, { ref: ref, className: cn(error && "text-destructive", className), ...props }));
});
FormLabel.displayName = "FormLabel";
const FormControl = forwardRefIfNeeded(({ ...props }, ref) => {
    const { error, formItemId, formDescriptionId, formMessageId } = useFormField();
    return (_jsx(Slot, { ref: ref, id: formItemId, "aria-describedby": !error
            ? `${formDescriptionId}`
            : `${formDescriptionId} ${formMessageId}`, "aria-invalid": !!error, ...props }));
});
FormControl.displayName = "FormControl";
const FormDescription = forwardRefIfNeeded(({ className, ...props }, ref) => {
    const { formDescriptionId } = useFormField();
    return (_jsx("p", { ref: ref, id: formDescriptionId, className: cn("text-[0.8rem] text-muted-foreground", className), ...props }));
});
FormDescription.displayName = "FormDescription";
const FormMessage = forwardRefIfNeeded(({ className, children, ...props }, ref) => {
    const { error, formMessageId } = useFormField();
    const body = error ? String(error.message) : children;
    if (!body) {
        return null;
    }
    return (_jsx("p", { ref: ref, id: formMessageId, className: cn("text-[0.8rem] font-medium text-destructive", className), ...props, children: body }));
});
FormMessage.displayName = "FormMessage";
export { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage, useFormField };
