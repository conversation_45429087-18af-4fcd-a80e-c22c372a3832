{"version": 3, "sources": ["../../../../src/components-page/account-settings/teams/leave-team-section.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { Button, Typography } from \"@stackframe/stack-ui\";\nimport { useState } from \"react\";\nimport { Team } from \"../../..\";\nimport { useUser } from \"../../../lib/hooks\";\nimport { useTranslation } from \"../../../lib/translations\";\nimport { Section } from \"../section\";\n\nexport function LeaveTeamSection(props: { team: Team }) {\n  const { t } = useTranslation();\n  const user = useUser({ or: 'redirect' });\n  const [leaving, setLeaving] = useState(false);\n\n  return (\n    <Section\n      title={t(\"Leave Team\")}\n      description={t(\"leave this team and remove your team profile\")}\n    >\n      {!leaving ? (\n        <div>\n          <Button\n            variant='secondary'\n            onClick={() => setLeaving(true)}\n          >\n            {t(\"Leave team\")}\n          </Button>\n        </div>\n      ) : (\n        <div className='flex flex-col gap-2'>\n          <Typography variant='destructive'>\n            {t(\"Are you sure you want to leave the team?\")}\n          </Typography>\n          <div className='flex gap-2'>\n            <Button\n              variant='destructive'\n              onClick={async () => {\n                await user.leaveTeam(props.team);\n                window.location.reload();\n              }}\n            >\n              {t(\"Leave\")}\n            </Button>\n            <Button\n              variant='secondary'\n              onClick={() => setLeaving(false)}\n            >\n              {t(\"Cancel\")}\n            </Button>\n          </div>\n        </div>\n      )}\n    </Section>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,sBAAmC;AACnC,mBAAyB;AAEzB,mBAAwB;AACxB,0BAA+B;AAC/B,qBAAwB;AAcd;AAZH,SAAS,iBAAiB,OAAuB;AACtD,QAAM,EAAE,EAAE,QAAI,oCAAe;AAC7B,QAAM,WAAO,sBAAQ,EAAE,IAAI,WAAW,CAAC;AACvC,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAS,KAAK;AAE5C,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO,EAAE,YAAY;AAAA,MACrB,aAAa,EAAE,8CAA8C;AAAA,MAE5D,WAAC,UACA,4CAAC,SACC;AAAA,QAAC;AAAA;AAAA,UACC,SAAQ;AAAA,UACR,SAAS,MAAM,WAAW,IAAI;AAAA,UAE7B,YAAE,YAAY;AAAA;AAAA,MACjB,GACF,IAEA,6CAAC,SAAI,WAAU,uBACb;AAAA,oDAAC,8BAAW,SAAQ,eACjB,YAAE,0CAA0C,GAC/C;AAAA,QACA,6CAAC,SAAI,WAAU,cACb;AAAA;AAAA,YAAC;AAAA;AAAA,cACC,SAAQ;AAAA,cACR,SAAS,YAAY;AACnB,sBAAM,KAAK,UAAU,MAAM,IAAI;AAC/B,uBAAO,SAAS,OAAO;AAAA,cACzB;AAAA,cAEC,YAAE,OAAO;AAAA;AAAA,UACZ;AAAA,UACA;AAAA,YAAC;AAAA;AAAA,cACC,SAAQ;AAAA,cACR,SAAS,MAAM,WAAW,KAAK;AAAA,cAE9B,YAAE,QAAQ;AAAA;AAAA,UACb;AAAA,WACF;AAAA,SACF;AAAA;AAAA,EAEJ;AAEJ;", "names": []}