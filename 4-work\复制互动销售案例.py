import csv
import requests

url = 'https://o.fkw.com/designerCenter/ajax/case_h.jsp?cmd=getCaseList&_TOKEN=41b11c1ea103ffca736d3e4b2d4cbc6b'
cookie = '_cliid=b4XJGyEyxsjM8TJz; first_ta=207; _ta=207; _tp=-; _newUnion=0; _kw=0; _s_pro=hd.fkw.com%2F; _c_pro=hd.fkw.com%2F; reg_sid=0; innerFlag=1; _faiHeDistictId=6613a76db8cb6d8d; grayUrl=; _pykey_=4250b6c7-c78d-5be1-8d84-d8b6aa28058e; _jzqx=1.1753933518.1753933518.1.jzqsr=hd%2Efkw%2Ecom|jzqct=/.-; _jzqa=1.1950796457289892400.1753665619.1753933518.1753950750.3; defaultAreaId32921243=0; defaultStoreId32921243=0; hdTopBarUpdateVer=-; hdTopBarMyNewsVer=-; mgCaseType=11; _vid_url=https%3A%2F%2Fwww.fkw.com%2F; Hm_lvt_26126ee052ae4ad30a36da928aff7654=1753260814,1753336765,1753685388,1754357869; enterView541351=true; defaultAreaId541351=0; defaultStoreId541351=0; loginCacct=faisco; loginCaid=1; loginSacct=byron; loginUseSacct=1; _FSESSIONID=MTBxFhZCxcVQYWEJ; __tmpaid=16384946; defaultAreaId16384946=0; defaultStoreId16384946=0; buyout=0; oid_16384946_664=manager; visitorA=0.8516676264028512; caseFlag=2048'
hd_data = {
    "webType": 1,
    "collectType": 0,
    "caseType": 11,
    "page": 1,
    "pageSize": 100,
    "searchType": 0,
    "searchWord": "",
    "sortList": [],
    "status": "",
    "version": "",
    "trade": "",
    "grade": "",
    "resource": "",
    "playType": "",
    "festival": "",
    "scene": "",
    "gameStyle": 80,
    "gameStartTimeBegin": "2023-01-01",
    "gameStartTimeEnd": "2025-07-31",
    "sitePackage": ""
}
cd_data = {
    "webType": 1,
    "collectType": 0,
    "caseType": 12,
    "page": 1,
    "pageSize": 100,
    "searchType": 0,
    "searchWord": "景区",
    "sortList": [],
    "status": "",
    "version": "",
    "grade": "",
    "resource": "",
    "cdTrade": "",
    "cdFestival": "",
    "cdScene": "",
    "cdFuncPage": "",
    "sitePackage": "",
}
headers = {
    'Referer': 'https://o.fkw.com/designerCenter/case.jsp?webType=1',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.88 Safari/537.36',
    'cookie':cookie,
}
res = requests.post(url, headers=headers, data=hd_data)
tree = res.json()
a = tree['list']
d = []

for k in a:
    t = [k.get('grade', ""), k.get('name', ""), k.get('templateAcct', ""), k.get('trade', ""), k.get('festival', ""), k['playType'], k.get('scene',""), k.get('int_1', ""), k.get('int_2', ""), k.get('int_3', ""), k['gameStartTime'], k['link'], k['qrCode']]
    # t = [k.get('eGameId', ""), k.get('grade', ""), k.get('name', ""), k.get('templateAcct', ""), k.get('trade', ""), k.get('festival', ""), k['playType'], k.get('scene',""), k.get('int_1', ""), k.get('int_2', ""), k.get('int_3', ""), k['gameStartTime'], k['link'], k['qrCode']]
    # t = [k.get('name', ""), k.get('cdTrade', ""), k.get('cdFestival', ""), k.get('cdScene',""), k['int_1'], k['int_2'], k['link'], k['qrCode']]
    d.append(t)

with open(f"E:/销售案例.csv", "w", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    writer.writerow(["案例等级", "企业名称", "互动模板", "行业", "节日", "玩法", "场景", "浏览人数", "参与人数", "分享人数", "活动开始时间", "活动链接", "活动二维码下载链接"])
    # writer.writerow(["活动id", "案例等级", "企业名称", "互动模板", "行业", "节日", "玩法", "场景", "浏览人数", "参与人数", "分享人数", "活动开始时间", "活动链接", "活动二维码下载链接"])
    for k in d:
        writer.writerow(k)
