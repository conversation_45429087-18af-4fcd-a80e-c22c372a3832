import http.client
import json

# prompt = input("请输入画图提示词：")
prompt = '''
请根据以下YAML规范创建一张城市探索手账插画：
city_info:
  name: "广州"
  slogan: "广州，粤食粤精彩！" # 结合了“食在广州”和城市活力
  theme: "都市活力与岭南风情" # 融合现代与传统

visual_style:
  main_color: "#f5e6bf" # 稍亮的复古米黄底色，体现活力
  accent_color: "#c04000" # 朱砂红文字/点缀色，增加岭南特色和视觉冲击力
  style_description: "复古手绘风格，岭南特色，民国风海报，木刻版画感，暖色调，色彩可以比泉州模板更鲜明一点"

layout:
  orientation: "portrait" # 竖版
  composition: "多景点地标分布，中心突出城市名称'广州'，周围环绕地标、美食和文化元素" # 保持中心构图

scenic_spots:
  - name: "广州塔 (小蛮腰)"
    description: "云山珠水旁，小蛮腰，广州地标!"
    importance: 9 # 现代地标，视觉核心之一
  - name: "陈家祠"
    description: "岭南建筑艺术瑰宝！"
    importance: 8 # 传统建筑代表
  - name: "沙面岛"
    description: "欧陆风情，悠闲漫步！"
    importance: 7 # 特色街区
  - name: "越秀公园五羊石像"
    description: "羊城传说，公园绿意！"
    importance: 6 # 城市象征

cultural_elements:
  - name: "岭南建筑"
    description: "锅耳墙，满洲窗，骑楼街" # 更具体的广州特色
  - name: "粤剧文化"
    description: "锣鼓喧天，唱念做打，睇大戏！"
  - name: "花城"
    description: "四季花开，花城美誉！可加入木棉花元素"

local_food: # 广州代表性美食
  - name: "虾饺"
  - name: "干蒸烧卖"
  - name: "肠粉"
  - name: "艇仔粥"
  - name: "烧鹅"

characters: # 体现广州生活气息
  - type: "早茶客"
    description: "悠闲地拿着报纸叹早茶的老广" # 叹：享受
  - type: "粤剧花旦"
    description: "穿着精致戏服，略施粉黛的花旦"
  - type: "年轻情侣"
    description: "在花城广场或珠江边散步的时尚青年"

design_elements:
  - "用**骑楼**线条或**木棉花**图案作为装饰边框或分隔元素" # 本地化设计元素
  - "复古**邮票**边框效果，营造旅行纪念感"
  - "手绘风格的菜名和地名标签"
  - "可加入'掂'（好）、'猴赛雷'（好厉害）等粤语俚语文字点缀，增加趣味性"
'''
headers = {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer sk-1r4ApCAZjjmjZLUJ9691C2B7946e47669254254c89066a46',
}
for k in ['flux.1.1-pro','flux-pro-max']:   # ['recraftv3','flux.1.1-pro','flux-pro-max']
    conn = http.client.HTTPSConnection("ai98.vip")
    payload = json.dumps({
       "model": k,
       "prompt": prompt,
       "size": "1024x1024"
    })
    conn.request("POST", "/v1/images/generations", payload, headers)
    res = conn.getresponse()
    data = res.read()
    print(data.decode("utf-8"))
