from google.oauth2 import service_account
from googleapiclient.discovery import build
import socket
import socks
import pandas as pd
from datetime import datetime
import time


def create_batch_request(service, urls):
    """
    创建批量请求
    """
    batch = service.new_batch_http_request()

    def callback(request_id, response, exception):
        if exception is not None:
            results[request_id] = {
                'URL': urls[int(request_id)],
                'is_indexed': '检查失败',
                'coverage_state': f'错误: {str(exception)}',
                'last_crawl': None
            }
        else:
            inspection_result = response.get('inspectionResult', {})
            indexing_result = inspection_result.get('indexStatusResult', {})
            coverage_state = indexing_result.get('coverageState')
            is_indexed = coverage_state in ['Indexed', 'Submitted and indexed']

            results[request_id] = {
                'URL': urls[int(request_id)],
                'is_indexed': '是' if is_indexed else '否',
                'coverage_state': coverage_state,
                'last_crawl': indexing_result.get('lastCrawlTime')
            }

    results = {}

    # 为每个URL创建请求并添加到批处理中
    for i, url in enumerate(urls):
        request = {
            'inspectionUrl': url,
            'siteUrl': 'https://faisco.com/'
        }
        batch.add(
            service.urlInspection().index().inspect(body=request),
            callback=callback,
            request_id=str(i)
        )

    return batch, results


def batch_check_urls(urls, credentials_path: str, batch_size=500):
    """
    批量检查URL列表的收录状态
    """
    # 设置代理
    socks.set_default_proxy(socks.SOCKS5, "127.0.0.1", 7897)
    socket.socket = socks.socksocket

    # 加载凭据
    credentials = service_account.Credentials.from_service_account_file(
        credentials_path,
        scopes=['https://www.googleapis.com/auth/webmasters']
    )

    # 创建服务
    service = build('searchconsole', 'v1', credentials=credentials)

    all_results = []
    total = len(urls)

    # 创建结果文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    excel_path = 'E:/indexing_results.xlsx'

    print(f"开始批量检查 {total} 个URL的收录状态...")

    # 分批处理
    for i in range(0, total, batch_size):
        batch_urls = urls[i:i + batch_size]
        print(f"\n处理第 {i + 1}-{min(i + batch_size, total)} 个URL")

        try:
            # 创建批量请求
            batch, results = create_batch_request(service, batch_urls)

            # 执行批量请求
            batch.execute()

            # 收集结果
            sorted_results = [results[str(j)] for j in range(len(batch_urls))]
            all_results.extend(sorted_results)

            # 保存当前批次结果
            df = pd.DataFrame(all_results)
            df.to_excel(excel_path, index=False)
            print(f"已保存当前批次结果到: {excel_path}")

            # 批次间隔
            if i + batch_size < total:
                wait_time = 30
                print(f"批次处理完成，休息{wait_time}秒...")
                time.sleep(wait_time)

        except Exception as e:
            print(f"处理批次时出错: {str(e)}")
            continue

    print(f"\n全部检查完成！最终结果已保存到: {excel_path}")
    return pd.DataFrame(all_results)


if __name__ == "__main__":
    credentials_path = "E:/个人/kezexiong-6265cb231375.json"

    # 从文本文件读取URL列表
    urls_file = "E:/urls.txt"  # 把你的URL列表放在这个文件中，每行一个URL
    with open(urls_file, 'r', encoding='utf-8') as f:
        urls = [line.strip() for line in f if line.strip()]

    # 开始批量检查
    results_df = batch_check_urls(urls, credentials_path)