{"version": 3, "sources": ["../../../src/components/elements/sidebar-layout.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { useHash } from '@stackframe/stack-shared/dist/hooks/use-hash';\nimport { Button, Typography, cn } from '@stackframe/stack-ui';\nimport { XIcon } from 'lucide-react';\nimport React, { ReactNode } from 'react';\nimport { useStackApp } from '../..';\n\nexport type SidebarItem = {\n  title: React.ReactNode,\n  type: 'item' | 'divider',\n  description?: React.ReactNode,\n  id?: string,\n  icon?: React.ReactNode,\n  content?: React.ReactNode,\n  contentTitle?: React.ReactNode,\n}\n\nexport function SidebarLayout(props: { items: SidebarItem[], title?: ReactNode, className?: string }) {\n  const hash = useHash();\n  const selectedIndex = props.items.findIndex(item => item.id && (item.id === hash));\n  return (\n    <>\n      <div className={cn(\"hidden sm:flex stack-scope h-full\", props.className)}>\n        <DesktopLayout items={props.items} title={props.title} selectedIndex={selectedIndex} />\n      </div>\n      <div className={cn(\"sm:hidden stack-scope h-full\", props.className)}>\n        <MobileLayout items={props.items} title={props.title} selectedIndex={selectedIndex} />\n      </div>\n    </>\n  );\n}\n\nfunction Items(props: { items: SidebarItem[], selectedIndex: number }) {\n  const app = useStackApp();\n  const navigate = app.useNavigate();\n\n\n  const activeItemIndex = props.selectedIndex === -1 ? 0 : props.selectedIndex;\n\n  return props.items.map((item, index) => (\n    item.type === 'item' ?\n      <Button\n        key={index}\n        variant='ghost'\n        size='sm'\n        className={cn(\n          activeItemIndex === index && \"sm:bg-muted\",\n          \"justify-start text-md text-zinc-800 dark:text-zinc-300 px-2 text-left\",\n        )}\n        onClick={() => {\n          if (item.id) {\n            navigate('#' + item.id);\n          }\n        }}\n      >\n        {item.icon}\n        {item.title}\n      </Button> :\n      <Typography key={index}>\n        {item.title}\n      </Typography>\n  ));\n\n}\n\nfunction DesktopLayout(props: { items: SidebarItem[], title?: ReactNode, selectedIndex: number }) {\n  const selectedItem = props.items[props.selectedIndex === -1 ? 0 : props.selectedIndex];\n\n  return (\n    <div className=\"stack-scope flex w-full h-full max-w-full relative\">\n      <div className=\"flex max-w-[200px] min-w-[200px] border-r flex-col items-stretch gap-2 p-2 overflow-y-auto\">\n        {props.title && <div className='mb-2 ml-2'>\n          <Typography type='h2' className=\"text-lg font-semibold text-zinc-800 dark:text-zinc-300\">{props.title}</Typography>\n        </div>}\n\n        <Items items={props.items} selectedIndex={props.selectedIndex} />\n      </div>\n      <div className=\"flex-1 w-0 flex justify-center gap-4 py-2 px-4\">\n        <div className='flex flex-col max-w-[800px] w-[800px]'>\n          <div className='mt-4 mb-6'>\n            <Typography type='h4' className='font-semibold'>{selectedItem.title}</Typography>\n            {selectedItem.description && <Typography variant='secondary' type='label'>{selectedItem.description}</Typography>}\n          </div>\n          <div className='flex-1'>\n            {selectedItem.content}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nfunction MobileLayout(props: { items: SidebarItem[], title?: ReactNode, selectedIndex: number }) {\n  const selectedItem = props.items[props.selectedIndex];\n  const app = useStackApp();\n  const navigate = app.useNavigate();\n\n  if (props.selectedIndex === -1) {\n    return (\n      <div className=\"flex flex-col gap-2 p-2\">\n        {props.title && <div className='mb-2 ml-2'>\n          <Typography type='h2' className=\"text-lg font-semibold text-zinc-800 dark:text-zinc-300\">{props.title}</Typography>\n        </div>}\n\n        <Items items={props.items} selectedIndex={props.selectedIndex} />\n      </div>\n    );\n  } else {\n    return (\n      <div className=\"flex-1 flex flex-col gap-4 py-2 px-4\">\n        <div className='flex flex-col'>\n          <div className='flex justify-between'>\n            <Typography type='h4' className='font-semibold'>{selectedItem.title}</Typography>\n            <Button\n              variant='ghost'\n              size='icon'\n              onClick={() => { navigate('#'); }}\n            >\n              <XIcon className='h-5 w-5' />\n            </Button>\n          </div>\n          {selectedItem.description && <Typography variant='secondary' type='label'>{selectedItem.description}</Typography>}\n        </div>\n        <div className='flex-1'>\n          {selectedItem.content}\n        </div>\n      </div>\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,sBAAwB;AACxB,sBAAuC;AACvC,0BAAsB;AAEtB,eAA4B;AAgBxB;AAJG,SAAS,cAAc,OAAwE;AACpG,QAAM,WAAO,yBAAQ;AACrB,QAAM,gBAAgB,MAAM,MAAM,UAAU,UAAQ,KAAK,MAAO,KAAK,OAAO,IAAK;AACjF,SACE,4EACE;AAAA,gDAAC,SAAI,eAAW,oBAAG,qCAAqC,MAAM,SAAS,GACrE,sDAAC,iBAAc,OAAO,MAAM,OAAO,OAAO,MAAM,OAAO,eAA8B,GACvF;AAAA,IACA,4CAAC,SAAI,eAAW,oBAAG,gCAAgC,MAAM,SAAS,GAChE,sDAAC,gBAAa,OAAO,MAAM,OAAO,OAAO,MAAM,OAAO,eAA8B,GACtF;AAAA,KACF;AAEJ;AAEA,SAAS,MAAM,OAAwD;AACrE,QAAM,UAAM,sBAAY;AACxB,QAAM,WAAW,IAAI,YAAY;AAGjC,QAAM,kBAAkB,MAAM,kBAAkB,KAAK,IAAI,MAAM;AAE/D,SAAO,MAAM,MAAM,IAAI,CAAC,MAAM,UAC5B,KAAK,SAAS,SACZ;AAAA,IAAC;AAAA;AAAA,MAEC,SAAQ;AAAA,MACR,MAAK;AAAA,MACL,eAAW;AAAA,QACT,oBAAoB,SAAS;AAAA,QAC7B;AAAA,MACF;AAAA,MACA,SAAS,MAAM;AACb,YAAI,KAAK,IAAI;AACX,mBAAS,MAAM,KAAK,EAAE;AAAA,QACxB;AAAA,MACF;AAAA,MAEC;AAAA,aAAK;AAAA,QACL,KAAK;AAAA;AAAA;AAAA,IAdD;AAAA,EAeP,IACA,4CAAC,8BACE,eAAK,SADS,KAEjB,CACH;AAEH;AAEA,SAAS,cAAc,OAA2E;AAChG,QAAM,eAAe,MAAM,MAAM,MAAM,kBAAkB,KAAK,IAAI,MAAM,aAAa;AAErF,SACE,6CAAC,SAAI,WAAU,sDACb;AAAA,iDAAC,SAAI,WAAU,8FACZ;AAAA,YAAM,SAAS,4CAAC,SAAI,WAAU,aAC7B,sDAAC,8BAAW,MAAK,MAAK,WAAU,0DAA0D,gBAAM,OAAM,GACxG;AAAA,MAEA,4CAAC,SAAM,OAAO,MAAM,OAAO,eAAe,MAAM,eAAe;AAAA,OACjE;AAAA,IACA,4CAAC,SAAI,WAAU,kDACb,uDAAC,SAAI,WAAU,yCACb;AAAA,mDAAC,SAAI,WAAU,aACb;AAAA,oDAAC,8BAAW,MAAK,MAAK,WAAU,iBAAiB,uBAAa,OAAM;AAAA,QACnE,aAAa,eAAe,4CAAC,8BAAW,SAAQ,aAAY,MAAK,SAAS,uBAAa,aAAY;AAAA,SACtG;AAAA,MACA,4CAAC,SAAI,WAAU,UACZ,uBAAa,SAChB;AAAA,OACF,GACF;AAAA,KACF;AAEJ;AAEA,SAAS,aAAa,OAA2E;AAC/F,QAAM,eAAe,MAAM,MAAM,MAAM,aAAa;AACpD,QAAM,UAAM,sBAAY;AACxB,QAAM,WAAW,IAAI,YAAY;AAEjC,MAAI,MAAM,kBAAkB,IAAI;AAC9B,WACE,6CAAC,SAAI,WAAU,2BACZ;AAAA,YAAM,SAAS,4CAAC,SAAI,WAAU,aAC7B,sDAAC,8BAAW,MAAK,MAAK,WAAU,0DAA0D,gBAAM,OAAM,GACxG;AAAA,MAEA,4CAAC,SAAM,OAAO,MAAM,OAAO,eAAe,MAAM,eAAe;AAAA,OACjE;AAAA,EAEJ,OAAO;AACL,WACE,6CAAC,SAAI,WAAU,wCACb;AAAA,mDAAC,SAAI,WAAU,iBACb;AAAA,qDAAC,SAAI,WAAU,wBACb;AAAA,sDAAC,8BAAW,MAAK,MAAK,WAAU,iBAAiB,uBAAa,OAAM;AAAA,UACpE;AAAA,YAAC;AAAA;AAAA,cACC,SAAQ;AAAA,cACR,MAAK;AAAA,cACL,SAAS,MAAM;AAAE,yBAAS,GAAG;AAAA,cAAG;AAAA,cAEhC,sDAAC,6BAAM,WAAU,WAAU;AAAA;AAAA,UAC7B;AAAA,WACF;AAAA,QACC,aAAa,eAAe,4CAAC,8BAAW,SAAQ,aAAY,MAAK,SAAS,uBAAa,aAAY;AAAA,SACtG;AAAA,MACA,4CAAC,SAAI,WAAU,UACZ,uBAAa,SAChB;AAAA,OACF;AAAA,EAEJ;AACF;", "names": []}