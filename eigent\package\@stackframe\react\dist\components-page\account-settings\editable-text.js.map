{"version": 3, "sources": ["../../../src/components-page/account-settings/editable-text.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { Button, Input, Typography } from \"@stackframe/stack-ui\";\nimport { Edit } from \"lucide-react\";\nimport { useState } from \"react\";\nimport { useTranslation } from \"../../lib/translations\";\n\n\nexport function EditableText(props: { value: string, onSave?: (value: string) => void | Promise<void> }) {\n  const [editing, setEditing] = useState(false);\n  const [editingValue, setEditingValue] = useState(props.value);\n  const { t } = useTranslation();\n\n  return (\n    <div className='flex items-center gap-2'>\n      {editing ? (\n        <>\n          <Input\n            value={editingValue}\n            onChange={(e) => setEditingValue(e.target.value)}\n          />\n          <Button\n            size='sm'\n            onClick={async () => {\n              await props.onSave?.(editingValue);\n              setEditing(false);\n            }}\n          >\n            {t(\"Save\")}\n          </Button>\n          <Button\n            size='sm'\n            variant='secondary'\n            onClick={() => {\n              setEditingValue(props.value);\n              setEditing(false);\n            }}>\n            {t(\"Cancel\")}\n          </Button>\n        </>\n      ) : (\n        <>\n          <Typography>{props.value}</Typography>\n          <Button onClick={() => setEditing(true)} size='icon' variant='ghost'>\n            <Edit className=\"w-4 h-4\" />\n          </Button>\n        </>\n      )}\n    </div>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,sBAA0C;AAC1C,0BAAqB;AACrB,mBAAyB;AACzB,0BAA+B;AAWvB;AARD,SAAS,aAAa,OAA4E;AACvG,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAS,KAAK;AAC5C,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS,MAAM,KAAK;AAC5D,QAAM,EAAE,EAAE,QAAI,oCAAe;AAE7B,SACE,4CAAC,SAAI,WAAU,2BACZ,oBACC,4EACE;AAAA;AAAA,MAAC;AAAA;AAAA,QACC,OAAO;AAAA,QACP,UAAU,CAAC,MAAM,gBAAgB,EAAE,OAAO,KAAK;AAAA;AAAA,IACjD;AAAA,IACA;AAAA,MAAC;AAAA;AAAA,QACC,MAAK;AAAA,QACL,SAAS,YAAY;AACnB,gBAAM,MAAM,SAAS,YAAY;AACjC,qBAAW,KAAK;AAAA,QAClB;AAAA,QAEC,YAAE,MAAM;AAAA;AAAA,IACX;AAAA,IACA;AAAA,MAAC;AAAA;AAAA,QACC,MAAK;AAAA,QACL,SAAQ;AAAA,QACR,SAAS,MAAM;AACb,0BAAgB,MAAM,KAAK;AAC3B,qBAAW,KAAK;AAAA,QAClB;AAAA,QACC,YAAE,QAAQ;AAAA;AAAA,IACb;AAAA,KACF,IAEA,4EACE;AAAA,gDAAC,8BAAY,gBAAM,OAAM;AAAA,IACzB,4CAAC,0BAAO,SAAS,MAAM,WAAW,IAAI,GAAG,MAAK,QAAO,SAAQ,SAC3D,sDAAC,4BAAK,WAAU,WAAU,GAC5B;AAAA,KACF,GAEJ;AAEJ;", "names": []}