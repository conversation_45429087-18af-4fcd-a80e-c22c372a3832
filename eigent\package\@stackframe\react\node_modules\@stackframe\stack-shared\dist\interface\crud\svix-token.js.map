{"version": 3, "sources": ["../../../src/interface/crud/svix-token.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport { yupObject, yupString } from \"../../schema-fields\";\n\nexport const svixTokenAdminReadSchema = yupObject({\n  token: yupString().defined(),\n}).defined();\n\nexport const svixTokenAdminCreateSchema = yupObject({}).defined();\n\nexport const svixTokenCrud = createCrud({\n  adminReadSchema: svixTokenAdminReadSchema,\n  adminCreateSchema: svixTokenAdminCreateSchema,\n  docs: {\n    adminCreate: {\n      hidden: true,\n    },\n  }\n});\nexport type SvixTokenCrud = CrudTypeOf<typeof svixTokenCrud>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAuC;AACvC,2BAAqC;AAE9B,IAAM,+BAA2B,gCAAU;AAAA,EAChD,WAAO,gCAAU,EAAE,QAAQ;AAC7B,CAAC,EAAE,QAAQ;AAEJ,IAAM,iCAA6B,gCAAU,CAAC,CAAC,EAAE,QAAQ;AAEzD,IAAM,oBAAgB,wBAAW;AAAA,EACtC,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,MAAM;AAAA,IACJ,aAAa;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,EACF;AACF,CAAC;", "names": []}