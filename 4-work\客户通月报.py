import csv
from datetime import datetime, timedelta
import sys
import requests

# pyinstaller -F E:/python/4-work/客户通月报.py

# 判断当前日期是否晚于2027年1月20号，如果是的话，程序直接结束运行
current_date = datetime.now()
end_date = datetime(2027, 1, 20)
if current_date > end_date:
    sys.exit("当前日期晚于2027年1月20号，程序结束。")

# 处理登录
session = requests.session()
url = 'https://account.faisco.biz/login/commit.action?backUrl=http%3A%2F%2Fmonitor.faisco.biz%2FinnerApi%2FcheckSession%3FredirectUrl%3Dhttp%3A%2F%2Fbi.faisco.biz%2F'
headers = {
    'Referer': 'https://account.faisco.biz/page/login.jsp?backUrl=http://monitor.faisco.biz/innerApi/checkSession?redirectUrl=http://bi.faisco.biz/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.88 Safari/537.36',
}
data = {
    'bUrl': 'http%3A%2F%2Fmonitor.faisco.biz%2FinnerApi%2FcheckSession%3FredirectUrl%3Dhttp%3A%2F%2Fbi.faisco.biz%2F',
    'account': 'byron',
    'password': '1c172daad5c7d70ffa472e4a3716c845'
}
res = session.post(url, headers=headers, data=data)

url = 'https://bi.faisco.biz/api/session'
json_data = {
    "password": "3292128Kzx",
    "username": "<EMAIL>",
    "remember": True
}
headers = {
    'Referer': 'https://bi.faisco.biz/auth/login?redirect=%2F',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.88 Safari/537.36',
    #    'cookie':bi_cookie
}
res = session.post(url, headers=headers, json=json_data)

url = 'https://fdo.faisco.biz/'
res = session.get(url, headers=headers)

# 时间
current_date = datetime.now()
previous_month_first_day = (current_date.replace(day=1) - timedelta(days=1)).replace(day=1)
previous_month_last_day = current_date.replace(day=1) - timedelta(days=1)
previous_previous_month_first_day = (previous_month_first_day.replace(day=1) - timedelta(days=1)).replace(day=1)
previous_previous_month_last_day = previous_month_first_day - timedelta(days=1)

beg_time1 = int(previous_previous_month_first_day.replace(hour=0, minute=0, second=0).timestamp())  # 上上月开始时间，格式为时间戳
end_time1 = int(previous_previous_month_last_day.replace(hour=23, minute=59, second=59).timestamp())  # 上上月结束时间，格式为时间戳
beg_time2 = int(previous_month_first_day.replace(hour=0, minute=0, second=0).timestamp())  # 上月开始时间，格式为时间戳
end_time2 = int(previous_month_last_day.replace(hour=23, minute=59, second=59).timestamp())  # 上月结束时间，格式为时间戳
begin_month = str(previous_previous_month_first_day.year) + '-' + str(
    previous_previous_month_first_day.month)  # 上上月，格式为：2023-06
end_month = str(previous_month_first_day.year) + '-' + str(previous_month_first_day.month)  # 上月，格式为：2023-07
if len(begin_month) < 7:
    begin_month = begin_month[:5] + '0' + begin_month[5:]
if len(end_month) < 7:
    end_month = end_month[:5] + '0' + end_month[5:]
month_list = [begin_month, end_month]
begin_day = str(previous_month_first_day)[0:10]  # 上月开始时间，格式为：2023-07-01
end_day = str(previous_month_last_day)[0:10]  # 上月结束时间，格式为：2023-07-31

# 打开文件，写入标题
file_name = '客户通月报' + end_month + '.csv'
with open(file_name, "w", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    writer.writerow(["大项", "小项", begin_month, end_month])

# 财务数据-直销付费来源
d1 = {
    '总付费额': 0,
    '注册客户通-SEM+DA': 0,
    '注册客户通-公众号引流': 0,
    '注册客户通-功能引流': 0,
    '注册客户通-SEO': 0,
    '注册客户通-自来': 0,
    '注册客户通-推荐组': 0,
    '注册客户通-商务合作引流': 0,
    '注册客户通-其他': 0,
    '注册建站': 0,
    '注册互动': 0,
    '注册凡科网': 0,
    '注册商城': 0,
    '注册轻站': 0,
    '注册快图': 0,
    '注册微传单': 0,
    '其他-助手+门店通+设计平台+教育': 0,
    '企微服务商合作': 0,
}
d2 = {
    '总付费额': 0,
    '注册客户通-SEM+DA': 0,
    '注册客户通-公众号引流': 0,
    '注册客户通-功能引流': 0,
    '注册客户通-SEO': 0,
    '注册客户通-自来': 0,
    '注册客户通-推荐组': 0,
    '注册客户通-商务合作引流': 0,
    '注册客户通-其他': 0,
    '注册建站': 0,
    '注册互动': 0,
    '注册凡科网': 0,
    '注册商城': 0,
    '注册轻站': 0,
    '注册快图': 0,
    '注册微传单': 0,
    '其他-助手+门店通+设计平台+教育': 0,
    '企微服务商合作': 0,
}
url1 = 'https://bi.faisco.biz/api/card/3232/query'
url2 = 'https://bi.faisco.biz/api/card/5832/query'
for i in range(len(month_list)):
    json_data = {
        "parameters": [{
            "type": "date/month-year",
            "value": month_list[i],
            "target": ["dimension", ["field", 1124, None]]
        }],
        "dashboard_id": 435
    }
    res = session.post(url1, headers=headers, json=json_data)
    tree = res.json()
    a = tree['data']['rows']
    for j in a:
        type = '注册' + j[0]
        num = round(j[1])
        if type == '注册客户通':
            continue
        if type == '注册门店通' or type == '注册教育' or type == '注册公众号助手' or type == '注册设计平台':
            type = '其他-助手+门店通+设计平台+教育'
        if i == 0:
            d1[type] += num
        elif i == 1:
            d2[type] += num

    res = session.post(url2, headers=headers, json=json_data)
    tree = res.json()
    a = tree['data']['rows']
    for j in a:
        type = j[0]
        num = round(j[1])
        if type == '自来组':
            type = '注册客户通-自来'
        elif type == '销售上传':
            type = '注册客户通-推荐组'
        elif type == '销售':
            type = '注册客户通-商务合作引流'
        elif type == '其他自来':
            type = '注册客户通-其他'
        else:
            type = '注册客户通-其他'
        if i == 0:
            d1[type] += num
        elif i == 1:
            d2[type] += num

d1['总付费额'] = sum(d1.values())
d2['总付费额'] = sum(d2.values())
title = list(d1.keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(d1)):
        writer.writerow(["财务数据-直销付费来源", title[i], d1[title[i]], d2[title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 财务数据-销售成单占比
url = 'https://fdo.faisco.biz/basicdata/payStat/get'
json_data = {
    "order_by": "pay_total_count",
    "group_field": "sale_type_id",
    "time": "month",
    "condition_list": [{
        "key": "is_valid",
        "op": "eq",
        "val": [1],
        "keyName": "有效订单（是/否）",
        "opName": "等于"
    }, {
        "key": "order_item_new_buy",
        "op": "eq",
        "val": [1],
        "keyName": "新购续费",
        "opName": "等于"
    }, {
        "key": "order_item_product_biz",
        "op": "eq",
        "val": [17],
        "keyName": "付费业务",
        "opName": "等于"
    }],
    "matcher": "and",
    "begTime": beg_time1,
    "endTime": end_time2,
    "page": "/basicdata/payStat",
    "columns": {
        "pay_total_count": True,
        "pay_total_original_price": True,
        "pay_total_coupon_price": True,
        "pay_total_price": True,
        "pay_total_price_arpu": True
    },
    "columns_name": {
        "group_field": "销售类型",
        "time": "日期",
        "pay_total_count": "付费量",
        "pay_total_original_price": "订单额",
        "pay_total_coupon_price": "优惠额",
        "pay_total_price": "付费额",
        "pay_total_price_arpu": "付费ARPU"
    },
    "dimension_list": ["reg_ta", "reg_ta_group", "reg_ta_group_label", "reg_know_from_ta", "reg_biz",
                       "open_product_first", "reg_keyword", "reg_from_spider_keyword", "reg_company_goal",
                       "reg_company_size", "reg_trade", "reg_corp_trade_name", "reg_entity", "reg_area", "reg_city",
                       "reg_province", "reg_source_pro", "reg_custom_pro", "reg_tw", "reg_user_title",
                       "reg_bind_type", "reg_client_type", "reg_browser_type", "ab_testing_type", "aid", "reg_time",
                       "reg_time_interval", "reg_open_time", "is_internal_acct", "first_sale_name",
                       "first_sale_group", "first_sale_department", "first_sale_battle_group", "order_pay_method",
                       "order_item_product_biz", "order_item_product", "is_refund", "order_item_new_buy",
                       "order_item_first_purchase_acct", "order_item_pay", "is_sales_cover",
                       "extra_pay_within_7days_after_reg", "extra_pay_within_7days_to_30days_after_reg",
                       "extra_pay_within_30days_to_90days_after_reg", "extra_pay_90days_after_reg", "sale_type_id",
                       "sale_biz_id", "sale_department_name", "sale_battle_group_name", "sale_group_name",
                       "sale_name", "active_time", "active_type_name", "active_ta", "active_ta_group",
                       "active_ta_group_label", "active_ta_biz_name", "sale_receive_ta", "sale_receive_ta_group",
                       "is_valid", "standard_trade_name", "order_item_package_name", "active_area",
                       "active_province", "active_city", "active_time_interval", "order_item_product_category_name",
                       "order_item_first_purchase_category", "order_item_first_purchase_biz"],
    "array_type_list": ["reg_ta_group_label", "active_ta_group_label"]
}
headers_fdo = {
    'Referer': 'https://fdo.faisco.biz/fe/basicdata/payStat',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.88 Safari/537.36',
    #    'Cookie': fdo_cookie
}
res = session.post(url, headers=headers_fdo, json=json_data)
tree = res.json()
a = tree['data']
d1 = {
    '新购收入': 0,
    '新购收入-面销': 0,
    '新购收入-电销': 0,
    '新购收入-无销售': 0,
}
d2 = {
    '新购收入': 0,
    '新购收入-面销': 0,
    '新购收入-电销': 0,
    '新购收入-无销售': 0,
}
for m in a:
    time = m['time']
    num = round(m['pay_total_price'])
    sale_type_id = m['sale_type_id']
    if time == begin_month:
        if sale_type_id == 0:
            d1['新购收入-无销售'] = num
        elif sale_type_id == 1:
            d1['新购收入-电销'] = num
        elif sale_type_id == 2:
            d1['新购收入-面销'] = num
    elif time == end_month:
        if sale_type_id == 0:
            d2['新购收入-无销售'] = num
        elif sale_type_id == 1:
            d2['新购收入-电销'] = num
        elif sale_type_id == 2:
            d2['新购收入-面销'] = num
d1['新购收入'] = sum(d1.values())
d2['新购收入'] = sum(d2.values())
title = list(d1.keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(d1)):
        writer.writerow(["财务数据-销售成单占比", title[i], d1[title[i]], d2[title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 直销数据-客户通开通来源
d2 = {
    '总开通量': 0,
    '注册客户通-SEM+DA': 0,
    '注册客户通-公众号引流': 0,
    '注册客户通-功能引流': 0,
    '注册客户通-SEO': 0,
    '注册客户通-自来': 0,
    '注册客户通-推荐组': 0,
    '注册客户通-其他': 0,
    '注册建站': 0,
    '注册互动': 0,
    '注册凡科网': 0,
    '注册商城': 0,
    '注册轻站': 0,
    '注册快图': 0,
    '注册微传单': 0,
    '其他-助手+门店通+设计平台+教育': 0,
}
url1 = 'https://bi.faisco.biz/api/card/3234/query'
url2 = 'https://bi.faisco.biz/api/card/3235/query'
json_data = {
    "parameters": [{
        "type": "date/month-year",
        "value": end_month,
        "target": ["dimension", ["field", 260017, None]]
    }],
    "dashboard_id": 435
}
res = session.post(url1, headers=headers, json=json_data)
tree = res.json()
a = tree['data']['rows']
for j in a:
    type = j[0]
    num = round(j[1])
    if (type == '其他自来') or (type == '客户通-企微引流') or (type == '客户网站组') or (type == '产品内引流') or (
            type == '易销-产品教育资料') or (type == '工单资源') or (type == '新媒体运营') or (type == '微信服务平台') or (type == '其他运营') or (type == '系统分配-代理商资源'):
        type = '注册客户通-其他'
    elif 'SEM' in type:
        type = '注册客户通-SEM+DA'
    elif 'SEO' in type:
        type = '注册客户通-SEO'
    elif (type == '推荐组') or (type == '销售上传') or (type == '销售') or (type == '销售领取-审批'):
        type = '注册客户通-推荐组'
    elif type == '易销-公众号引流':
        type = '注册客户通-公众号引流'
    elif type == '易销-功能引流':
        type = '注册客户通-功能引流'
    elif (type == '易销-官网注册') or (type == '自来组'):
        type = '注册客户通-自来'
    d2[type] += num

res = session.post(url2, headers=headers, json=json_data)
tree = res.json()
a = tree['data']['rows']
for j in a:
    type = '注册' + j[0]
    num = round(j[1])
    if type == '注册客户通':
        continue
    if ('助手' in type) or ('教育' in type) or ('门店通' in type) or ('设计平台' in type):
        type = '其他-助手+门店通+设计平台+教育'
    if type == '注册流量A':
        continue
    d2[type] += num

d2['总开通量'] = sum(d2.values())
title = list(d2.keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(d2)):
        writer.writerow(["直销数据-客户通开通来源", title[i], "", d2[title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 直销数据-开通Arpu
d2 = {
    '所有渠道': 0,
    '注册客户通-SEM+DA': 0,
    '注册客户通-公众号引流': 0,
    '注册客户通-功能引流': 0,
    '注册客户通-SEO': 0,
    '注册客户通-自来': 0,
    '注册客户通-推荐组': 0,
    '注册客户通-其他': 0,
    '注册建站': 0,
    '注册互动': 0,
    '注册凡科网': 0,
    '注册商城': 0,
    '注册轻站': 0,
    '注册快图': 0,
    '注册微传单': 0,
    '其他-助手+门店通+设计平台+教育': 0,
}
url1 = 'https://bi.faisco.biz/api/card/1226/query'
url2 = 'https://bi.faisco.biz/api/card/10754/query'
json_data = {
    "parameters": [{
        "type": "date/single",
        "value": begin_day,
        "target": ["variable", ["template-tag", "open_time_start"]]
    }, {
        "type": "date/single",
        "value": end_day,
        "target": ["variable", ["template-tag", "open_time_end"]]
    }],
    "dashboard_id": 230
}
res = session.post(url1, headers=headers, json=json_data)
tree = res.json()
a = tree['data']['rows']
for j in a:
    type = '注册' + j[0]
    num = round(j[4], 2)
    if type == '注册客户通':
        continue
    if type == '注册全部':
        type = '所有渠道'
    if type == '注册门店通' or type == '注册教育' or type == '注册公众号助手' or type == '注册设计平台':
        type = '其他-助手+门店通+设计平台+教育'
    if type == '注册流量A':
        continue
    d2[type] += num

json_data = {
    "parameters": [{
        "type": "date/month-year",
        "value": end_month,
        "target": ["dimension", ["field", 94627, None]]
    }],
    "dashboard_id": 435
}
res = session.post(url2, headers=headers, json=json_data)
tree = res.json()
a = tree['data']['rows']
for j in a:
    type = j[0]
    num = round(j[1], 2)
    if (type == '其他自来') or (type == '客户通-企微引流') or (type == '客户网站组') or (type == '产品内引流'):
        type = '注册客户通-其他'
    elif 'SEM' in type:
        type = '注册客户通-SEM+DA'
    elif 'SEO' in type:
        type = '注册客户通-SEO'
    elif (type == '推荐组') or (type == '销售上传') or (type == '销售'):
        type = '注册客户通-推荐组'
    elif type == '易销-公众号引流':
        type = '注册客户通-公众号引流'
    elif type == '易销-功能引流':
        type = '注册客户通-功能引流'
    elif (type == '易销-官网注册') or (type == '自来组'):
        type = '注册客户通-自来'
    d2[type] = num

title = list(d2.keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(d2)):
        writer.writerow(["直销数据-开通Arpu", title[i], "", d2[title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 直销数据-注册客户通7天内付费
d2 = {
    '注册量': 0,
    '总付费量': 0,
    '总付费额': 0,
    '总付费率': 0,
    '总付费ARPU': 0,
    '总注册ARPU': 0,
    '客户通付费量': 0,
    '客户通付费额': 0,
    '客户通付费率': 0,
    '客户通付费ARPU': 0,
    '客户通注册ARPU': 0,
}
url = 'https://bi.faisco.biz/api/card/1229/query'
json_data = {
    "parameters": [{
        "type": "date/single",
        "value": begin_day,
        "target": ["variable", ["template-tag", "reg_time_start"]]
    }, {
        "type": "date/single",
        "value": end_day,
        "target": ["variable", ["template-tag", "reg_time_end"]]
    }],
    "dashboard_id": 230
}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
a = tree['data']['rows'][0]
display_name = []
for j in tree['data']['cols']:
    display_name.append(j['display_name'])
for i in range(len(a)):
    if a[i] is None:
        a[i] = 0
    if display_name[i] == '注册来源':
        continue
    else:
        d2[display_name[i]] = a[i]

title = list(d2.keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(d2)):
        writer.writerow(["直销数据-注册客户通7天内付费", title[i], "", d2[title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 直销数据-注册客户通30天内付费
d2 = {
    '注册量': 0,
    '总付费量': 0,
    '总付费额': 0,
    '总付费率': 0,
    '总付费ARPU': 0,
    '总注册ARPU': 0,
    '客户通付费量': 0,
    '客户通付费额': 0,
    '客户通付费率': 0,
    '客户通付费ARPU': 0,
    '客户通注册ARPU': 0,
}
url = 'https://bi.faisco.biz/api/card/1232/query'
json_data = {
    "parameters": [{
        "type": "date/single",
        "value": begin_day,
        "target": ["variable", ["template-tag", "reg_time_start"]]
    }, {
        "type": "date/single",
        "value": end_day,
        "target": ["variable", ["template-tag", "reg_time_end"]]
    }],
    "dashboard_id": 230
}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
a = tree['data']['rows'][0]
display_name = []
for j in tree['data']['cols']:
    display_name.append(j['display_name'])
for i in range(len(a)):
    if a[i] is None:
        a[i] = 0
    if display_name[i] == '注册来源':
        continue
    else:
        d2[display_name[i]] = a[i]

title = list(d2.keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(d2)):
        writer.writerow(["直销数据-注册客户通30天内付费", title[i], "", d2[title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 直销数据-开通客户通7天内付费
d2 = {
    '开通量': 0,
    '客户通付费量': 0,
    '客户通付费额': 0,
    '客户通付费率': 0,
    '客户通付费ARPU': 0,
    '客户通开通ARPU': 0,
}
url = 'https://bi.faisco.biz/api/card/1233/query'
json_data = {
    "parameters": [{
        "type": "date/single",
        "value": begin_day,
        "target": ["variable", ["template-tag", "open_time_start"]]
    }, {
        "type": "date/single",
        "value": end_day,
        "target": ["variable", ["template-tag", "open_time_end"]]
    }],
    "dashboard_id": 230
}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
a = tree['data']['rows'][0]
display_name = []
for j in tree['data']['cols']:
    display_name.append(j['display_name'])
for i in range(len(a)):
    if a[i] is None:
        a[i] = 0
    if display_name[i] == '注册产品':
        continue
    else:
        d2[display_name[i]] = a[i]
d2['客户通付费率'] /= 100

title = list(d2.keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(d2)):
        writer.writerow(["直销数据-开通客户通7天内付费", title[i], "", d2[title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 直销数据-开通客户通各来源7天内付费率
d2 = {
    '注册客户通-SEM+DA': 0,
    '注册客户通-公众号引流': 0,
    '注册客户通-功能引流': 0,
    '注册客户通-SEO': 0,
    '注册客户通-自来': 0,
    '注册客户通-推荐组': 0,
    '注册客户通-其他': 0,
    '注册建站': 0,
    '注册互动': 0,
    '注册凡科网': 0,
    '注册商城': 0,
    '注册轻站': 0,
    '注册快图': 0,
    '注册微传单': 0,
    '其他-助手+门店通+设计平台+教育': 0,
}
url = 'https://bi.faisco.biz/api/card/1233/query'
json_data = {
    "parameters": [{
        "type": "date/single",
        "value": begin_day,
        "target": ["variable", ["template-tag", "open_time_start"]]
    }, {
        "type": "date/single",
        "value": end_day,
        "target": ["variable", ["template-tag", "open_time_end"]]
    }],
    "dashboard_id": 230
}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
a = tree['data']['rows']
m1 = 0  # 其他-助手+门店通+设计平台+教育的开通量总和
m2 = 0  # 其他-助手+门店通+设计平台+教育的付费量总和
for k in a:
    if (k[0] == '全部') or (k[0] == '客户通'):
        continue
    elif ('助手' in k[0]) or ('门店通' in k[0]) or ('教育' in k[0]):
        m1 += k[1]
        m2 += k[2]
    else:
        k[0] = '注册' + k[0]
        d2[k[0]] = float(k[4] / 100)

d2['其他-助手+门店通+设计平台+教育'] = float(m2 / m1)
title = list(d2.keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(d2)):
        writer.writerow(["直销数据-开通客户通各来源7天内付费率", title[i], "", d2[title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 直销数据-开通客户通各来源30天内付费率
d2 = {
    '注册客户通-SEM+DA': 0,
    '注册客户通-公众号引流': 0,
    '注册客户通-功能引流': 0,
    '注册客户通-SEO': 0,
    '注册客户通-自来': 0,
    '注册客户通-推荐组': 0,
    '注册客户通-其他': 0,
    '注册建站': 0,
    '注册互动': 0,
    '注册凡科网': 0,
    '注册商城': 0,
    '注册轻站': 0,
    '注册快图': 0,
    '注册微传单': 0,
    '其他-助手+门店通+设计平台+教育': 0,
}
url = 'https://bi.faisco.biz/api/card/1238/query'
json_data = {
    "parameters": [{
        "type": "date/single",
        "value": begin_day,
        "target": ["variable", ["template-tag", "open_time_start"]]
    }, {
        "type": "date/single",
        "value": end_day,
        "target": ["variable", ["template-tag", "open_time_end"]]
    }],
    "dashboard_id": 230
}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
a = tree['data']['rows']
m1 = 0  # 其他-助手+门店通+设计平台+教育的开通量总和
m2 = 0  # 其他-助手+门店通+设计平台+教育的付费量总和
for k in a:
    if (k[0] == '全部') or (k[0] == '客户通'):
        continue
    elif ('助手' in k[0]) or ('门店通' in k[0]) or ('教育' in k[0]):
        m1 += k[1]
        m2 += k[2]
    else:
        k[0] = '注册' + k[0]
        d2[k[0]] = float(k[4] / 100)

d2['其他-助手+门店通+设计平台+教育'] = float(m2 / m1)
title = list(d2.keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(d2)):
        writer.writerow(["直销数据-开通客户通各来源30天内付费率", title[i], "", d2[title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 直销数据-退款
d2 = {
    '首购量': 0,
    '退款量': 0,
    '退款率': 0,
    '首购额': 0,
    '退款额': 0,
    '退款额占比': 0,
}
name_list = ['首购量', '首购额', '退款量', '退款额']
url_list = ['1889', '1888', '1890', '1891']
json_data = {
    "parameters": [{
        "type": "date/month-year",
        "value": end_month,
        "target": ["dimension", ["field", 157564, None]]
    }],
    "dashboard_id": 299
}
for i in range(len(url_list)):
    url = f'https://bi.faisco.biz/api/card/{url_list[i]}/query'
    res = session.post(url, headers=headers, json=json_data)
    tree = res.json()
    a = tree['data']['rows']
    if a[0][0] is None:
        a[0][0] = 0
    d2[name_list[i]] = int(a[0][0])
d2['退款率'] = float(d2['退款量'] / d2['首购量'])
if d2['退款额'] == 0:
    d2['退款率'] = 0
d2['退款额占比'] = float(d2['退款额'] / d2['首购额'])

title = list(d2.keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(d2)):
        writer.writerow(["直销数据-退款", title[i], "", d2[title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 直销数据-留存率
num = [0, 0, 0, 0]
name = ['开通量', '次日留存量', '首周留存量', '次三周留存量']
url_list = ['1244', '1469', '1249', '1471']
json_data = {
    "parameters": [{
        "type": "date/single",
        "value": begin_day,
        "target": ["variable", ["template-tag", "open_time_start"]]
    }, {
        "type": "date/single",
        "value": end_day,
        "target": ["variable", ["template-tag", "open_time_end"]]
    }, {
        "type": "category",
        "value": ["汇总"],
        "target": ["dimension", ["template-tag", "list_stat_type"]]
    }],
    "dashboard_id": 232
}
for i in range(len(url_list)):
    url = f'https://bi.faisco.biz/api/card/{url_list[i]}/query'
    res = session.post(url, headers=headers, json=json_data)
    tree = res.json()
    a = tree['data']['rows']
    if a[0][0] is None:
        a[0][0] = 0
    num[i] = int(a[0][0])

with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(num)):
        writer.writerow(["直销数据-留存率", name[i], "", num[i]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 直销数据-漏斗-试用用户
d2 = {
    '开通量': num[0],
    '创建文章（默认不统计）': '',
    '创建文章模板': 0,
    '分享文章': 0,
    '编辑名片': 0,
    '分享名片': 0,
}
json_data = {
    "parameters": [{
        "type": "date/single",
        "value": begin_day,
        "target": ["variable", ["template-tag", "open_time_start"]]
    }, {
        "type": "date/single",
        "value": end_day,
        "target": ["variable", ["template-tag", "open_time_end"]]
    }, {
        "type": "category",
        "value": ["汇总"],
        "target": ["dimension", ["template-tag", "list_stat_type"]]
    }],
    "dashboard_id": 232
}
url = 'https://bi.faisco.biz/api/card/1262/query'
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
a = tree['data']['rows']
for k in a:
    if k[0] in d2.keys():
        d2[k[0]] = k[3]

title = list(d2.keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(d2)):
        writer.writerow(["直销数据-漏斗-试用用户", title[i], "", d2[title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 直销数据-直销当月活跃帐号量
num = [0, 0]
name = ['当月进入客户通平台的帐号量', '每日进入客户通平台的帐号量（30天前开通）']
url_list = ['1287', '1288']
json_data = {
    "parameters": [{
        "type": "date/single",
        "value": begin_day,
        "target": ["variable", ["template-tag", "start_time"]]
    }, {
        "type": "date/single",
        "value": end_day,
        "target": ["variable", ["template-tag", "end_time"]]
    }, {
        "type": "category",
        "value": ["汇总"],
        "target": ["dimension", ["template-tag", "list_stat_type"]]
    }],
    "dashboard_id": 236
}
for i in range(len(url_list)):
    url = f'https://bi.faisco.biz/api/card/{url_list[i]}/query'
    res = session.post(url, headers=headers, json=json_data)
    tree = res.json()
    a = tree['data']['rows']
    if a[0][0] is None:
        a[0][0] = 0
    num[i] = int(a[0][0])

with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(num)):
        writer.writerow(["直销数据-直销当月活跃帐号量", name[i], "", num[i]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 直销数据-直销当月分享数据（全部）
num = [0, 0, 0, 0, 0, 0]
name = ['当月分享的文章数（全部）', '当月分享的名片数（全部）', '当月访问的名片数（全部）', '当月访问的文章数（全部）',
        '当月访问名片的客户数（全部）', '当月阅读文章的客户数（全部）']
url_list = ['1295', '1296', '1298', '1299', '1300', '1301']
json_data = {
    "parameters": [{
        "type": "date/single",
        "value": begin_day,
        "target": ["variable", ["template-tag", "start_time"]]
    }, {
        "type": "date/single",
        "value": end_day,
        "target": ["variable", ["template-tag", "end_time"]]
    }, {
        "type": "category",
        "value": ["汇总"],
        "target": ["dimension", ["template-tag", "list_stat_type"]]
    }],
    "dashboard_id": 236
}
for i in range(len(url_list)):
    url = f'https://bi.faisco.biz/api/card/{url_list[i]}/query'
    res = session.post(url, headers=headers, json=json_data)
    tree = res.json()
    a = tree['data']['rows']
    if a[0][0] is None:
        a[0][0] = 0
    num[i] = int(a[0][0])

with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(num)):
        writer.writerow(["直销数据-直销当月分享数据", name[i], "", num[i]])

# 直销数据-直销当月分享数据（付费）
num = [0, 0, 0, 0, 0, 0]
name = ['当月分享的文章数（付费）', '当月分享的名片数（付费）', '当月访问的名片数（付费）', '当月访问的文章数（付费）',
        '当月访问名片的客户数（付费）', '当月阅读文章的客户数（付费）']
url_list = ['1295', '1296', '1298', '1299', '1300', '1301']
json_data = {
    "parameters": [{
        "type": "date/single",
        "value": begin_day,
        "target": ["variable", ["template-tag", "start_time"]]
    }, {
        "type": "date/single",
        "value": end_day,
        "target": ["variable", ["template-tag", "end_time"]]
    }, {
        "type": "category",
        "value": ["是"],
        "target": ["dimension", ["template-tag", "if_pay_yx"]]
    }, {
        "type": "category",
        "value": ["汇总"],
        "target": ["dimension", ["template-tag", "list_stat_type"]]
    }],
    "dashboard_id": 236
}
for i in range(len(url_list)):
    url = f'https://bi.faisco.biz/api/card/{url_list[i]}/query'
    res = session.post(url, headers=headers, json=json_data)
    tree = res.json()
    a = tree['data']['rows']
    if a[0][0] is None:
        a[0][0] = 0
    num[i] = int(a[0][0])

with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(num)):
        writer.writerow(["直销数据-直销当月分享数据", name[i], "", num[i]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 代理商消费数据-总体情况
d2 = {
    '消费额': 0,
    '消费代理商量': 0,
    '消费客户量': 0,
    '代理商arpu': 0,
    '客户arpu': 0,
}
url = 'https://agent.faisco.biz/agentConsumeRecord/findRecord'
agent_headers = {
    'Referer': 'https://account.faisco.biz/page/login.jsp?backUrl=http://monitor.faisco.biz/innerApi/checkSession?redirectUrl=http://bi.faisco.biz/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.88 Safari/537.36',
    #    'cookie': agent_cookie
}
data = {
    'productLine': 0,
    'levelType': 0,
    'levelMiniType': '',
    'level': '',
    'joinTime[]': '',
    'acct': '',
    'province': '',
    'tradeText': '',
    'joinType': '',
    'operate': '',
    'joinReason': '',
    'agentOther': '',
    'salesAfterAcct': '',
    'isInterior': 1,
    'consumeTotalType': -1,
    'delZero': True,
    'timeStart': begin_day + ' 00:00:00',
    'timeEnd': end_day + ' 23:59:59',
    'allLevelList': ''
}
res = session.post(url, headers=agent_headers, data=data)
tree = res.json()
a = tree['data']
for k in a:
    if k['businessType'] == '销售系统':
        d2['消费额'] = int(k['consumeTotal'])
        d2['消费代理商量'] = int(k['consumeCntTotal'])
        d2['消费客户量'] = int(k['customerCntTotal'])
        d2['代理商arpu'] = int(k['agentArpu'])
        d2['客户arpu'] = int(k['customerArpu'])

title = list(d2.keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(d2)):
        writer.writerow(["代理商消费数据-总体情况", title[i], "", d2[title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 代理商消费数据-消费项目-金额
d2 = {
    '消费总金额': 0,
    '个人版消费金额': 0,
    '标准版消费金额': 0,
    '高级版消费金额': 0,
    '尊享版消费金额': 0,
    '专用版消费金额': 0,
    '扩容包消费金额': 0,
    '其他消费金额': 0,
}
url = 'https://bi.faisco.biz/api/dataset'
json_data = {
    "type": "query",
    "query": {
        "source-table": 27945,
        "filter": ["and", ["=", ["field", 541377, None], 1],
                   ["=", ["field", 541418, None], 0],
                   ["between", ["field", 541360, None], begin_day, end_day]
                   ],
        "aggregation": [
            ["count"],
            ["sum", ["field", 541406, None]]
        ],
        "breakout": [
            ["field", 541352, None],
            ["field", 541356, None]
        ]
    },
    "database": 46,
    "parameters": []
}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
a = tree['data']['rows']
for k in a:
    name = k[0]
    num = k[3]
    if name == '企微激活码':
        name = '其他消费金额'
    if '扩容包' in name:
        name = '扩容包消费金额'
    if '买断' in name:
        name = name.replace('买断', '')
    if '升级为' in name:
        name = name.replace('升级为', '')
    if '销售系统' in name:
        name = name.replace('销售系统', '')
        name = name + '消费金额'
    if name in d2.keys():
        d2[name] += int(num)
d2['消费总金额'] = sum(d2.values())

title = list(d2.keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(d2)):
        writer.writerow(["代理商消费数据-消费项目-金额", title[i], "", d2[title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 代理商消费数据-消费项目-订单量
d2 = {
    '总订单量': 0,
    '个人版订单量': 0,
    '标准版订单量': 0,
    '高级版订单量': 0,
    '尊享版订单量': 0,
    '专用版订单量': 0,
    '扩容包订单量': 0,
    '其他订单量': 0,
}
url = 'https://bi.faisco.biz/api/dataset'
json_data = {
    "type": "query",
    "query": {
        "source-table": 27945,
        "filter": ["and", ["=", ["field", 541377, None], 1],
                   ["=", ["field", 541418, None], 0],
                   ["between", ["field", 541360, None], begin_day, end_day]
                   ],
        "aggregation": [
            ["count"],
            ["sum", ["field", 541406, None]]
        ],
        "breakout": [
            ["field", 541352, None],
            ["field", 541356, None]
        ]
    },
    "database": 46,
    "parameters": []
}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
a = tree['data']['rows']
for k in a:
    name = k[0]
    num = k[2]
    if '扩容包' in name:
        name = '扩容包订单量'
    if name == '企微激活码':
        name = '其他订单量'
    if '买断' in name:
        name = name.replace('买断', '')
    if '升级为' in name:
        name = name.replace('升级为', '')
    if '销售系统' in name:
        name = name.replace('销售系统', '') + '订单量'
    if name in d2.keys():
        d2[name] += int(num)
d2['总订单量'] = sum(d2.values())

title = list(d2.keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(d2)):
        writer.writerow(["代理商消费数据-消费项目-订单量", title[i], "", d2[title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 代理商消费数据-消费多年情况
d2 = {
    '标准版订单量': 0,
    '标准版-单年订单量': 0,
    '标准版-多年订单量': 0,
    '高级版订单量': 0,
    '高级版-单年订单量': 0,
    '高级版-多年订单量': 0,
    '尊享版订单量': 0,
    '尊享版-单年订单量': 0,
    '尊享版-多年订单量': 0,
}
url = 'https://bi.faisco.biz/api/dataset'
json_data = {
    "type": "query",
    "query": {
        "source-table": 27945,
        "filter": ["and", ["=", ["field", 541377, None], 1],
                   ["=", ["field", 541418, None], 0],
                   ["between", ["field", 541360, None], begin_day, end_day]
                   ],
        "aggregation": [
            ["count"],
            ["sum", ["field", 541406, None]]
        ],
        "breakout": [
            ["field", 541352, None],
            ["field", 541356, None]
        ]
    },
    "database": 46,
    "parameters": []
}
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
a = tree['data']['rows']
for k in a:
    name = k[0]
    num = k[2]
    year = int(k[1] / 12)
    if '特殊项目' in name:
        continue
    if '升级为' in name:
        name = name.replace('升级为', '')
    if '买断' in name:
        name = name.replace('买断', '')
        year = 1000
    if '销售系统' in name:
        if '专用版' in name:
            continue
        name = name.replace('销售系统', '')
        if year == 1:
            name = name + '-单年订单量'
        elif year > 1:
            name = name + '-多年订单量'
        # 针对小于1年的订单，不做统计
        elif year < 1:
            name = name + '订单量'
        d2[name] += num

d2['标准版订单量'] += d2['标准版-单年订单量'] + d2['标准版-多年订单量']
d2['高级版订单量'] += d2['高级版-单年订单量'] + d2['高级版-多年订单量']
d2['尊享版订单量'] += d2['尊享版-单年订单量'] + d2['尊享版-多年订单量']
title = list(d2.keys())
with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(d2)):
        writer.writerow(["代理商消费数据-消费项目-订单量", title[i], "", d2[title[i]]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 代理商消费数据-付费版访客量-排除专用版
num = [0, 0, 0, 0, 0, 0, 0]
name = ['付费版当月访客总量', '付费版文章访客总量', '付费版名片访客总量', '付费版海报访客总量', '付费版文件夹访客总量',
        '付费版表单访客总量', '其他']
url_list = ['1850', '1852', '1851', '1853', '1855', '1854']
json_data = {
    "parameters": [{
        "type": "date/single",
        "value": begin_day,
        "target": ["variable", ["template-tag", "start_time"]]
    }, {
        "type": "date/single",
        "value": end_day,
        "target": ["variable", ["template-tag", "end_time"]]
    }, {
        "type": "category",
        "value": ["分销标准版", "分销个人版", "分销尊享版", "分销高级版"],
        "target": ["dimension", ["template-tag", "version"]]
    }],
    "dashboard_id": 294
}
for i in range(len(url_list)):
    url = f'https://bi.faisco.biz/api/card/{url_list[i]}/query'
    res = session.post(url, headers=headers, json=json_data)
    tree = res.json()
    a = tree['data']['rows']
    if a[0][0] is None:
        a[0][0] = 0
    num[i] = int(a[0][0])

with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(num)):
        writer.writerow(["直销数据-付费版访客量-排除专用版", name[i], "", num[i]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])

# 代理商消费数据-付费版分享企业量-排除专用版
num = []
name = []
json_data = {
    "parameters": [{
        "type": "date/single",
        "value": begin_day,
        "target": ["variable", ["template-tag", "start_time"]]
    }, {
        "type": "date/single",
        "value": end_day,
        "target": ["variable", ["template-tag", "end_time"]]
    }, {
        "type": "category",
        "value": ["分销标准版", "分销个人版", "分销尊享版", "分销高级版"],
        "target": ["dimension", ["template-tag", "version"]]
    }],
    "dashboard_id": 294
}
url = 'https://bi.faisco.biz/api/card/1863/query'
res = session.post(url, headers=headers, json=json_data)
tree = res.json()
a = tree['data']['rows']
b = tree['data']['cols']
for i in range(len(b)):
    name.append(b[i]['display_name'])
    num.append(a[0][i])

with open(file_name, "a", newline="", encoding="utf-8") as f:
    writer = csv.writer(f)
    for i in range(len(num)):
        writer.writerow(["直销数据-付费版分享企业量-排除专用版", name[i], "", num[i]])
    writer.writerow([])
    writer.writerow([])
    writer.writerow([])
