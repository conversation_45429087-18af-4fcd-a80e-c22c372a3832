{"version": 3, "sources": ["../../../../../../src/lib/stack-app/apps/interfaces/admin-app.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { EmailTemplateType } from \"@stackframe/stack-shared/dist/interface/crud/email-templates\";\nimport { InternalSession } from \"@stackframe/stack-shared/dist/sessions\";\nimport { Result } from \"@stackframe/stack-shared/dist/utils/results\";\nimport { AsyncStoreProperty, EmailConfig } from \"../../common\";\nimport { AdminSentEmail } from \"../../email\";\nimport { AdminEmailTemplate, AdminEmailTemplateUpdateOptions } from \"../../email-templates\";\nimport { InternalApiKey, InternalApiKeyCreateOptions, InternalApiKeyFirstView } from \"../../internal-api-keys\";\nimport { AdminProjectPermission, AdminProjectPermissionDefinition, AdminProjectPermissionDefinitionCreateOptions, AdminProjectPermissionDefinitionUpdateOptions, AdminTeamPermission, AdminTeamPermissionDefinition, AdminTeamPermissionDefinitionCreateOptions, AdminTeamPermissionDefinitionUpdateOptions } from \"../../permissions\";\nimport { AdminProject } from \"../../projects\";\nimport { _StackAdminAppImpl } from \"../implementations\";\nimport { StackServerApp, StackServerAppConstructorOptions } from \"./server-app\";\n\n\nexport type StackAdminAppConstructorOptions<HasTokenStore extends boolean, ProjectId extends string> = (\n  | (\n    & StackServerAppConstructorOptions<HasTokenStore, ProjectId>\n    & {\n      superSecretAdminKey?: string,\n    }\n  )\n  | (\n    & Omit<StackServerAppConstructorOptions<HasTokenStore, ProjectId>, \"publishableClientKey\" | \"secretServerKey\">\n    & {\n      projectOwnerSession: InternalSession,\n    }\n  )\n);\n\n\nexport type StackAdminApp<HasTokenStore extends boolean = boolean, ProjectId extends string = string> = (\n  & AsyncStoreProperty<\"project\", [], AdminProject, false>\n  & AsyncStoreProperty<\"internalApiKeys\", [], InternalApiKey[], true>\n  & AsyncStoreProperty<\"teamPermissionDefinitions\", [], AdminTeamPermissionDefinition[], true>\n  & AsyncStoreProperty<\"projectPermissionDefinitions\", [], AdminProjectPermissionDefinition[], true>\n  & {\n    useEmailTemplates(): AdminEmailTemplate[], // THIS_LINE_PLATFORM react-like\n    listEmailTemplates(): Promise<AdminEmailTemplate[]>,\n    updateEmailTemplate(type: EmailTemplateType, data: AdminEmailTemplateUpdateOptions): Promise<void>,\n    resetEmailTemplate(type: EmailTemplateType): Promise<void>,\n\n    createInternalApiKey(options: InternalApiKeyCreateOptions): Promise<InternalApiKeyFirstView>,\n\n    createTeamPermissionDefinition(data: AdminTeamPermissionDefinitionCreateOptions): Promise<AdminTeamPermission>,\n    updateTeamPermissionDefinition(permissionId: string, data: AdminTeamPermissionDefinitionUpdateOptions): Promise<void>,\n    deleteTeamPermissionDefinition(permissionId: string): Promise<void>,\n\n    createProjectPermissionDefinition(data: AdminProjectPermissionDefinitionCreateOptions): Promise<AdminProjectPermission>,\n    updateProjectPermissionDefinition(permissionId: string, data: AdminProjectPermissionDefinitionUpdateOptions): Promise<void>,\n    deleteProjectPermissionDefinition(permissionId: string): Promise<void>,\n\n    useSvixToken(): string, // THIS_LINE_PLATFORM react-like\n\n    sendTestEmail(options: {\n      recipientEmail: string,\n      emailConfig: EmailConfig,\n    }): Promise<Result<undefined, { errorMessage: string }>>,\n\n    listSentEmails(): Promise<AdminSentEmail[]>,\n  }\n  & StackServerApp<HasTokenStore, ProjectId>\n);\nexport type StackAdminAppConstructor = {\n  new <\n    HasTokenStore extends boolean,\n    ProjectId extends string\n  >(options: StackAdminAppConstructorOptions<HasTokenStore, ProjectId>): StackAdminApp<HasTokenStore, ProjectId>,\n  new (options: StackAdminAppConstructorOptions<boolean, string>): StackAdminApp<boolean, string>,\n};\nexport const StackAdminApp: StackAdminAppConstructor = _StackAdminAppImpl;\n"], "mappings": ";AAaA,SAAS,0BAA0B;AA2D5B,IAAM,gBAA0C;", "names": []}