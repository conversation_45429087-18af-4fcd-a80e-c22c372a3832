{"version": 3, "sources": ["../../../../src/interface/crud/project-permissions.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport * as schemaFields from \"../../schema-fields\";\nimport { yupMixed, yupObject } from \"../../schema-fields\";\nimport { WebhookEvent } from \"../webhooks\";\n\n// =============== Project permissions =================\n\nexport const projectPermissionsCrudClientReadSchema = yupObject({\n  id: schemaFields.permissionDefinitionIdSchema.defined(),\n  user_id: schemaFields.userIdSchema.defined(),\n}).defined();\n\nexport const projectPermissionsCrudServerCreateSchema = yupObject({\n}).defined();\n\nexport const projectPermissionsCrudServerDeleteSchema = yupMixed();\n\nexport const projectPermissionsCrud = createCrud({\n  clientReadSchema: projectPermissionsCrudClientReadSchema,\n  serverCreateSchema: projectPermissionsCrudServerCreateSchema,\n  serverDeleteSchema: projectPermissionsCrudServerDeleteSchema,\n  docs: {\n    clientList: {\n      summary: \"List project permissions\",\n      description: \"List global permissions of the current user. `user_id=me` must be set for client requests. `(user_id, permission_id)` together uniquely identify a permission.\",\n      tags: [\"Permissions\"],\n    },\n    serverList: {\n      summary: \"List project permissions\",\n      description: \"Query and filter the permission with `user_id` and `permission_id`. `(user_id, permission_id)` together uniquely identify a permission.\",\n      tags: [\"Permissions\"],\n    },\n    serverCreate: {\n      summary: \"Grant a global permission to a user\",\n      description: \"Grant a global permission to a user (the permission must be created first on the Stack dashboard)\",\n      tags: [\"Permissions\"],\n    },\n    serverDelete: {\n      summary: \"Revoke a global permission from a user\",\n      description: \"Revoke a global permission from a user\",\n      tags: [\"Permissions\"],\n    },\n  },\n});\nexport type ProjectPermissionsCrud = CrudTypeOf<typeof projectPermissionsCrud>;\n\nexport const projectPermissionCreatedWebhookEvent = {\n  type: \"project_permission.created\",\n  schema: projectPermissionsCrud.server.readSchema,\n  metadata: {\n    summary: \"Project Permission Created\",\n    description: \"This event is triggered when a project permission is created.\",\n    tags: [\"Users\"],\n  },\n} satisfies WebhookEvent<typeof projectPermissionsCrud.server.readSchema>;\n\nexport const projectPermissionDeletedWebhookEvent = {\n  type: \"project_permission.deleted\",\n  schema: projectPermissionsCrud.server.readSchema,\n  metadata: {\n    summary: \"Project Permission Deleted\",\n    description: \"This event is triggered when a project permission is deleted.\",\n    tags: [\"Users\"],\n  },\n} satisfies WebhookEvent<typeof projectPermissionsCrud.server.readSchema>;\n\n// =============== Project permission definitions =================\n\nexport const projectPermissionDefinitionsCrudAdminReadSchema = yupObject({\n  id: schemaFields.permissionDefinitionIdSchema.defined(),\n  description: schemaFields.teamPermissionDescriptionSchema.optional(),\n  contained_permission_ids: schemaFields.containedPermissionIdsSchema.defined(),\n}).defined();\n\nexport const projectPermissionDefinitionsCrudAdminCreateSchema = yupObject({\n  id: schemaFields.customPermissionDefinitionIdSchema.defined(),\n  description: schemaFields.teamPermissionDescriptionSchema.optional(),\n  contained_permission_ids: schemaFields.containedPermissionIdsSchema.optional(),\n}).defined();\n\nexport const projectPermissionDefinitionsCrudAdminUpdateSchema = yupObject({\n  id: schemaFields.customPermissionDefinitionIdSchema.optional(),\n  description: schemaFields.teamPermissionDescriptionSchema.optional(),\n  contained_permission_ids: schemaFields.containedPermissionIdsSchema.optional(),\n}).defined();\n\nexport const projectPermissionDefinitionsCrudAdminDeleteSchema = yupMixed();\n\nexport const projectPermissionDefinitionsCrud = createCrud({\n  adminReadSchema: projectPermissionDefinitionsCrudAdminReadSchema,\n  adminCreateSchema: projectPermissionDefinitionsCrudAdminCreateSchema,\n  adminUpdateSchema: projectPermissionDefinitionsCrudAdminUpdateSchema,\n  adminDeleteSchema: projectPermissionDefinitionsCrudAdminDeleteSchema,\n  docs: {\n    adminList: {\n      summary: \"List project permission definitions\",\n      description: \"Query and filter project permission definitions (the equivalent of listing permissions on the Stack dashboard)\",\n      tags: [\"Permissions\"],\n    },\n    adminCreate: {\n      summary: \"Create a new project permission definition\",\n      description: \"Create a new project permission definition (the equivalent of creating a new permission on the Stack dashboard)\",\n      tags: [\"Permissions\"],\n    },\n    adminUpdate: {\n      summary: \"Update a project permission definition\",\n      description: \"Update a project permission definition (the equivalent of updating a permission on the Stack dashboard)\",\n      tags: [\"Permissions\"],\n    },\n    adminDelete: {\n      summary: \"Delete a project permission definition\",\n      description: \"Delete a project permission definition (the equivalent of deleting a permission on the Stack dashboard)\",\n      tags: [\"Permissions\"],\n    },\n  },\n});\n\nexport type ProjectPermissionDefinitionsCrud = CrudTypeOf<typeof projectPermissionDefinitionsCrud>;\n"], "mappings": ";AAAA,SAAqB,kBAAkB;AACvC,YAAY,kBAAkB;AAC9B,SAAS,UAAU,iBAAiB;AAK7B,IAAM,yCAAyC,UAAU;AAAA,EAC9D,IAAiB,0CAA6B,QAAQ;AAAA,EACtD,SAAsB,0BAAa,QAAQ;AAC7C,CAAC,EAAE,QAAQ;AAEJ,IAAM,2CAA2C,UAAU,CAClE,CAAC,EAAE,QAAQ;AAEJ,IAAM,2CAA2C,SAAS;AAE1D,IAAM,yBAAyB,WAAW;AAAA,EAC/C,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,MAAM;AAAA,IACJ,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,aAAa;AAAA,IACtB;AAAA,IACA,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,aAAa;AAAA,IACtB;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,aAAa;AAAA,IACtB;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,aAAa;AAAA,IACtB;AAAA,EACF;AACF,CAAC;AAGM,IAAM,uCAAuC;AAAA,EAClD,MAAM;AAAA,EACN,QAAQ,uBAAuB,OAAO;AAAA,EACtC,UAAU;AAAA,IACR,SAAS;AAAA,IACT,aAAa;AAAA,IACb,MAAM,CAAC,OAAO;AAAA,EAChB;AACF;AAEO,IAAM,uCAAuC;AAAA,EAClD,MAAM;AAAA,EACN,QAAQ,uBAAuB,OAAO;AAAA,EACtC,UAAU;AAAA,IACR,SAAS;AAAA,IACT,aAAa;AAAA,IACb,MAAM,CAAC,OAAO;AAAA,EAChB;AACF;AAIO,IAAM,kDAAkD,UAAU;AAAA,EACvE,IAAiB,0CAA6B,QAAQ;AAAA,EACtD,aAA0B,6CAAgC,SAAS;AAAA,EACnE,0BAAuC,0CAA6B,QAAQ;AAC9E,CAAC,EAAE,QAAQ;AAEJ,IAAM,oDAAoD,UAAU;AAAA,EACzE,IAAiB,gDAAmC,QAAQ;AAAA,EAC5D,aAA0B,6CAAgC,SAAS;AAAA,EACnE,0BAAuC,0CAA6B,SAAS;AAC/E,CAAC,EAAE,QAAQ;AAEJ,IAAM,oDAAoD,UAAU;AAAA,EACzE,IAAiB,gDAAmC,SAAS;AAAA,EAC7D,aAA0B,6CAAgC,SAAS;AAAA,EACnE,0BAAuC,0CAA6B,SAAS;AAC/E,CAAC,EAAE,QAAQ;AAEJ,IAAM,oDAAoD,SAAS;AAEnE,IAAM,mCAAmC,WAAW;AAAA,EACzD,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,MAAM;AAAA,IACJ,WAAW;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,aAAa;AAAA,IACtB;AAAA,IACA,aAAa;AAAA,MACX,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,aAAa;AAAA,IACtB;AAAA,IACA,aAAa;AAAA,MACX,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,aAAa;AAAA,IACtB;AAAA,IACA,aAAa;AAAA,MACX,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,aAAa;AAAA,IACtB;AAAA,EACF;AACF,CAAC;", "names": []}