{"version": 3, "sources": ["../../../../src/components-page/account-settings/teams/team-member-invitation-section.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { yupResolver } from \"@hookform/resolvers/yup\";\nimport { strictEmailSchema, yupObject } from \"@stackframe/stack-shared/dist/schema-fields\";\nimport { runAsynchronouslyWithAlert } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { Button, Input, Table, TableBody, TableCell, TableHead, TableHeader, TableRow, Typography } from \"@stackframe/stack-ui\";\nimport { Trash } from \"lucide-react\";\nimport { useEffect, useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport * as yup from \"yup\";\nimport { Team } from \"../../..\";\nimport { FormWarningText } from \"../../../components/elements/form-warning\";\nimport { useUser } from \"../../../lib/hooks\";\nimport { useTranslation } from \"../../../lib/translations\";\nimport { Section } from \"../section\";\n\nexport function TeamMemberInvitationSection(props: { team: Team }) {\n  const user = useUser({ or: 'redirect' });\n  const inviteMemberPermission = user.usePermission(props.team, '$invite_members');\n\n  if (!inviteMemberPermission) {\n    return null;\n  }\n\n  return <MemberInvitationSectionInner team={props.team} />;\n}\n\nfunction MemberInvitationsSectionInvitationsList(props: { team: Team }) {\n  const user = useUser({ or: 'redirect' });\n  const { t } = useTranslation();\n  const invitationsToShow = props.team.useInvitations();\n  const removeMemberPermission = user.usePermission(props.team, '$remove_members');\n\n  return <>\n    <Table className='mt-6'>\n      <TableHeader>\n        <TableRow>\n          <TableHead className=\"w-[200px]\">{t(\"Outstanding invitations\")}</TableHead>\n          <TableHead className=\"w-[60px]\">{t(\"Expires\")}</TableHead>\n          <TableHead className=\"w-[36px] max-w-[36px]\"></TableHead>\n        </TableRow>\n      </TableHeader>\n      <TableBody>\n        {invitationsToShow.map((invitation, i) => (\n          <TableRow key={invitation.id}>\n            <TableCell>\n              <Typography>{invitation.recipientEmail}</Typography>\n            </TableCell>\n            <TableCell>\n              <Typography variant='secondary'>{invitation.expiresAt.toLocaleString()}</Typography>\n            </TableCell>\n            <TableCell align='right' className='max-w-[36px]'>\n              {removeMemberPermission && (\n                <Button onClick={async () => await invitation.revoke()} size='icon' variant='ghost'>\n                  <Trash className=\"w-4 h-4\" />\n                </Button>\n              )}\n            </TableCell>\n          </TableRow>\n        ))}\n        {invitationsToShow.length === 0 && <TableRow>\n          <TableCell colSpan={3}>\n            <Typography variant='secondary'>{t(\"No outstanding invitations\")}</Typography>\n          </TableCell>\n        </TableRow>}\n      </TableBody>\n    </Table>\n  </>;\n}\n\nfunction MemberInvitationSectionInner(props: { team: Team }) {\n  const user = useUser({ or: 'redirect' });\n  const { t } = useTranslation();\n  const readMemberPermission = user.usePermission(props.team, '$read_members');\n\n  const invitationSchema = yupObject({\n    email: strictEmailSchema(t('Please enter a valid email address')).defined().nonEmpty(t('Please enter an email address')),\n  });\n\n  const { register, handleSubmit, formState: { errors }, watch } = useForm({\n    resolver: yupResolver(invitationSchema)\n  });\n  const [loading, setLoading] = useState(false);\n  const [invitedEmail, setInvitedEmail] = useState<string | null>(null);\n\n  const onSubmit = async (data: yup.InferType<typeof invitationSchema>) => {\n    setLoading(true);\n\n    try {\n      await props.team.inviteUser({ email: data.email });\n      setInvitedEmail(data.email);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    setInvitedEmail(null);\n  }, [watch('email')]);\n\n  return (\n    <>\n      <Section\n        title={t(\"Invite member\")}\n        description={t(\"Invite a user to your team through email\")}\n      >\n        <form\n          onSubmit={e => runAsynchronouslyWithAlert(handleSubmit(onSubmit)(e))}\n          noValidate\n          className='w-full'\n        >\n          <div className=\"flex flex-col gap-4 sm:flex-row w-full\">\n            <Input\n              placeholder={t(\"Email\")}\n              {...register(\"email\")}\n            />\n            <Button type=\"submit\" loading={loading}>{t(\"Invite User\")}</Button>\n          </div>\n          <FormWarningText text={errors.email?.message?.toString()} />\n          {invitedEmail && <Typography type='label' variant='secondary'>Invited {invitedEmail}</Typography>}\n        </form>\n      </Section>\n      {readMemberPermission && <MemberInvitationsSectionInvitationsList team={props.team} />}\n    </>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,iBAA4B;AAC5B,2BAA6C;AAC7C,sBAA2C;AAC3C,sBAAyG;AACzG,0BAAsB;AACtB,mBAAoC;AACpC,6BAAwB;AAGxB,0BAAgC;AAChC,mBAAwB;AACxB,0BAA+B;AAC/B,qBAAwB;AAUf;AARF,SAAS,4BAA4B,OAAuB;AACjE,QAAM,WAAO,sBAAQ,EAAE,IAAI,WAAW,CAAC;AACvC,QAAM,yBAAyB,KAAK,cAAc,MAAM,MAAM,iBAAiB;AAE/E,MAAI,CAAC,wBAAwB;AAC3B,WAAO;AAAA,EACT;AAEA,SAAO,4CAAC,gCAA6B,MAAM,MAAM,MAAM;AACzD;AAEA,SAAS,wCAAwC,OAAuB;AACtE,QAAM,WAAO,sBAAQ,EAAE,IAAI,WAAW,CAAC;AACvC,QAAM,EAAE,EAAE,QAAI,oCAAe;AAC7B,QAAM,oBAAoB,MAAM,KAAK,eAAe;AACpD,QAAM,yBAAyB,KAAK,cAAc,MAAM,MAAM,iBAAiB;AAE/E,SAAO,2EACL,uDAAC,yBAAM,WAAU,QACf;AAAA,gDAAC,+BACC,uDAAC,4BACC;AAAA,kDAAC,6BAAU,WAAU,aAAa,YAAE,yBAAyB,GAAE;AAAA,MAC/D,4CAAC,6BAAU,WAAU,YAAY,YAAE,SAAS,GAAE;AAAA,MAC9C,4CAAC,6BAAU,WAAU,yBAAwB;AAAA,OAC/C,GACF;AAAA,IACA,6CAAC,6BACE;AAAA,wBAAkB,IAAI,CAAC,YAAY,MAClC,6CAAC,4BACC;AAAA,oDAAC,6BACC,sDAAC,8BAAY,qBAAW,gBAAe,GACzC;AAAA,QACA,4CAAC,6BACC,sDAAC,8BAAW,SAAQ,aAAa,qBAAW,UAAU,eAAe,GAAE,GACzE;AAAA,QACA,4CAAC,6BAAU,OAAM,SAAQ,WAAU,gBAChC,oCACC,4CAAC,0BAAO,SAAS,YAAY,MAAM,WAAW,OAAO,GAAG,MAAK,QAAO,SAAQ,SAC1E,sDAAC,6BAAM,WAAU,WAAU,GAC7B,GAEJ;AAAA,WAba,WAAW,EAc1B,CACD;AAAA,MACA,kBAAkB,WAAW,KAAK,4CAAC,4BAClC,sDAAC,6BAAU,SAAS,GAClB,sDAAC,8BAAW,SAAQ,aAAa,YAAE,4BAA4B,GAAE,GACnE,GACF;AAAA,OACF;AAAA,KACF,GACF;AACF;AAEA,SAAS,6BAA6B,OAAuB;AAC3D,QAAM,WAAO,sBAAQ,EAAE,IAAI,WAAW,CAAC;AACvC,QAAM,EAAE,EAAE,QAAI,oCAAe;AAC7B,QAAM,uBAAuB,KAAK,cAAc,MAAM,MAAM,eAAe;AAE3E,QAAM,uBAAmB,gCAAU;AAAA,IACjC,WAAO,wCAAkB,EAAE,oCAAoC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,+BAA+B,CAAC;AAAA,EACzH,CAAC;AAED,QAAM,EAAE,UAAU,cAAc,WAAW,EAAE,OAAO,GAAG,MAAM,QAAI,gCAAQ;AAAA,IACvE,cAAU,wBAAY,gBAAgB;AAAA,EACxC,CAAC;AACD,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAS,KAAK;AAC5C,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAwB,IAAI;AAEpE,QAAM,WAAW,OAAO,SAAiD;AACvE,eAAW,IAAI;AAEf,QAAI;AACF,YAAM,MAAM,KAAK,WAAW,EAAE,OAAO,KAAK,MAAM,CAAC;AACjD,sBAAgB,KAAK,KAAK;AAAA,IAC5B,UAAE;AACA,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AAEA,8BAAU,MAAM;AACd,oBAAgB,IAAI;AAAA,EACtB,GAAG,CAAC,MAAM,OAAO,CAAC,CAAC;AAEnB,SACE,4EACE;AAAA;AAAA,MAAC;AAAA;AAAA,QACC,OAAO,EAAE,eAAe;AAAA,QACxB,aAAa,EAAE,0CAA0C;AAAA,QAEzD;AAAA,UAAC;AAAA;AAAA,YACC,UAAU,WAAK,4CAA2B,aAAa,QAAQ,EAAE,CAAC,CAAC;AAAA,YACnE,YAAU;AAAA,YACV,WAAU;AAAA,YAEV;AAAA,2DAAC,SAAI,WAAU,0CACb;AAAA;AAAA,kBAAC;AAAA;AAAA,oBACC,aAAa,EAAE,OAAO;AAAA,oBACrB,GAAG,SAAS,OAAO;AAAA;AAAA,gBACtB;AAAA,gBACA,4CAAC,0BAAO,MAAK,UAAS,SAAmB,YAAE,aAAa,GAAE;AAAA,iBAC5D;AAAA,cACA,4CAAC,uCAAgB,MAAM,OAAO,OAAO,SAAS,SAAS,GAAG;AAAA,cACzD,gBAAgB,6CAAC,8BAAW,MAAK,SAAQ,SAAQ,aAAY;AAAA;AAAA,gBAAS;AAAA,iBAAa;AAAA;AAAA;AAAA,QACtF;AAAA;AAAA,IACF;AAAA,IACC,wBAAwB,4CAAC,2CAAwC,MAAM,MAAM,MAAM;AAAA,KACtF;AAEJ;", "names": []}