{"version": 3, "sources": ["../../../../src/interface/crud/team-invitation.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport * as schemaFields from \"../../schema-fields\";\nimport { yupObject } from \"../../schema-fields\";\n\nexport const teamInvitationDetailsClientReadSchema = yupObject({\n  id: schemaFields.yupString().uuid().defined(),\n  team_id: schemaFields.teamIdSchema.defined(),\n  expires_at_millis: schemaFields.yupNumber().defined(),\n  recipient_email: schemaFields.emailSchema.defined(),\n}).defined();\n\nexport const teamInvitationCrud = createCrud({\n  clientReadSchema: teamInvitationDetailsClientReadSchema,\n  clientDeleteSchema: schemaFields.yupMixed(),\n  docs: {\n    clientRead: {\n      summary: \"Get the team details with invitation code\",\n      description: \"\",\n      tags: [\"Teams\"],\n    },\n    clientList: {\n      summary: \"List team invitations\",\n      description: \"\",\n      tags: [\"Teams\"],\n    },\n    clientDelete: {\n      summary: \"Delete a team invitation\",\n      description: \"\",\n      tags: [\"Teams\"],\n    },\n  },\n});\n\nexport type TeamInvitationCrud = CrudTypeOf<typeof teamInvitationCrud>;\n"], "mappings": ";AAAA,SAAqB,kBAAkB;AACvC,YAAY,kBAAkB;AAC9B,SAAS,iBAAiB;AAEnB,IAAM,wCAAwC,UAAU;AAAA,EAC7D,IAAiB,uBAAU,EAAE,KAAK,EAAE,QAAQ;AAAA,EAC5C,SAAsB,0BAAa,QAAQ;AAAA,EAC3C,mBAAgC,uBAAU,EAAE,QAAQ;AAAA,EACpD,iBAA8B,yBAAY,QAAQ;AACpD,CAAC,EAAE,QAAQ;AAEJ,IAAM,qBAAqB,WAAW;AAAA,EAC3C,kBAAkB;AAAA,EAClB,oBAAiC,sBAAS;AAAA,EAC1C,MAAM;AAAA,IACJ,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,EACF;AACF,CAAC;", "names": []}