{"version": 3, "sources": ["../../../../src/components-page/account-settings/email-and-auth/email-and-auth-page.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { PageLayout } from \"../page-layout\";\nimport { EmailsSection } from \"./emails-section\";\nimport { MfaSection } from \"./mfa-section\";\nimport { OtpSection } from \"./otp-section\";\nimport { PasskeySection } from \"./passkey-section\";\nimport { PasswordSection } from \"./password-section\";\n\nexport function EmailsAndAuthPage() {\n  return (\n    <PageLayout>\n      <EmailsSection/>\n      <PasswordSection />\n      <PasskeySection />\n      <OtpSection />\n      <MfaSection />\n    </PageLayout>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,yBAA2B;AAC3B,4BAA8B;AAC9B,yBAA2B;AAC3B,yBAA2B;AAC3B,6BAA+B;AAC/B,8BAAgC;AAI5B;AAFG,SAAS,oBAAoB;AAClC,SACE,6CAAC,iCACC;AAAA,gDAAC,uCAAa;AAAA,IACd,4CAAC,2CAAgB;AAAA,IACjB,4CAAC,yCAAe;AAAA,IAChB,4CAAC,iCAAW;AAAA,IACZ,4CAAC,iCAAW;AAAA,KACd;AAEJ;", "names": []}