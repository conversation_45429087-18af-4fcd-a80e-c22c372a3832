"""
🐾 数据库连接和会话管理
Database Connection and Session Management

提供异步数据库连接、会话管理和基础操作
"""

from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.pool import NullPool
from loguru import logger

from app.core.config import settings


# 创建异步数据库引擎
engine = create_async_engine(
    settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://"),
    echo=settings.DEBUG,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    poolclass=NullPool if "test" in settings.DATABASE_URL else None,
)

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)

# 创建基础模型类
Base = declarative_base()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    获取数据库会话
    
    用于FastAPI依赖注入，自动管理数据库会话的生命周期
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception as e:
            await session.rollback()
            logger.error(f"❌ 数据库会话错误: {e}")
            raise
        finally:
            await session.close()


async def init_db() -> None:
    """
    初始化数据库
    
    创建所有表结构，仅在开发和测试环境使用
    生产环境应使用Alembic进行数据库迁移
    """
    try:
        # 导入所有模型以确保它们被注册
        from app.models import user, stock, order, transaction, position, market_data
        
        logger.info("🔄 正在初始化数据库...")
        
        async with engine.begin() as conn:
            # 在测试环境下删除所有表重新创建
            if "test" in settings.DATABASE_URL:
                await conn.run_sync(Base.metadata.drop_all)
                logger.info("🗑️ 测试环境：已删除所有表")
            
            # 创建所有表
            await conn.run_sync(Base.metadata.create_all)
            logger.info("✅ 数据库表创建完成")
            
        # 初始化基础数据
        await init_base_data()
        
    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        raise


async def init_base_data() -> None:
    """初始化基础数据"""
    try:
        async with AsyncSessionLocal() as session:
            # 检查是否已有基础数据
            from app.models.stock import Stock
            from sqlalchemy import select
            
            result = await session.execute(select(Stock))
            existing_stocks = result.scalars().all()
            
            if not existing_stocks:
                logger.info("🔄 正在初始化股票基础数据...")
                await init_stocks(session)
                await session.commit()
                logger.info("✅ 股票基础数据初始化完成")
            else:
                logger.info("ℹ️ 股票基础数据已存在，跳过初始化")
                
    except Exception as e:
        logger.error(f"❌ 基础数据初始化失败: {e}")
        raise


async def init_stocks(session: AsyncSession) -> None:
    """初始化股票数据"""
    from app.models.stock import Stock
    import random
    
    # A股常见股票代码和名称
    stock_data = [
        ("000001", "平安银行", "银行"),
        ("000002", "万科A", "房地产"),
        ("000858", "五粮液", "食品饮料"),
        ("002415", "海康威视", "电子"),
        ("600000", "浦发银行", "银行"),
        ("600036", "招商银行", "银行"),
        ("600519", "贵州茅台", "食品饮料"),
        ("600887", "伊利股份", "食品饮料"),
        ("000858", "五粮液", "食品饮料"),
        ("002594", "比亚迪", "汽车"),
        ("300059", "东方财富", "非银金融"),
        ("300750", "宁德时代", "电池"),
        ("600276", "恒瑞医药", "医药生物"),
        ("000725", "京东方A", "电子"),
        ("002230", "科大讯飞", "计算机"),
        ("600031", "三一重工", "机械设备"),
        ("000063", "中兴通讯", "通信"),
        ("002352", "顺丰控股", "交通运输"),
        ("600009", "上海机场", "交通运输"),
        ("000776", "广发证券", "非银金融"),
    ]
    
    for code, name, industry in stock_data:
        # 生成随机初始价格
        initial_price = round(random.uniform(
            settings.INITIAL_STOCK_PRICE_MIN,
            settings.INITIAL_STOCK_PRICE_MAX
        ), 2)
        
        stock = Stock(
            code=code,
            name=name,
            industry=industry,
            current_price=initial_price,
            opening_price=initial_price,
            closing_price=initial_price,
            highest_price=initial_price,
            lowest_price=initial_price,
            volume=0,
            market_cap=initial_price * **********,  # 假设10亿股本
            is_active=True
        )
        
        session.add(stock)
    
    logger.info(f"✅ 已添加 {len(stock_data)} 只股票到数据库")


async def close_db() -> None:
    """关闭数据库连接"""
    try:
        await engine.dispose()
        logger.info("✅ 数据库连接已关闭")
    except Exception as e:
        logger.error(f"❌ 关闭数据库连接失败: {e}")


# 数据库健康检查
async def check_db_health() -> bool:
    """检查数据库连接健康状态"""
    try:
        async with AsyncSessionLocal() as session:
            await session.execute("SELECT 1")
            return True
    except Exception as e:
        logger.error(f"❌ 数据库健康检查失败: {e}")
        return False


# 导出
__all__ = [
    "engine",
    "AsyncSessionLocal", 
    "Base",
    "get_db",
    "init_db",
    "close_db",
    "check_db_health"
]
