/* Tech Blog Theme */
body {
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.7;
  color: #2d3748;
  max-width: 820px;
  margin: 0 auto;
  padding: 2rem;
  background-color: #f8fafc;
}

h1 {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

h2 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2d3748;
  margin-top: 2.5rem;
  margin-bottom: 1rem;
  letter-spacing: -0.015em;
}

h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #4a5568;
  margin-top: 2rem;
  margin-bottom: 0.75rem;
}

p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

a {
  color: #3182ce;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s;
}

a:hover {
  color: #2c5282;
  border-bottom-color: #3182ce;
}

blockquote {
  border-left: 4px solid #3182ce;
  padding: 0.5rem 1.5rem;
  margin: 1.5rem 0;
  background-color: #ebf8ff;
  color: #2c5282;
}

code {
  background: #edf2f7;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', monospace;
  font-size: 0.9rem;
  color: #3182ce;
}

ul, ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
}

li {
  margin-bottom: 0.75rem;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 2rem auto;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

th, td {
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  text-align: left;
}

th {
  background-color: #edf2f7;
  font-weight: 600;
  color: #1a202c;
}

tr:nth-child(even) {
  background-color: #f7fafc;
}

@media (max-width: 768px) {
  body {
    padding: 1rem;
    font-size: 0.95rem;
  }
  
  h1 {
    font-size: 2.2rem;
  }
  
  h2 {
    font-size: 1.6rem;
  }
  
  h3 {
    font-size: 1.3rem;
  }
}