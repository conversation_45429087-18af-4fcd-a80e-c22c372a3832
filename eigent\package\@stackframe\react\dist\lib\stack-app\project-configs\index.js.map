{"version": 3, "sources": ["../../../../src/lib/stack-app/project-configs/index.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { AdminTeamPermission } from \"../permissions\";\n\n\nexport type ProjectConfig = {\n  readonly signUpEnabled: boolean,\n  readonly credentialEnabled: boolean,\n  readonly magicLinkEnabled: boolean,\n  readonly passkeyEnabled: boolean,\n  readonly clientTeamCreationEnabled: boolean,\n  readonly clientUserDeletionEnabled: boolean,\n  readonly oauthProviders: OAuthProviderConfig[],\n  readonly allowUserApiKeys: boolean,\n  readonly allowTeamApiKeys: boolean,\n};\n\nexport type OAuthProviderConfig = {\n  readonly id: string,\n};\n\nexport type AdminProjectConfig = {\n  readonly signUpEnabled: boolean,\n  readonly credentialEnabled: boolean,\n  readonly magicLinkEnabled: boolean,\n  readonly passkeyEnabled: boolean,\n  readonly clientTeamCreationEnabled: boolean,\n  readonly clientUserDeletionEnabled: boolean,\n  readonly allowLocalhost: boolean,\n  readonly oauthProviders: AdminOAuthProviderConfig[],\n  readonly emailConfig?: AdminEmailConfig,\n  readonly domains: AdminDomainConfig[],\n  readonly createTeamOnSignUp: boolean,\n  readonly teamCreatorDefaultPermissions: AdminTeamPermission[],\n  readonly teamMemberDefaultPermissions: AdminTeamPermission[],\n  readonly userDefaultPermissions: AdminTeamPermission[],\n  readonly oauthAccountMergeStrategy: 'link_method' | 'raise_error' | 'allow_duplicates',\n  readonly allowUserApiKeys: boolean,\n  readonly allowTeamApiKeys: boolean,\n};\n\nexport type AdminEmailConfig = (\n  {\n    type: \"standard\",\n    senderName: string,\n    senderEmail: string,\n    host: string,\n    port: number,\n    username: string,\n    password: string,\n  }\n  | {\n    type: \"shared\",\n  }\n);\n\nexport type AdminDomainConfig = {\n  domain: string,\n  handlerPath: string,\n};\n\nexport type AdminOAuthProviderConfig = {\n  id: string,\n} & (\n  | { type: 'shared' }\n  | {\n    type: 'standard',\n    clientId: string,\n    clientSecret: string,\n    facebookConfigId?: string,\n    microsoftTenantId?: string,\n  }\n) & OAuthProviderConfig;\n\nexport type AdminProjectConfigUpdateOptions = {\n  domains?: {\n    domain: string,\n    handlerPath: string,\n  }[],\n  oauthProviders?: AdminOAuthProviderConfig[],\n  signUpEnabled?: boolean,\n  credentialEnabled?: boolean,\n  magicLinkEnabled?: boolean,\n  passkeyEnabled?: boolean,\n  clientTeamCreationEnabled?: boolean,\n  clientUserDeletionEnabled?: boolean,\n  allowLocalhost?: boolean,\n  createTeamOnSignUp?: boolean,\n  emailConfig?: AdminEmailConfig,\n  teamCreatorDefaultPermissions?: { id: string }[],\n  teamMemberDefaultPermissions?: { id: string }[],\n  userDefaultPermissions?: { id: string }[],\n  oauthAccountMergeStrategy?: 'link_method' | 'raise_error' | 'allow_duplicates',\n  allowUserApiKeys?: boolean,\n  allowTeamApiKeys?: boolean,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;", "names": []}