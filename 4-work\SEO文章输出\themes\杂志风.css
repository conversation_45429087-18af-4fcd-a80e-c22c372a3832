/* Magazine Style Theme */
body {
  font-family: 'Merriweather', Georgia, serif;
  line-height: 1.8;
  color: #333;
  max-width: 780px;
  margin: 0 auto;
  padding: 2rem;
  background-color: #fff;
}

h1 {
  font-family: 'Playfair Display', Georgia, serif;
  font-size: 2.8rem;
  font-weight: 900;
  color: #111;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  letter-spacing: -0.01em;
}

h2 {
  font-family: 'Playfair Display', Georgia, serif;
  font-size: 1.9rem;
  font-weight: 700;
  color: #222;
  margin-top: 2.5rem;
  margin-bottom: 1rem;
  line-height: 1.3;
}

h3 {
  font-family: 'Playfair Display', Georgia, serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin-top: 2rem;
  margin-bottom: 0.75rem;
}

p {
  margin-bottom: 1.5rem;
  font-size: 1.05rem;
}

a {
  color: #d32f2f;
  text-decoration: none;
  border-bottom: 1px solid rgba(211, 47, 47, 0.2);
  transition: all 0.2s;
}

a:hover {
  color: #b71c1c;
  border-bottom-color: rgba(211, 47, 47, 0.6);
}

blockquote {
  border-left: 2px solid #d32f2f;
  padding: 0.5rem 1.5rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: #555;
}

code {
  background: #f5f5f5;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Consolas', 'Liberation Mono', monospace;
  font-size: 0.9rem;
}

ul, ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
}

li {
  margin-bottom: 0.75rem;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 2rem auto;
}

.featured-image {
  margin: 2rem -2rem;
  max-width: calc(100% + 4rem);
}

table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
}

th, td {
  padding: 0.75rem;
  border-bottom: 1px solid #eee;
  text-align: left;
}

th {
  font-weight: 600;
  border-bottom: 2px solid #ddd;
}

@media (max-width: 768px) {
  body {
    padding: 1rem;
  }
  
  h1 {
    font-size: 2.2rem;
  }
  
  h2 {
    font-size: 1.6rem;
  }
  
  h3 {
    font-size: 1.3rem;
  }
  
  .featured-image {
    margin: 2rem -1rem;
    max-width: calc(100% + 2rem);
  }
}