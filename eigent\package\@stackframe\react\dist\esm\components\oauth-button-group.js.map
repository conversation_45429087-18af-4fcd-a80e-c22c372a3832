{"version": 3, "sources": ["../../../src/components/oauth-button-group.tsx"], "sourcesContent": ["'use client';\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { useStackApp } from \"../lib/hooks\";\nimport { OAuthButton } from \"./oauth-button\";\n\nexport function OAuthButtonGroup({\n  type,\n  mockProject,\n}: {\n  type: 'sign-in' | 'sign-up',\n  mockProject?: {\n    config: {\n      oauthProviders: {\n        id: string,\n      }[],\n    },\n  },\n}) {\n  const stackApp = useStackApp();\n  const project = mockProject || stackApp.useProject();\n  return (\n    <div className='gap-4 flex flex-col items-stretch stack-scope'>\n      {project.config.oauthProviders.map(p => (\n        <OAuthButton key={p.id} provider={p.id} type={type}\n          isMock={!!mockProject}\n        />\n      ))}\n    </div>\n  );\n}\n"], "mappings": ";;;AAOA,SAAS,mBAAmB;AAC5B,SAAS,mBAAmB;AAoBpB;AAlBD,SAAS,iBAAiB;AAAA,EAC/B;AAAA,EACA;AACF,GASG;AACD,QAAM,WAAW,YAAY;AAC7B,QAAM,UAAU,eAAe,SAAS,WAAW;AACnD,SACE,oBAAC,SAAI,WAAU,iDACZ,kBAAQ,OAAO,eAAe,IAAI,OACjC;AAAA,IAAC;AAAA;AAAA,MAAuB,UAAU,EAAE;AAAA,MAAI;AAAA,MACtC,QAAQ,CAAC,CAAC;AAAA;AAAA,IADM,EAAE;AAAA,EAEpB,CACD,GACH;AAEJ;", "names": []}