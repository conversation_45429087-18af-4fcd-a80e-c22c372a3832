{"version": 3, "sources": ["../../../../../src/components-page/account-settings/email-and-auth/mfa-section.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { createTOTPKeyURI, verifyTOTP } from \"@oslojs/otp\";\nimport { useAsyncCallback } from '@stackframe/stack-shared/dist/hooks/use-async-callback';\nimport { generateRandomValues } from '@stackframe/stack-shared/dist/utils/crypto';\nimport { throwErr } from \"@stackframe/stack-shared/dist/utils/errors\";\nimport { runAsynchronouslyWithAlert } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { Button, Input, Typography } from \"@stackframe/stack-ui\";\nimport * as QRCode from 'qrcode';\nimport { useEffect, useState } from \"react\";\nimport { CurrentUser, Project } from '../../..';\nimport { useStackApp, useUser } from \"../../../lib/hooks\";\nimport { useTranslation } from \"../../../lib/translations\";\nimport { Section } from \"../section\";\n\nexport function MfaSection() {\n  const { t } = useTranslation();\n  const project = useStackApp().useProject();\n  const user = useUser({ or: \"throw\" });\n  const [generatedSecret, setGeneratedSecret] = useState<Uint8Array | null>(null);\n  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);\n  const [mfaCode, setMfaCode] = useState<string>(\"\");\n  const [isMaybeWrong, setIsMaybeWrong] = useState(false);\n  const isEnabled = user.isMultiFactorRequired;\n\n  const [handleSubmit, isLoading] = useAsyncCallback(async () => {\n    await user.update({\n      totpMultiFactorSecret: generatedSecret,\n    });\n    setGeneratedSecret(null);\n    setQrCodeUrl(null);\n    setMfaCode(\"\");\n  }, [generatedSecret, user]);\n\n  useEffect(() => {\n    setIsMaybeWrong(false);\n    runAsynchronouslyWithAlert(async () => {\n      if (generatedSecret && verifyTOTP(generatedSecret, 30, 6, mfaCode)) {\n        await handleSubmit();\n      }\n      setIsMaybeWrong(true);\n    });\n  }, [mfaCode, generatedSecret, handleSubmit]);\n\n  return (\n    <Section\n      title={t(\"Multi-factor authentication\")}\n      description={isEnabled\n        ? t(\"Multi-factor authentication is currently enabled.\")\n        : t(\"Multi-factor authentication is currently disabled.\")}\n    >\n      <div className='flex flex-col gap-4'>\n        {!isEnabled && generatedSecret && (\n          <>\n            <Typography>{t(\"Scan this QR code with your authenticator app:\")}</Typography>\n            <img width={200} height={200} src={qrCodeUrl ?? throwErr(\"TOTP QR code failed to generate\")} alt={t(\"TOTP multi-factor authentication QR code\")} />\n            <Typography>{t(\"Then, enter your six-digit MFA code:\")}</Typography>\n            <Input\n              value={mfaCode}\n              onChange={(e) => {\n                setIsMaybeWrong(false);\n                setMfaCode(e.target.value);\n              }}\n              placeholder=\"123456\"\n              maxLength={6}\n              disabled={isLoading}\n            />\n            {isMaybeWrong && mfaCode.length === 6 && (\n              <Typography variant=\"destructive\">{t(\"Incorrect code. Please try again.\")}</Typography>\n            )}\n            <div className='flex'>\n              <Button\n                variant='secondary'\n                onClick={() => {\n                  setGeneratedSecret(null);\n                  setQrCodeUrl(null);\n                  setMfaCode(\"\");\n                }}\n              >\n                {t(\"Cancel\")}\n              </Button>\n            </div>\n          </>\n        )}\n        <div className='flex gap-2'>\n          {isEnabled ? (\n            <Button\n              variant='secondary'\n              onClick={async () => {\n                await user.update({\n                  totpMultiFactorSecret: null,\n                });\n              }}\n            >\n              {t(\"Disable MFA\")}\n            </Button>\n          ) : !generatedSecret && (\n            <Button\n              variant='secondary'\n              onClick={async () => {\n                const secret = generateRandomValues(new Uint8Array(20));\n                setQrCodeUrl(await generateTotpQrCode(project, user, secret));\n                setGeneratedSecret(secret);\n              }}\n            >\n              {t(\"Enable MFA\")}\n            </Button>\n          )}\n        </div>\n      </div>\n    </Section>\n  );\n}\n\n\nasync function generateTotpQrCode(project: Project, user: CurrentUser, secret: Uint8Array) {\n  const uri = createTOTPKeyURI(project.displayName, user.primaryEmail ?? user.id, secret, 30, 6);\n  return await QRCode.toDataURL(uri) as any;\n}\n"], "mappings": ";AAIA,SAAS,kBAAkB,kBAAkB;AAC7C,SAAS,wBAAwB;AACjC,SAAS,4BAA4B;AACrC,SAAS,gBAAgB;AACzB,SAAS,kCAAkC;AAC3C,SAAS,QAAQ,OAAO,kBAAkB;AAC1C,YAAY,YAAY;AACxB,SAAS,WAAW,gBAAgB;AAEpC,SAAS,aAAa,eAAe;AACrC,SAAS,sBAAsB;AAC/B,SAAS,eAAe;AAwCd,mBACE,KADF;AAtCH,SAAS,aAAa;AAC3B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,UAAU,YAAY,EAAE,WAAW;AACzC,QAAM,OAAO,QAAQ,EAAE,IAAI,QAAQ,CAAC;AACpC,QAAM,CAAC,iBAAiB,kBAAkB,IAAI,SAA4B,IAAI;AAC9E,QAAM,CAAC,WAAW,YAAY,IAAI,SAAwB,IAAI;AAC9D,QAAM,CAAC,SAAS,UAAU,IAAI,SAAiB,EAAE;AACjD,QAAM,CAAC,cAAc,eAAe,IAAI,SAAS,KAAK;AACtD,QAAM,YAAY,KAAK;AAEvB,QAAM,CAAC,cAAc,SAAS,IAAI,iBAAiB,YAAY;AAC7D,UAAM,KAAK,OAAO;AAAA,MAChB,uBAAuB;AAAA,IACzB,CAAC;AACD,uBAAmB,IAAI;AACvB,iBAAa,IAAI;AACjB,eAAW,EAAE;AAAA,EACf,GAAG,CAAC,iBAAiB,IAAI,CAAC;AAE1B,YAAU,MAAM;AACd,oBAAgB,KAAK;AACrB,+BAA2B,YAAY;AACrC,UAAI,mBAAmB,WAAW,iBAAiB,IAAI,GAAG,OAAO,GAAG;AAClE,cAAM,aAAa;AAAA,MACrB;AACA,sBAAgB,IAAI;AAAA,IACtB,CAAC;AAAA,EACH,GAAG,CAAC,SAAS,iBAAiB,YAAY,CAAC;AAE3C,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO,EAAE,6BAA6B;AAAA,MACtC,aAAa,YACT,EAAE,mDAAmD,IACrD,EAAE,oDAAoD;AAAA,MAE1D,+BAAC,SAAI,WAAU,uBACZ;AAAA,SAAC,aAAa,mBACb,iCACE;AAAA,8BAAC,cAAY,YAAE,gDAAgD,GAAE;AAAA,UACjE,oBAAC,SAAI,OAAO,KAAK,QAAQ,KAAK,KAAK,aAAa,SAAS,iCAAiC,GAAG,KAAK,EAAE,0CAA0C,GAAG;AAAA,UACjJ,oBAAC,cAAY,YAAE,sCAAsC,GAAE;AAAA,UACvD;AAAA,YAAC;AAAA;AAAA,cACC,OAAO;AAAA,cACP,UAAU,CAAC,MAAM;AACf,gCAAgB,KAAK;AACrB,2BAAW,EAAE,OAAO,KAAK;AAAA,cAC3B;AAAA,cACA,aAAY;AAAA,cACZ,WAAW;AAAA,cACX,UAAU;AAAA;AAAA,UACZ;AAAA,UACC,gBAAgB,QAAQ,WAAW,KAClC,oBAAC,cAAW,SAAQ,eAAe,YAAE,mCAAmC,GAAE;AAAA,UAE5E,oBAAC,SAAI,WAAU,QACb;AAAA,YAAC;AAAA;AAAA,cACC,SAAQ;AAAA,cACR,SAAS,MAAM;AACb,mCAAmB,IAAI;AACvB,6BAAa,IAAI;AACjB,2BAAW,EAAE;AAAA,cACf;AAAA,cAEC,YAAE,QAAQ;AAAA;AAAA,UACb,GACF;AAAA,WACF;AAAA,QAEF,oBAAC,SAAI,WAAU,cACZ,sBACC;AAAA,UAAC;AAAA;AAAA,YACC,SAAQ;AAAA,YACR,SAAS,YAAY;AACnB,oBAAM,KAAK,OAAO;AAAA,gBAChB,uBAAuB;AAAA,cACzB,CAAC;AAAA,YACH;AAAA,YAEC,YAAE,aAAa;AAAA;AAAA,QAClB,IACE,CAAC,mBACH;AAAA,UAAC;AAAA;AAAA,YACC,SAAQ;AAAA,YACR,SAAS,YAAY;AACnB,oBAAM,SAAS,qBAAqB,IAAI,WAAW,EAAE,CAAC;AACtD,2BAAa,MAAM,mBAAmB,SAAS,MAAM,MAAM,CAAC;AAC5D,iCAAmB,MAAM;AAAA,YAC3B;AAAA,YAEC,YAAE,YAAY;AAAA;AAAA,QACjB,GAEJ;AAAA,SACF;AAAA;AAAA,EACF;AAEJ;AAGA,eAAe,mBAAmB,SAAkB,MAAmB,QAAoB;AACzF,QAAM,MAAM,iBAAiB,QAAQ,aAAa,KAAK,gBAAgB,KAAK,IAAI,QAAQ,IAAI,CAAC;AAC7F,SAAO,MAAa,iBAAU,GAAG;AACnC;", "names": []}