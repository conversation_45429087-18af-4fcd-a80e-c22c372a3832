{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nexport * from './lib/stack-app';\n\nexport { default as StackHandler } from \"./components-page/stack-handler\";\nexport { useStackApp, useUser } from \"./lib/hooks\";\nexport { default as StackProvider } from \"./providers/stack-provider\";\nexport { StackTheme } from './providers/theme-provider';\n\nexport { AccountSettings } from \"./components-page/account-settings\";\nexport { AuthPage } from \"./components-page/auth-page\";\nexport { EmailVerification } from \"./components-page/email-verification\";\nexport { ForgotPassword } from \"./components-page/forgot-password\";\nexport { PasswordReset } from \"./components-page/password-reset\";\nexport { SignIn } from \"./components-page/sign-in\";\nexport { SignUp } from \"./components-page/sign-up\";\nexport { CredentialSignIn as CredentialSignIn } from \"./components/credential-sign-in\";\nexport { CredentialSignUp as CredentialSignUp } from \"./components/credential-sign-up\";\nexport { UserAvatar } from \"./components/elements/user-avatar\";\nexport { MagicLinkSignIn as MagicLinkSignIn } from \"./components/magic-link-sign-in\";\nexport { MessageCard } from \"./components/message-cards/message-card\";\nexport { OAuthButton } from \"./components/oauth-button\";\nexport { OAuthButtonGroup } from \"./components/oauth-button-group\";\nexport { SelectedTeamSwitcher } from \"./components/selected-team-switcher\";\nexport { UserButton } from \"./components/user-button\";\nexport { CliAuthConfirmation } from \"./components-page/cli-auth-confirm\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,0BAAc,4BAJd;AAMA,2BAAwC;AACxC,mBAAqC;AACrC,4BAAyC;AACzC,4BAA2B;AAE3B,8BAAgC;AAChC,uBAAyB;AACzB,gCAAkC;AAClC,6BAA+B;AAC/B,4BAA8B;AAC9B,qBAAuB;AACvB,qBAAuB;AACvB,gCAAqD;AACrD,gCAAqD;AACrD,yBAA2B;AAC3B,gCAAmD;AACnD,0BAA4B;AAC5B,0BAA4B;AAC5B,gCAAiC;AACjC,oCAAqC;AACrC,yBAA2B;AAC3B,8BAAoC;", "names": []}