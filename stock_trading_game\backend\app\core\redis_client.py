"""
🐾 Redis客户端管理
Redis Client Management

提供Redis连接、缓存操作和实时数据存储功能
"""

import json
from typing import Any, Optional, Dict, List
import aioredis
from loguru import logger

from app.core.config import settings


class RedisClient:
    """Redis客户端封装类"""
    
    def __init__(self):
        self.redis: Optional[aioredis.Redis] = None
        self.pubsub: Optional[aioredis.client.PubSub] = None
    
    async def connect(self) -> None:
        """连接Redis"""
        try:
            self.redis = aioredis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True,
                max_connections=settings.REDIS_POOL_SIZE
            )
            
            # 测试连接
            await self.redis.ping()
            logger.info("✅ Redis连接成功")
            
        except Exception as e:
            logger.error(f"❌ Redis连接失败: {e}")
            raise
    
    async def disconnect(self) -> None:
        """断开Redis连接"""
        try:
            if self.pubsub:
                await self.pubsub.close()
            if self.redis:
                await self.redis.close()
            logger.info("✅ Redis连接已关闭")
        except Exception as e:
            logger.error(f"❌ 关闭Redis连接失败: {e}")
    
    async def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """设置键值对"""
        try:
            if isinstance(value, (dict, list)):
                value = json.dumps(value, ensure_ascii=False)
            
            result = await self.redis.set(key, value, ex=expire)
            return result
        except Exception as e:
            logger.error(f"❌ Redis设置失败 {key}: {e}")
            return False
    
    async def get(self, key: str) -> Optional[Any]:
        """获取值"""
        try:
            value = await self.redis.get(key)
            if value is None:
                return None
            
            # 尝试解析JSON
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
                
        except Exception as e:
            logger.error(f"❌ Redis获取失败 {key}: {e}")
            return None
    
    async def delete(self, key: str) -> bool:
        """删除键"""
        try:
            result = await self.redis.delete(key)
            return result > 0
        except Exception as e:
            logger.error(f"❌ Redis删除失败 {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            result = await self.redis.exists(key)
            return result > 0
        except Exception as e:
            logger.error(f"❌ Redis检查存在失败 {key}: {e}")
            return False
    
    async def expire(self, key: str, seconds: int) -> bool:
        """设置过期时间"""
        try:
            result = await self.redis.expire(key, seconds)
            return result
        except Exception as e:
            logger.error(f"❌ Redis设置过期时间失败 {key}: {e}")
            return False
    
    async def hset(self, name: str, mapping: Dict[str, Any]) -> int:
        """设置哈希表"""
        try:
            # 将字典值转换为JSON字符串
            json_mapping = {}
            for k, v in mapping.items():
                if isinstance(v, (dict, list)):
                    json_mapping[k] = json.dumps(v, ensure_ascii=False)
                else:
                    json_mapping[k] = str(v)
            
            result = await self.redis.hset(name, mapping=json_mapping)
            return result
        except Exception as e:
            logger.error(f"❌ Redis哈希设置失败 {name}: {e}")
            return 0
    
    async def hget(self, name: str, key: str) -> Optional[Any]:
        """获取哈希表值"""
        try:
            value = await self.redis.hget(name, key)
            if value is None:
                return None
            
            # 尝试解析JSON
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
                
        except Exception as e:
            logger.error(f"❌ Redis哈希获取失败 {name}.{key}: {e}")
            return None
    
    async def hgetall(self, name: str) -> Dict[str, Any]:
        """获取整个哈希表"""
        try:
            data = await self.redis.hgetall(name)
            result = {}
            
            for k, v in data.items():
                try:
                    result[k] = json.loads(v)
                except (json.JSONDecodeError, TypeError):
                    result[k] = v
            
            return result
        except Exception as e:
            logger.error(f"❌ Redis哈希获取全部失败 {name}: {e}")
            return {}
    
    async def lpush(self, key: str, *values: Any) -> int:
        """列表左侧推入"""
        try:
            json_values = []
            for value in values:
                if isinstance(value, (dict, list)):
                    json_values.append(json.dumps(value, ensure_ascii=False))
                else:
                    json_values.append(str(value))
            
            result = await self.redis.lpush(key, *json_values)
            return result
        except Exception as e:
            logger.error(f"❌ Redis列表推入失败 {key}: {e}")
            return 0
    
    async def rpop(self, key: str) -> Optional[Any]:
        """列表右侧弹出"""
        try:
            value = await self.redis.rpop(key)
            if value is None:
                return None
            
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
                
        except Exception as e:
            logger.error(f"❌ Redis列表弹出失败 {key}: {e}")
            return None
    
    async def lrange(self, key: str, start: int = 0, end: int = -1) -> List[Any]:
        """获取列表范围"""
        try:
            values = await self.redis.lrange(key, start, end)
            result = []
            
            for value in values:
                try:
                    result.append(json.loads(value))
                except (json.JSONDecodeError, TypeError):
                    result.append(value)
            
            return result
        except Exception as e:
            logger.error(f"❌ Redis列表范围获取失败 {key}: {e}")
            return []
    
    async def publish(self, channel: str, message: Any) -> int:
        """发布消息"""
        try:
            if isinstance(message, (dict, list)):
                message = json.dumps(message, ensure_ascii=False)
            
            result = await self.redis.publish(channel, message)
            return result
        except Exception as e:
            logger.error(f"❌ Redis发布消息失败 {channel}: {e}")
            return 0
    
    async def subscribe(self, *channels: str):
        """订阅频道"""
        try:
            if not self.pubsub:
                self.pubsub = self.redis.pubsub()
            
            await self.pubsub.subscribe(*channels)
            return self.pubsub
        except Exception as e:
            logger.error(f"❌ Redis订阅失败 {channels}: {e}")
            return None
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            await self.redis.ping()
            return True
        except Exception as e:
            logger.error(f"❌ Redis健康检查失败: {e}")
            return False


# 全局Redis客户端实例
redis_client = RedisClient()


async def init_redis() -> None:
    """初始化Redis连接"""
    await redis_client.connect()


async def close_redis() -> None:
    """关闭Redis连接"""
    await redis_client.disconnect()


# 便捷函数
async def cache_set(key: str, value: Any, expire: Optional[int] = None) -> bool:
    """缓存设置"""
    return await redis_client.set(key, value, expire or settings.CACHE_TTL)


async def cache_get(key: str) -> Optional[Any]:
    """缓存获取"""
    return await redis_client.get(key)


async def cache_delete(key: str) -> bool:
    """缓存删除"""
    return await redis_client.delete(key)


# 导出
__all__ = [
    "RedisClient",
    "redis_client",
    "init_redis",
    "close_redis",
    "cache_set",
    "cache_get", 
    "cache_delete"
]
