{"version": 3, "sources": ["../../src/utils/types.tsx"], "sourcesContent": ["export type IsAny<T> = 0 extends (1 & T) ? true : false;\nexport type isNullish<T> = T extends null | undefined ? true : false;\n\nexport type NullishCoalesce<T, U> = T extends null | undefined ? U : T;\n\n// distributive conditional type magic. See: https://stackoverflow.com/a/50375286\nexport type UnionToIntersection<U> =\n  (U extends any ? (x: U) => void : never) extends ((x: infer I) => void) ? I : never\n\n\n/**\n * A variation of TypeScript's conditionals with slightly different semantics. It is the perfect type for cases where:\n *\n * - If all possible values are contained in `Extends`, then it will be mapped to `Then`.\n * - If all possible values are not contained in `Extends`, then it will be mapped to `Otherwise`.\n * - If some possible values are contained in `Extends` and some are not, then it will be mapped to `Then | Otherwise`.\n *\n * This is different from TypeScript's built-in conditional types (`Value extends Extends ? Then : Otherwise`), which\n * returns `Otherwise` for the third case (causing unsoundness in many real-world cases).\n */\nexport type IfAndOnlyIf<Value, Extends, Then, Otherwise> =\n  | (Value extends Extends ? never : Otherwise)\n  | (Value & Extends extends never ? never : Then);\n\n\n/**\n * Can be used to prettify a type in the IDE; for example, some complicated intersected types can be flattened into a single type.\n */\nexport type PrettifyType<T> = T extends object ? { [K in keyof T]: T[K] } & {} : T;\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;", "names": []}