{"version": 3, "sources": ["../../../../../src/lib/stack-app/apps/implementations/admin-app-impl.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { StackAdminInterface } from \"@stackframe/stack-shared\";\nimport { getProductionModeErrors } from \"@stackframe/stack-shared/dist/helpers/production-mode\";\nimport { InternalApiKeyCreateCrudResponse } from \"@stackframe/stack-shared/dist/interface/adminInterface\";\nimport { EmailTemplateCrud, EmailTemplateType } from \"@stackframe/stack-shared/dist/interface/crud/email-templates\";\nimport { InternalApiKeysCrud } from \"@stackframe/stack-shared/dist/interface/crud/internal-api-keys\";\nimport { ProjectsCrud } from \"@stackframe/stack-shared/dist/interface/crud/projects\";\nimport { StackAssertionError, throwErr } from \"@stackframe/stack-shared/dist/utils/errors\";\nimport { pick } from \"@stackframe/stack-shared/dist/utils/objects\";\nimport { Result } from \"@stackframe/stack-shared/dist/utils/results\";\nimport { useMemo } from \"react\"; // THIS_LINE_PLATFORM react-like\nimport { AdminSentEmail } from \"../..\";\nimport { EmailConfig, stackAppInternalsSymbol } from \"../../common\";\nimport { AdminEmailTemplate, AdminEmailTemplateUpdateOptions, adminEmailTemplateUpdateOptionsToCrud } from \"../../email-templates\";\nimport { InternalApiKey, InternalApiKeyBase, InternalApiKeyBaseCrudRead, InternalApiKeyCreateOptions, InternalApiKeyFirstView, internalApiKeyCreateOptionsToCrud } from \"../../internal-api-keys\";\nimport { AdminProjectPermission, AdminProjectPermissionDefinition, AdminProjectPermissionDefinitionCreateOptions, AdminProjectPermissionDefinitionUpdateOptions, AdminTeamPermission, AdminTeamPermissionDefinition, AdminTeamPermissionDefinitionCreateOptions, AdminTeamPermissionDefinitionUpdateOptions, adminProjectPermissionDefinitionCreateOptionsToCrud, adminProjectPermissionDefinitionUpdateOptionsToCrud, adminTeamPermissionDefinitionCreateOptionsToCrud, adminTeamPermissionDefinitionUpdateOptionsToCrud } from \"../../permissions\";\nimport { AdminOwnedProject, AdminProject, AdminProjectUpdateOptions, adminProjectUpdateOptionsToCrud } from \"../../projects\";\nimport { StackAdminApp, StackAdminAppConstructorOptions } from \"../interfaces/admin-app\";\nimport { clientVersion, createCache, getBaseUrl, getDefaultProjectId, getDefaultPublishableClientKey, getDefaultSecretServerKey, getDefaultSuperSecretAdminKey } from \"./common\";\nimport { _StackServerAppImplIncomplete } from \"./server-app-impl\";\n\nimport { useAsyncCache } from \"./common\"; // THIS_LINE_PLATFORM react-like\n\nexport class _StackAdminAppImplIncomplete<HasTokenStore extends boolean, ProjectId extends string> extends _StackServerAppImplIncomplete<HasTokenStore, ProjectId> implements StackAdminApp<HasTokenStore, ProjectId>\n{\n  declare protected _interface: StackAdminInterface;\n\n  private readonly _adminProjectCache = createCache(async () => {\n    return await this._interface.getProject();\n  });\n  private readonly _internalApiKeysCache = createCache(async () => {\n    const res = await this._interface.listInternalApiKeys();\n    return res;\n  });\n  private readonly _adminEmailTemplatesCache = createCache(async () => {\n    return await this._interface.listEmailTemplates();\n  });\n  private readonly _adminTeamPermissionDefinitionsCache = createCache(async () => {\n    return await this._interface.listTeamPermissionDefinitions();\n  });\n  private readonly _adminProjectPermissionDefinitionsCache = createCache(async () => {\n    return await this._interface.listProjectPermissionDefinitions();\n  });\n  private readonly _svixTokenCache = createCache(async () => {\n    return await this._interface.getSvixToken();\n  });\n  private readonly _metricsCache = createCache(async () => {\n    return await this._interface.getMetrics();\n  });\n\n  constructor(options: StackAdminAppConstructorOptions<HasTokenStore, ProjectId>) {\n    super({\n      interface: new StackAdminInterface({\n        getBaseUrl: () => getBaseUrl(options.baseUrl),\n        projectId: options.projectId ?? getDefaultProjectId(),\n        extraRequestHeaders: options.extraRequestHeaders ?? {},\n        clientVersion,\n        ...\"projectOwnerSession\" in options ? {\n          projectOwnerSession: options.projectOwnerSession,\n        } : {\n          publishableClientKey: options.publishableClientKey ?? getDefaultPublishableClientKey(),\n          secretServerKey: options.secretServerKey ?? getDefaultSecretServerKey(),\n          superSecretAdminKey: options.superSecretAdminKey ?? getDefaultSuperSecretAdminKey(),\n        },\n      }),\n      baseUrl: options.baseUrl,\n      extraRequestHeaders: options.extraRequestHeaders,\n      projectId: options.projectId,\n      tokenStore: options.tokenStore,\n      urls: options.urls,\n      oauthScopesOnSignIn: options.oauthScopesOnSignIn,\n      redirectMethod: options.redirectMethod,\n    });\n  }\n\n  _adminOwnedProjectFromCrud(data: ProjectsCrud['Admin']['Read'], onRefresh: () => Promise<void>): AdminOwnedProject {\n    if (this._tokenStoreInit !== null) {\n      throw new StackAssertionError(\"Owned apps must always have tokenStore === null — did you not create this project with app._createOwnedApp()?\");\n    }\n    return {\n      ...this._adminProjectFromCrud(data, onRefresh),\n      app: this as StackAdminApp<false>,\n    };\n  }\n\n  _adminProjectFromCrud(data: ProjectsCrud['Admin']['Read'], onRefresh: () => Promise<void>): AdminProject {\n    if (data.id !== this.projectId) {\n      throw new StackAssertionError(`The project ID of the provided project JSON (${data.id}) does not match the project ID of the app (${this.projectId})!`);\n    }\n\n    const app = this;\n    return {\n      id: data.id,\n      displayName: data.display_name,\n      description: data.description,\n      createdAt: new Date(data.created_at_millis),\n      userCount: data.user_count,\n      isProductionMode: data.is_production_mode,\n      config: {\n        signUpEnabled: data.config.sign_up_enabled,\n        credentialEnabled: data.config.credential_enabled,\n        magicLinkEnabled: data.config.magic_link_enabled,\n        passkeyEnabled: data.config.passkey_enabled,\n        clientTeamCreationEnabled: data.config.client_team_creation_enabled,\n        clientUserDeletionEnabled: data.config.client_user_deletion_enabled,\n        allowLocalhost: data.config.allow_localhost,\n        oauthAccountMergeStrategy: data.config.oauth_account_merge_strategy,\n        allowUserApiKeys: data.config.allow_user_api_keys,\n        allowTeamApiKeys: data.config.allow_team_api_keys,\n        oauthProviders: data.config.oauth_providers.map((p) => ((p.type === 'shared' ? {\n          id: p.id,\n          type: 'shared',\n        } as const : {\n          id: p.id,\n          type: 'standard',\n          clientId: p.client_id ?? throwErr(\"Client ID is missing\"),\n          clientSecret: p.client_secret ?? throwErr(\"Client secret is missing\"),\n          facebookConfigId: p.facebook_config_id,\n          microsoftTenantId: p.microsoft_tenant_id,\n        } as const))),\n        emailConfig: data.config.email_config.type === 'shared' ? {\n          type: 'shared'\n        } : {\n          type: 'standard',\n          host: data.config.email_config.host ?? throwErr(\"Email host is missing\"),\n          port: data.config.email_config.port ?? throwErr(\"Email port is missing\"),\n          username: data.config.email_config.username ?? throwErr(\"Email username is missing\"),\n          password: data.config.email_config.password ?? throwErr(\"Email password is missing\"),\n          senderName: data.config.email_config.sender_name ?? throwErr(\"Email sender name is missing\"),\n          senderEmail: data.config.email_config.sender_email ?? throwErr(\"Email sender email is missing\"),\n        },\n        domains: data.config.domains.map((d) => ({\n          domain: d.domain,\n          handlerPath: d.handler_path,\n        })),\n        createTeamOnSignUp: data.config.create_team_on_sign_up,\n        teamCreatorDefaultPermissions: data.config.team_creator_default_permissions,\n        teamMemberDefaultPermissions: data.config.team_member_default_permissions,\n        userDefaultPermissions: data.config.user_default_permissions,\n      },\n\n      async update(update: AdminProjectUpdateOptions) {\n        await app._interface.updateProject(adminProjectUpdateOptionsToCrud(update));\n        await onRefresh();\n      },\n      async delete() {\n        await app._interface.deleteProject();\n      },\n      async getProductionModeErrors() {\n        return getProductionModeErrors(data);\n      },\n      useProductionModeErrors() {\n        return getProductionModeErrors(data);\n      },\n    };\n  }\n\n  _adminEmailTemplateFromCrud(data: EmailTemplateCrud['Admin']['Read']): AdminEmailTemplate {\n    return {\n      type: data.type,\n      subject: data.subject,\n      content: data.content,\n      isDefault: data.is_default,\n    };\n  }\n\n  override async getProject(): Promise<AdminProject> {\n    return this._adminProjectFromCrud(\n      Result.orThrow(await this._adminProjectCache.getOrWait([], \"write-only\")),\n      () => this._refreshProject()\n    );\n  }\n\n  override useProject(): AdminProject {\n    const crud = useAsyncCache(this._adminProjectCache, [], \"useProjectAdmin()\");\n    return useMemo(() => this._adminProjectFromCrud(\n      crud,\n      () => this._refreshProject()\n    ), [crud]);\n  }\n\n  protected _createInternalApiKeyBaseFromCrud(data: InternalApiKeyBaseCrudRead): InternalApiKeyBase {\n    const app = this;\n    return {\n      id: data.id,\n      description: data.description,\n      expiresAt: new Date(data.expires_at_millis),\n      manuallyRevokedAt: data.manually_revoked_at_millis ? new Date(data.manually_revoked_at_millis) : null,\n      createdAt: new Date(data.created_at_millis),\n      isValid() {\n        return this.whyInvalid() === null;\n      },\n      whyInvalid() {\n        if (this.expiresAt.getTime() < Date.now()) return \"expired\";\n        if (this.manuallyRevokedAt) return \"manually-revoked\";\n        return null;\n      },\n      async revoke() {\n        const res = await app._interface.revokeInternalApiKeyById(data.id);\n        await app._refreshInternalApiKeys();\n        return res;\n      }\n    };\n  }\n\n  protected _createInternalApiKeyFromCrud(data: InternalApiKeysCrud[\"Admin\"][\"Read\"]): InternalApiKey {\n    return {\n      ...this._createInternalApiKeyBaseFromCrud(data),\n      publishableClientKey: data.publishable_client_key ? { lastFour: data.publishable_client_key.last_four } : null,\n      secretServerKey: data.secret_server_key ? { lastFour: data.secret_server_key.last_four } : null,\n      superSecretAdminKey: data.super_secret_admin_key ? { lastFour: data.super_secret_admin_key.last_four } : null,\n    };\n  }\n\n  protected _createInternalApiKeyFirstViewFromCrud(data: InternalApiKeyCreateCrudResponse): InternalApiKeyFirstView {\n    return {\n      ...this._createInternalApiKeyBaseFromCrud(data),\n      publishableClientKey: data.publishable_client_key,\n      secretServerKey: data.secret_server_key,\n      superSecretAdminKey: data.super_secret_admin_key,\n    };\n  }\n\n  async listInternalApiKeys(): Promise<InternalApiKey[]> {\n    const crud = Result.orThrow(await this._internalApiKeysCache.getOrWait([], \"write-only\"));\n    return crud.map((j) => this._createInternalApiKeyFromCrud(j));\n  }\n\n  useInternalApiKeys(): InternalApiKey[] {\n    const crud = useAsyncCache(this._internalApiKeysCache, [], \"useInternalApiKeys()\");\n    return useMemo(() => {\n      return crud.map((j) => this._createInternalApiKeyFromCrud(j));\n    }, [crud]);\n  }\n\n  async createInternalApiKey(options: InternalApiKeyCreateOptions): Promise<InternalApiKeyFirstView> {\n    const crud = await this._interface.createInternalApiKey(internalApiKeyCreateOptionsToCrud(options));\n    await this._refreshInternalApiKeys();\n    return this._createInternalApiKeyFirstViewFromCrud(crud);\n  }\n\n  useEmailTemplates(): AdminEmailTemplate[] {\n    const crud = useAsyncCache(this._adminEmailTemplatesCache, [], \"useEmailTemplates()\");\n    return useMemo(() => {\n      return crud.map((j) => this._adminEmailTemplateFromCrud(j));\n    }, [crud]);\n  }\n  async listEmailTemplates(): Promise<AdminEmailTemplate[]> {\n    const crud = Result.orThrow(await this._adminEmailTemplatesCache.getOrWait([], \"write-only\"));\n    return crud.map((j) => this._adminEmailTemplateFromCrud(j));\n  }\n\n  async updateEmailTemplate(type: EmailTemplateType, data: AdminEmailTemplateUpdateOptions): Promise<void> {\n    await this._interface.updateEmailTemplate(type, adminEmailTemplateUpdateOptionsToCrud(data));\n    await this._adminEmailTemplatesCache.refresh([]);\n  }\n\n  async resetEmailTemplate(type: EmailTemplateType) {\n    await this._interface.resetEmailTemplate(type);\n    await this._adminEmailTemplatesCache.refresh([]);\n  }\n\n  async createTeamPermissionDefinition(data: AdminTeamPermissionDefinitionCreateOptions): Promise<AdminTeamPermission>{\n    const crud = await this._interface.createTeamPermissionDefinition(adminTeamPermissionDefinitionCreateOptionsToCrud(data));\n    await this._adminTeamPermissionDefinitionsCache.refresh([]);\n    return this._serverTeamPermissionDefinitionFromCrud(crud);\n  }\n\n  async updateTeamPermissionDefinition(permissionId: string, data: AdminTeamPermissionDefinitionUpdateOptions) {\n    await this._interface.updateTeamPermissionDefinition(permissionId, adminTeamPermissionDefinitionUpdateOptionsToCrud(data));\n    await this._adminTeamPermissionDefinitionsCache.refresh([]);\n  }\n\n  async deleteTeamPermissionDefinition(permissionId: string): Promise<void> {\n    await this._interface.deleteTeamPermissionDefinition(permissionId);\n    await this._adminTeamPermissionDefinitionsCache.refresh([]);\n  }\n\n  async listTeamPermissionDefinitions(): Promise<AdminTeamPermissionDefinition[]> {\n    const crud = Result.orThrow(await this._adminTeamPermissionDefinitionsCache.getOrWait([], \"write-only\"));\n    return crud.map((p) => this._serverTeamPermissionDefinitionFromCrud(p));\n  }\n\n  useTeamPermissionDefinitions(): AdminTeamPermissionDefinition[] {\n    const crud = useAsyncCache(this._adminTeamPermissionDefinitionsCache, [], \"usePermissions()\");\n    return useMemo(() => {\n      return crud.map((p) => this._serverTeamPermissionDefinitionFromCrud(p));\n    }, [crud]);\n  }\n\n  async createProjectPermissionDefinition(data: AdminProjectPermissionDefinitionCreateOptions): Promise<AdminProjectPermission> {\n    const crud = await this._interface.createProjectPermissionDefinition(adminProjectPermissionDefinitionCreateOptionsToCrud(data));\n    await this._adminProjectPermissionDefinitionsCache.refresh([]);\n    return this._serverProjectPermissionDefinitionFromCrud(crud);\n  }\n\n  async updateProjectPermissionDefinition(permissionId: string, data: AdminProjectPermissionDefinitionUpdateOptions) {\n    await this._interface.updateProjectPermissionDefinition(permissionId, adminProjectPermissionDefinitionUpdateOptionsToCrud(data));\n    await this._adminProjectPermissionDefinitionsCache.refresh([]);\n  }\n\n  async deleteProjectPermissionDefinition(permissionId: string): Promise<void> {\n    await this._interface.deleteProjectPermissionDefinition(permissionId);\n    await this._adminProjectPermissionDefinitionsCache.refresh([]);\n  }\n\n  async listProjectPermissionDefinitions(): Promise<AdminProjectPermissionDefinition[]> {\n    const crud = Result.orThrow(await this._adminProjectPermissionDefinitionsCache.getOrWait([], \"write-only\"));\n    return crud.map((p) => this._serverProjectPermissionDefinitionFromCrud(p));\n  }\n\n  useProjectPermissionDefinitions(): AdminProjectPermissionDefinition[] {\n    const crud = useAsyncCache(this._adminProjectPermissionDefinitionsCache, [], \"useProjectPermissions()\");\n    return useMemo(() => {\n      return crud.map((p) => this._serverProjectPermissionDefinitionFromCrud(p));\n    }, [crud]);\n  }\n  useSvixToken(): string {\n    const crud = useAsyncCache(this._svixTokenCache, [], \"useSvixToken()\");\n    return crud.token;\n  }\n\n  protected override async _refreshProject() {\n    await Promise.all([\n      super._refreshProject(),\n      this._adminProjectCache.refresh([]),\n    ]);\n  }\n\n  protected async _refreshInternalApiKeys() {\n    await this._internalApiKeysCache.refresh([]);\n  }\n\n  get [stackAppInternalsSymbol]() {\n    return {\n      ...super[stackAppInternalsSymbol],\n      useMetrics: (): any => {\n        return useAsyncCache(this._metricsCache, [], \"useMetrics()\");\n      }\n    };\n  }\n\n  async sendTestEmail(options: {\n    recipientEmail: string,\n    emailConfig: EmailConfig,\n  }): Promise<Result<undefined, { errorMessage: string }>> {\n    const response = await this._interface.sendTestEmail({\n      recipient_email: options.recipientEmail,\n      email_config: {\n        ...(pick(options.emailConfig, ['host', 'port', 'username', 'password'])),\n        sender_email: options.emailConfig.senderEmail,\n        sender_name: options.emailConfig.senderName,\n      },\n    });\n\n    if (response.success) {\n      return Result.ok(undefined);\n    } else {\n      return Result.error({ errorMessage: response.error_message ?? throwErr(\"Email test error not specified\") });\n    }\n  }\n\n  async listSentEmails(): Promise<AdminSentEmail[]> {\n    const response = await this._interface.listSentEmails();\n    return response.items.map((email) => ({\n      id: email.id,\n      to: email.to ?? [],\n      subject: email.subject,\n      recipient: email.to?.[0] ?? \"\",\n      sentAt: new Date(email.sent_at_millis),\n      error: email.error,\n    }));\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,0BAAoC;AACpC,6BAAwC;AAKxC,oBAA8C;AAC9C,qBAAqB;AACrB,qBAAuB;AACvB,mBAAwB;AAExB,oBAAqD;AACrD,6BAA2G;AAC3G,+BAAwK;AACxK,yBAAigB;AACjgB,sBAA4G;AAE5G,IAAAA,iBAAsK;AACtK,6BAA8C;AAE9C,IAAAA,iBAA8B;AAEvB,IAAM,+BAAN,cAAoG,qDAC3G;AAAA,EA0BE,YAAY,SAAoE;AAC9E,UAAM;AAAA,MACJ,WAAW,IAAI,wCAAoB;AAAA,QACjC,YAAY,UAAM,2BAAW,QAAQ,OAAO;AAAA,QAC5C,WAAW,QAAQ,iBAAa,oCAAoB;AAAA,QACpD,qBAAqB,QAAQ,uBAAuB,CAAC;AAAA,QACrD;AAAA,QACA,GAAG,yBAAyB,UAAU;AAAA,UACpC,qBAAqB,QAAQ;AAAA,QAC/B,IAAI;AAAA,UACF,sBAAsB,QAAQ,4BAAwB,+CAA+B;AAAA,UACrF,iBAAiB,QAAQ,uBAAmB,0CAA0B;AAAA,UACtE,qBAAqB,QAAQ,2BAAuB,8CAA8B;AAAA,QACpF;AAAA,MACF,CAAC;AAAA,MACD,SAAS,QAAQ;AAAA,MACjB,qBAAqB,QAAQ;AAAA,MAC7B,WAAW,QAAQ;AAAA,MACnB,YAAY,QAAQ;AAAA,MACpB,MAAM,QAAQ;AAAA,MACd,qBAAqB,QAAQ;AAAA,MAC7B,gBAAgB,QAAQ;AAAA,IAC1B,CAAC;AA7CH,SAAiB,yBAAqB,4BAAY,YAAY;AAC5D,aAAO,MAAM,KAAK,WAAW,WAAW;AAAA,IAC1C,CAAC;AACD,SAAiB,4BAAwB,4BAAY,YAAY;AAC/D,YAAM,MAAM,MAAM,KAAK,WAAW,oBAAoB;AACtD,aAAO;AAAA,IACT,CAAC;AACD,SAAiB,gCAA4B,4BAAY,YAAY;AACnE,aAAO,MAAM,KAAK,WAAW,mBAAmB;AAAA,IAClD,CAAC;AACD,SAAiB,2CAAuC,4BAAY,YAAY;AAC9E,aAAO,MAAM,KAAK,WAAW,8BAA8B;AAAA,IAC7D,CAAC;AACD,SAAiB,8CAA0C,4BAAY,YAAY;AACjF,aAAO,MAAM,KAAK,WAAW,iCAAiC;AAAA,IAChE,CAAC;AACD,SAAiB,sBAAkB,4BAAY,YAAY;AACzD,aAAO,MAAM,KAAK,WAAW,aAAa;AAAA,IAC5C,CAAC;AACD,SAAiB,oBAAgB,4BAAY,YAAY;AACvD,aAAO,MAAM,KAAK,WAAW,WAAW;AAAA,IAC1C,CAAC;AAAA,EAyBD;AAAA,EAEA,2BAA2B,MAAqC,WAAmD;AACjH,QAAI,KAAK,oBAAoB,MAAM;AACjC,YAAM,IAAI,kCAAoB,oHAA+G;AAAA,IAC/I;AACA,WAAO;AAAA,MACL,GAAG,KAAK,sBAAsB,MAAM,SAAS;AAAA,MAC7C,KAAK;AAAA,IACP;AAAA,EACF;AAAA,EAEA,sBAAsB,MAAqC,WAA8C;AACvG,QAAI,KAAK,OAAO,KAAK,WAAW;AAC9B,YAAM,IAAI,kCAAoB,gDAAgD,KAAK,EAAE,+CAA+C,KAAK,SAAS,IAAI;AAAA,IACxJ;AAEA,UAAM,MAAM;AACZ,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,MACT,aAAa,KAAK;AAAA,MAClB,aAAa,KAAK;AAAA,MAClB,WAAW,IAAI,KAAK,KAAK,iBAAiB;AAAA,MAC1C,WAAW,KAAK;AAAA,MAChB,kBAAkB,KAAK;AAAA,MACvB,QAAQ;AAAA,QACN,eAAe,KAAK,OAAO;AAAA,QAC3B,mBAAmB,KAAK,OAAO;AAAA,QAC/B,kBAAkB,KAAK,OAAO;AAAA,QAC9B,gBAAgB,KAAK,OAAO;AAAA,QAC5B,2BAA2B,KAAK,OAAO;AAAA,QACvC,2BAA2B,KAAK,OAAO;AAAA,QACvC,gBAAgB,KAAK,OAAO;AAAA,QAC5B,2BAA2B,KAAK,OAAO;AAAA,QACvC,kBAAkB,KAAK,OAAO;AAAA,QAC9B,kBAAkB,KAAK,OAAO;AAAA,QAC9B,gBAAgB,KAAK,OAAO,gBAAgB,IAAI,CAAC,MAAQ,EAAE,SAAS,WAAW;AAAA,UAC7E,IAAI,EAAE;AAAA,UACN,MAAM;AAAA,QACR,IAAa;AAAA,UACX,IAAI,EAAE;AAAA,UACN,MAAM;AAAA,UACN,UAAU,EAAE,iBAAa,wBAAS,sBAAsB;AAAA,UACxD,cAAc,EAAE,qBAAiB,wBAAS,0BAA0B;AAAA,UACpE,kBAAkB,EAAE;AAAA,UACpB,mBAAmB,EAAE;AAAA,QACvB,CAAY;AAAA,QACZ,aAAa,KAAK,OAAO,aAAa,SAAS,WAAW;AAAA,UACxD,MAAM;AAAA,QACR,IAAI;AAAA,UACF,MAAM;AAAA,UACN,MAAM,KAAK,OAAO,aAAa,YAAQ,wBAAS,uBAAuB;AAAA,UACvE,MAAM,KAAK,OAAO,aAAa,YAAQ,wBAAS,uBAAuB;AAAA,UACvE,UAAU,KAAK,OAAO,aAAa,gBAAY,wBAAS,2BAA2B;AAAA,UACnF,UAAU,KAAK,OAAO,aAAa,gBAAY,wBAAS,2BAA2B;AAAA,UACnF,YAAY,KAAK,OAAO,aAAa,mBAAe,wBAAS,8BAA8B;AAAA,UAC3F,aAAa,KAAK,OAAO,aAAa,oBAAgB,wBAAS,+BAA+B;AAAA,QAChG;AAAA,QACA,SAAS,KAAK,OAAO,QAAQ,IAAI,CAAC,OAAO;AAAA,UACvC,QAAQ,EAAE;AAAA,UACV,aAAa,EAAE;AAAA,QACjB,EAAE;AAAA,QACF,oBAAoB,KAAK,OAAO;AAAA,QAChC,+BAA+B,KAAK,OAAO;AAAA,QAC3C,8BAA8B,KAAK,OAAO;AAAA,QAC1C,wBAAwB,KAAK,OAAO;AAAA,MACtC;AAAA,MAEA,MAAM,OAAO,QAAmC;AAC9C,cAAM,IAAI,WAAW,kBAAc,iDAAgC,MAAM,CAAC;AAC1E,cAAM,UAAU;AAAA,MAClB;AAAA,MACA,MAAM,SAAS;AACb,cAAM,IAAI,WAAW,cAAc;AAAA,MACrC;AAAA,MACA,MAAM,0BAA0B;AAC9B,mBAAO,gDAAwB,IAAI;AAAA,MACrC;AAAA,MACA,0BAA0B;AACxB,mBAAO,gDAAwB,IAAI;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,4BAA4B,MAA8D;AACxF,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,SAAS,KAAK;AAAA,MACd,WAAW,KAAK;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,MAAe,aAAoC;AACjD,WAAO,KAAK;AAAA,MACV,sBAAO,QAAQ,MAAM,KAAK,mBAAmB,UAAU,CAAC,GAAG,YAAY,CAAC;AAAA,MACxE,MAAM,KAAK,gBAAgB;AAAA,IAC7B;AAAA,EACF;AAAA,EAES,aAA2B;AAClC,UAAM,WAAO,8BAAc,KAAK,oBAAoB,CAAC,GAAG,mBAAmB;AAC3E,eAAO,sBAAQ,MAAM,KAAK;AAAA,MACxB;AAAA,MACA,MAAM,KAAK,gBAAgB;AAAA,IAC7B,GAAG,CAAC,IAAI,CAAC;AAAA,EACX;AAAA,EAEU,kCAAkC,MAAsD;AAChG,UAAM,MAAM;AACZ,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,MACT,aAAa,KAAK;AAAA,MAClB,WAAW,IAAI,KAAK,KAAK,iBAAiB;AAAA,MAC1C,mBAAmB,KAAK,6BAA6B,IAAI,KAAK,KAAK,0BAA0B,IAAI;AAAA,MACjG,WAAW,IAAI,KAAK,KAAK,iBAAiB;AAAA,MAC1C,UAAU;AACR,eAAO,KAAK,WAAW,MAAM;AAAA,MAC/B;AAAA,MACA,aAAa;AACX,YAAI,KAAK,UAAU,QAAQ,IAAI,KAAK,IAAI,EAAG,QAAO;AAClD,YAAI,KAAK,kBAAmB,QAAO;AACnC,eAAO;AAAA,MACT;AAAA,MACA,MAAM,SAAS;AACb,cAAM,MAAM,MAAM,IAAI,WAAW,yBAAyB,KAAK,EAAE;AACjE,cAAM,IAAI,wBAAwB;AAClC,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EAEU,8BAA8B,MAA4D;AAClG,WAAO;AAAA,MACL,GAAG,KAAK,kCAAkC,IAAI;AAAA,MAC9C,sBAAsB,KAAK,yBAAyB,EAAE,UAAU,KAAK,uBAAuB,UAAU,IAAI;AAAA,MAC1G,iBAAiB,KAAK,oBAAoB,EAAE,UAAU,KAAK,kBAAkB,UAAU,IAAI;AAAA,MAC3F,qBAAqB,KAAK,yBAAyB,EAAE,UAAU,KAAK,uBAAuB,UAAU,IAAI;AAAA,IAC3G;AAAA,EACF;AAAA,EAEU,uCAAuC,MAAiE;AAChH,WAAO;AAAA,MACL,GAAG,KAAK,kCAAkC,IAAI;AAAA,MAC9C,sBAAsB,KAAK;AAAA,MAC3B,iBAAiB,KAAK;AAAA,MACtB,qBAAqB,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA,EAEA,MAAM,sBAAiD;AACrD,UAAM,OAAO,sBAAO,QAAQ,MAAM,KAAK,sBAAsB,UAAU,CAAC,GAAG,YAAY,CAAC;AACxF,WAAO,KAAK,IAAI,CAAC,MAAM,KAAK,8BAA8B,CAAC,CAAC;AAAA,EAC9D;AAAA,EAEA,qBAAuC;AACrC,UAAM,WAAO,8BAAc,KAAK,uBAAuB,CAAC,GAAG,sBAAsB;AACjF,eAAO,sBAAQ,MAAM;AACnB,aAAO,KAAK,IAAI,CAAC,MAAM,KAAK,8BAA8B,CAAC,CAAC;AAAA,IAC9D,GAAG,CAAC,IAAI,CAAC;AAAA,EACX;AAAA,EAEA,MAAM,qBAAqB,SAAwE;AACjG,UAAM,OAAO,MAAM,KAAK,WAAW,yBAAqB,4DAAkC,OAAO,CAAC;AAClG,UAAM,KAAK,wBAAwB;AACnC,WAAO,KAAK,uCAAuC,IAAI;AAAA,EACzD;AAAA,EAEA,oBAA0C;AACxC,UAAM,WAAO,8BAAc,KAAK,2BAA2B,CAAC,GAAG,qBAAqB;AACpF,eAAO,sBAAQ,MAAM;AACnB,aAAO,KAAK,IAAI,CAAC,MAAM,KAAK,4BAA4B,CAAC,CAAC;AAAA,IAC5D,GAAG,CAAC,IAAI,CAAC;AAAA,EACX;AAAA,EACA,MAAM,qBAAoD;AACxD,UAAM,OAAO,sBAAO,QAAQ,MAAM,KAAK,0BAA0B,UAAU,CAAC,GAAG,YAAY,CAAC;AAC5F,WAAO,KAAK,IAAI,CAAC,MAAM,KAAK,4BAA4B,CAAC,CAAC;AAAA,EAC5D;AAAA,EAEA,MAAM,oBAAoB,MAAyB,MAAsD;AACvG,UAAM,KAAK,WAAW,oBAAoB,UAAM,8DAAsC,IAAI,CAAC;AAC3F,UAAM,KAAK,0BAA0B,QAAQ,CAAC,CAAC;AAAA,EACjD;AAAA,EAEA,MAAM,mBAAmB,MAAyB;AAChD,UAAM,KAAK,WAAW,mBAAmB,IAAI;AAC7C,UAAM,KAAK,0BAA0B,QAAQ,CAAC,CAAC;AAAA,EACjD;AAAA,EAEA,MAAM,+BAA+B,MAA+E;AAClH,UAAM,OAAO,MAAM,KAAK,WAAW,mCAA+B,qEAAiD,IAAI,CAAC;AACxH,UAAM,KAAK,qCAAqC,QAAQ,CAAC,CAAC;AAC1D,WAAO,KAAK,wCAAwC,IAAI;AAAA,EAC1D;AAAA,EAEA,MAAM,+BAA+B,cAAsB,MAAkD;AAC3G,UAAM,KAAK,WAAW,+BAA+B,kBAAc,qEAAiD,IAAI,CAAC;AACzH,UAAM,KAAK,qCAAqC,QAAQ,CAAC,CAAC;AAAA,EAC5D;AAAA,EAEA,MAAM,+BAA+B,cAAqC;AACxE,UAAM,KAAK,WAAW,+BAA+B,YAAY;AACjE,UAAM,KAAK,qCAAqC,QAAQ,CAAC,CAAC;AAAA,EAC5D;AAAA,EAEA,MAAM,gCAA0E;AAC9E,UAAM,OAAO,sBAAO,QAAQ,MAAM,KAAK,qCAAqC,UAAU,CAAC,GAAG,YAAY,CAAC;AACvG,WAAO,KAAK,IAAI,CAAC,MAAM,KAAK,wCAAwC,CAAC,CAAC;AAAA,EACxE;AAAA,EAEA,+BAAgE;AAC9D,UAAM,WAAO,8BAAc,KAAK,sCAAsC,CAAC,GAAG,kBAAkB;AAC5F,eAAO,sBAAQ,MAAM;AACnB,aAAO,KAAK,IAAI,CAAC,MAAM,KAAK,wCAAwC,CAAC,CAAC;AAAA,IACxE,GAAG,CAAC,IAAI,CAAC;AAAA,EACX;AAAA,EAEA,MAAM,kCAAkC,MAAsF;AAC5H,UAAM,OAAO,MAAM,KAAK,WAAW,sCAAkC,wEAAoD,IAAI,CAAC;AAC9H,UAAM,KAAK,wCAAwC,QAAQ,CAAC,CAAC;AAC7D,WAAO,KAAK,2CAA2C,IAAI;AAAA,EAC7D;AAAA,EAEA,MAAM,kCAAkC,cAAsB,MAAqD;AACjH,UAAM,KAAK,WAAW,kCAAkC,kBAAc,wEAAoD,IAAI,CAAC;AAC/H,UAAM,KAAK,wCAAwC,QAAQ,CAAC,CAAC;AAAA,EAC/D;AAAA,EAEA,MAAM,kCAAkC,cAAqC;AAC3E,UAAM,KAAK,WAAW,kCAAkC,YAAY;AACpE,UAAM,KAAK,wCAAwC,QAAQ,CAAC,CAAC;AAAA,EAC/D;AAAA,EAEA,MAAM,mCAAgF;AACpF,UAAM,OAAO,sBAAO,QAAQ,MAAM,KAAK,wCAAwC,UAAU,CAAC,GAAG,YAAY,CAAC;AAC1G,WAAO,KAAK,IAAI,CAAC,MAAM,KAAK,2CAA2C,CAAC,CAAC;AAAA,EAC3E;AAAA,EAEA,kCAAsE;AACpE,UAAM,WAAO,8BAAc,KAAK,yCAAyC,CAAC,GAAG,yBAAyB;AACtG,eAAO,sBAAQ,MAAM;AACnB,aAAO,KAAK,IAAI,CAAC,MAAM,KAAK,2CAA2C,CAAC,CAAC;AAAA,IAC3E,GAAG,CAAC,IAAI,CAAC;AAAA,EACX;AAAA,EACA,eAAuB;AACrB,UAAM,WAAO,8BAAc,KAAK,iBAAiB,CAAC,GAAG,gBAAgB;AACrE,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,MAAyB,kBAAkB;AACzC,UAAM,QAAQ,IAAI;AAAA,MAChB,MAAM,gBAAgB;AAAA,MACtB,KAAK,mBAAmB,QAAQ,CAAC,CAAC;AAAA,IACpC,CAAC;AAAA,EACH;AAAA,EAEA,MAAgB,0BAA0B;AACxC,UAAM,KAAK,sBAAsB,QAAQ,CAAC,CAAC;AAAA,EAC7C;AAAA,EAEA,KAAK,qCAAuB,IAAI;AAC9B,WAAO;AAAA,MACL,GAAG,MAAM,qCAAuB;AAAA,MAChC,YAAY,MAAW;AACrB,mBAAO,8BAAc,KAAK,eAAe,CAAC,GAAG,cAAc;AAAA,MAC7D;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,cAAc,SAGqC;AACvD,UAAM,WAAW,MAAM,KAAK,WAAW,cAAc;AAAA,MACnD,iBAAiB,QAAQ;AAAA,MACzB,cAAc;AAAA,QACZ,OAAI,qBAAK,QAAQ,aAAa,CAAC,QAAQ,QAAQ,YAAY,UAAU,CAAC;AAAA,QACtE,cAAc,QAAQ,YAAY;AAAA,QAClC,aAAa,QAAQ,YAAY;AAAA,MACnC;AAAA,IACF,CAAC;AAED,QAAI,SAAS,SAAS;AACpB,aAAO,sBAAO,GAAG,MAAS;AAAA,IAC5B,OAAO;AACL,aAAO,sBAAO,MAAM,EAAE,cAAc,SAAS,qBAAiB,wBAAS,gCAAgC,EAAE,CAAC;AAAA,IAC5G;AAAA,EACF;AAAA,EAEA,MAAM,iBAA4C;AAChD,UAAM,WAAW,MAAM,KAAK,WAAW,eAAe;AACtD,WAAO,SAAS,MAAM,IAAI,CAAC,WAAW;AAAA,MACpC,IAAI,MAAM;AAAA,MACV,IAAI,MAAM,MAAM,CAAC;AAAA,MACjB,SAAS,MAAM;AAAA,MACf,WAAW,MAAM,KAAK,CAAC,KAAK;AAAA,MAC5B,QAAQ,IAAI,KAAK,MAAM,cAAc;AAAA,MACrC,OAAO,MAAM;AAAA,IACf,EAAE;AAAA,EACJ;AACF;", "names": ["import_common"]}