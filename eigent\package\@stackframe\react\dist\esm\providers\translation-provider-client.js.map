{"version": 3, "sources": ["../../../src/providers/translation-provider-client.tsx"], "sourcesContent": ["\"use client\";\n\n\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { createContext, useContext } from \"react\";\n\nexport const TranslationContext = createContext<null | {\n  quetzalKeys: Map<string, string>,\n  quetzalLocale: Map<string, string>,\n}>(null);\n\nexport function TranslationProviderClient(props: {\n  children: React.ReactNode,\n  quetzalKeys: Map<string, string>,\n  quetzalLocale: Map<string, string>,\n}) {\n  return (\n    <TranslationContext.Provider value={{\n      quetzalKeys: props.quetzalKeys,\n      quetzalLocale: props.quetzalLocale,\n    }}>\n      {props.children}\n    </TranslationContext.Provider>\n  );\n}\n"], "mappings": ";;;AAOA,SAAS,qBAAiC;AAatC;AAXG,IAAM,qBAAqB,cAG/B,IAAI;AAEA,SAAS,0BAA0B,OAIvC;AACD,SACE,oBAAC,mBAAmB,UAAnB,EAA4B,OAAO;AAAA,IAClC,aAAa,MAAM;AAAA,IACnB,eAAe,MAAM;AAAA,EACvB,GACG,gBAAM,UACT;AAEJ;", "names": []}