{"version": 3, "sources": ["../../../src/utils/html.tsx"], "sourcesContent": ["import { templateIdentity } from \"./strings\";\n\nexport function escapeHtml(unsafe: string): string {\n  return `${unsafe}`\n    .replace(/&/g, '&amp;')\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, \"&quot;\")\n    .replace(/'/g, \"&#039;\");\n}\nundefined?.test(\"escapeHtml\", ({ expect }) => {\n  // Test with empty string\n  expect(escapeHtml(\"\")).toBe(\"\");\n\n  // Test with string without special characters\n  expect(escapeHtml(\"hello world\")).toBe(\"hello world\");\n\n  // Test with special characters\n  expect(escapeHtml(\"<div>\")).toBe(\"&lt;div&gt;\");\n  expect(escapeHtml(\"a & b\")).toBe(\"a &amp; b\");\n  expect(escapeHtml('a \"quoted\" string')).toBe(\"a &quot;quoted&quot; string\");\n  expect(escapeHtml(\"it's a test\")).toBe(\"it&#039;s a test\");\n\n  // Test with multiple special characters\n  expect(escapeHtml(\"<a href=\\\"test\\\">It's a link</a>\")).toBe(\n    \"&lt;a href=&quot;test&quot;&gt;It&#039;s a link&lt;/a&gt;\"\n  );\n});\n\nexport function html(strings: TemplateStringsArray, ...values: any[]): string {\n  return templateIdentity(strings, ...values.map(v => escapeHtml(`${v}`)));\n}\nundefined?.test(\"html\", ({ expect }) => {\n  // Test with no interpolation\n  expect(html`simple string`).toBe(\"simple string\");\n\n  // Test with string interpolation\n  expect(html`Hello, ${\"world\"}!`).toBe(\"Hello, world!\");\n\n  // Test with number interpolation\n  expect(html`Count: ${42}`).toBe(\"Count: 42\");\n\n  // Test with HTML special characters in interpolated values\n  expect(html`<div>${\"<script>\"}</div>`).toBe(\"<div>&lt;script&gt;</div>\");\n\n  // Test with multiple interpolations\n  expect(html`${1} + ${2} = ${\"<3\"}`).toBe(\"1 + 2 = &lt;3\");\n\n  // Test with object interpolation\n  const obj = { toString: () => \"<object>\" };\n  expect(html`Object: ${obj}`).toBe(\"Object: &lt;object&gt;\");\n});\n"], "mappings": ";AAAA,SAAS,wBAAwB;AAE1B,SAAS,WAAW,QAAwB;AACjD,SAAO,GAAG,MAAM,GACb,QAAQ,MAAM,OAAO,EACrB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,QAAQ,EACtB,QAAQ,MAAM,QAAQ;AAC3B;AAoBO,SAAS,KAAK,YAAkC,QAAuB;AAC5E,SAAO,iBAAiB,SAAS,GAAG,OAAO,IAAI,OAAK,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC;AACzE;", "names": []}