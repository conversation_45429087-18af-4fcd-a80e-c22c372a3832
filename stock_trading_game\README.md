# 🎮 模拟炒股游戏 Stock Trading Game

一个基于Python的模拟A股交易游戏，支持真实玩家交易和AI机器人参与的动态市场环境。

## 🚀 项目特性

- 🏦 **真实市场模拟**：模拟A股市场的价格波动和交易机制
- 👥 **多玩家交易**：支持多个玩家同时在线交易
- 🤖 **AI机器人**：多种交易策略的虚拟机器人参与市场
- 📊 **实时数据**：WebSocket实时推送股价和交易数据
- 🎯 **游戏化元素**：排行榜、成就系统、模拟比赛
- 📈 **专业图表**：K线图、技术指标、交易深度

## 🏗️ 技术架构

### 后端技术栈
- **Python 3.11+**
- **FastAPI**：高性能Web框架
- **WebSocket**：实时数据推送
- **SQLAlchemy**：ORM数据库操作
- **PostgreSQL**：主数据库
- **Redis**：缓存和消息队列
- **Celery**：异步任务处理

### 前端技术栈
- **React 18**
- **TypeScript**
- **Chart.js**：股票图表
- **WebSocket Client**：实时数据接收
- **Ant Design**：UI组件库

## 📁 项目结构

```
stock_trading_game/
├── backend/                 # 后端服务
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务逻辑
│   │   ├── utils/          # 工具函数
│   │   └── websocket/      # WebSocket处理
│   ├── tests/              # 测试文件
│   ├── requirements.txt    # Python依赖
│   └── main.py            # 应用入口
├── frontend/               # 前端应用
│   ├── src/
│   │   ├── components/     # React组件
│   │   ├── pages/          # 页面组件
│   │   ├── services/       # API服务
│   │   ├── utils/          # 工具函数
│   │   └── types/          # TypeScript类型
│   ├── public/             # 静态资源
│   └── package.json        # 前端依赖
├── docker/                 # Docker配置
├── docs/                   # 项目文档
└── scripts/                # 部署脚本
```

## 🚀 快速开始

### 环境要求
- Python 3.11+
- Node.js 18+
- PostgreSQL 14+
- Redis 6+

### 后端启动
```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload --port 8000
```

### 前端启动
```bash
cd frontend
npm install
npm run dev
```

## 🎯 核心功能模块

### 1. 市场数据模拟引擎
- 基于真实A股数据的价格波动算法
- 支持多种影响因子：交易量、市场情绪、新闻事件
- 可配置的波动率和趋势参数

### 2. 交易撮合系统
- 价格优先、时间优先的撮合算法
- 支持市价单、限价单、止损单
- 实时计算成交价格和数量

### 3. 虚拟机器人交易
- 多种交易策略：趋势跟随、均值回归、随机交易
- 可配置的风险偏好和资金管理
- 智能化的买卖决策算法

### 4. 用户管理系统
- 用户注册、登录、权限管理
- 虚拟资金管理和交易记录
- 个人资产统计和收益分析

### 5. 实时数据推送
- WebSocket实时推送股价变化
- 交易深度、成交量实时更新
- 个人持仓和资产实时同步

## 📊 数据库设计

### 核心表结构
- `users`：用户信息表
- `stocks`：股票基础信息表
- `orders`：交易订单表
- `transactions`：成交记录表
- `positions`：持仓信息表
- `market_data`：市场数据表

## 🤖 AI机器人策略

### 1. 趋势跟随策略
- 基于移动平均线的趋势判断
- 突破买入，跌破卖出

### 2. 均值回归策略
- 基于价格偏离均值的交易信号
- 超买卖出，超卖买入

### 3. 随机交易策略
- 模拟散户的随机交易行为
- 增加市场的不确定性和真实感

## 🎮 游戏化功能

- **排行榜**：收益率排名、交易量排名
- **成就系统**：交易里程碑、收益目标
- **每日任务**：交易挑战、学习任务
- **模拟比赛**：定期举办交易竞赛

## 📈 技术指标支持

- **基础指标**：MA、EMA、MACD、RSI、KDJ
- **成交量指标**：OBV、VWAP
- **波动率指标**：布林带、ATR

## 🔧 开发工具

- **代码格式化**：Black、isort
- **代码检查**：Pylint、Flake8
- **类型检查**：MyPy
- **测试框架**：Pytest
- **API文档**：FastAPI自动生成

## 📝 开发日志

- [x] 项目初始化和架构设计
- [ ] 数据库模型设计
- [ ] 市场数据模拟引擎
- [ ] 交易撮合系统
- [ ] 用户认证系统
- [ ] WebSocket实时推送
- [ ] 前端界面开发
- [ ] AI机器人策略
- [ ] 游戏化功能
- [ ] 测试和优化

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
