{"version": 3, "sources": ["../../../src/utils/booleans.tsx"], "sourcesContent": ["export type Truthy<T> = T extends null | undefined | 0 | \"\" | false ? false : true;\nexport type Falsy<T> = T extends null | undefined | 0 | \"\" | false ? true : false;\n\nexport function isTruthy<T>(value: T): value is T & Truthy<T> {\n  return !!value;\n}\nundefined?.test(\"isTruthy\", ({ expect }) => {\n  expect(isTruthy(true)).toBe(true);\n  expect(isTruthy(1)).toBe(true);\n  expect(isTruthy(\"hello\")).toBe(true);\n  expect(isTruthy({})).toBe(true);\n  expect(isTruthy([])).toBe(true);\n  expect(isTruthy(false)).toBe(false);\n  expect(isTruthy(0)).toBe(false);\n  expect(isTruthy(\"\")).toBe(false);\n  expect(isTruthy(null)).toBe(false);\n  expect(isTruthy(undefined)).toBe(false);\n});\n\nexport function isFalsy<T>(value: T): value is T & Falsy<T> {\n  return !value;\n}\nundefined?.test(\"isFalsy\", ({ expect }) => {\n  expect(isFalsy(false)).toBe(true);\n  expect(isFalsy(0)).toBe(true);\n  expect(isFalsy(\"\")).toBe(true);\n  expect(isFalsy(null)).toBe(true);\n  expect(isFalsy(undefined)).toBe(true);\n  expect(isFalsy(true)).toBe(false);\n  expect(isFalsy(1)).toBe(false);\n  expect(isFalsy(\"hello\")).toBe(false);\n  expect(isFalsy({})).toBe(false);\n  expect(isFalsy([])).toBe(false);\n});\n"], "mappings": ";AAGO,SAAS,SAAY,OAAkC;AAC5D,SAAO,CAAC,CAAC;AACX;AAcO,SAAS,QAAW,OAAiC;AAC1D,SAAO,CAAC;AACV;", "names": []}