import requests
from bs4 import BeautifulSoup
import time
from urllib.parse import urlparse

def normalize_url(url_string):
    parsed_url = urlparse(url_string)
    normalized_url = parsed_url.scheme + "://" + parsed_url.netloc + parsed_url.path.rstrip('/')
    return normalized_url.lower()

def check_bing_index_info_v5(url):
    search_query = f"info:{url}"
    bing_url = f"https://www.bing.com/search?q={search_query}"
    headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}
    try:
        response = requests.get(bing_url, headers=headers, timeout=10)
        response.raise_for_status()

        soup = BeautifulSoup(response.content, 'html.parser')
        results_list = soup.find('ol', {'id': 'b_results'})

        if results_list:
            found_url_in_results = False
            is_domain_url = not urlparse(url).path

            for result_item in results_list.find_all('li', {'class': 'b_algo'}):
                link_element = result_item.find('a')
                if link_element and link_element.get('href'):
                    result_url = link_element['href']
                    normalized_result_url = normalize_url(result_url)
                    normalized_target_url = normalize_url(url)

                    if is_domain_url:
                        result_domain = urlparse(result_url).netloc.lower()
                        target_domain = urlparse(url).netloc.lower()
                        if result_domain == target_domain:
                            found_url_in_results = True
                            break

                    else:
                        if normalized_result_url.startswith(normalized_target_url):
                            found_url_in_results = True
                            break

            if found_url_in_results:
                print(f"URL: {url} - 疑似已收录")
            else:
                print(f"URL: {url} - 未收录或未显示")


        else:
            no_results = soup.find('div', {'class': 'b_nores'})
            if no_results:
                print(f"URL: {url} -  未收录或未显示")
            else:
                print(f"URL: {url} - 检查结果异常, 请手动检查")

    except requests.exceptions.RequestException:
        print(f"URL: {url} - 请求错误")

    except Exception:
        print(f"URL: {url} - 解析错误")

if __name__ == '__main__':
    urls_to_check = [
        "https://faisco.com/marketing/3481.html",
        "https://faisco.com/marketing/3482.html",
        "https://faisco.com/marketing/6100.html",
        "https://faisco.com/marketing/6600.html",
        "https://www.bing.com",
        "https://www.faisco.com"
    ]

    for url in urls_to_check:
        check_bing_index_info_v5(url)
        time.sleep(1)