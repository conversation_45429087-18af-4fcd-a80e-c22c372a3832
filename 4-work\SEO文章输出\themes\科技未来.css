/* 科技未来风格 - 突出特点：霓虹效果、未来感、强对比度 */
body {
  font-family: 'Space Grotesk', 'SF Pro Display', -apple-system, sans-serif;
  line-height: 1.7;
  color: #e0e0e0;
  max-width: 850px;
  margin: 0 auto;
  padding: 2rem;
  background-color: #0f172a;
  letter-spacing: 0.01em;
}

h1 {
  font-size: 3.5rem;
  font-weight: 700;
  color: #fff;
  margin: 2rem 0;
  line-height: 1.1;
  text-transform: uppercase;
  letter-spacing: -0.02em;
  background: linear-gradient(to right, #06b6d4, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  text-shadow: 0 0 20px rgba(6, 182, 212, 0.5);
}

h2 {
  font-size: 2.2rem;
  font-weight: 700;
  color: #fff;
  margin-top: 3.5rem;
  margin-bottom: 1.5rem;
  position: relative;
  padding-left: 1.5rem;
  border-left: 4px solid #06b6d4;
  text-shadow: 0 0 10px rgba(6, 182, 212, 0.3);
}

h2 .content {
  position: relative;
  background: linear-gradient(to right, #06b6d4, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #06b6d4;
  margin-top: 2.5rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

h3 .content {
  position: relative;
}

h3 .content::before {
  content: ">";
  margin-right: 0.5rem;
  color: #3b82f6;
  font-weight: 700;
}

p {
  margin-bottom: 1.5rem;
  font-size: 1.05rem;
  line-height: 1.8;
  color: #e0e0e0;
}

a {
  color: #06b6d4;
  text-decoration: none;
  position: relative;
  transition: all 0.3s;
  padding: 0 0.2rem;
  font-weight: 500;
}

a:hover {
  color: #3b82f6;
  text-shadow: 0 0 8px rgba(6, 182, 212, 0.5);
}

a::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, #06b6d4, #3b82f6);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

a:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

blockquote {
  margin: 2rem 0;
  padding: 1.5rem 2rem;
  background-color: rgba(6, 182, 212, 0.1);
  border-left: 4px solid #3b82f6;
  color: #e0e0e0;
  border-radius: 0 8px 8px 0;
  position: relative;
}

blockquote::before {
  content: """;
  position: absolute;
  top: 5px;
  left: 10px;
  font-size: 3rem;
  color: rgba(59, 130, 246, 0.3);
  font-family: Georgia, serif;
}

code {
  background: rgba(6, 182, 212, 0.1);
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
  font-size: 0.9em;
  padding: 0.2em 0.4em;
  border-radius: 4px;
  color: #06b6d4;
  border: 1px solid rgba(6, 182, 212, 0.3);
}

ul, ol {
  margin: 1.5rem 0;
  padding-left: 1.2rem;
}

ul {
  list-style: none;
}

ul li {
  margin-bottom: 1rem;
  position: relative;
  padding-left: 1.8rem;
}

ul li::before {
  content: "◉";
  color: #06b6d4;
  position: absolute;
  left: 0;
  top: 0.1rem;
  font-size: 1rem;
}

ol li {
  margin-bottom: 1rem;
  padding-left: 0.5rem;
  color: #e0e0e0;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 2.5rem auto;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(6, 182, 212, 0.5);
  border: 1px solid rgba(6, 182, 212, 0.5);
}

table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin: 2.5rem 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(6, 182, 212, 0.2);
}

th, td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid rgba(6, 182, 212, 0.2);
}

th {
  background-color: rgba(6, 182, 212, 0.1);
  color: #06b6d4;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-size: 0.9rem;
}

tr:hover {
  background-color: rgba(6, 182, 212, 0.05);
}

/* 强调元素 */
.highlight-word {
  background: linear-gradient(to right, rgba(6, 182, 212, 0.2), rgba(59, 130, 246, 0.2));
  padding: 0 5px;
  border-radius: 3px;
  color: #06b6d4;
  font-weight: 600;
}