import { jsx as _jsx } from "react/jsx-runtime";
import { forwardRefIfNeeded } from "@stackframe/stack-shared/dist/utils/react";
import { cn } from "../../lib/utils";
const Card = forwardRefIfNeeded(({ className, ...props }, ref) => (_jsx("div", { ref: ref, className: cn("stack-scope rounded-xl border bg-card text-card-foreground shadow-sm", className), ...props })));
Card.displayName = "Card";
const CardHeader = forwardRefIfNeeded(({ className, ...props }, ref) => (_jsx("div", { ref: ref, className: cn("flex flex-col space-y-1.5 p-6 pb-0", className), ...props })));
CardHeader.displayName = "CardHeader";
const CardTitle = forwardRefIfNeeded(({ className, ...props }, ref) => (_jsx("h3", { ref: ref, className: cn("font-semibold leading-none tracking-tight capitalize", className), ...props })));
CardTitle.displayName = "CardTitle";
const CardDescription = forwardRefIfNeeded(({ className, ...props }, ref) => (_jsx("p", { ref: ref, className: cn("text-sm text-muted-foreground", className), ...props })));
CardDescription.displayName = "CardDescription";
const CardContent = forwardRefIfNeeded(({ className, ...props }, ref) => (_jsx("div", { ref: ref, className: cn("p-6", className), ...props })));
CardContent.displayName = "CardContent";
const CardSubtitle = forwardRefIfNeeded(({ className, ...props }, ref) => (_jsx("h4", { ref: ref, className: cn("text-sm text-muted-foreground font-bold", className), ...props })));
const CardFooter = forwardRefIfNeeded(({ className, ...props }, ref) => (_jsx("div", { ref: ref, className: cn("flex items-center p-6 pt-0", className), ...props })));
CardFooter.displayName = "CardFooter";
export { Card, CardContent, CardDescription, CardFooter, CardHeader, CardSubtitle, CardTitle };
