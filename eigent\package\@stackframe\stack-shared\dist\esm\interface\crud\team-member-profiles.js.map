{"version": 3, "sources": ["../../../../src/interface/crud/team-member-profiles.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport * as schemaFields from \"../../schema-fields\";\nimport { yupObject } from \"../../schema-fields\";\nimport { usersCrudServerReadSchema } from \"./users\";\n\n\nexport const teamMemberProfilesCrudClientReadSchema = yupObject({\n  team_id: schemaFields.teamIdSchema.defined(),\n  user_id: schemaFields.userIdSchema.defined(),\n  display_name: schemaFields.teamMemberDisplayNameSchema.nullable().defined(),\n  profile_image_url: schemaFields.teamMemberProfileImageUrlSchema.nullable().defined(),\n}).defined();\n\nexport const teamMemberProfilesCrudServerReadSchema = teamMemberProfilesCrudClientReadSchema.concat(yupObject({\n  user: usersCrudServerReadSchema.defined(),\n})).defined();\n\nexport const teamMemberProfilesCrudClientUpdateSchema = yupObject({\n  display_name: schemaFields.teamMemberDisplayNameSchema.optional(),\n  profile_image_url: schemaFields.teamMemberProfileImageUrlSchema.nullable().optional(),\n}).defined();\n\nexport const teamMemberProfilesCrud = createCrud({\n  clientReadSchema: teamMemberProfilesCrudClientReadSchema,\n  serverReadSchema: teamMemberProfilesCrudServerReadSchema,\n  clientUpdateSchema: teamMemberProfilesCrudClientUpdateSchema,\n  docs: {\n    clientList: {\n      summary: \"List team members profiles\",\n      description: \"List team members profiles. You always need to specify a `team_id` that your are a member of on the client. You can always filter for your own profile by setting `me` as the `user_id` in the path parameters. If you want list all the profiles in a team, you need to have the `$read_members` permission in that team.\",\n      tags: [\"Teams\"],\n    },\n    serverList: {\n      summary: \"List team members profiles\",\n      description: \"List team members profiles and filter by team ID and user ID\",\n      tags: [\"Teams\"],\n    },\n    clientRead: {\n      summary: \"Get a team member profile\",\n      description: \"Get a team member profile. you can always get your own profile by setting `me` as the `user_id` in the path parameters on the client. If you want to get someone else's profile in a team, you need to have the `$read_members` permission in that team.\",\n      tags: [\"Teams\"],\n    },\n    serverRead: {\n      summary: \"Get a team member profile\",\n      description: \"Get a team member profile by user ID\",\n      tags: [\"Teams\"],\n    },\n    clientUpdate: {\n      summary: \"Update your team member profile\",\n      description: \"Update your own team member profile. `user_id` must be `me` in the path parameters on the client.\",\n      tags: [\"Teams\"],\n    },\n    serverUpdate: {\n      summary: \"Update a team member profile\",\n      description: \"Update a team member profile by user ID\",\n      tags: [\"Teams\"],\n    },\n  },\n});\n\nexport type TeamMemberProfilesCrud = CrudTypeOf<typeof teamMemberProfilesCrud>;\n"], "mappings": ";AAAA,SAAqB,kBAAkB;AACvC,YAAY,kBAAkB;AAC9B,SAAS,iBAAiB;AAC1B,SAAS,iCAAiC;AAGnC,IAAM,yCAAyC,UAAU;AAAA,EAC9D,SAAsB,0BAAa,QAAQ;AAAA,EAC3C,SAAsB,0BAAa,QAAQ;AAAA,EAC3C,cAA2B,yCAA4B,SAAS,EAAE,QAAQ;AAAA,EAC1E,mBAAgC,6CAAgC,SAAS,EAAE,QAAQ;AACrF,CAAC,EAAE,QAAQ;AAEJ,IAAM,yCAAyC,uCAAuC,OAAO,UAAU;AAAA,EAC5G,MAAM,0BAA0B,QAAQ;AAC1C,CAAC,CAAC,EAAE,QAAQ;AAEL,IAAM,2CAA2C,UAAU;AAAA,EAChE,cAA2B,yCAA4B,SAAS;AAAA,EAChE,mBAAgC,6CAAgC,SAAS,EAAE,SAAS;AACtF,CAAC,EAAE,QAAQ;AAEJ,IAAM,yBAAyB,WAAW;AAAA,EAC/C,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,MAAM;AAAA,IACJ,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,EACF;AACF,CAAC;", "names": []}