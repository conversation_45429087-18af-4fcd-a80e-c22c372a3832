{"version": 3, "sources": ["../../src/config/format.ts"], "sourcesContent": ["// see https://github.com/stack-auth/info/blob/main/eng-handbook/random-thoughts/config-json-format.md\n\nimport { StackAssertionError, throwErr } from \"../utils/errors\";\nimport { deleteKey, filterUndefined, get, hasAndNotUndefined, set } from \"../utils/objects\";\n\n\nexport type ConfigValue = string | number | boolean | null | ConfigValue[] | Config;\nexport type Config = {\n  [keyOrDotNotation: string]: ConfigValue | undefined,  // must support undefined for optional values\n};\n\nexport type NormalizedConfigValue = string | number | boolean | NormalizedConfig | NormalizedConfigValue[];\nexport type NormalizedConfig = {\n  [key: string]: NormalizedConfigValue | undefined,  // must support undefined for optional values\n};\n\nexport type _NormalizesTo<N> = N extends object ? (\n  & Config\n  & { [K in keyof N]?: _NormalizesTo<N[K]> | null }\n  & { [K in `${string}.${string}`]: ConfigValue }\n) : N;\nexport type NormalizesTo<N extends NormalizedConfig> = _NormalizesTo<N>;\n\n/**\n * Note that a config can both be valid and not normalizable.\n */\nexport function isValidConfig(c: unknown): c is Config {\n  return getInvalidConfigReason(c) === undefined;\n}\n\nexport function getInvalidConfigReason(c: unknown, options: { configName?: string } = {}): string | undefined {\n  const configName = options.configName ?? 'config';\n  if (c === null || typeof c !== 'object') return `${configName} must be a non-null object`;\n  for (const [key, value] of Object.entries(c)) {\n    if (value === undefined) continue;\n    if (typeof key !== 'string') return `${configName} must have only string keys (found: ${typeof key})`;\n    if (!key.match(/^[a-zA-Z0-9_:$][a-zA-Z_:$0-9\\-]*(?:\\.[a-zA-Z0-9_:$][a-zA-Z_:$0-9\\-]*)*$/)) return `All keys of ${configName} must consist of only alphanumeric characters, dots, underscores, colons, dollar signs, or hyphens and start with a character other than a hyphen (found: ${key})`;\n\n    const entryName = `${configName}.${key}`;\n    const reason = getInvalidConfigValueReason(value, { valueName: entryName });\n    if (reason) return reason;\n  }\n  return undefined;\n}\n\nfunction getInvalidConfigValueReason(value: unknown, options: { valueName?: string } = {}): string | undefined {\n  const valueName = options.valueName ?? 'value';\n  switch (typeof value) {\n    case 'string':\n    case 'number':\n    case 'boolean': {\n      break;\n    }\n    case 'object': {\n      if (value === null) {\n        break;\n      } else if (Array.isArray(value)) {\n        for (const [index, v] of value.entries()) {\n          const reason = getInvalidConfigValueReason(v, { valueName: `${valueName}[${index}]` });\n          if (reason) return reason;\n        }\n      } else {\n        const reason = getInvalidConfigReason(value, { configName: valueName });\n        if (reason) return reason;\n      }\n      break;\n    }\n    default: {\n      return `${valueName} has an invalid value type ${typeof value} (value: ${value})`;\n    }\n  }\n  return undefined;\n}\n\nexport function assertValidConfig(c: unknown) {\n  const reason = getInvalidConfigReason(c);\n  if (reason) throw new StackAssertionError(`Invalid config: ${reason}`, { c });\n}\n\nexport function override(c1: Config, ...configs: Config[]) {\n  if (configs.length === 0) return c1;\n  if (configs.length > 1) return override(override(c1, configs[0]), ...configs.slice(1));\n  const c2 = configs[0];\n\n  assertValidConfig(c1);\n  assertValidConfig(c2);\n\n  let result = c1;\n  for (const key of Object.keys(filterUndefined(c2))) {\n    result = Object.fromEntries(\n      Object.entries(result).filter(([k]) => k !== key && !k.startsWith(key + '.'))\n    );\n  }\n\n  return {\n    ...result,\n    ...filterUndefined(c2),\n  };\n}\n\nundefined?.test(\"override(...)\", ({ expect }) => {\n  expect(\n    override(\n      {\n        a: 1,\n        b: 2,\n        \"c.d\": 3,\n        \"c.e.f\": 4,\n        \"c.g\": 5,\n        h: [6, { i: 7 }, 8],\n        k: 123,\n        l: undefined,\n      },\n      {\n        a: 9,\n        \"c.d\": 10,\n        \"c.e\": null,\n        \"h.0\": 11,\n        \"h.1\": {\n          j: 12,\n        },\n        k: undefined,\n      },\n    )\n  ).toEqual({\n    a: 9,\n    b: 2,\n    \"c.d\": 10,\n    \"c.e\": null,\n    \"c.g\": 5,\n    h: [6, { i: 7 }, 8],\n    \"h.0\": 11,\n    \"h.1\": {\n      j: 12,\n    },\n    k: 123,\n    l: undefined,\n  });\n});\n\ntype NormalizeOptions = {\n  /**\n   * What to do if a dot notation is used on null.\n   *\n   * - \"empty\" (default): Replace the null with an empty object.\n   * - \"throw\": Throw an error.\n   * - \"ignore\": Ignore the dot notation field.\n   */\n  onDotIntoNull?: \"empty\" | \"throw\" | \"ignore\",\n}\n\nexport class NormalizationError extends Error {\n  constructor(...args: ConstructorParameters<typeof Error>) {\n    super(...args);\n  }\n}\nNormalizationError.prototype.name = \"NormalizationError\";\n\nexport function normalize(c: Config, options: NormalizeOptions = {}): NormalizedConfig {\n  assertValidConfig(c);\n  const onDotIntoNull = options.onDotIntoNull ?? \"empty\";\n\n  const countDots = (s: string) => s.match(/\\./g)?.length ?? 0;\n  const result: NormalizedConfig = {};\n  const keysByDepth = Object.keys(c).sort((a, b) => countDots(a) - countDots(b));\n\n  outer: for (const key of keysByDepth) {\n    const keySegmentsWithoutLast = key.split('.');\n    const last = keySegmentsWithoutLast.pop() ?? throwErr('split returns empty array?');\n    const value = get(c, key);\n    if (value === undefined) continue;\n\n    let current: NormalizedConfig = result;\n    for (const keySegment of keySegmentsWithoutLast) {\n      if (!hasAndNotUndefined(current, keySegment)) {\n        switch (onDotIntoNull) {\n          case \"empty\": {\n            set(current, keySegment, {});\n            break;\n          }\n          case \"throw\": {\n            throw new NormalizationError(`Tried to use dot notation to access ${JSON.stringify(key)}, but ${JSON.stringify(keySegment)} doesn't exist on the object (or is null). Maybe this config is not normalizable?`);\n          }\n          case \"ignore\": {\n            continue outer;\n          }\n        }\n      }\n      const value = get(current, keySegment);\n      if (typeof value !== 'object') {\n        throw new NormalizationError(`Tried to use dot notation to access ${JSON.stringify(key)}, but ${JSON.stringify(keySegment)} is not an object. Maybe this config is not normalizable?`);\n      }\n      current = value as NormalizedConfig;\n    }\n    setNormalizedValue(current, last, value);\n  }\n  return result;\n}\n\nfunction normalizeValue(value: ConfigValue): NormalizedConfigValue {\n  if (value === null) throw new NormalizationError(\"Tried to normalize a null value\");\n  if (Array.isArray(value)) return value.map(normalizeValue);\n  if (typeof value === 'object') return normalize(value);\n  return value;\n}\n\nfunction setNormalizedValue(result: NormalizedConfig, key: string, value: ConfigValue) {\n  if (value === null) {\n    if (hasAndNotUndefined(result, key)) {\n      deleteKey(result, key);\n    }\n  } else {\n    set(result, key, normalizeValue(value));\n  }\n}\n\nundefined?.test(\"normalize(...)\", ({ expect }) => {\n  expect(normalize({\n    a: 9,\n    b: 2,\n    c: {},\n    \"c.d\": 10,\n    \"c.e\": null,\n    \"c.g\": 5,\n    h: [6, { i: 7 }, 8],\n    \"h.0\": 11,\n    \"h.1\": {\n      j: 12,\n    },\n    k: { l: {} },\n    \"k.l.m\": 13,\n    n: undefined,\n  })).toEqual({\n    a: 9,\n    b: 2,\n    c: {\n      d: 10,\n      g: 5,\n    },\n    h: [11, { j: 12 }, 8],\n    k: { l: { m: 13 } },\n  });\n\n  // dotting into null\n  expect(normalize({\n    \"b.c\": 2,\n  })).toEqual({ b: { c: 2 } });\n  expect(() => normalize({\n    \"b.c\": 2,\n  }, { onDotIntoNull: \"throw\" })).toThrow(`Tried to use dot notation to access \"b.c\", but \"b\" doesn't exist on the object (or is null). Maybe this config is not normalizable?`);\n  expect(() => normalize({\n    b: null,\n    \"b.c\": 2,\n  }, { onDotIntoNull: \"throw\" })).toThrow(`Tried to use dot notation to access \"b.c\", but \"b\" doesn't exist on the object (or is null). Maybe this config is not normalizable?`);\n  expect(normalize({\n    \"b.c\": 2,\n  }, { onDotIntoNull: \"ignore\" })).toEqual({});\n\n  // dotting into non-object\n  expect(() => normalize({\n    b: 1,\n    \"b.c\": 2,\n  })).toThrow(`Tried to use dot notation to access \"b.c\", but \"b\" is not an object. Maybe this config is not normalizable?`);\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA8C;AAC9C,qBAAyE;AAuBlE,SAAS,cAAc,GAAyB;AACrD,SAAO,uBAAuB,CAAC,MAAM;AACvC;AAEO,SAAS,uBAAuB,GAAY,UAAmC,CAAC,GAAuB;AAC5G,QAAM,aAAa,QAAQ,cAAc;AACzC,MAAI,MAAM,QAAQ,OAAO,MAAM,SAAU,QAAO,GAAG,UAAU;AAC7D,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,CAAC,GAAG;AAC5C,QAAI,UAAU,OAAW;AACzB,QAAI,OAAO,QAAQ,SAAU,QAAO,GAAG,UAAU,uCAAuC,OAAO,GAAG;AAClG,QAAI,CAAC,IAAI,MAAM,yEAAyE,EAAG,QAAO,eAAe,UAAU,6JAA6J,GAAG;AAE3R,UAAM,YAAY,GAAG,UAAU,IAAI,GAAG;AACtC,UAAM,SAAS,4BAA4B,OAAO,EAAE,WAAW,UAAU,CAAC;AAC1E,QAAI,OAAQ,QAAO;AAAA,EACrB;AACA,SAAO;AACT;AAEA,SAAS,4BAA4B,OAAgB,UAAkC,CAAC,GAAuB;AAC7G,QAAM,YAAY,QAAQ,aAAa;AACvC,UAAQ,OAAO,OAAO;AAAA,IACpB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,WAAW;AACd;AAAA,IACF;AAAA,IACA,KAAK,UAAU;AACb,UAAI,UAAU,MAAM;AAClB;AAAA,MACF,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC/B,mBAAW,CAAC,OAAO,CAAC,KAAK,MAAM,QAAQ,GAAG;AACxC,gBAAM,SAAS,4BAA4B,GAAG,EAAE,WAAW,GAAG,SAAS,IAAI,KAAK,IAAI,CAAC;AACrF,cAAI,OAAQ,QAAO;AAAA,QACrB;AAAA,MACF,OAAO;AACL,cAAM,SAAS,uBAAuB,OAAO,EAAE,YAAY,UAAU,CAAC;AACtE,YAAI,OAAQ,QAAO;AAAA,MACrB;AACA;AAAA,IACF;AAAA,IACA,SAAS;AACP,aAAO,GAAG,SAAS,8BAA8B,OAAO,KAAK,YAAY,KAAK;AAAA,IAChF;AAAA,EACF;AACA,SAAO;AACT;AAEO,SAAS,kBAAkB,GAAY;AAC5C,QAAM,SAAS,uBAAuB,CAAC;AACvC,MAAI,OAAQ,OAAM,IAAI,kCAAoB,mBAAmB,MAAM,IAAI,EAAE,EAAE,CAAC;AAC9E;AAEO,SAAS,SAAS,OAAe,SAAmB;AACzD,MAAI,QAAQ,WAAW,EAAG,QAAO;AACjC,MAAI,QAAQ,SAAS,EAAG,QAAO,SAAS,SAAS,IAAI,QAAQ,CAAC,CAAC,GAAG,GAAG,QAAQ,MAAM,CAAC,CAAC;AACrF,QAAM,KAAK,QAAQ,CAAC;AAEpB,oBAAkB,EAAE;AACpB,oBAAkB,EAAE;AAEpB,MAAI,SAAS;AACb,aAAW,OAAO,OAAO,SAAK,gCAAgB,EAAE,CAAC,GAAG;AAClD,aAAS,OAAO;AAAA,MACd,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,MAAM,OAAO,CAAC,EAAE,WAAW,MAAM,GAAG,CAAC;AAAA,IAC9E;AAAA,EACF;AAEA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,OAAG,gCAAgB,EAAE;AAAA,EACvB;AACF;AAqDO,IAAM,qBAAN,cAAiC,MAAM;AAAA,EAC5C,eAAe,MAA2C;AACxD,UAAM,GAAG,IAAI;AAAA,EACf;AACF;AACA,mBAAmB,UAAU,OAAO;AAE7B,SAAS,UAAU,GAAW,UAA4B,CAAC,GAAqB;AACrF,oBAAkB,CAAC;AACnB,QAAM,gBAAgB,QAAQ,iBAAiB;AAE/C,QAAM,YAAY,CAAC,MAAc,EAAE,MAAM,KAAK,GAAG,UAAU;AAC3D,QAAM,SAA2B,CAAC;AAClC,QAAM,cAAc,OAAO,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,UAAU,CAAC,IAAI,UAAU,CAAC,CAAC;AAE7E,QAAO,YAAW,OAAO,aAAa;AACpC,UAAM,yBAAyB,IAAI,MAAM,GAAG;AAC5C,UAAM,OAAO,uBAAuB,IAAI,SAAK,wBAAS,4BAA4B;AAClF,UAAM,YAAQ,oBAAI,GAAG,GAAG;AACxB,QAAI,UAAU,OAAW;AAEzB,QAAI,UAA4B;AAChC,eAAW,cAAc,wBAAwB;AAC/C,UAAI,KAAC,mCAAmB,SAAS,UAAU,GAAG;AAC5C,gBAAQ,eAAe;AAAA,UACrB,KAAK,SAAS;AACZ,oCAAI,SAAS,YAAY,CAAC,CAAC;AAC3B;AAAA,UACF;AAAA,UACA,KAAK,SAAS;AACZ,kBAAM,IAAI,mBAAmB,uCAAuC,KAAK,UAAU,GAAG,CAAC,SAAS,KAAK,UAAU,UAAU,CAAC,mFAAmF;AAAA,UAC/M;AAAA,UACA,KAAK,UAAU;AACb,qBAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF;AACA,YAAMA,aAAQ,oBAAI,SAAS,UAAU;AACrC,UAAI,OAAOA,WAAU,UAAU;AAC7B,cAAM,IAAI,mBAAmB,uCAAuC,KAAK,UAAU,GAAG,CAAC,SAAS,KAAK,UAAU,UAAU,CAAC,2DAA2D;AAAA,MACvL;AACA,gBAAUA;AAAA,IACZ;AACA,uBAAmB,SAAS,MAAM,KAAK;AAAA,EACzC;AACA,SAAO;AACT;AAEA,SAAS,eAAe,OAA2C;AACjE,MAAI,UAAU,KAAM,OAAM,IAAI,mBAAmB,iCAAiC;AAClF,MAAI,MAAM,QAAQ,KAAK,EAAG,QAAO,MAAM,IAAI,cAAc;AACzD,MAAI,OAAO,UAAU,SAAU,QAAO,UAAU,KAAK;AACrD,SAAO;AACT;AAEA,SAAS,mBAAmB,QAA0B,KAAa,OAAoB;AACrF,MAAI,UAAU,MAAM;AAClB,YAAI,mCAAmB,QAAQ,GAAG,GAAG;AACnC,oCAAU,QAAQ,GAAG;AAAA,IACvB;AAAA,EACF,OAAO;AACL,4BAAI,QAAQ,KAAK,eAAe,KAAK,CAAC;AAAA,EACxC;AACF;", "names": ["value"]}