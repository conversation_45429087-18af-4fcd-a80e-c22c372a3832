{"version": 3, "sources": ["../../../src/providers/translation-provider.tsx"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { throwErr } from \"@stackframe/stack-shared/dist/utils/errors\";\nimport { quetzalKeys, quetzalLocales } from \"../generated/quetzal-translations\";\nimport { TranslationProviderClient } from \"./translation-provider-client\";\n\nexport function TranslationProvider({ lang, translationOverrides, children }: {\n  lang: Parameters<typeof quetzalLocales.get>[0] | undefined,\n  translationOverrides?: Record<string, string>,\n  children: React.ReactNode,\n}) {\n  const locale = quetzalLocales.get(lang ?? (undefined as never));\n\n  const localeWithOverrides = new Map<string, string>(locale);\n  for (const [orig, override] of Object.entries(translationOverrides ?? {})) {\n    const key = quetzalKeys.get(orig as never) ?? throwErr(new Error(`Invalid translation override: Original key ${JSON.stringify(orig)} not found. Make sure you are passing the correct values into the translationOverrides property of the component.`));\n    localeWithOverrides.set(key, override);\n  }\n\n  return <TranslationProviderClient quetzalKeys={quetzalKeys} quetzalLocale={localeWithOverrides}>\n    {children}\n  </TranslationProviderClient>;\n}\n"], "mappings": ";AAIA,SAAS,gBAAgB;AACzB,SAAS,aAAa,sBAAsB;AAC5C,SAAS,iCAAiC;AAejC;AAbF,SAAS,oBAAoB,EAAE,MAAM,sBAAsB,SAAS,GAIxE;AACD,QAAM,SAAS,eAAe,IAAI,QAAS,MAAmB;AAE9D,QAAM,sBAAsB,IAAI,IAAoB,MAAM;AAC1D,aAAW,CAAC,MAAM,QAAQ,KAAK,OAAO,QAAQ,wBAAwB,CAAC,CAAC,GAAG;AACzE,UAAM,MAAM,YAAY,IAAI,IAAa,KAAK,SAAS,IAAI,MAAM,8CAA8C,KAAK,UAAU,IAAI,CAAC,mHAAmH,CAAC;AACvP,wBAAoB,IAAI,KAAK,QAAQ;AAAA,EACvC;AAEA,SAAO,oBAAC,6BAA0B,aAA0B,eAAe,qBACxE,UACH;AACF;", "names": []}